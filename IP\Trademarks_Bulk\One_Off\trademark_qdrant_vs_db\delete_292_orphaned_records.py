#!/usr/bin/env python3
"""
Delete 292 Orphaned Embeddings from Qdrant Collections

This script safely deletes the 292 orphaned embeddings that have N/A registration numbers
from both IP_Assets and IP_Assets_optimized collections (if they exist).

SAFETY FEATURES:
1. Backs up all record details to CSV before deletion
2. Verifies collections exist before attempting deletion
3. Only deletes records with reg_no = 'N/A' (the 292 identified orphaned records)
4. Provides detailed logging and confirmation prompts
"""

import os
import sys
import csv
import datetime
from typing import List, Dict, Any

# Add project paths
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Import Qdrant client
from qdrant_client import QdrantClient, models
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), 'orphaned_embeddings_results')
ORPHANED_CSV_PATH = os.path.join(OUTPUT_DIR, 'orphaned_embeddings_20250806_150007.csv')

# Qdrant configuration
QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")

# Collections to check and clean (Note: IP_Assets_Optimized has capital O)
COLLECTIONS_TO_CHECK = ["IP_Assets", "IP_Assets_Optimized"]


def get_qdrant_client():
    """Initialize Qdrant client."""
    if not QDRANT_URL or not QDRANT_API_KEY:
        raise ValueError("QDRANT_URL or QDRANT_API_KEY environment variables not set")
    
    return QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)


def get_292_orphaned_uuids():
    """
    Extract the 292 orphaned UUIDs from the analysis CSV.
    Returns list of UUIDs that have BOTH reg_no = 'N/A' AND plaintiff_id = 'N/A'.
    This double-check ensures we only delete the truly orphaned records.
    """
    orphaned_uuids = []

    if not os.path.exists(ORPHANED_CSV_PATH):
        raise FileNotFoundError(f"Orphaned embeddings CSV not found: {ORPHANED_CSV_PATH}")

    with open(ORPHANED_CSV_PATH, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            # Double safety check: both reg_no AND plaintiff_id must be 'N/A'
            if (row.get('reg_no') == 'N/A' and
                row.get('plaintiff_id') == 'N/A' and
                row.get('has_plaintiff_id') == 'No'):
                orphaned_uuids.append(row['point_id'])

    return orphaned_uuids


def check_collection_exists(client: QdrantClient, collection_name: str) -> bool:
    """Check if a collection exists in Qdrant."""
    try:
        client.get_collection(collection_name)
        return True
    except Exception:
        return False


def backup_records_before_deletion(client: QdrantClient, collection_name: str, uuids_to_delete: List[str]) -> str:
    """
    Backup all record details before deletion.
    Returns the backup file path.
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"backup_292_records_{collection_name}_{timestamp}.csv"
    backup_path = os.path.join(OUTPUT_DIR, backup_filename)
    
    print(f"📋 Backing up {len(uuids_to_delete)} records from {collection_name}...")
    
    # Retrieve records in batches
    batch_size = 100
    all_records = []
    
    for i in range(0, len(uuids_to_delete), batch_size):
        batch_uuids = uuids_to_delete[i:i + batch_size]
        
        try:
            # Retrieve points by IDs
            points = client.retrieve(
                collection_name=collection_name,
                ids=batch_uuids,
                with_payload=True,
                with_vectors=True
            )
            
            for point in points:
                record = {
                    'point_id': str(point.id),
                    'collection': collection_name,
                    'payload': str(point.payload) if point.payload else 'N/A',
                    'vector_size': len(point.vector.get('siglip_vector', [])) if point.vector else 0,
                    'backup_timestamp': timestamp
                }
                all_records.append(record)
                
        except Exception as e:
            print(f"⚠️  Warning: Could not retrieve batch {i//batch_size + 1}: {str(e)}")
    
    # Save backup to CSV
    if all_records:
        with open(backup_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['point_id', 'collection', 'payload', 'vector_size', 'backup_timestamp']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_records)
        
        print(f"✅ Backup saved: {backup_filename} ({len(all_records)} records)")
    else:
        print(f"⚠️  No records found to backup in {collection_name}")
    
    return backup_path


def delete_records_from_collection(client: QdrantClient, collection_name: str, uuids_to_delete: List[str]) -> Dict[str, Any]:
    """
    Delete records from a specific collection.
    Returns deletion results.
    """
    print(f"🗑️  Deleting {len(uuids_to_delete)} records from {collection_name}...")
    
    try:
        # Delete points by IDs
        delete_result = client.delete(
            collection_name=collection_name,
            points_selector=models.PointIdsList(points=uuids_to_delete)
        )
        
        return {
            'success': True,
            'collection': collection_name,
            'requested_deletions': len(uuids_to_delete),
            'result': delete_result,
            'error': None
        }
        
    except Exception as e:
        return {
            'success': False,
            'collection': collection_name,
            'requested_deletions': len(uuids_to_delete),
            'result': None,
            'error': str(e)
        }


def main():
    """
    Main function to delete 292 orphaned records.
    """
    print("🗑️  DELETE 292 ORPHANED EMBEDDINGS FROM QDRANT")
    print("=" * 60)
    print()
    
    try:
        # Step 1: Load orphaned UUIDs with safety validation
        print("📋 Step 1: Loading orphaned UUIDs with safety checks...")
        print("   Safety criteria: reg_no='N/A' AND plaintiff_id='N/A' AND has_plaintiff_id='No'")

        orphaned_uuids = get_292_orphaned_uuids()
        print(f"   ✅ Loaded {len(orphaned_uuids)} orphaned UUIDs that meet ALL safety criteria")

        if len(orphaned_uuids) != 292:
            print(f"⚠️  WARNING: Expected 292 orphaned UUIDs, found {len(orphaned_uuids)}")
            print(f"   This could mean:")
            print(f"   - CSV file has different data than expected")
            print(f"   - Safety criteria filtered out some records")
            print(f"   - Analysis results have changed")
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                print("❌ Operation cancelled by user")
                return
        else:
            print("   ✅ Count matches expected 292 orphaned records")
        print()
        
        # Step 2: Initialize Qdrant client
        print("📋 Step 2: Connecting to Qdrant...")
        client = get_qdrant_client()
        print("   ✅ Connected to Qdrant successfully")
        print()
        
        # Step 3: Check which collections exist
        print("📋 Step 3: Checking collection existence...")
        existing_collections = []
        
        for collection_name in COLLECTIONS_TO_CHECK:
            if check_collection_exists(client, collection_name):
                existing_collections.append(collection_name)
                print(f"   ✅ {collection_name}: EXISTS")
            else:
                print(f"   ❌ {collection_name}: NOT FOUND")
        
        if not existing_collections:
            print("❌ No target collections found. Nothing to delete.")
            return
        print()
        
        # Step 4: Confirmation prompt
        print("📋 Step 4: Deletion confirmation...")
        print(f"   Collections to clean: {existing_collections}")
        print(f"   Records to delete: {len(orphaned_uuids)}")
        print(f"   Total deletions: {len(orphaned_uuids) * len(existing_collections)}")
        print()
        
        print("🚨 FINAL CONFIRMATION:")
        print("   This will PERMANENTLY DELETE the 292 orphaned embeddings")
        print("   from the specified Qdrant collections.")
        print()
        response = input("Are you sure you want to proceed? Type 'DELETE' to confirm: ")
        
        if response != 'DELETE':
            print("❌ Operation cancelled by user")
            return
        print()
        
        # Step 5: Backup and delete from each collection
        deletion_results = []
        backup_files = []
        
        for collection_name in existing_collections:
            print(f"📋 Processing collection: {collection_name}")
            print("-" * 40)
            
            # Backup records
            backup_path = backup_records_before_deletion(client, collection_name, orphaned_uuids)
            backup_files.append(backup_path)
            
            # Delete records
            result = delete_records_from_collection(client, collection_name, orphaned_uuids)
            deletion_results.append(result)
            
            # Report result
            if result['success']:
                print(f"   ✅ Successfully deleted from {collection_name}")
            else:
                print(f"   ❌ Failed to delete from {collection_name}: {result['error']}")
            print()
        
        # Step 6: Final summary
        print("📊 DELETION SUMMARY")
        print("=" * 30)
        
        successful_deletions = sum(1 for r in deletion_results if r['success'])
        failed_deletions = sum(1 for r in deletion_results if not r['success'])
        
        print(f"Collections processed: {len(existing_collections)}")
        print(f"Successful deletions: {successful_deletions}")
        print(f"Failed deletions: {failed_deletions}")
        print(f"Records per collection: {len(orphaned_uuids)}")
        print(f"Total records deleted: {successful_deletions * len(orphaned_uuids)}")
        print()
        
        print("📁 BACKUP FILES CREATED:")
        for backup_file in backup_files:
            if os.path.exists(backup_file):
                print(f"   ✅ {os.path.basename(backup_file)}")
            else:
                print(f"   ❌ {os.path.basename(backup_file)} (not created)")
        print()
        
        if failed_deletions > 0:
            print("⚠️  FAILED DELETIONS:")
            for result in deletion_results:
                if not result['success']:
                    print(f"   ❌ {result['collection']}: {result['error']}")
        
        print("✅ DELETION PROCESS COMPLETED")
        
    except Exception as e:
        print(f"❌ Error during deletion process: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
