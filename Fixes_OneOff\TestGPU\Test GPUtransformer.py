import tensorflow as tf
from PIL import Image
from sentence_transformers import SentenceTransformer
import os
import time

print(tf.config.list_physical_devices('GPU'))

# Additional check to confirm Tensor<PERSON>low can see the GPU
if tf.config.list_physical_devices('GPU'):

    print("TensorFlow is using the GPU")
else:
    print("TensorFlow is NOT using the GPU")
    print("Check CUDA and cuDNN installation and environment variables.")

# You can also try setting memory growth to allow Tensor<PERSON>low to allocate memory as needed
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        # Currently, memory growth needs to be the same across GPUs
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
    except RuntimeError as e:
        # Memory growth must be set before GPUs have been initialized
        print(e)

huggingface_cache_dir = os.path.join(os.getcwd(), "data", "Models", "huggingface")
os.makedirs(huggingface_cache_dir, exist_ok=True)
model_clipv2 = SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True, cache_folder=huggingface_cache_dir)

folder_path = os.path.join(os.getcwd(), "data", "EvidencesJanFeb")
image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
image_paths = image_paths[:50]

time_start = time.time()
print(f"Starting to encode {len(image_paths)} images")
embeddings = model_clipv2.encode([Image.open(image) for image in image_paths])
time_end = time.time()
print(f"Time taken: {time_end - time_start:.1f} seconds")

