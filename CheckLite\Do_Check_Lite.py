import os, time, tempfile, asyncio
from FileManagement.Tencent_COS import get_cos_client
from Check.Data_Cache import get_cached_plaintiff_df, get_cached_cases_df
from CheckLite.Do_Check_Trademark import check_trademarks
from CheckLite.Do_Check_Copyright import check_copyrights
from CheckLite.Do_Check_Patent import check_patents
from langfuse import observe
import langfuse
from FileManagement.Tencent_COS import async_upload_file_with_retry
from Check.gRPC_Client import get_siglip_embeddings_grpc
from Check.Do_Check_Download import download

async def download_and_check(check_id, client_id, client_name, api_key, product_category, main_product_image, ip_keywords, description, language='zh', **kwargs):
    print(f"📋 [CHECK:{check_id}] Starting download_and_check process")
    print(f"📋 [CHECK:{check_id}] client_id: {client_id}")
    print(f"📋 [CHECK:{check_id}] product image: {'present' if main_product_image else 'none'}")
    print(f"📋 [CHECK:{check_id}] ip_keywords count: {len(ip_keywords)}")
    print(f"📋 [CHECK:{check_id}] description length: {len(description) if description else 0}")
    
    # Get COS client: used to 1. download product images (in case it is not base64 or url) and 2. upload product images (in do_check, and parts in do_check_copyright)
    cos_client, cos_bucket = get_cos_client(secret_id_env="COS_MDLV_SECRET_ID",secret_key_env="COS_MDLV_SECRET_KEY",bucket="tro-1330776830")
    
    # Create temporary directory for this check
    with tempfile.TemporaryDirectory() as temp_dir:
        start_time = time.time()
        print(f"📋 [CHECK:{check_id}] Starting file downloads")
        # The download function now raises ImageDownloadError on failure.
        local_product_images, _, _ = await download(cos_client, cos_bucket, temp_dir, check_id, main_product_image)
        download_time = time.time() - start_time

        print(f"🔨 [CHECK:{check_id}] Downloaded files in {download_time:.1f} seconds")

        start_time = time.time()
        try:
            print(f"📋 [CHECK:{check_id}] Starting check process")
            check_kwargs = {
                'cos_client': cos_client,
                'cos_bucket': cos_bucket,
                'temp_dir': temp_dir,
                'check_id': check_id,
                'client_id': client_id,
                'local_product_images': local_product_images,
                'description': description,
                'ip_keywords': ip_keywords,
                'language': language
            }
           
            results = await check(**check_kwargs)
            check_time = time.time() - start_time
            print(f"🔨 [CHECK:{check_id}] Check completed in {check_time:.1f} seconds with results count: {len(results.get('results', []))}")
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            print(f"\033[91m[CHECK:{check_id}] CRITICAL ERROR IN CHECK: {str(e)}\nTRACEBACK:\n{tb}\033[0m")
            raise

        return results

# @profile
@observe(capture_input=False, capture_output=False)
async def check(cos_client, cos_bucket, temp_dir, check_id, client_id, local_product_images, description, ip_keywords, language='zh', **kwargs):
    langfuse.get_client().update_current_span(input={
        "check_id": check_id, "client_id": client_id,
        "local_product_images": local_product_images,
        "description": description, "ip_keywords": ip_keywords, "language": language
    })

    # Use cached DataFrames if available
    start_time = time.time()
    plaintiff_df = get_cached_plaintiff_df()
    cases_df = get_cached_cases_df()
    metadata = {"cache_time": time.time() - start_time}
    print(f"🔨 [CHECK:{check_id}] Check: got dataframes (plaintiff_df & cases_df) in {time.time() - start_time:.1f} seconds")


    start_time = time.time()

    print(f"📋 [CHECK:{check_id}] Starting IP checks (trademark, copyright, patent)")

    # Start the embedding generation as a background task.
    precomputed_embeddings_map = await get_siglip_embeddings_grpc(local_product_images, "image")
    metadata["embedding_time"] = time.time() - start_time

    start_time = time.time()
    tasks = [check_trademarks(cos_client, cos_bucket, temp_dir, client_id, check_id, local_product_images, description, ip_keywords, cases_df, plaintiff_df, precomputed_embeddings_map=precomputed_embeddings_map, language=language),
             check_copyrights(cos_client, cos_bucket, temp_dir, client_id, check_id, local_product_images, cases_df, plaintiff_df, precomputed_embeddings_map=precomputed_embeddings_map, language=language),
             check_patents(cos_client, cos_bucket, temp_dir, client_id, check_id, local_product_images, description, ip_keywords, cases_df, plaintiff_df, precomputed_embeddings_map=precomputed_embeddings_map, language=language)
             ]
    all_ip_results = await asyncio.gather(*tasks)

    
    results = [item for sublist in all_ip_results if sublist is not None for item in sublist]
    ip_check_time = time.time() - start_time
    print(f"🔨 [CHECK:{check_id}] All tasks (trademark, copyright, patent) completed in {ip_check_time:.1f} seconds, with total raw results: {len(results)}")
    metadata["ip_check_time"] = ip_check_time
    
    start_time = time.time()
    unique_results = []
    seen_keys = set()
    for result in results:
        # Determine key type and validate
        if "ip_asset_urls" in result and len(result["ip_asset_urls"]) > 0: # Trademark text now have an empty list for ip_asset_urls
            current_key = (result["ip_type"], result["ip_asset_urls"][0])
        else:
            # print(f"ip_owner for result: {result['ip_owner']} of type: {result['type']}")
            current_key = (result["ip_type"], result["ip_owner"])

        # Common processing for both cases
        if current_key not in seen_keys:
            seen_keys.add(current_key)
            
            if "plaintiff_id" in result and result["plaintiff_id"]:
                # This is already done in qdrant_search, but not for trademark text. In the future maybe we only keep it here.
                plaintiff_id = int(float(result["plaintiff_id"]))
                plaintiff_info = plaintiff_df[plaintiff_df['id'] == plaintiff_id]
                if not plaintiff_info.empty:
                    result['plaintiff_name'] = plaintiff_info.iloc[0]['plaintiff_name']
                
                case_info = cases_df[cases_df["plaintiff_id"] == plaintiff_id]
                result["number_of_cases"] = case_info.shape[0]
                if not case_info.empty:
                    last_case = case_info.sort_values("date_filed", ascending=False).iloc[0]
                    result["last_case_docket"] = last_case['docket']
                    result["last_case_date_filed"] = str(last_case['date_filed'])
                    
            unique_results.append(result)

    print(f"📋 [CHECK:{check_id}] Unique results: {len(unique_results)}")

    # Sort results by similarity score (highest first) before cleaning up
    unique_results.sort(key=lambda x: float(x.get("similarity", 0)), reverse=True)

    # Clean up results before returning by removing temporary keys
    keys_to_pop = ["product_local_path", "internal_type", "similarity", "risk_score", "risk_level"]
    for result in unique_results:
        for key in keys_to_pop:
            result.pop(key, None)

    print(f'🔨 [CHECK:{check_id}] Check Analysis Completed')
    
    dedupe_time = time.time() - start_time
    metadata["dedupe_time"] = dedupe_time

    final_results = {
        "check_id": check_id,
        "status": "success",
        "results": unique_results
    }
    langfuse.get_client().update_current_span(output=final_results, metadata=metadata)
    return final_results