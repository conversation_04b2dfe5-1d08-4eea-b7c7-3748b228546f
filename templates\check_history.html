{% include 'common/api_studio_header.html' %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="{{ url_path_for('static', path='api_studio/results-display.js') }}"></script>
<script src="{{ url_path_for('static', path='api_studio/check-history.js') }}"></script>

    <div class="container main-content">
        <div class="left">
            <h2 data-i18n="check_history">Check History</h2>
            <div class="form-group">
                <label for="api_key" data-i18n="api_key">API Key:</label><span class="required-field">*</span>
                <input type="text" id="api_key" name="api_key">
                <button type="button" id="fetchHistoryBtn" data-i18n="fetch_history">Fetch History</button>
            </div>
            <div id="historyControls" style="display: none;">
                <div class="form-group">
                    <label for="datePicker" data-i18n="select_date">Select Date:</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <button id="prevDateBtn"><</button>
                        <input type="text" id="datePicker" placeholder="Select a date">
                        <button id="nextDateBtn">></button>
                    </div>
                </div>
                <div id="checkList" class="reverse-check-list">
                    <!-- Check history will be loaded here -->
                </div>
            </div>
        </div>
        <div class="right">
            <h2 data-i18n="check_details">Check Details</h2>
            <div id="output">
                <div id="request-info"></div>
                <hr/>
                <div id="results-display"></div>
            </div>
        </div>
    </div>
    <input type="hidden" id="language" name="language" value="en">
</body>
</html>