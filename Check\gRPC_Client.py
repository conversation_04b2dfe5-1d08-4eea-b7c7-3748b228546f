import grpc
import os
import numpy as np
from Protos import embedding_pb2, embedding_pb2_grpc
from langfuse import observe
import langfuse

# Global variable to hold the gRPC channel
channel = None

def init_grpc_channel():
    """Initializes the shared gRPC channel."""
    global channel
    if channel is None:
        channel = grpc.aio.insecure_channel('localhost:5001', options=[
            ('grpc.max_send_message_length', 100 * 1024 * 1024),
            ('grpc.max_receive_message_length', 100 * 1024 * 1024),
        ])

def get_grpc_channel():
    """Returns the shared gRPC channel, initializing it if necessary."""
    if channel is None:
        init_grpc_channel()
    return channel

async def close_grpc_channel():
    """Closes the shared gRPC channel."""
    global channel
    if channel:
        await channel.close()
        channel = None

@observe(capture_input=False, capture_output=False)
async def get_siglip_embeddings_grpc(data_list, data_type="image"):
    if not data_list:
        return {}
        
    if isinstance(data_list, str):
        data_list = [data_list]
    langfuse.get_client().update_current_span(input=f"Getting SigLIP embeddings for {len(data_list)} {data_type}(s)")

    ch = get_grpc_channel()
    stub = embedding_pb2_grpc.EmbeddingServiceStub(ch)

    inputs_proto = []
    for item in data_list:
        if data_type == "image":
            image_bytes = item
            if isinstance(item, str) and os.path.exists(item):
                with open(item, 'rb') as f:
                    image_bytes = f.read()
            inputs_proto.append(embedding_pb2.EmbeddingInput(image_data=image_bytes))
        elif data_type == "text":
            inputs_proto.append(embedding_pb2.EmbeddingInput(text_data=item))

    request = embedding_pb2.GetEmbeddingsRequest(inputs=inputs_proto)
    
    try:
        metadata = [('authorization', f'Bearer {os.getenv("API_BEARER_TOKEN")}')]
        response = await stub.GetEmbeddings(request, timeout=120, metadata=metadata)
        embeddings = np.array([list(e.embedding) for e in response.embeddings])
        return {path: emb for path, emb in zip(data_list, embeddings)}
    except grpc.aio.AioRpcError as e:
        print(f"Async gRPC call failed: {e.details()}")
        return {}

async def get_all_matches_grpc(description: str):
    """
    Finds all trademark matches and checks for a perfect, whole-word match via gRPC.

    Returns:
        A tuple containing:
        - A list of all matches found (start, end, metadata).
        - A boolean indicating if a perfect match for the entire description was found.
    """
    ch = get_grpc_channel()
    stub = embedding_pb2_grpc.EmbeddingServiceStub(ch)
    request = embedding_pb2.GetAllMatchesRequest(text=description)
    try:
        response = await stub.GetAllMatches(request)
        return response.matches, response.is_perfect
    except grpc.aio.AioRpcError as e:
        print(f"Async gRPC call failed: {e.details()}")
        return [], False