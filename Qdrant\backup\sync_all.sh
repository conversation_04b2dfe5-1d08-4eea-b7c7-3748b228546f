#!/bin/bash
# This script runs all sync processes

set -euo pipefail

# Sync Qdrant
echo "--- Starting Qdrant Sync ---"
/bin/bash "$(dirname "$0")/sync_qdrant.sh"
echo "--- Qdrant Sync Finished ---"

# Sync Postgres
echo "--- Starting Postgres Sync ---"
/bin/bash "$(dirname "$0")/sync_postgres.sh"
echo "--- Postgres Sync Finished ---"

echo "--- All Sync Processes Completed ---"

# Sync Kong
echo "--- Starting Kong Sync ---"
/bin/bash "$(dirname "$0")/sync_kong.sh"
echo "--- Kong Sync Finished ---"

echo "--- All Sync Processes Completed ---"