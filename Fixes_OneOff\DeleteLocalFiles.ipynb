{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["import os\n", "import shutil\n", "\n", "project_root = os.path.dirname(os.getcwd())\n", "\n", "local_case_folder = os.path.join(project_root, \"Documents\", \"Case Files\")\n", "for item in os.listdir(local_case_folder):\n", "    item_path = os.path.join(local_case_folder, item)\n", "    if os.path.isdir(item_path):\n", "        shutil.rmtree(item_path)\n", "        print(f\" 🔥🔥🔥 Deleting folder: {item_path}\")\n", "\n", "local_plaintiff_folder = os.path.join(project_root, \"Documents\", \"Plaintiff Files\")\n", "for item in os.listdir(local_plaintiff_folder):\n", "    item_path = os.path.join(local_plaintiff_folder, item)\n", "    if os.path.isdir(item_path):\n", "        shutil.rmtree(item_path)\n", "        print(f\" 🔥🔥🔥 Deleting folder: {item_path}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}