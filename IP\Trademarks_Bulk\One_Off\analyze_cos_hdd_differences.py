#!/usr/bin/env python3
"""
Analyze Differences Between HDD and Tencent COS

Transformed from images_cos_sync() to analyze and print differences
between local HDD images and Tencent COS without making any changes.

Based on: FileManagement/Tencent_COS.py lines 285-386
"""

import os
import sys
import time
import csv
import datetime
from collections import defaultdict

# Add project paths
sys.path.append(os.getcwd())
from logdata import log_message
from Common.uuid_utils import generate_obfuscated_key
from FileManagement.Tencent_COS import get_cos_client, list_all_files_in_COS


def analyze_cos_hdd_differences(base_path, cos_prefix, extension, output_csv=True):
    """
    Analyze differences between local HDD and Tencent COS without making changes.
    
    Args:
        base_path: Local directory path to analyze
        cos_prefix: COS prefix to check
        extension: File extension to analyze (e.g., 'webp', 'png')
        output_csv: Whether to save results to CSV
    
    Returns:
        dict: Analysis results with differences
    """
    print(f"🔍 ANALYZING DIFFERENCES: HDD vs Tencent COS")
    print(f"📁 Local path: {base_path}")
    print(f"☁️  COS prefix: {cos_prefix}")
    print(f"📄 Extension: {extension}")
    print("=" * 70)
    
    if not os.path.exists(base_path):
        print(f"❌ Directory not found: {base_path}")
        return None

    # Get COS client with custom credentials (same as original)
    client, bucket = get_cos_client(
        secret_id_env="COS_MDLV_SECRET_ID",
        secret_key_env="COS_MDLV_SECRET_KEY",
        bucket="tro-1330776830"
    )

    # Step 1: Collect all local image paths
    print("📋 Step 1: Scanning local HDD...")
    start_time = time.time()
    
    local_images = []
    local_files_by_name = {}
    
    for root, _, files in os.walk(base_path):
        for file in files:
            if file.endswith(f".{extension}"):
                full_path = os.path.join(root, file)
                filename_without_ext = os.path.splitext(file)[0]
                local_images.append({
                    'full_path': full_path,
                    'filename': filename_without_ext,
                    'relative_path': os.path.relpath(full_path, base_path),
                    'size_bytes': os.path.getsize(full_path),
                    'modified_time': os.path.getmtime(full_path)
                })
                local_files_by_name[filename_without_ext] = full_path

    scan_time = time.time() - start_time
    print(f"   Found {len(local_images):,} local {extension} files in {scan_time:.2f} seconds")
    print()

    # Step 2: Get existing files in COS
    print("📋 Step 2: Scanning Tencent COS...")
    start_time = time.time()
    
    existing_cos_files = set(list_all_files_in_COS(prefix=cos_prefix, client=client, bucket=bucket))
    cos_scan_time = time.time() - start_time
    print(f"   Found {len(existing_cos_files):,} COS files in {cos_scan_time:.2f} seconds")
    print()

    # Step 3: Generate expected COS keys for local files
    print("📋 Step 3: Mapping local files to expected COS keys...")
    
    # Remove trailing slash from cos_prefix (same logic as original)
    if cos_prefix.endswith('/'):
        cos_prefix = cos_prefix[:-1]
    
    expected_cos_keys = set()
    local_to_cos_mapping = {}
    
    for image in local_images:
        filename = image['filename']
        obfuscated = generate_obfuscated_key(filename)
        expected_cos_key = f"{cos_prefix}/{obfuscated}.{extension}"
        expected_cos_keys.add(expected_cos_key)
        local_to_cos_mapping[expected_cos_key] = image
    
    print(f"   Generated {len(expected_cos_keys):,} expected COS keys")
    print()

    # Step 4: Analyze differences
    print("📋 Step 4: Analyzing differences...")
    
    # Files that exist locally but not in COS (need upload)
    missing_in_cos = expected_cos_keys - existing_cos_files
    
    # Files that exist in COS but not locally (need deletion from COS)
    extra_in_cos = existing_cos_files - expected_cos_keys
    
    # Files that exist in both (synchronized)
    synchronized_files = expected_cos_keys & existing_cos_files
    
    print(f"   Analysis complete!")
    print()

    # Step 5: Generate detailed results
    print("📊 ANALYSIS RESULTS")
    print("=" * 30)
    
    print(f"📁 LOCAL HDD:")
    print(f"   Total files: {len(local_images):,}")
    print(f"   Total size: {sum(img['size_bytes'] for img in local_images) / (1024**3):.2f} GB")
    print()
    
    print(f"☁️  TENCENT COS:")
    print(f"   Total files: {len(existing_cos_files):,}")
    print()
    
    print(f"🔄 SYNCHRONIZATION STATUS:")
    print(f"   Synchronized files: {len(synchronized_files):,}")
    print(f"   Missing in COS (need upload): {len(missing_in_cos):,}")
    print(f"   Extra in COS (need deletion): {len(extra_in_cos):,}")
    print()
    
    sync_percentage = (len(synchronized_files) / len(expected_cos_keys)) * 100 if expected_cos_keys else 0
    print(f"📈 SYNC PERCENTAGE: {sync_percentage:.1f}%")
    print()

    # Step 6: Show examples
    if missing_in_cos:
        print(f"📋 EXAMPLES - MISSING IN COS (first 10):")
        for i, cos_key in enumerate(list(missing_in_cos)[:10], 1):
            local_info = local_to_cos_mapping.get(cos_key, {})
            local_path = local_info.get('relative_path', 'Unknown')
            size_mb = local_info.get('size_bytes', 0) / (1024**2)
            print(f"   {i}. {local_path} → {cos_key} ({size_mb:.1f} MB)")
        print()
    
    if extra_in_cos:
        print(f"📋 EXAMPLES - EXTRA IN COS (first 10):")
        for i, cos_key in enumerate(list(extra_in_cos)[:10], 1):
            print(f"   {i}. {cos_key}")
        print()

    # Step 7: Save to CSV if requested
    results = {
        'local_files': local_images,
        'cos_files': list(existing_cos_files),
        'missing_in_cos': list(missing_in_cos),
        'extra_in_cos': list(extra_in_cos),
        'synchronized': list(synchronized_files),
        'analysis_time': datetime.datetime.now().isoformat(),
        'base_path': base_path,
        'cos_prefix': cos_prefix,
        'extension': extension
    }
    
    if output_csv:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"cos_hdd_analysis_{extension}_{timestamp}.csv"
        csv_path = os.path.join(os.path.dirname(__file__), csv_filename)
        
        try:
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'category', 'cos_key', 'local_path', 'size_bytes', 'size_mb', 
                    'modified_time', 'recommendation'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                # Write missing in COS
                for cos_key in missing_in_cos:
                    local_info = local_to_cos_mapping.get(cos_key, {})
                    writer.writerow({
                        'category': 'MISSING_IN_COS',
                        'cos_key': cos_key,
                        'local_path': local_info.get('relative_path', ''),
                        'size_bytes': local_info.get('size_bytes', 0),
                        'size_mb': local_info.get('size_bytes', 0) / (1024**2),
                        'modified_time': local_info.get('modified_time', ''),
                        'recommendation': 'UPLOAD_TO_COS'
                    })
                
                # Write extra in COS
                for cos_key in extra_in_cos:
                    writer.writerow({
                        'category': 'EXTRA_IN_COS',
                        'cos_key': cos_key,
                        'local_path': '',
                        'size_bytes': 0,
                        'size_mb': 0,
                        'modified_time': '',
                        'recommendation': 'DELETE_FROM_COS'
                    })
                
                # Write synchronized
                for cos_key in list(synchronized_files)[:1000]:  # Limit to 1000 for CSV size
                    local_info = local_to_cos_mapping.get(cos_key, {})
                    writer.writerow({
                        'category': 'SYNCHRONIZED',
                        'cos_key': cos_key,
                        'local_path': local_info.get('relative_path', ''),
                        'size_bytes': local_info.get('size_bytes', 0),
                        'size_mb': local_info.get('size_bytes', 0) / (1024**2),
                        'modified_time': local_info.get('modified_time', ''),
                        'recommendation': 'NO_ACTION_NEEDED'
                    })
            
            print(f"📄 Detailed analysis saved to: {csv_filename}")
            
        except Exception as e:
            print(f"❌ Error saving CSV: {str(e)}")
    
    print()
    print("✅ ANALYSIS COMPLETED")
    
    return results


def main():
    """
    Main function to run COS/HDD difference analysis.
    """
    # Example usage - analyze trademarks
    from Common.Constants import local_ip_folder
    
    print("🔍 COS/HDD DIFFERENCE ANALYSIS")
    print("=" * 40)
    print()
    
    # Analyze trademark images
    trademark_path = os.path.join(local_ip_folder, "Trademarks", "USPTO_Daily", "Images")
    results = analyze_cos_hdd_differences(
        base_path=trademark_path,
        cos_prefix="ip_assets/Trademarks/",
        extension="webp",
        output_csv=True
    )
    
    if results:
        print(f"📊 SUMMARY:")
        print(f"   Local files: {len(results['local_files']):,}")
        print(f"   COS files: {len(results['cos_files']):,}")
        print(f"   Need upload: {len(results['missing_in_cos']):,}")
        print(f"   Need deletion: {len(results['extra_in_cos']):,}")
        print(f"   Synchronized: {len(results['synchronized']):,}")


if __name__ == "__main__":
    main()
