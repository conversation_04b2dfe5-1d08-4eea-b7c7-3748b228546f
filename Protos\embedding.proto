syntax = "proto3";

package embedding;

message Embedding {
    repeated float embedding = 1;
}

message ImageSplit {
    bytes image_data = 1;
    repeated float embedding = 2;
    Rectangle rectangle = 3;
    string filename = 4;
}

message Rectangle {
    repeated float points = 1;
}

message RectangleList {
    repeated Rectangle rects = 1;
}

message ImageInfo {
    bytes image_data = 1;
    string original_filename = 2;
}

message GetImageSplitWithEmbeddingsRequest {
    repeated ImageInfo images = 1;
}

message ImageSplitResult {
    repeated ImageSplit image_splits = 1;
    string original_filename = 2;
    map<string, RectangleList> rects_map = 3;
}

message GetImageSplitWithEmbeddingsResponse {
    repeated ImageSplitResult results = 1;
}

message EmbeddingInput {
    oneof data {
        bytes image_data = 1;
        string text_data = 2;
    }
}

message GetEmbeddingsRequest {
    repeated EmbeddingInput inputs = 1;
}

message GetEmbeddingsResponse {
    repeated Embedding embeddings = 1;
}

message TrademarkMetadata {
    string mark_text = 1;
    optional int32 plaintiff_id = 2;
    string reg_no = 3;
    string ser_no = 4;
    string applicant_name = 5;
    repeated int32 int_cls = 6;
    string goods_services_text_daily = 7;
}

message GetAllMatchesRequest {
    string text = 1;
}

message Match {
    int32 start = 1;
    int32 end = 2;
    TrademarkMetadata metadata = 3;
}

message GetAllMatchesResponse {
    repeated Match matches = 1;
    bool is_perfect = 2;
}

service EmbeddingService {
    rpc GetEmbeddings(GetEmbeddingsRequest) returns (GetEmbeddingsResponse);
    rpc GetImageSplitWithEmbeddings(GetImageSplitWithEmbeddingsRequest) returns (GetImageSplitWithEmbeddingsResponse);
    rpc GetAllMatches(GetAllMatchesRequest) returns (GetAllMatchesResponse);
}