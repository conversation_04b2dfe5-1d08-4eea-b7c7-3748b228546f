import os
import shutil
import pandas as pd
from tqdm import tqdm
import sys
import imagehash
from PIL import Image
from Common.Constants import local_ip_tro_folder
from IP.Trademarks_Bulk.trademark_db import get_db_connection
from qdrant_client import QdrantClient, models
import uuid

sys.path.append(os.getcwd())

# Qdrant Configuration
QDRANT_API_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", None)
QDRANT_COLLECTION_IP_ASSETS = "IP_Assets_Optimized"
qdrant_client = QdrantClient(url=QDRANT_API_URL, api_key=QDRANT_API_KEY)

def update_copyright_production_status(filename, status):
    """Updates the production status of a copyright file in the database."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        sql = "UPDATE copyrights_files SET production = %s WHERE filename = %s"
        cursor.execute(sql, (status, filename))
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error updating production status for {filename}: {e}")
    finally:
        if conn:
            conn.close()

def find_and_copy_duplicates(hash_size=16, threshold_critical=15, threshold_moderate=25):
    """
    Identifies visually similar images and processes them based on similarity thresholds.
    """
    production_folder = os.path.join(local_ip_tro_folder, "copyrights", "Production")
    all_folder = os.path.join(local_ip_tro_folder, "copyrights", "All")
    duplicate_folder = os.path.join(local_ip_tro_folder, "copyrights", "DuplicateOneOff")
    os.makedirs(duplicate_folder, exist_ok=True)

    all_hashes = {}
    for filename in tqdm(os.listdir(all_folder), desc="Hashing 'All' folder images"):
        if filename.lower().endswith('.webp') and filename.split('_')[1].startswith("VA"):
            try:
                file_path = os.path.join(all_folder, filename)
                with Image.open(file_path) as img:
                    img_hash = imagehash.phash(img, hash_size=hash_size)
                    all_hashes[img_hash] = filename
            except (IndexError, FileNotFoundError):
                continue

    pair_index = 1
    processed_files = set()

    for prod_filename in tqdm(os.listdir(production_folder), desc="Processing Production files"):
        if prod_filename.lower().endswith('.webp') and prod_filename.split('_')[1].startswith("MD") and prod_filename not in processed_files:
            prod_file_path = os.path.join(production_folder, prod_filename)
            try:
                with Image.open(prod_file_path) as img:
                    prod_hash = imagehash.phash(img, hash_size=hash_size)
                  
                for all_hash, all_filename in all_hashes.items():
                    if all_filename not in processed_files:
                        hash_diff = prod_hash - all_hash
                        
                        if hash_diff < threshold_critical:
                            print(f"Critical match found: {prod_filename} and {all_filename} (Diff: {hash_diff})")
                            
                            # Move VA file to production
                            shutil.copy(os.path.join(all_folder, all_filename), production_folder)
                            
                            # Update database
                            update_copyright_production_status(prod_filename, False)
                            update_copyright_production_status(all_filename, True)
                            
                            # Update Qdrant
                            md_point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, prod_filename))
                            va_point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, all_filename))
                            
                            try:
                                md_point = qdrant_client.retrieve(collection_name=QDRANT_COLLECTION_IP_ASSETS, ids=[md_point_id], with_vectors=True)[0]
                                if md_point:
                                    new_payload = md_point.payload
                                    new_payload['filename'] = all_filename
                                    new_payload['certificate_name'] = all_filename.replace('.webp', '_full.webp')
                                    new_payload['reg_no'] = all_filename.split('_')[1]
                                    new_payload["plaintiff_id"] = int(all_filename.split('_')[0])

                                    qdrant_client.upsert(
                                        collection_name=QDRANT_COLLECTION_IP_ASSETS,
                                        points=[models.PointStruct(id=va_point_id, vector=md_point.vector, payload=new_payload)],
                                        wait=True
                                    )
                                    qdrant_client.delete(collection_name=QDRANT_COLLECTION_IP_ASSETS, points_selector=[md_point_id])
                                    print(f"Qdrant vector for {prod_filename} transferred to {all_filename}")
                            except Exception as e:
                                print(f"Error updating Qdrant for {prod_filename}: {e}")

                            # Delete MD file from production
                            os.remove(prod_file_path)

                            processed_files.add(prod_filename)
                            processed_files.add(all_filename)
                            break

                        elif threshold_critical <= hash_diff < threshold_moderate:
                            print(f"Moderate match found: {prod_filename} and {all_filename} (Diff: {hash_diff})")
                            shutil.copy(prod_file_path, os.path.join(duplicate_folder, f"{pair_index}_MD.webp"))
                            shutil.copy(os.path.join(all_folder, all_filename), os.path.join(duplicate_folder, f"{pair_index}_VA.webp"))
                            
                            processed_files.add(prod_filename)
                            processed_files.add(all_filename)
                            pair_index += 1
                            break

            except (IndexError, FileNotFoundError, Exception) as e:
                print(f"Error processing {prod_filename}: {e}")
                continue
            
    print("All done")

if __name__ == "__main__":
    find_and_copy_duplicates()