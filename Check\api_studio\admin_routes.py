from fastapi import <PERSON><PERSON>out<PERSON>, Request, Header
from fastapi.responses import JSONResponse
import os
from typing import Optional

# Create APIRouter
admin_router = APIRouter()

def create_error_response(error_code, message, details=None, status_code=400):
    """Creates a standardized error response."""
    content = {
        "error": {
            "error_code": error_code,
            "message": message
        }
    }
    if details:
        content["error"]["details"] = details
    return JSONResponse(content=content, status_code=status_code)