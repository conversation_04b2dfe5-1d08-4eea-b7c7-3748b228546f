"""
<PERSON><PERSON>lare Worker for Google API Relay (Python)
This worker handles Google API authentication and content generation
"""

from google import genai
import json

# Cloudflare Worker for Python (wrangler.toml compatibility)
# This worker relays specific Google API requests through Cloudflare's infrastructure

async def handle_request(request, env, ctx):
    """Main request handler"""
    url = new URL(request.url)
    
    # Only process specific paths for Google API requests
    if not url.pathname.startswith('/google-api/'):
        return new Response("Not Found", status=404)
    
    # Get request body which includes credentials and request details
    body = await request.json()
    
    # Extract credentials and request details
    service_account = body.get('service_account', {})
    model_name = body.get('model_name')
    contents = body.get('contents')
    config = body.get('config')
    
    try:
        # Create Google API client with provided credentials
        client = genai.Client(
            project=service_account.get('project_id'),
            credentials=service_account.get('credentials')
        )
        
        # Generate content using the client
        response = await client.aio.models.generate_content(
            model=model_name,
            contents=contents,
            config=config
        )
        
        # Return the response
        return new Response(
            json.dumps({"text": response.text}),
            headers={"Content-Type": "application/json"}
        )
        
    except Exception as e:
        return new Response(
            json.dumps({"error": str(e)}),
            headers={"Content-Type": "application/json"},
            status=500
        )

# Main handler function for Cloudflare Workers
async def on_fetch(request, env, ctx):
    return await handle_request(request, env, ctx) 