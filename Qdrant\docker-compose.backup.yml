services:
  # --- Application Services ---
  qdrant:
    image: qdrant/qdrant
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "6333:6333" # Standard REST/gRPC port
      - "6334:6334" # Internal gRPC port (often not needed externally)
    environment:
      # Set the API key for security
      - QDRANT__SERVICE__API_KEY=${QDRANT_API_KEY}
            # --- Enable TLS and point to wildcard certs in /ssl ---
      - QDRANT__SERVICE__ENABLE_TLS=true
      # Paths will be under the base domain name used for the cert request
      - QDRANT__TLS__CERT=/ssl/live/maidalv.com/fullchain.pem
      - QDRANT__TLS__KEY=/ssl/live/maidalv.com/privkey.pem
    volumes:
      # Mount persistent storage for Qdrant data
      - /docker/qdrant_storage:/qdrant/storage
      - /mnt/4tb/qdrant/snapshots:/qdrant/snapshots
      - /mnt/4tb/qdrant/temp:/qdrant/temp
      - /ssl:/ssl:ro
    networks:
      monitoring:
          aliases:
            - qdrantinternal.maidalv.com   # matches your *.maidalv.com cert

  postgresql:
    image: postgres # Use official postgres image
    container_name: postgresql
    restart: unless-stopped
    ports:
      - "5432:5432" # Standard PostgreSQL port
    environment:
      # Set user and password for the database
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      # Optionally specify a database name, otherwise defaults to POSTGRES_USER
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      # Mount persistent storage for PostgreSQL data
      - /docker/postgresql:/var/lib/postgresql/data
    networks:
      - monitoring
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: qdrant_check_api
    restart: unless-stopped
    ports:
      - "8000:8000" # FastAPI port
    env_file:
      - ./api/.env
    volumes:
      - '/mnt/4tb/maidalv_data/IP_Data:/app/Documents/IP'
    depends_on:
      - qdrant
      - postgresql
    networks:
      - monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 10s
      timeout: 5s
      retries: 3

  # --- SSL Certificate Management ---
  certbot:
    build:
      context: .
      dockerfile: Dockerfile.certbot
    container_name: certbot
    restart: unless-stopped
    volumes:
      # Use a named volume to store Let's Encrypt data persistently
      - /ssl:/etc/letsencrypt
    environment:
      # Cloudflare API Token (ensure this is set in your .env file or environment)
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      # Email for Let's Encrypt notifications
      - CERTBOT_EMAIL=<EMAIL>
      # Domains to certify (base domain first for wildcard)
      - CERTBOT_DOMAINS=maidalv.com,*.maidalv.com
      # Optional: Key type (default is ecdsa, rsa might have wider compatibility)
      # - CERTBOT_KEY_TYPE=rsa
      # Optional: Set renewal interval if needed (default is 12 hours)
      # - RENEWAL_INTERVAL=43200
    networks:
      - monitoring

  # --- Lightweight Monitoring Exporters ---
  node-exporter:
    image: quay.io/prometheus/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    # Node exporter needs access to host system details
    pid: host
    ports:
      - "9100:9100"  # Expose for main server to scrape
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      # Mount host root filesystem to `/rootfs`
      - '--path.rootfs=/rootfs'
      # Disable collectors that might not work well or be needed in Docker
      - '--no-collector.wifi'
      - '--no-collector.zfs'
    networks:
      - monitoring

  postgres-exporter:
    image: quay.io/prometheuscommunity/postgres-exporter:latest
    container_name: postgres-exporter
    restart: unless-stopped
    ports:
      - "9187:9187"  # Expose for main server to scrape
    environment:
      # Connection string for the exporter to connect to PostgreSQL
      # Uses the service name 'postgresql' because they are on the same network
      - DATA_SOURCE_NAME=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgresql:5432/${POSTGRES_DB}?sslmode=disable
      # Optionally add custom metrics via PG_EXPORTER_EXTEND_QUERY_PATH
    networks:
      - monitoring
    depends_on:
      - postgresql # Ensure postgres is ready before exporter starts

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"  # Expose for main server to scrape
    volumes:
      # cAdvisor needs access to host system details for container metrics
      - /:/rootfs:ro
      - /var/run:/var/run:rw # Needs rw for Docker socket access
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    networks:
      - monitoring

  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - ./promtail-config.backup.yml:/etc/promtail/config.yml:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring

networks:
  monitoring:
    driver: bridge
    name: qdrant_monitoring