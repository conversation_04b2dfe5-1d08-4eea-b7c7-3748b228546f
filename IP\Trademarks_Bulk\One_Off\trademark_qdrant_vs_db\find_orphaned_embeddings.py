#!/usr/bin/env python3
"""
Find Orphaned Embeddings Tool

This script identifies trademark embeddings in Qdrant that don't have corresponding 
records in the trademark database. These are "orphaned" embeddings that should be 
investigated or cleaned up.

Features:
- Fetches all trademark embeddings from Qdrant
- Checks which ones don't exist in the trademark database
- Outputs results to CSV file with detailed information
- Provides terminal summary with statistics
- Reuses existing functions to avoid code duplication
"""

import os
import sys
import csv
import time
import datetime
from tqdm import tqdm
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models

# Import database functions (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from trademark_db import get_db_connection

# Load environment variables
load_dotenv()

# Qdrant configuration
QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = "IP_Assets"

# Output configuration
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), 'orphaned_embeddings_results')
os.makedirs(OUTPUT_DIR, exist_ok=True)


def get_database_trademark_ids():
    """
    Get all trademark IDs from database with mark_feature_code 2, 3, 5.
    Reuses pattern from trademark_qdrant_vs_db.py
    
    Returns:
        set: Set of trademark IDs (UUIDs) that exist in database
    """
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            print("❌ Could not establish database connection")
            return set()
        
        print("🔍 Fetching trademark IDs from database...")
        
        # Get total count first
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*) FROM trademarks 
                WHERE mark_feature_code IN (2, 3, 5)
            """)
            total_count = cursor.fetchone()[0]
            print(f"   Total eligible trademarks in database: {total_count:,}")
        
        # Fetch all trademark IDs (we need the UUID format for comparison with Qdrant)
        # Import UUID generation function
        sys.path.append(os.path.join(os.path.dirname(__file__), '..','..', '..', '..', 'Qdrant', 'api', 'Common'))
        from Common.uuid_utils import generate_uuid
        
        db_trademark_ids = set()
        
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT ser_no FROM trademarks 
                WHERE mark_feature_code IN (2, 3, 5)
                ORDER BY ser_no
            """)
            
            records = cursor.fetchall()
            print(f"   Converting {len(records):,} serial numbers to UUIDs...")
            
            for record in tqdm(records, desc="Converting to UUIDs", unit="record"):
                ser_no = record[0]
                uuid = generate_uuid(ser_no)
                db_trademark_ids.add(uuid)
        
        print(f"✅ Successfully processed {len(db_trademark_ids):,} trademark UUIDs from database")
        return db_trademark_ids
        
    except Exception as e:
        print(f"❌ Error fetching database trademark IDs: {str(e)}")
        return set()
    finally:
        if conn:
            conn.close()


def get_qdrant_trademark_points():
    """
    Get all trademark points from Qdrant.
    Reuses pattern from trademark_qdrant_vs_db.py
    
    Returns:
        dict: Dictionary mapping point IDs to their payloads
    """
    try:
        client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
        
        print("🔍 Fetching trademark points from Qdrant...")
        
        # Get total count first
        count_result = client.count(
            collection_name=COLLECTION_NAME,
            count_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="ip_type",
                        match=models.MatchValue(value="Trademark")
                    )
                ]
            ),
            exact=True
        )
        total_points = count_result.count
        print(f"   Total trademark points in Qdrant: {total_points:,}")
        
        qdrant_points = {}
        offset = None
        points_fetched = 0
        
        # Fetch all points using scroll
        while True:
            points, next_offset = client.scroll(
                collection_name=COLLECTION_NAME,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="ip_type",
                            match=models.MatchValue(value="Trademark")
                        )
                    ]
                ),
                limit=10000,  # Smaller batch size for better progress tracking
                offset=offset,
                with_payload=True,
                with_vectors=False
            )
            
            points_in_batch = len(points)
            points_fetched += points_in_batch
            
            if total_points > 0:
                progress = (points_fetched / total_points) * 100
                print(f"   Progress: {points_fetched:,}/{total_points:,} ({progress:.1f}%)")
            
            for point in points:
                qdrant_points[point.id] = point.payload
            
            if next_offset is None:
                break
            offset = next_offset
        
        print(f"✅ Successfully fetched {len(qdrant_points):,} trademark points from Qdrant")
        return qdrant_points
        
    except Exception as e:
        print(f"❌ Error fetching Qdrant trademark points: {str(e)}")
        return {}


def find_orphaned_embeddings(db_trademark_ids, qdrant_points):
    """
    Find embeddings in Qdrant that don't have corresponding database records.
    
    Args:
        db_trademark_ids (set): Set of trademark UUIDs from database
        qdrant_points (dict): Dictionary of Qdrant points
        
    Returns:
        list: List of orphaned embedding records
    """
    print("🔍 Analyzing embeddings for orphaned records...")
    
    qdrant_ids = set(qdrant_points.keys())
    orphaned_ids = qdrant_ids - db_trademark_ids
    
    print(f"   Qdrant trademark embeddings: {len(qdrant_ids):,}")
    print(f"   Database trademark records: {len(db_trademark_ids):,}")
    print(f"   Orphaned embeddings found: {len(orphaned_ids):,}")
    
    if not orphaned_ids:
        print("🎉 No orphaned embeddings found!")
        return []
    
    # Create detailed records for orphaned embeddings
    orphaned_records = []
    
    print("📋 Processing orphaned embedding details...")
    for point_id in tqdm(orphaned_ids, desc="Processing orphaned embeddings", unit="embedding"):
        payload = qdrant_points.get(point_id, {})
        
        record = {
            'point_id': str(point_id),
            'ip_type': payload.get('ip_type', 'Unknown'),
            'reg_no': payload.get('reg_no', 'N/A'),
            'plaintiff_id': payload.get('plaintiff_id', 'N/A'),
            'has_plaintiff_id': 'Yes' if payload.get('plaintiff_id') else 'No',
            'payload_keys': '|'.join(payload.keys()) if payload else 'Empty',
            'db_status': '❌ Missing',
            'qdrant_status': '✅ Found'
        }
        
        orphaned_records.append(record)
    
    print(f"✅ Processed {len(orphaned_records):,} orphaned embedding records")
    return orphaned_records


def save_orphaned_embeddings_to_csv(orphaned_records, filename):
    """
    Save orphaned embeddings to CSV file.
    Similar pattern to find_missing_embeddings.py
    """
    if not orphaned_records:
        print("ℹ️ No orphaned embeddings to save")
        return
    
    csv_path = os.path.join(OUTPUT_DIR, filename)
    
    try:
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'point_id', 'ip_type', 'reg_no', 'plaintiff_id', 'has_plaintiff_id',
                'payload_keys', 'db_status', 'qdrant_status'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in orphaned_records:
                writer.writerow(record)
        
        print(f"✅ Orphaned embeddings saved to: {csv_path}")
        print(f"📊 Total records saved: {len(orphaned_records):,}")
        
    except Exception as e:
        print(f"❌ Error saving CSV: {str(e)}")


def print_summary_statistics(orphaned_records, total_qdrant_points, total_db_records):
    """
    Print summary statistics in terminal.
    Similar pattern to find_missing_embeddings.py
    """
    print(f"\n{'='*80}")
    print(f"🔍 ORPHANED EMBEDDINGS ANALYSIS SUMMARY")
    print(f"{'='*80}")
    
    print(f"\n📋 DATA SOURCE STATISTICS:")
    print(f"   Total Qdrant trademark embeddings: {total_qdrant_points:,}")
    print(f"   Total database trademark records: {total_db_records:,}")
    print(f"   Orphaned embeddings found: {len(orphaned_records):,}")
    
    if len(orphaned_records) > 0:
        orphan_rate = (len(orphaned_records) / total_qdrant_points) * 100
        print(f"   Orphan rate: {orphan_rate:.1f}%")
        
        # Analyze orphaned records
        with_plaintiff = sum(1 for r in orphaned_records if r['has_plaintiff_id'] == 'Yes')
        without_plaintiff = len(orphaned_records) - with_plaintiff
        
        print(f"\n🏷️ ORPHANED EMBEDDINGS BREAKDOWN:")
        print(f"   With plaintiff_id: {with_plaintiff:,} ({(with_plaintiff/len(orphaned_records)*100):.1f}%)")
        print(f"   Without plaintiff_id: {without_plaintiff:,} ({(without_plaintiff/len(orphaned_records)*100):.1f}%)")
        
        # Show some examples
        print(f"\n📋 SAMPLE ORPHANED EMBEDDINGS (first 5):")
        for i, record in enumerate(orphaned_records[:5]):
            print(f"   {i+1}. Point ID: {record['point_id'][:8]}...")
            print(f"      Reg No: {record['reg_no']}")
            print(f"      Plaintiff ID: {record['plaintiff_id']}")
    else:
        print(f"\n🎉 EXCELLENT! No orphaned embeddings found!")
        print(f"   All Qdrant embeddings have corresponding database records")


def main():
    """
    Main function to find orphaned embeddings.
    """
    print("🔍 Starting Orphaned Embeddings Analysis")
    print(f"📁 Current working directory: {os.getcwd()}")
    print(f"📁 Script location: {os.path.dirname(__file__)}")
    print(f"📁 Output directory: {OUTPUT_DIR}")
    print("=" * 50)
    
    start_time = time.time()
    
    # Step 1: Get database trademark IDs
    print("\n📖 STEP 1: Fetching database trademark records")
    db_trademark_ids = get_database_trademark_ids()
    if not db_trademark_ids:
        print("❌ No database records found")
        return
    
    # Step 2: Get Qdrant trademark points
    print("\n🔍 STEP 2: Fetching Qdrant trademark embeddings")
    qdrant_points = get_qdrant_trademark_points()
    if not qdrant_points:
        print("❌ No Qdrant embeddings found")
        return
    
    # Step 3: Find orphaned embeddings
    print("\n🔍 STEP 3: Finding orphaned embeddings")
    orphaned_records = find_orphaned_embeddings(db_trademark_ids, qdrant_points)
    
    # Step 4: Save results to CSV
    print("\n💾 STEP 4: Saving results to CSV")
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"orphaned_embeddings_{timestamp}.csv"
    save_orphaned_embeddings_to_csv(orphaned_records, csv_filename)
    
    # Step 5: Print summary statistics
    print_summary_statistics(orphaned_records, len(qdrant_points), len(db_trademark_ids))
    
    # Final summary
    total_time = time.time() - start_time
    print(f"\n✅ Analysis completed in {total_time:.1f} seconds")
    print(f"📁 Results saved to: {OUTPUT_DIR}/{csv_filename}")


if __name__ == "__main__":
    main()
