#!/usr/bin/env python3
"""
Initialization script for the Statistics system.
Run this script to set up the statistics system for the first time.
"""

import os
import sys
from datetime import datetime

def check_environment():
    """Check if all required environment variables are set."""
    required_vars = [
        'POSTGRES_HOST',
        'POSTGRES_PORT', 
        'POSTGRES_USER',
        'POSTGRES_PASSWORD',
        'POSTGRES_DB'
    ]
    
    missing = []
    for var in required_vars:
        if not os.getenv(var):
            missing.append(var)
    
    if missing:
        print("❌ Missing required environment variables:")
        for var in missing:
            print(f"   - {var}")
        print("\nPlease set these environment variables and try again.")
        return False
    
    print("✅ All required environment variables are set")
    return True

def initialize_database():
    """Initialize the statistics database table."""
    try:
        print("🔧 Initializing statistics database table...")
        from DatabaseManagement.StatisticsDB import init_statistics_table
        init_statistics_table()
        print("✅ Database table initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize database table: {e}")
        return False

def collect_initial_statistics():
    """Collect initial statistics data."""
    try:
        print("📊 Collecting initial statistics...")
        from Statistics.ScheduledTasks import daily_statistics_collection
        success = daily_statistics_collection()
        
        if success:
            print("✅ Initial statistics collection completed")
            return True
        else:
            print("❌ Initial statistics collection failed")
            return False
    except Exception as e:
        print(f"❌ Failed to collect initial statistics: {e}")
        return False

def verify_setup():
    """Verify the setup is working correctly."""
    try:
        print("🔍 Verifying setup...")
        from DatabaseManagement.StatisticsDB import get_latest_statistics
        
        df = get_latest_statistics()
        
        if len(df) > 0:
            print(f"✅ Setup verification successful - found {len(df)} metrics")
            print("📈 Sample metrics:")
            for _, row in df.head(5).iterrows():
                print(f"   {row['metric_name']}: {row['metric_value']}")
            return True
        else:
            print("⚠️  No statistics found - this might be normal for a fresh setup")
            return True
    except Exception as e:
        print(f"❌ Setup verification failed: {e}")
        return False

def main():
    """Main initialization process."""
    print("🚀 Statistics System Initialization")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Check environment
    if not check_environment():
        return False
    
    print()
    
    # Step 2: Initialize database
    if not initialize_database():
        return False
    
    print()
    
    # Step 3: Collect initial data
    if not collect_initial_statistics():
        return False
    
    print()
    
    # Step 4: Verify setup
    if not verify_setup():
        return False
    
    print()
    print("=" * 50)
    print("🎉 Statistics System Initialization Complete!")
    print()
    print("Next steps:")
    print("1. Start your Flask application")
    print("2. Navigate to /statistics to view the dashboard")
    print("3. The system will automatically collect statistics daily at 6 AM EST")
    print()
    print("For manual collection, you can:")
    print("- Use the 'Collect New Statistics' button on the dashboard")
    print("- Run: python Statistics/ScheduledTasks.py")
    print("- Call the API: POST /api/statistics/collect")
    print()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Initialization interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error during initialization: {e}")
        sys.exit(1)
