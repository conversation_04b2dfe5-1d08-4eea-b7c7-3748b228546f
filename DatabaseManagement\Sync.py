import os
import mysql.connector
from DatabaseManagement.Connections import get_gz_connection

def copy_table(source_table: str, dest_table: str, copy_type: str):
    """Copy table structure and optionally data between database servers
    Args:
        source_table: Name of the source table in the original database
        dest_table: Name of the destination table in the target database
        copy_type: "data" or "structure"
    """
    
    # Source connection (default port 3306)
    src_conn = get_gz_connection()
    # Destination connection (port 3307)
    dst_conn = get_gz_connection(host="maidalv.com",port=3307,)

    try:
        with src_conn.cursor() as src_cur, dst_conn.cursor() as dst_cur:
            # Check if destination table exists
            dst_cur.execute(f"SHOW TABLES LIKE '{dest_table}'")
            if dst_cur.fetchone():
                print(f"Dropping existing table {dest_table} in destination")
                dst_cur.execute(f"DROP TABLE {dest_table}")
                dst_conn.commit()

            # Get table structure from source
            src_cur.execute(f"SHOW CREATE TABLE {source_table}")
            create_stmt = src_cur.fetchone()[1].replace(
                f"CREATE TABLE `{source_table}`", 
                f"CREATE TABLE `{dest_table}`", 
                1
            )

            # Create table in destination
            dst_cur.execute(create_stmt)
            dst_conn.commit()
            print(f"Created table {dest_table} in destination")

            # Print table structure
            print(f"\nTable structure for {dest_table}:")
            dst_cur.execute(f"DESCRIBE {dest_table}")
            for col in dst_cur.fetchall():
                print(f"  Column: {col[0]:<20} Type: {col[1]:<15} Null: {col[2]:<8} Key: {col[3]}")

            if copy_type.lower() == "data":
                # Copy data
                src_cur.execute(f"SELECT * FROM {source_table}")
                rows = src_cur.fetchall()
                
                # Get column names from source
                src_cur.execute(f"DESCRIBE {source_table}")
                columns = [col[0] for col in src_cur.fetchall()]
                placeholders = ", ".join(["%s"] * len(columns))
                
                # Insert data into destination
                dst_cur.executemany(
                    f"INSERT INTO {dest_table} ({', '.join(columns)}) VALUES ({placeholders})",
                    rows
                )
                dst_conn.commit()
                print(f"Copied {dst_cur.rowcount} rows to {dest_table}")

    finally:
        src_conn.close()
        dst_conn.close()

if __name__ == "__main__":
    copy_table("tb_case_check", "tb_case_check_api", "structure")
    copy_table("tb_case_check_result", "tb_case_check_result_api", "structure")
