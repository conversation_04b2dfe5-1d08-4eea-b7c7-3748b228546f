import {
    applyFiltersBtn,
    refreshAllDataBtn,
    refreshSelectionBtn,
    prevPageBtn,
    nextPageBtn,
    caseTypeCheckboxes,
    caseTypeDropdownHeader,
    caseTypeDropdownContent,
    casesContainer,
    caseNumberInput,
    plaintiffNameInput,
    plaintiffIdInput,
    validationStatusInput,
    ipSourceInput,
    pictureTypeInput,
    dateFromInput,
    dateToInput,
    sortByInput,
    sortOrderInput,
    limitInput
} from './dom.js';
import {
    fetchCases
} from './api.js';
import {
    currentPage,
    totalPages,
    setFilters,
    setCurrentPage,
    resetFilters
} from './state.js';
import {
    updateCaseTypeDropdownHeader
} from './ui.js';

export function updateFilters() {
    const newFilters = {
        case_number: caseNumberInput.value,
        plaintiff_name: plaintiffNameInput.value,
        plaintiff_id: plaintiffIdInput.value,
        validation_status: validationStatusInput.value,
        ip_source: ipSourceInput.value,
        picture_type: pictureTypeInput.value,
        date_from: dateFromInput.value,
        date_to: dateToInput.value,
        sort_by: sortByInput.value,
        sort_order: sortOrderInput.value,
        limit: parseInt(limitInput.value),
        offset: 0
    };

    const selectedCaseTypes = [];
    caseTypeCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
            selectedCaseTypes.push(checkbox.value);
        }
    });
    newFilters.case_type = selectedCaseTypes;

    setFilters(newFilters);
}

function initializeDropdowns() {
    caseTypeDropdownHeader.addEventListener('click', function() {
        caseTypeDropdownContent.classList.toggle('show');
    });

    window.addEventListener('click', function(event) {
        if (!event.target.closest('.dropdown-checkbox')) {
            caseTypeDropdownContent.classList.remove('show');
            updateCaseTypeDropdownHeader();
        }
    });

    caseTypeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateCaseTypeDropdownHeader);
    });
}

function fetchProposedPlaintiffNames() {
    casesContainer.addEventListener('casesLoaded', function(e) {
        if (e.detail && e.detail.proposed_names) {
            setProposedPlaintiffNames(e.detail.proposed_names);
        }
    });
}

export function initializeEventListeners() {
    applyFiltersBtn.addEventListener('click', function() {
        resetFilters();
        updateFilters();
        fetchCases();
    });

    refreshAllDataBtn.addEventListener('click', function() {
        fetchCases(true);
    });

    refreshSelectionBtn.addEventListener('click', function() {
        fetchCases(false, true);
    });

    prevPageBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
            updateFilters(); // to get the correct limit
            setFilters({ offset: (currentPage - 1) * limitInput.value });
            fetchCases();
        }
    });

    nextPageBtn.addEventListener('click', function() {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
            updateFilters(); // to get the correct limit
            setFilters({ offset: (currentPage - 1) * limitInput.value });
            fetchCases();
        }
    });

    initializeDropdowns();
    fetchProposedPlaintiffNames();
}