import requests
import base64
import json
import time
import os

# API Configuration
API_KEY = "37457-48774-8887-1882"
BASE_URL = "https://api.maidalv.com"
IMAGE_PATH = r"D:\Win10User\Downloads\Check images\35_A_Chanel.jpg"

def encode_image_to_base64(image_path):
    """
    Encode image file to base64 string with data URI header
    """
    try:
        # Determine MIME type based on file extension
        if image_path.lower().endswith(('.jpg', '.jpeg')):
            mime_type = 'image/jpeg'
        elif image_path.lower().endswith('.png'):
            mime_type = 'image/png'
        elif image_path.lower().endswith('.webp'):
            mime_type = 'image/webp'
        elif image_path.lower().endswith('.gif'):
            mime_type = 'image/gif'
        else:
            mime_type = 'image/jpeg'  # default

        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            # Add data URI header as expected by the backend
            data_uri = f"data:{mime_type};base64,{encoded_string}"
            return data_uri
    except FileNotFoundError:
        print(f"Error: Image file not found at {image_path}")
        return None
    except Exception as e:
        print(f"Error encoding image: {e}")
        return None

def submit_ip_check(api_key, image_base64):
    """
    Submit IP check request to the API
    """
    url = f"{BASE_URL}/check_api"

    # Prepare request data
    data = {
        "api_key": api_key,
        "main_product_image": image_base64,
        "description": "Chanel product image",  # Product title/description
        "language": "en"  # Response language
    }

    try:
        print("Submitting IP check request...")
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            check_id = result.get("check_id")
            status = result.get("status")

            print(f"✓ Check submitted successfully!")
            print(f"  Check ID: {check_id}")
            print(f"  Status: {status}")

            if "estimated_completion_time" in result:
                print(f"  Estimated completion time: {result['estimated_completion_time']} seconds")

            return check_id, result.get("estimated_completion_time", 45)

        else:
            error_data = response.json()
            print(f"✗ Error submitting check: {error_data}")
            return None, None

    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {e}")
        return None, None
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return None, None

def poll_for_results(check_id, max_attempts=120):
    """
    Poll for analysis results with exponential backoff
    """
    url = f"{BASE_URL}/check_status/{check_id}"
    attempt = 0

    print(f"\nPolling for results (Check ID: {check_id})")
    print("This may take 30-90 seconds...")

    while attempt < max_attempts:
        try:
            response = requests.get(url, timeout=30)

            if response.status_code == 200:
                result = response.json()
                status = result.get("status")

                print(f"Attempt {attempt + 1}: Status = {status}")

                if status == "completed":
                    print("✓ Analysis completed!")
                    return result.get("result")

                elif status == "error":
                    print(f"✗ Analysis failed: {result.get('message')}")
                    return None

                elif status in ["queued", "processing"]:
                    # Wait based on status
                    wait_time = 20 if status == "queued" else 3
                    print(f"  Waiting {wait_time} seconds...")
                    time.sleep(wait_time)

            else:
                print(f"✗ HTTP Error: {response.status_code}")
                error_data = response.json()
                print(f"  Error details: {error_data}")
                break

        except requests.exceptions.RequestException as e:
            print(f"✗ Request failed: {e}")
            break

        attempt += 1

    print("✗ Max attempts reached or error occurred")
    return None

def print_results(results):
    """
    Pretty print the analysis results
    """
    if not results:
        print("No results to display")
        return

    print("\n" + "="*60)
    print("📋 IP CHECK RESULTS")
    print("="*60)

    # Overall assessment
    risk_level = results.get("risk_level", "Unknown")
    print(f"\n🏆 OVERALL RISK ASSESSMENT: {risk_level}")

    # Detailed results
    result_list = results.get("results", [])
    if not result_list:
        print("✅ No significant IP infringements found!")
        return

    print(f"\n🔍 DETAILED FINDINGS ({len(result_list)} potential issues):")
    print("-" * 60)

    for i, finding in enumerate(result_list, 1):
        print(f"\n{i}. 📋 IP TYPE: {finding.get('ip_type', 'Unknown')}")
        print(f"   👤 IP Owner: {finding.get('ip_owner', 'Unknown')}")
        print(f"   ⚠️  Risk Level: {finding.get('risk_level', 'Unknown')}")
        print(f"   📊 Risk Score: {finding.get('risk_score', 'N/A')}/10")

        if finding.get('text'):
            print(f"   📝 IP Text: {finding.get('text')}")

        if finding.get('reg_no'):
            print(f"   🔢 Registration Number: {finding.get('reg_no')}")

        # Court case information (if available)
        if finding.get('plaintiff_name'):
            print(f"   ⚖️  Plaintiff: {finding.get('plaintiff_name')}")
            if finding.get('number_of_cases'):
                print(f"   📊 Number of Cases: {finding.get('number_of_cases')}")
            if finding.get('last_case_docket'):
                print(f"   📋 Latest Case: {finding.get('last_case_docket')}")
            if finding.get('last_case_date_filed'):
                print(f"   📅 Latest Case Date: {finding.get('last_case_date_filed')}")

        # IP asset URLs
        if finding.get('ip_asset_urls'):
            print(f"   🖼️  Evidence Images: {len(finding['ip_asset_urls'])} available")
            for j, url in enumerate(finding['ip_asset_urls'][:2]):  # Show first 2 URLs
                print(f"      {j+1}. {url}")

        # Legal report (if available)
        if finding.get('report'):
            print(f"   📄 Legal Assessment: Available (detailed report provided)")

        print(f"   💡 Risk Description: {finding.get('risk_description', 'No description available')}")

        if i < len(result_list):
            print("   " + "-"*40)

def main():
    """
    Main function to run the IP check
    """
    print("="*60)
    print("🔍 MAIDALV IP CHECK API - PYTHON CLIENT")
    print("="*60)

    # Check if image file exists
    if not os.path.exists(IMAGE_PATH):
        print(f"❌ Image file not found: {IMAGE_PATH}")
        print("Please ensure the image file exists at the specified path.")
        return

    # Encode image to base64
    print(f"📁 Loading image: {IMAGE_PATH}")
    image_base64 = encode_image_to_base64(IMAGE_PATH)

    if not image_base64:
        print("❌ Failed to encode image. Exiting.")
        return

    print(f"✅ Image encoded successfully ({len(image_base64)} characters)")

    # Submit IP check request
    check_id, estimated_time = submit_ip_check(API_KEY, image_base64)

    if not check_id:
        print("❌ Failed to submit check request. Exiting.")
        return

    # Poll for results
    print(f"\n⏱️  Starting to poll for results...")
    print(f"   Estimated wait time: ~{estimated_time} seconds")

    results = poll_for_results(check_id)

    if results:
        print_results(results)
        print(f"\n✅ IP Check completed successfully!")
    else:
        print(f"\n❌ Failed to retrieve results for Check ID: {check_id}")
        print("You can try polling again later with this Check ID.")

if __name__ == "__main__":
    main()