import os
import sys
import subprocess
import time
import re
import shlex
import tempfile
import shutil
import pandas as pd # Needed for test_transfer_speed
from logdata import log_message

# Add project root to path to allow importing Common
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from Common.Constants import nas_case_folder, sanitize_name # Removed unused constants

class NASTransferMixin:
    """
    Mixin class providing file transfer capabilities using rsync and scp.
    Relies on methods from NASConnectionBase.
    """

    def transfer_file_with_rsync(self, local_path, remote_path, to_nas=True):
        """
        Transfers a file or directory using rsync with automatic connection handling and retries.
        Handles Windows path conversion for cwrsync compatibility.
        Relies on self.ensure_connection from NASConnectionBase.
        """
        # Relies on self.ensure_connection from NASConnectionBase
        self.ensure_connection()
        absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa"))
        # Ensure the .ssh directory exists relative to the workspace root or project root
        ssh_dir = os.path.abspath(".ssh")
        if not os.path.isdir(ssh_dir):
             script_grandparent_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
             ssh_dir_alt = os.path.join(script_grandparent_dir, ".ssh")
             if os.path.isdir(ssh_dir_alt):
                 absolute_ssh_key_path = os.path.join(ssh_dir_alt, "id_rsa")
             else:
                 print(f"Warning: .ssh directory not found in workspace root ({ssh_dir}) or project root ({ssh_dir_alt}). Ensure id_rsa exists.")
                 absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa")) # Fallback

        if not os.path.exists(absolute_ssh_key_path):
             raise FileNotFoundError(f"SSH private key not found at expected location: {absolute_ssh_key_path}")


        nas_user = os.getenv("NAS_USERNAME")
        # Use Tailscale IP for rsync reliability if available, else fallback to NAS_HOST
        nas_ip = os.getenv("NAS_TAILSCALE_IP", os.getenv("NAS_HOST"))
        if not nas_ip:
             raise ValueError("NAS IP address not found in environment variables (NAS_TAILSCALE_IP or NAS_HOST).")

        host_spec = f'{nas_user}@{nas_ip}'
        # Use /volume1 prefix for remote path
        full_remote_path_part = f'/volume1{remote_path}'

        # Escape the remote path part for the shell (especially spaces)
        # shlex.quote is generally safer for the remote specifier part
        # Revert to original logic (no shlex.quote):
        remote_specifier = f'{host_spec}:{full_remote_path_part}'

        max_retries = 3
        retry_delay = 5

        # --- Path Conversion Function (Helper) ---
        def convert_to_cygdrive(win_path):
            # Converts a Windows path (e.g., C:\Users\<USER>\\", "/") # Only convert on Windows, ensure forward slashes otherwise
            # Handle UNC paths
            if win_path.startswith("\\\\"):
                 path_no_leading_slashes = win_path.strip("\\")
                 parts = path_no_leading_slashes.split("\\", 1)
                 if len(parts) == 2:
                     # Standard UNC: //host/share/path -> /host/share/path (rsync might handle this)
                     # Or sometimes needed as /cygdrive/host/share... let's try simpler first
                     return "/" + path_no_leading_slashes.replace("\\", "/")
                 else:
                     return "/" + path_no_leading_slashes.replace("\\", "/") # Fallback
            # Handle drive letters
            elif re.match(r"^[a-zA-Z]:\\", win_path):
                drive = win_path[0].lower()
                path_part = win_path[2:].replace("\\", "/")
                return f"/cygdrive/{drive}{path_part}"
            # Handle paths starting with just "\" (relative to current drive root)
            elif win_path.startswith("\\"):
                 # Need current drive - this is tricky, assume C: for now or get dynamically?
                 # For simplicity, let's assume it's relative to the script's drive if possible
                 script_drive = os.path.splitdrive(os.path.abspath(__file__))[0]
                 if script_drive:
                      drive = script_drive[0].lower()
                      path_part = win_path[1:].replace("\\", "/")
                      return f"/cygdrive/{drive}/{path_part}"
                 else: # Fallback if drive detection fails
                      return win_path.replace("\\", "/")
            # Assume it might already be in a compatible format or is a relative path
            else:
                return win_path.replace("\\", "/") # Ensure forward slashes

        ssh_executable_path_used = "ssh" # For error reporting

        for attempt in range(max_retries):
            rsync_command = None # Define for potential use in error messages
            try:
                # --- Path Handling for rsync arguments and -e string ---
                rsync_local_path = local_path
                ssh_exe_path_for_e_string = "ssh" # Default for non-Windows
                key_path_for_e_string = absolute_ssh_key_path # Default for non-Windows

                if os.name == 'nt':
                    # 1. Convert local source/destination path for rsync command itself
                    rsync_local_path = convert_to_cygdrive(os.path.abspath(local_path)) # Use absolute path for conversion
                    if attempt == 0: log_message(f"NAS Rsync: Converted Win local path for rsync arg: '{local_path}' -> '{rsync_local_path}'", level="debug")

                    # 2. Set the specific SSH executable path (Windows format initially)
                    # Allow overriding via environment variable
                    ssh_executable_path = os.getenv("RSYNC_SSH_EXECUTABLE", r"D:\PortableApps\cwrsync_6.4.1_x64_free\bin\ssh.exe")
                    if not os.path.exists(ssh_executable_path):
                         log_message(f"NAS Rsync: Specified SSH executable not found: {ssh_executable_path}. Falling back to 'ssh'.", level="warning")
                         ssh_executable_path = "ssh" # Fallback to default ssh if specific one not found
                         ssh_executable_path_used = "ssh (fallback)"
                    else:
                         ssh_executable_path_used = ssh_executable_path # For error reporting

                    # 3. Convert SSH executable path and Key path for the '-e' string
                    ssh_exe_path_for_e_string = convert_to_cygdrive(ssh_executable_path)
                    key_path_for_e_string = convert_to_cygdrive(absolute_ssh_key_path)

                    if attempt == 0:
                        log_message(f"NAS Rsync: Using SSH for -e: {ssh_exe_path_for_e_string}", level="debug")
                        log_message(f"NAS Rsync: Using key for -e: {key_path_for_e_string}", level="debug")


                # Revert to original manual quoting logic for -e string components:
                quoted_ssh_executable_for_e = f'"{ssh_exe_path_for_e_string}"' if ' ' in ssh_exe_path_for_e_string else ssh_exe_path_for_e_string
                quoted_key_path_for_e = f'"{key_path_for_e_string}"' if ' ' in key_path_for_e_string else key_path_for_e_string

                # Assemble the final string for the -e argument
                ssh_command_str_for_e = (
                    f"{quoted_ssh_executable_for_e} "
                    f"-i {quoted_key_path_for_e} "
                    f"-p 22 "
                    f"-o StrictHostKeyChecking=no "
                    f"-o UserKnownHostsFile=/dev/null"
                )
                # --- End Path Handling ---


                rsync_command = [
                    "rsync",
                    "-av", # Archive mode, verbose
                    "--no-perms", # Don't preserve permissions (often problematic)
                    "--times", # Preserve modification times
                    # "--checksum", # Use checksum instead of mod-time & size (slower but more robust?) - Optional
                    # "--progress", # Show progress during transfer - Optional
                    "-e", ssh_command_str_for_e,
                ]

                # Add source and destination
                if to_nas:
                    # If local path is a directory, add trailing slash to copy contents
                    # rsync treats source/ dest/ differently than source dest
                    # Let's assume we want to copy the item itself unless specified otherwise
                    # For consistency, let rsync decide based on whether dest exists.
                    rsync_command.extend([rsync_local_path, remote_specifier])
                else:
                    # If remote path is a directory, add trailing slash?
                    # Let's keep it simple for now.
                    rsync_command.extend([remote_specifier, rsync_local_path])

                # Use shlex.join for safer display if paths might have tricky chars
                display_command = ' '.join(shlex.quote(str(arg)) for arg in rsync_command)
                # Log execution start (will add result later)
                log_message(f"NAS Rsync: Executing rsync (attempt {attempt + 1}): {display_command}", level="debug")

                # Execute the command
                result = subprocess.run(
                    rsync_command, # Pass the list directly
                    check=True, # Raise exception on non-zero exit code
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8', errors='ignore' # Ignore decoding errors
                )
                # Log success on the same line as execution intent (conceptually)
                log_message(f"NAS Rsync: Executing rsync (attempt {attempt + 1}): {display_command} ====> Exit code 0", level="info")
                # Stderr might contain stats even on success, log as debug
                if result.stderr:
                    log_message(f"NAS Rsync: Rsync stderr (success): {result.stderr.strip()[:500]}{'...' if len(result.stderr.strip())>500 else ''}", level="debug")
                return # Success

            except FileNotFoundError as e:
                 # Improved error message for missing rsync or ssh
                 if os.name == 'nt':
                     specific_ssh_msg = f"the specified SSH client ('{ssh_executable_path_used}')"
                     potential_fix = f"Ensure 'rsync.exe' and the SSH client exist at the specified paths ('{os.getenv('RSYNC_SSH_EXECUTABLE', 'D:/PortableApps/cwrsync_6.4.1_x64_free/bin/ssh.exe')}') and are accessible in the system PATH or configured correctly."
                 else:
                     specific_ssh_msg = "the default SSH client ('ssh')"
                     potential_fix = "Ensure 'rsync' and 'ssh' are installed and accessible via the system PATH."

                 error_detail = f"Attempted to use rsync ('rsync') and {specific_ssh_msg}. {potential_fix}"
                 log_message(f"NAS Rsync: ERROR - Rsync or SSH executable not found. {error_detail}. Original error: {e}", level="critical")
                 raise RuntimeError(f"Rsync/SSH not found. {error_detail}") from e

            except subprocess.CalledProcessError as e:
                # Handle non-zero exit codes from rsync
                try: failed_cmd_str = ' '.join(shlex.quote(str(arg)) for arg in rsync_command)
                except NameError: failed_cmd_str = "(command list not fully constructed)"

                # Log failure on the same line as execution intent
                log_message(f"NAS Rsync: Executing rsync (attempt {attempt + 1}): {failed_cmd_str} ====> Exit code {e.returncode}", level="warning")

                error_msg = f"Rsync attempt {attempt + 1} failed." # Simplified initial message
                # Log stdout only on failure
                if e.stdout:
                    log_message(f"NAS Rsync: Rsync stdout on failure: {e.stdout.strip()[:1000]}{'...' if len(e.stdout.strip())>1000 else ''}", level="debug")
                # Log stderr on failure
                if e.stderr:
                    log_message(f"NAS Rsync: Rsync stderr on failure: {e.stderr.strip()[:1000]}{'...' if len(e.stderr.strip())>1000 else ''}", level="warning")
                else:
                     error_msg += "\n(No specific stderr captured from process)" # Clarify if stderr attribute is empty

                # Add specific hints based on common errors
                stderr_lower = e.stderr.lower() if e.stderr else ""
                if "permission denied" in stderr_lower and "publickey" not in stderr_lower:
                     error_msg += f"\nHint: SSH auth likely succeeded, but file/directory access denied on NAS. Check NAS folder permissions for user '{nas_user}', rsync service permissions (Control Panel > File Services > rsync), and potentially SSH command restrictions."
                elif "permission denied (publickey" in stderr_lower or "could not resolve hostname" in stderr_lower or "connection timed out" in stderr_lower or "connection refused" in stderr_lower:
                    error_msg += f"\nHint: SSH connection/authentication failed. Verify NAS IP ({nas_ip}), port (22), username ('{nas_user}'), key path ('{key_path_for_e_string}' inside -e), and NAS firewall/SSH service status."
                    error_msg += f"\n      Also check the SSH client used: '{ssh_executable_path_used}'"
                elif "dup() in/out/err failed" in stderr_lower or "connection unexpectedly closed" in stderr_lower or "lost connection" in stderr_lower:
                     error_msg += f"\nHint: Error likely within the SSH connection established via the -e option. Check quoting/paths within the -e string, compatibility between '{ssh_executable_path_used}' and the NAS SSH server, or potential network instability."
                elif "the source and destination cannot both be remote" in stderr_lower:
                    error_msg += f"\nHint: Rsync misinterpreted a local Windows path ('{rsync_local_path}') as remote. Check the /cygdrive/ conversion logic and ensure the path is absolute."
                elif "invalid argument" in stderr_lower:
                     error_msg += f"\nHint: Check the syntax of the rsync command arguments, especially paths and options like --no-perms or the -e string."
                elif "rsync error: some files/attrs were not transferred" in stderr_lower:
                     error_msg += f"\nHint: Some files might have failed to transfer due to permissions, special file types, or other issues. Check NAS logs and file permissions."

                if attempt < max_retries - 1:
                    error_msg += f"\nRetrying in {retry_delay:.1f} seconds..."
                    log_message(f"NAS Rsync: {error_msg}", level="warning")
                    time.sleep(retry_delay)
                else:
                    log_message(f"NAS Rsync: Final failing rsync command: {failed_cmd_str}", level="error")
                    log_message(f"NAS Rsync: {error_msg}", level="error")
                    raise RuntimeError(f"Rsync failed after {max_retries} attempts.") from e

            except Exception as e:
                # Catch any other unexpected errors
                try: failed_cmd_str = ' '.join(shlex.quote(str(arg)) for arg in rsync_command)
                except NameError: failed_cmd_str = "(command list not fully constructed)"
                # Log failure on the same line as execution intent
                log_message(f"NAS Rsync: Executing rsync (attempt {attempt + 1}): {failed_cmd_str} ====> Failed ({type(e).__name__})", level="error")

                error_msg = f"Rsync attempt {attempt + 1} encountered an unexpected error: {type(e).__name__} - {e}"
                # Stderr/Stdout might not be available on generic exceptions

                if attempt < max_retries - 1:
                    error_msg += f"\nRetrying in {retry_delay:.1f} seconds..."
                    log_message(f"NAS Rsync: {error_msg}", level="warning")
                    time.sleep(retry_delay)
                else:
                     log_message(f"NAS Rsync: {error_msg}", level="error")
                     raise RuntimeError(f"Rsync failed after {max_retries} attempts due to unexpected error.") from e


    def transfer_file_with_scp(self, local_path, remote_path, to_nas=True):
        """
        Transfers a file or directory using SCP with automatic connection handling and retries.
        Relies on self.ensure_connection from NASConnectionBase.
        """
        # Relies on self.ensure_connection from NASConnectionBase
        self.ensure_connection() # Although SCP runs externally, ensure Paramiko connection is conceptually ready
        absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa"))
        # Ensure the .ssh directory exists relative to the workspace root or project root
        ssh_dir = os.path.abspath(".ssh")
        if not os.path.isdir(ssh_dir):
             script_grandparent_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
             ssh_dir_alt = os.path.join(script_grandparent_dir, ".ssh")
             if os.path.isdir(ssh_dir_alt):
                 absolute_ssh_key_path = os.path.join(ssh_dir_alt, "id_rsa")
             else:
                 print(f"Warning: .ssh directory not found in workspace root ({ssh_dir}) or project root ({ssh_dir_alt}). Ensure id_rsa exists.")
                 absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa")) # Fallback

        if not os.path.exists(absolute_ssh_key_path):
             raise FileNotFoundError(f"SSH private key not found at expected location: {absolute_ssh_key_path}")

        nas_host = os.getenv("NAS_HOST")
        nas_user = os.getenv("NAS_USERNAME")
        if not nas_host or not nas_user:
             raise ValueError("NAS_HOST or NAS_USERNAME not found in environment variables.")

        # Construct remote specifier: user@host:/volume1/remote/path
        # Apply OS-specific path handling and quoting as in the original version
        # Revert to original logic:
        if os.name != 'nt':  # Check if not Windows
            # Use /volume1 prefix and single quotes for non-Windows path part
            full_remote_path = f'/volume1{remote_path}'
            remote_specifier = f'{nas_user}@{nas_host}:\'{full_remote_path}\'' # Manual single quotes
        else:
            # For Windows ('nt'), use the remote_path directly without /volume1 or extra quotes
            remote_specifier = f'{nas_user}@{nas_host}:{remote_path}'

        max_retries = 3
        retry_delay = 5  # seconds

        for attempt in range(max_retries):
            scp_command = None # Define for potential use in error messages
            try:
                # Construct command list EXACTLY like the original
                scp_command = [
                    "scp", "-r",
                    "-i", absolute_ssh_key_path, # Raw path
                    "-P", "22",
                    "-o", "StrictHostKeyChecking=no",
                    "-o", "UserKnownHostsFile=/dev/null"
                ]
                if to_nas:
                     # Append raw local path and OS-specific remote specifier
                     scp_command.extend([local_path, remote_specifier])
                else:
                     # Append OS-specific remote specifier and raw local path
                     scp_command.extend([remote_specifier, local_path])

                # Log the list representation or reconstruct string carefully for logging ONLY
                # Quote arguments containing spaces just for the log display
                log_display_command = ' '.join(f'"{arg}"' if ' ' in str(arg) else str(arg) for arg in scp_command)
                # Log execution start (will add result later)
                # log_message(f"NAS SCP: Executing scp (attempt {attempt + 1}): {log_display_command}", level="debug")

                result = subprocess.run(
                    scp_command,     # Pass the LIST
                    shell=False,     # Ensure NO shell=True (or omit as False is default)
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8', errors='ignore'
                )
                # Log success on the same line as execution intent (conceptually)
                # log_message(f"NAS SCP: Executing scp (attempt {attempt + 1}): {log_display_command} ====> Exit code 0", level="info")
                # Log stdout/stderr only if present, as debug info even on success
                # if result.stdout:
                #     log_message(f"NAS SCP: SCP stdout (success): {result.stdout.strip()[:500]}{'...' if len(result.stdout.strip())>500 else ''}", level="debug")
                # if result.stderr:
                #     log_message(f"NAS SCP: SCP stderr (success): {result.stderr.strip()[:500]}{'...' if len(result.stderr.strip())>500 else ''}", level="debug")
                return  # Success

            except FileNotFoundError as e:
                 # Handle missing scp executable
                 error_detail = "Ensure 'scp' command is installed and accessible in the system PATH."
                 log_message(f"NAS SCP: ERROR - SCP executable not found. {error_detail}. Original error: {e}", level="critical")
                 raise RuntimeError(f"SCP executable not found. {error_detail}") from e

            except subprocess.CalledProcessError as e:
                # Handle non-zero exit codes from scp
                # Reconstruct command string for error message display
                try:
                    failed_cmd_str = ' '.join(f'"{arg}"' if ' ' in str(arg) else str(arg) for arg in scp_command)
                except NameError:
                    failed_cmd_str = "(command list not constructed)"

                # Log failure on the same line as execution intent
                log_message(f"NAS SCP: Executing scp (attempt {attempt + 1}): {failed_cmd_str} ====> Exit code {e.returncode}", level="warning")

                error_msg = f"SCP attempt {attempt + 1} failed." # Simplified initial message
                # Log stdout only on failure
                if e.stdout:
                    log_message(f"NAS SCP: SCP stdout on failure: {e.stdout.strip()[:1000]}{'...' if len(e.stdout.strip())>1000 else ''}", level="debug")
                # Log stderr on failure
                if e.stderr:
                    log_message(f"NAS SCP: SCP stderr on failure: {e.stderr.strip()[:1000]}{'...' if len(e.stderr.strip())>1000 else ''}", level="warning")
                else:
                    error_msg += "\n(No specific stderr captured from process)" # Clarify if stderr attribute is empty

                # Add hints based on common SCP errors
                stderr_lower = e.stderr.lower() if e.stderr else ""
                if "permission denied" in stderr_lower and "publickey" not in stderr_lower:
                     error_msg += f"\nHint: SSH auth likely succeeded, but file/directory access denied on NAS. Check NAS folder permissions for user '{nas_user}' and ensure SCP is enabled (Control Panel > Terminal & SNMP > Enable SCP)."
                elif "permission denied (publickey" in stderr_lower or "could not resolve hostname" in stderr_lower or "connection timed out" in stderr_lower or "connection refused" in stderr_lower:
                    error_msg += f"\nHint: SSH connection/authentication failed. Verify NAS host ({nas_host}), port (22), username ('{nas_user}'), key path ('{absolute_ssh_key_path}'), and NAS firewall/SSH service status."
                elif "lost connection" in stderr_lower:
                     error_msg += f"\nHint: Connection dropped during transfer. Check network stability or potential timeouts on the NAS SSH server."

                if attempt < max_retries - 1:
                    error_msg += f"\nRetrying in {retry_delay:.1f} seconds..."
                    log_message(f"NAS SCP: {error_msg}", level="warning")
                    time.sleep(retry_delay)
                else:
                    log_message(f"NAS SCP: Final failing scp command: {failed_cmd_str}", level="error")
                    log_message(f"NAS SCP: {error_msg}", level="error")
                    raise RuntimeError(f"SCP failed after {max_retries} attempts.") from e

            except Exception as e:
                 # Catch any other unexpected errors
                 try:
                     # Reconstruct command string for error message display
                     failed_cmd_str = ' '.join(f'"{arg}"' if ' ' in str(arg) else str(arg) for arg in scp_command)
                 except NameError:
                     failed_cmd_str = "(command list not constructed)"
                 # Log failure on the same line as execution intent
                 log_message(f"NAS SCP: Executing scp (attempt {attempt + 1}): {failed_cmd_str} ====> Failed ({type(e).__name__})", level="error")

                 error_msg = f"SCP attempt {attempt + 1} encountered an unexpected error: {type(e).__name__} - {e}"
                 # Stderr/Stdout might not be available on generic exceptions

                 if attempt < max_retries - 1:
                     error_msg += f"\nRetrying in {retry_delay:.1f} seconds..."
                     log_message(f"NAS SCP: {error_msg}", level="warning")
                     time.sleep(retry_delay)
                 else:
                      log_message(f"NAS SCP: {error_msg}", level="error")
                      raise RuntimeError(f"SCP failed after {max_retries} attempts due to unexpected error.") from e


    def test_transfer_speed(self, df, dockets):
        """
        Tests and compares the speed of transfer_file_with_scp and transfer_file_with_rsync.
        Requires a DataFrame `df` with case information to find test data.
        Relies on methods from NASConnectionBase and this Mixin.
        """
        log_message("NAS Test: Starting transfer speed test.", level="info")

        num_files_to_test = 1 # Test with one representative file/folder first
        case_names = []
        case_directories_nas = []
        case_temp_local_dirs = [] # Store paths to temporary local directories

        # Find existing cases on NAS to use for testing transfer
        found_cases = 0
        log_message("NAS Test: Searching for existing case data on NAS for testing...", level="debug")
        for docket in dockets:
            row = df[df['docket'].str.contains(docket)].iloc[0]
            if found_cases >= num_files_to_test:
                break

            try:
                date_filed_str = pd.to_datetime(row['date_filed']).strftime('%Y-%m-%d')
                case_name = sanitize_name(f"{date_filed_str} - {row['docket']}")
                case_directory_nas = f"{nas_case_folder}/{case_name}"

                if self.ssh_exists(case_directory_nas) and self.ssh_is_dir(case_directory_nas): # Ensure it's a directory
                    # Create a temporary local directory for download testing
                    temp_local_dir = tempfile.mkdtemp(prefix=f"test_dl_{case_name}_")
                    case_temp_local_dirs.append(temp_local_dir) # Store the temp dir path
                    case_names.append(case_name)
                    case_directories_nas.append(case_directory_nas) # NAS path to the *folder*
                    found_cases += 1
                    log_message(f"NAS Test: Found test case on NAS: {case_directory_nas}", level="info")
                else:
                    log_message(f"NAS Test: Directory not found or not a directory on NAS, skipping: {case_directory_nas}", level="debug")
            except Exception as e:
                log_message(f"NAS Test: Error checking NAS directory {case_directory_nas}: {e}", level="warning")
                continue # Skip this case if check fails


        if not case_directories_nas:
            log_message("NAS Test: Could not find any suitable test case directories on the NAS. Aborting speed test.", level="warning")
            self._cleanup_temp_dirs(case_temp_local_dirs)
            return {}

        # --- Prepare Test File (Zip the first found case directory on NAS, download it) ---
        source_nas_folder = case_directories_nas[0]
        temp_local_staging = tempfile.mkdtemp(prefix="test_staging_")
        local_zip_base_name = os.path.join(temp_local_staging, case_names[0])
        local_zip_path = local_zip_base_name + '.zip'
        # Use a consistent remote zip name in the parent folder or nas_case_folder
        remote_folder_parent = os.path.dirname(source_nas_folder)
        if remote_folder_parent and remote_folder_parent != '/':
             remote_zip_path = f"{remote_folder_parent}/{case_names[0]}.zip"
        else:
             remote_zip_path = f"{nas_case_folder}/{case_names[0]}.zip"


        log_message(f"NAS Test: Preparing test zip file from {source_nas_folder}...", level="info")
        results = {}
        try:
            # 1. Zip the folder on NAS first (using _zip_remote_folder from Base)
            log_message(f"NAS Test: Zipping {source_nas_folder} on NAS to {remote_zip_path}...", level="debug")
            actual_remote_zip_path = self._zip_remote_folder(source_nas_folder) # Let the method determine the exact path
            # Use the actual path returned by _zip_remote_folder
            remote_zip_path = actual_remote_zip_path

            # 2. Download the zip using SCP (baseline, using transfer_file_with_scp from this Mixin)
            log_message(f"NAS Test: Downloading zip {remote_zip_path} using SCP to {local_zip_path}...", level="debug")
            self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=False)

            # 3. Remove the zip from NAS for clean upload tests (using ssh_remove_file from Base)
            log_message(f"NAS Test: Removing temporary zip {remote_zip_path} from NAS...", level="debug")
            self.ssh_remove_file(remote_zip_path)

            if not os.path.exists(local_zip_path):
                 raise FileNotFoundError(f"Failed to create local test zip: {local_zip_path}")
            zip_size_mb = os.path.getsize(local_zip_path) / (1024*1024)
            log_message(f"NAS Test: Test zip file ready: {local_zip_path} ({zip_size_mb:.2f} MB)", level="info")

        except Exception as e:
            log_message(f"NAS Test: Error preparing test zip file: {e}", level="error")
            self._cleanup_temp_dirs([temp_local_staging] + case_temp_local_dirs)
            # Attempt remote cleanup
            try:
                if remote_zip_path and self.ssh_exists(remote_zip_path): self.ssh_remove_file(remote_zip_path)
            except Exception: pass
            return {}

        # --- Test Upload ---
        log_message(f"\n--- Testing Upload Speed ({zip_size_mb:.2f} MB) ---", level="info")
        # SCP Upload
        log_message("NAS Test: Testing SCP Upload...", level="info")
        try:
            start_time = time.time()
            self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True)
            end_time = time.time()
            results['scp_upload_time'] = end_time - start_time
            log_message(f"  SCP Upload Time: {results['scp_upload_time']:.4f} seconds", level="info")
            if self.ssh_exists(remote_zip_path): self.ssh_remove_file(remote_zip_path) # Clean up after test
            else: log_message("NAS Test: Warning - SCP upload verification failed (file not found on NAS).", level="warning")
        except Exception as e:
            log_message(f"  SCP Upload Failed: {e}", level="error")
            results['scp_upload_time'] = float('inf') # Indicate failure

        # Rsync Upload
        log_message("NAS Test: Testing Rsync Upload...", level="info")
        try:
            start_time = time.time()
            self.transfer_file_with_rsync(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True)
            end_time = time.time()
            results['rsync_upload_time'] = end_time - start_time
            log_message(f"  Rsync Upload Time: {results['rsync_upload_time']:.4f} seconds", level="info")
            if self.ssh_exists(remote_zip_path): self.ssh_remove_file(remote_zip_path) # Clean up after test
            else: log_message("NAS Test: Warning - Rsync upload verification failed (file not found on NAS).", level="warning")
        except Exception as e:
            log_message(f"  Rsync Upload Failed: {e}", level="error")
            results['rsync_upload_time'] = float('inf') # Indicate failure


        # --- Test Download ---
        log_message("\n--- Testing Download Speed ---", level="info")
        # Re-upload file once for download tests (using SCP as baseline)
        log_message("NAS Test: Setting up file on NAS for download tests...", level="debug")
        try:
            self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True)
            if not self.ssh_exists(remote_zip_path): raise Exception("Failed to upload file for download test setup.")
        except Exception as e:
            log_message(f"NAS Test: Failed to set up remote file for download tests: {e}", level="error")
            self._cleanup_temp_dirs([temp_local_staging] + case_temp_local_dirs)
            return results # Return partial results if setup fails

        # SCP Download
        local_download_path_scp = os.path.join(case_temp_local_dirs[0], "downloaded_scp.zip")
        log_message("NAS Test: Testing SCP Download...", level="info")
        try:
            if os.path.exists(local_download_path_scp): os.remove(local_download_path_scp) # Clean previous download
            start_time = time.time()
            self.transfer_file_with_scp(local_path=local_download_path_scp, remote_path=remote_zip_path, to_nas=False)
            end_time = time.time()
            results['scp_download_time'] = end_time - start_time
            log_message(f"  SCP Download Time: {results['scp_download_time']:.4f} seconds", level="info")
            if not os.path.exists(local_download_path_scp): log_message("NAS Test: Warning - SCP download verification failed (file not found locally).", level="warning")
        except Exception as e:
            log_message(f"  SCP Download Failed: {e}", level="error")
            results['scp_download_time'] = float('inf') # Indicate failure

        # Rsync Download
        local_download_path_rsync = os.path.join(case_temp_local_dirs[0], "downloaded_rsync.zip")
        log_message("NAS Test: Testing Rsync Download...", level="info")
        try:
            if os.path.exists(local_download_path_rsync): os.remove(local_download_path_rsync) # Clean previous download
            start_time = time.time()
            self.transfer_file_with_rsync(local_path=local_download_path_rsync, remote_path=remote_zip_path, to_nas=False)
            end_time = time.time()
            results['rsync_download_time'] = end_time - start_time
            log_message(f"  Rsync Download Time: {results['rsync_download_time']:.4f} seconds", level="info")
            if not os.path.exists(local_download_path_rsync): log_message("NAS Test: Warning - Rsync download verification failed (file not found locally).", level="warning")
        except Exception as e:
            log_message(f"  Rsync Download Failed: {e}", level="error")
            results['rsync_download_time'] = float('inf') # Indicate failure

        # --- Cleanup ---
        log_message("\nNAS Test: Cleaning up test files...", level="info")
        try:
            # Remove remote zip file
            if self.ssh_exists(remote_zip_path):
                self.ssh_remove_file(remote_zip_path)
                log_message(f"NAS Test: Removed remote zip: {remote_zip_path}", level="debug")
        except Exception as e:
            log_message(f"NAS Test: Warning - Error during remote cleanup: {e}", level="warning")

        # Remove local staging/download directories
        self._cleanup_temp_dirs([temp_local_staging] + case_temp_local_dirs)

        log_message("\n--- Speed Test Summary ---", level="info")
        log_message(f"SCP Upload:    {results.get('scp_upload_time', 'N/A'):.4f} seconds", level="info")
        log_message(f"Rsync Upload:  {results.get('rsync_upload_time', 'N/A'):.4f} seconds", level="info")
        log_message(f"SCP Download:  {results.get('scp_download_time', 'N/A'):.4f} seconds", level="info")
        log_message(f"Rsync Download:{results.get('rsync_download_time', 'N/A'):.4f} seconds", level="info")
        log_message("-------------------------", level="info")

        return results

    def _cleanup_temp_dirs(self, dir_paths):
        """Safely removes a list of temporary directories."""
        for dir_path in dir_paths:
            if dir_path and os.path.exists(dir_path):
                try:
                    shutil.rmtree(dir_path)
                    log_message(f"NAS Test: Removed temp dir: {dir_path}", level="debug")
                except Exception as e:
                    log_message(f"NAS Test: Warning - Error removing temp dir {dir_path}: {e}", level="warning")