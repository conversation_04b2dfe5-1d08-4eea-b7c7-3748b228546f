#!/usr/bin/env python3
"""
Check Existing Images and Update Database

This script checks if image files exist on disk for trademark records in the database.
If an image file exists, it updates the image_source field to "USPTO_URL" in the database.
If the image file doesn't exist, it leaves the record unchanged.

Features:
- Processes 1.2M+ records in configurable batches
- Parallel file existence checking for performance
- Bulk database updates
- Progress tracking with ETA
- Checkpoint/resume capability
- Detailed statistics and logging
"""

import os
import sys
import time
import datetime
import concurrent.futures
from logdata import log_message
from dotenv import load_dotenv
import psycopg2.extras

# Import local modules
# Path: /app/IP/Trademarks_Bulk/One_Off/ -> /app/IP/Trademarks_Bulk/
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from trademark_db import get_db_connection, update_image_source
from trademark_image import get_image_subdirectory

# Import Common constants
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from Common.Constants import local_ip_folder

# Load environment variables
load_dotenv()

# Configuration - Following the same pattern as get_bulk_trademarks.py
BASE_DIR = os.path.join(local_ip_folder, "Trademarks")
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")

# Checkpoint file
CHECKPOINT_FILE = os.path.join(os.path.dirname(__file__), 'image_check_checkpoint.txt')


def get_total_eligible_records():
    """
    Get total count of trademark records that have mark_feature_code in [2, 3, 5].

    Returns:
        int: Total number of eligible records
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        sql = """
        SELECT COUNT(*)
        FROM trademarks
        WHERE mark_feature_code IN (2, 3, 5)
        """

        cursor.execute(sql)
        total = cursor.fetchone()[0]

        log_message(f"Total eligible records in database: {total:,}", level='INFO')
        return total

    except Exception as e:
        log_message(f"Error getting total record count: {str(e)}", level='ERROR')
        return 0
    finally:
        if conn:
            conn.close()


def get_trademark_records_batch(limit=5000, offset=0):
    """
    Get a batch of trademark records efficiently using LIMIT and OFFSET.

    Args:
        limit (int): Number of records to fetch
        offset (int): Starting offset for pagination

    Returns:
        list: List of trademark records with ser_no, image_source, and mark_feature_code
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Use ORDER BY ser_no for consistent batching
        sql = """
        SELECT ser_no, image_source, mark_feature_code
        FROM trademarks
        WHERE mark_feature_code IN (2, 3, 5)
        ORDER BY ser_no
        LIMIT %s OFFSET %s
        """

        cursor.execute(sql, (limit, offset))
        records = cursor.fetchall()

        # Convert to list of dictionaries
        columns = ['ser_no', 'image_source', 'mark_feature_code']
        result = [dict(zip(columns, record)) for record in records]

        log_message(f"Retrieved {len(result)} records (offset: {offset:,})", level='INFO')
        return result

    except Exception as e:
        log_message(f"Error fetching trademark records batch: {str(e)}", level='ERROR')
        return []
    finally:
        if conn:
            conn.close()


def check_image_file_exists(ser_no):
    """
    Check if image file exists on disk for the given serial number.

    Args:
        ser_no (str): Serial number of the trademark

    Returns:
        tuple: (exists: bool, image_path: str)
    """
    try:
        # Calculate image path using the same logic as the main pipeline
        image_sub_dir = get_image_subdirectory(ser_no)
        if not image_sub_dir:
            return False, None

        image_path = os.path.join(IMAGES_DIR, image_sub_dir, f"{ser_no}.webp")
        exists = os.path.exists(image_path)

        return exists, image_path

    except Exception as e:
        return False, None


def check_files_exist_in_parallel(records, max_workers=20):
    """
    Check if multiple files exist in parallel for better performance.

    Args:
        records: List of trademark records
        max_workers: Maximum number of parallel workers

    Returns:
        List of (ser_no, exists, image_path) tuples
    """
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_ser_no = {
            executor.submit(check_image_file_exists, record['ser_no']): record['ser_no']
            for record in records
        }

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_ser_no):
            ser_no = future_to_ser_no[future]
            try:
                exists, image_path = future.result()
                results.append((ser_no, exists, image_path))
            except Exception as e:
                log_message(f"Error checking file for {ser_no}: {str(e)}", level='ERROR')
                results.append((ser_no, False, None))

    return results


def bulk_update_image_source(updates):
    """
    Perform bulk updates to the database for better performance.

    Args:
        updates: List of (ser_no, image_source) tuples

    Returns:
        int: Number of records updated
    """
    if not updates:
        return 0

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Use psycopg2's execute_batch for efficient updates
        sql = """
        UPDATE trademarks
        SET image_source = %s
        WHERE ser_no = %s
        """

        # Reverse the tuple order for SQL parameter order
        batch_data = [(image_source, ser_no) for ser_no, image_source in updates]

        # Execute batch update
        psycopg2.extras.execute_batch(cursor, sql, batch_data)
        conn.commit()

        log_message(f"Bulk updated {len(updates)} records", level='INFO')
        return len(updates)

    except Exception as e:
        if conn:
            conn.rollback()
        log_message(f"Error in bulk update: {str(e)}", level='ERROR')
        return 0
    finally:
        if conn:
            conn.close()


def save_checkpoint(offset):
    """Save current progress to a checkpoint file"""
    try:
        with open(CHECKPOINT_FILE, 'w') as f:
            f.write(str(offset))
        log_message(f"Checkpoint saved: offset={offset:,}", level='INFO')
    except Exception as e:
        log_message(f"Error saving checkpoint: {str(e)}", level='ERROR')


def load_checkpoint():
    """Load progress from checkpoint file if it exists"""
    try:
        if os.path.exists(CHECKPOINT_FILE):
            with open(CHECKPOINT_FILE, 'r') as f:
                offset = int(f.read().strip())
            log_message(f"Resuming from checkpoint: offset={offset:,}", level='INFO')
            return offset
        return 0
    except Exception as e:
        log_message(f"Error loading checkpoint: {str(e)}", level='WARNING')
        return 0


def clear_checkpoint():
    """Remove checkpoint file"""
    try:
        if os.path.exists(CHECKPOINT_FILE):
            os.remove(CHECKPOINT_FILE)
            log_message("Checkpoint file cleared", level='INFO')
    except Exception as e:
        log_message(f"Error clearing checkpoint: {str(e)}", level='WARNING')


def format_time(seconds):
    """Format seconds into human-readable time"""
    if seconds < 60:
        return f"{seconds:.0f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{minutes:.0f}m {secs:.0f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours:.0f}h {minutes:.0f}m"


def get_statistics():
    """
    Get statistics about image_source distribution in the database.
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Total trademarks with mark_feature_code in [2, 3, 5]
        cursor.execute("""
            SELECT COUNT(*) FROM trademarks 
            WHERE mark_feature_code IN (2, 3, 5)
        """)
        total_eligible = cursor.fetchone()[0]
        
        # By image_source type
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN image_source IS NULL THEN 'NULL'
                    WHEN image_source = '' THEN 'EMPTY'
                    ELSE image_source
                END as source_type,
                COUNT(*) as count
            FROM trademarks 
            WHERE mark_feature_code IN (2, 3, 5)
            GROUP BY 
                CASE 
                    WHEN image_source IS NULL THEN 'NULL'
                    WHEN image_source = '' THEN 'EMPTY'
                    ELSE image_source
                END
            ORDER BY count DESC
        """)
        by_source = cursor.fetchall()
        
        log_message("📊 DATABASE STATISTICS:", level='INFO')
        log_message(f"   Total eligible trademarks (mark_feature_code 2,3,5): {total_eligible:,}", level='INFO')
        log_message("   Image source distribution:", level='INFO')
        for source, count in by_source:
            percentage = (count/total_eligible*100) if total_eligible > 0 else 0
            log_message(f"     {source}: {count:,} ({percentage:.1f}%)", level='INFO')
            
        return {
            'total_eligible': total_eligible,
            'by_source': dict(by_source)
        }
        
    except Exception as e:
        log_message(f"Error getting statistics: {str(e)}", level='ERROR')
        return None
    finally:
        if conn:
            conn.close()


def process_batch(records):
    """
    Process a single batch of records efficiently.

    Args:
        records: List of trademark records to process

    Returns:
        dict: Statistics for this batch
    """
    if not records:
        return {'files_found': 0, 'files_missing': 0, 'database_updates': 0, 'errors': 0}

    batch_start_time = time.time()

    # Check file existence in parallel
    log_message(f"   🔍 Checking {len(records)} files in parallel...", level='INFO')
    file_results = check_files_exist_in_parallel(records, max_workers=20)

    # Create lookup for current image_source values
    current_sources = {record['ser_no']: record['image_source'] for record in records}

    # Prepare bulk updates
    updates_needed = []
    files_found = 0
    files_missing = 0

    for ser_no, exists, image_path in file_results:
        current_source = current_sources.get(ser_no)

        if exists:
            files_found += 1
            # Only update if not already "USPTO_URL"
            if current_source != "USPTO_URL":
                updates_needed.append((ser_no, "USPTO_URL"))
        else:
            files_missing += 1

    # Perform bulk database updates
    database_updates = 0
    errors = 0

    if updates_needed:
        log_message(f"   📝 Bulk updating {len(updates_needed)} records...", level='INFO')
        try:
            database_updates = bulk_update_image_source(updates_needed)
        except Exception as e:
            log_message(f"   ❌ Bulk update failed: {str(e)}", level='ERROR')
            errors = len(updates_needed)

    batch_time = time.time() - batch_start_time
    records_per_second = len(records) / batch_time if batch_time > 0 else 0

    log_message(f"   ⏱️  Batch completed in {batch_time:.1f}s ({records_per_second:.1f} records/sec)", level='INFO')
    log_message(f"   📊 Found: {files_found}, Missing: {files_missing}, Updated: {database_updates}", level='INFO')

    return {
        'files_found': files_found,
        'files_missing': files_missing,
        'database_updates': database_updates,
        'errors': errors
    }


def process_all_records_in_batches(batch_size=5000, start_offset=0):
    """
    Process all eligible trademark records in batches.

    Args:
        batch_size (int): Number of records per batch
        start_offset (int): Starting offset (for resuming)
    """
    # Get total count first
    total_records = get_total_eligible_records()
    if total_records == 0:
        log_message("No eligible records found", level='WARNING')
        return

    log_message(f"Found {total_records:,} total eligible records", level='INFO')

    # Calculate number of batches
    total_batches = (total_records + batch_size - 1) // batch_size

    # Initialize counters
    total_files_found = 0
    total_files_missing = 0
    total_database_updates = 0
    total_errors = 0

    # Process each batch
    current_offset = start_offset
    start_time = time.time()

    for batch_num in range(start_offset // batch_size, total_batches):
        batch_start_time = time.time()

        log_message(f"\n===== BATCH {batch_num+1}/{total_batches} =====", level='INFO')
        log_message(f"Processing records {current_offset+1:,}-{min(current_offset+batch_size, total_records):,}", level='INFO')

        # Get this batch of records
        records = get_trademark_records_batch(limit=batch_size, offset=current_offset)

        if not records:
            log_message("No more records to process", level='INFO')
            break

        # Process this batch
        batch_stats = process_batch(records)

        # Update totals
        total_files_found += batch_stats['files_found']
        total_files_missing += batch_stats['files_missing']
        total_database_updates += batch_stats['database_updates']
        total_errors += batch_stats['errors']

        # Update progress and ETA
        current_offset += len(records)
        records_processed = min(current_offset, total_records)
        percent_complete = (records_processed / total_records) * 100

        elapsed_time = time.time() - start_time
        records_per_second = records_processed / elapsed_time if elapsed_time > 0 else 0

        remaining_records = total_records - records_processed
        eta_seconds = remaining_records / records_per_second if records_per_second > 0 else 0

        log_message(f"📊 Overall Progress: {percent_complete:.1f}% ({records_processed:,}/{total_records:,})", level='INFO')
        log_message(f"⚡ Speed: {records_per_second:.1f} records/second", level='INFO')
        log_message(f"⏰ ETA: {format_time(eta_seconds)}", level='INFO')

        # Save checkpoint
        save_checkpoint(current_offset)

        # Optional: Add a small delay to prevent system overload
        time.sleep(0.5)

    # Final summary
    log_message("\n" + "=" * 80, level='INFO')
    log_message("🎉 FINAL PROCESSING SUMMARY:", level='INFO')
    log_message(f"   Total records processed: {records_processed:,}", level='INFO')
    log_message(f"   Files found: {total_files_found:,}", level='INFO')
    log_message(f"   Files missing: {total_files_missing:,}", level='INFO')
    log_message(f"   Database updates: {total_database_updates:,}", level='INFO')
    log_message(f"   Errors: {total_errors:,}", level='INFO')

    if records_processed > 0:
        success_rate = (total_files_found / records_processed) * 100
        log_message(f"   File existence rate: {success_rate:.1f}%", level='INFO')

    total_time = time.time() - start_time
    log_message(f"   Total processing time: {format_time(total_time)}", level='INFO')

    # Clear checkpoint on successful completion
    if current_offset >= total_records:
        clear_checkpoint()
        log_message("✅ All records processed successfully!", level='INFO')


def process_image_check_small_batch(limit=10):
    """
    Process a small batch for testing (legacy function for compatibility).

    Args:
        limit (int): Number of records to process
    """
    log_message("🧪 Running small batch test mode", level='INFO')
    log_message(f"📊 Processing {limit} records", level='INFO')

    # Get records using the batch function
    records = get_trademark_records_batch(limit=limit, offset=0)

    if not records:
        log_message("No records found for processing", level='WARNING')
        return

    # Process the batch
    batch_stats = process_batch(records)

    # Show results
    log_message("\n📊 SMALL BATCH RESULTS:", level='INFO')
    log_message(f"   Files found: {batch_stats['files_found']}", level='INFO')
    log_message(f"   Files missing: {batch_stats['files_missing']}", level='INFO')
    log_message(f"   Database updates: {batch_stats['database_updates']}", level='INFO')
    log_message(f"   Errors: {batch_stats['errors']}", level='INFO')


if __name__ == "__main__":
    # Configuration parameters (instead of argparser)
    BATCH_SIZE = 5000       # Number of records per batch for full processing
    SMALL_BATCH_SIZE = 5    # Number of records for testing
    STATS_ONLY = False      # Set to True to show statistics only
    TEST_MODE = False       # Set to True for small batch testing
    RESUME = True           # Resume from checkpoint if available

    log_message("🚀 Starting Image File Check Script", level='INFO')
    log_message(f"📁 Images directory: {IMAGES_DIR}", level='INFO')
    log_message("=" * 80, level='INFO')

    if STATS_ONLY:
        # Show database statistics without processing any records
        log_message("📊 STATISTICS ONLY MODE", level='INFO')
        get_statistics()

    elif TEST_MODE:
        # Run small batch for testing
        log_message("🧪 TEST MODE - Processing small batch", level='INFO')
        get_statistics()
        process_image_check_small_batch(limit=SMALL_BATCH_SIZE)
        get_statistics()

    else:
        # Run full batch processing
        log_message("🚀 FULL PROCESSING MODE", level='INFO')

        # Show initial statistics
        log_message("📊 INITIAL STATISTICS:", level='INFO')
        get_statistics()

        # Get starting offset (0 or from checkpoint)
        start_offset = load_checkpoint() if RESUME else 0

        if start_offset > 0:
            log_message(f"🔄 Resuming from offset: {start_offset:,}", level='INFO')
        else:
            log_message("🆕 Starting fresh processing", level='INFO')

        # Process all records in batches
        process_all_records_in_batches(
            batch_size=BATCH_SIZE,
            start_offset=start_offset
        )

        # Show final statistics
        log_message("\n📊 FINAL STATISTICS:", level='INFO')
        get_statistics()

        log_message("\n🎉 Image file check process completed!", level='INFO')
