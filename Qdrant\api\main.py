#!/usr/bin/env python3
"""
FastAPI application for IP Infringement Detection.
"""

from fastapi import FastAPI
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import routes
from routes import forward_check, reverse_check, delete_points, reverse_check_status

# Initialize FastAPI app
app = FastAPI(
    title="IP Infringement Detection API",
    description="API for detecting potential IP infringements using vector search",
    version="1.0.0"
)

# Include routers
app.include_router(forward_check.router)
app.include_router(reverse_check.router)
app.include_router(delete_points.router)
app.include_router(reverse_check_status.router)

@app.get("/")
async def root():
    """
    Root endpoint.

    Returns:
        A welcome message.
    """
    return {
        "message": "Welcome to the IP Infringement Detection API",
        "docs": "/docs",
        "endpoints": [
            "/forward_check",
            "/reverse_check",
            "/delete_points"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
