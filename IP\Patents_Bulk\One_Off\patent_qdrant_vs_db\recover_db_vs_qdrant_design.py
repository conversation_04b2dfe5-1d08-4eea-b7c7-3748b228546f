#!/usr/bin/env python3
"""
Recovery Pipeline: DB vs Qdrant

Recovers patent files present in DB but missing in Qdrant by re-enqueuing images for embedding and upsert.
"""
import os
import sys
import csv
from tqdm import tqdm
from dotenv import load_dotenv
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from IP.embedding_queue import EmbeddingQueue
from Common.Constants import local_ip_folder
from Common.uuid_utils import generate_uuid

# Load environment variables
load_dotenv()

# Path to discrepancy CSV (from find_discrepancies.py output)
import glob
# csv_files = glob.glob(os.path.join(os.path.dirname(__file__), 'discrepancy_results', 'missing_in_qdrant_*.csv'))
csv_files = glob.glob(os.path.join(os.path.dirname(__file__), 'discrepancy_results', 'missing_1000.csv'))
if not csv_files:
    raise FileNotFoundError("No missing_in_qdrant CSV found in discrepancy_results directory.")
DISCREPANCY_CSV = max(csv_files, key=os.path.getmtime)  # Use latest file

async def main():
    print("Starting DB vs Qdrant recovery pipeline...")
    # Read discrepancy CSV (from find_discrepancies.py output)
    missing_records = []
    with open(DISCREPANCY_CSV, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            missing_records.append(row)
    print(f"Found {len(missing_records):,} records to recover from {DISCREPANCY_CSV}.")
    # Prepare image paths
    base_dir = Path(local_ip_folder) / "Patents" / "USPTO_Grants" / "Extracted"
    items_to_enqueue = []
    for rec in tqdm(missing_records, desc="Preparing image paths"):
        reg_no = rec.get('reg_no')
        file_name = rec.get('file_name')
        # Find image file in Extracted folder structure
        found_path = None
        for xx_dir in base_dir.iterdir():
            if not xx_dir.is_dir(): continue
            for yy_dir in xx_dir.iterdir():
                if not yy_dir.is_dir(): continue
                for patent_folder in yy_dir.iterdir():
                    if not patent_folder.is_dir(): continue
                    if reg_no and reg_no in patent_folder.name:
                        candidate = patent_folder / file_name if file_name else None
                        if candidate and candidate.exists():
                            found_path = str(candidate)
                            break
                if found_path: break
            if found_path: break
        if found_path:
            items_to_enqueue.append(("Patent", reg_no, found_path))
    print(f"Prepared {len(items_to_enqueue):,} images for embedding recovery.")
    # Enqueue for embedding
    embedding_queue = EmbeddingQueue(max_concurrent=2)
    await embedding_queue.start()
    enqueued, skipped = await embedding_queue.batch_enqueue_patents(items_to_enqueue)
    print(f"Enqueued {enqueued:,} images, skipped {skipped:,} (already exist)")
    await embedding_queue.drain()
    await embedding_queue.stop()
    print("DB vs Qdrant recovery completed.")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
