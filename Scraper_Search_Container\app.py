import os
import tempfile
import shutil
from flask import Flask, request, jsonify, send_file
from werkzeug.utils import secure_filename
import base64 # Added for base64 encoding

# Assume these are in the parent directory or accessible via PYTHONPATH
from Chrome_Driver import get_driver
from ChineseWebsites_C_copyright_google import reverse_search_and_download as google_reverse_search
from ChineseWebsites_C_copyright_tineye import reverse_search_and_download as tineye_reverse_search
from logdata import log_message

app = Flask(__name__)

# Configuration
UPLOAD_FOLDER = tempfile.gettempdir() # Use system temp directory for uploads
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

@app.route('/search_image', methods=['POST'])
def search_image():
    """
    Receives an image file, performs reverse image search on Google and TinEye,
    and returns the results.
    """
    log_message("Received request to /search_image")

    # Check if the post request has the file part
    if 'image' not in request.files:
        log_message("No 'image' file part in request.")
        return jsonify({"error": "No image file provided"}), 400
    
    # Get max_results from form data, default to 10 if not provided or invalid
    max_results_str = request.form.get('max_results', '10')
    max_results = int(max_results_str) if max_results_str.isdigit() else 10

    file = request.files['image']

    # If user does not select a file, browser submits an empty file without a filename
    if file.filename == '':
        log_message("No selected file.")
        return jsonify({"error": "No selected file"}), 400

    if file:
        # Secure the filename and save it temporarily
        filename = secure_filename(file.filename)
        temp_upload_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        try:
            file.save(temp_upload_path)
            log_message(f"Saved uploaded file temporarily to: {temp_upload_path}")
        except Exception as e:
            log_message(f"Error saving uploaded file: {e}")
            return jsonify({"error": f"Failed to save uploaded file: {e}"}), 500

        # Create a temporary directory for scraper outputs for this request
        temp_output_dir = None
        driver = None
        all_results = []

        try:
            temp_output_dir = tempfile.mkdtemp(prefix="scraper_output_")
            log_message(f"Created temporary output directory: {temp_output_dir}")

            # Get Selenium driver instance
            log_message("Initializing Chrome driver...")
            driver = get_driver()
            if driver is None:
                 raise Exception("Failed to initialize Chrome driver")
            log_message("Chrome driver initialized.")

            # Perform Google Search
            log_message("Starting Google reverse search...")
            google_output_folder = os.path.join(temp_output_dir, "google_images")
            os.makedirs(google_output_folder, exist_ok=True)
            google_results = google_reverse_search(driver, temp_upload_path, google_output_folder, max_images=max_results)
            log_message(f"Google search returned {len(google_results)} results.")
            all_results.extend(google_results)

            # Perform TinEye Search
            log_message("Starting TinEye reverse search...")
            tineye_output_folder = os.path.join(temp_output_dir, "tineye_images")
            os.makedirs(tineye_output_folder, exist_ok=True)
            tineye_results = tineye_reverse_search(driver, temp_upload_path, tineye_output_folder, max_images=max_results)
            log_message(f"TinEye search returned {len(tineye_results)} results.")
            all_results.extend(tineye_results)

            # Prepare results for JSON response with base64 encoded images
            formatted_results = []
            for image_path in all_results: # all_results is now a list of paths
                try:
                    with open(image_path, "rb") as img_file:
                        img_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                    
                    source = "unknown"
                    filename = os.path.basename(image_path)
                    if "google_" in filename: # Assuming google scraper prefixes filenames with "google_"
                        source = "google"
                    elif "tineye_" in filename: # Assuming tineye scraper prefixes filenames with "tineye_"
                        source = "tineye"
                    
                    formatted_results.append({
                        "source": source,
                        "filename": filename,
                        "image_data": img_base64
                    })
                except FileNotFoundError:
                    log_message(f"Error: File not found at path {image_path} during base64 encoding. Skipping.")
                except Exception as e:
                    log_message(f"Error processing file {image_path} for base64 encoding: {e}")

            log_message(f"Returning {len(formatted_results)} total results.")
            return jsonify({"success": True, "results": formatted_results})

        except Exception as e:
            log_message(f"An error occurred during image search: {e}")
            # Log traceback for debugging
            import traceback
            log_message(traceback.format_exc())
            return jsonify({"error": f"An error occurred during image search: {e}"}), 500
        finally:
            # Clean up temporary files and directories
            if driver:
                try:
                    driver.quit()
                    log_message("Chrome driver quit.")
                except Exception as dq_e:
                    log_message(f"Error quitting driver: {dq_e}")
            if os.path.exists(temp_upload_path):
                try:
                    os.remove(temp_upload_path)
                    log_message(f"Cleaned up temporary upload file: {temp_upload_path}")
                except Exception as rm_e:
                    log_message(f"Error removing temp upload file: {rm_e}")
            if temp_output_dir and os.path.exists(temp_output_dir):
                try:
                    shutil.rmtree(temp_output_dir)
                    log_message(f"Cleaned up temporary output directory: {temp_output_dir}")
                except Exception as rmtree_e:
                    log_message(f"Error removing temp output directory: {rmtree_e}")

    return jsonify({"error": "Something went wrong processing the file"}), 500 # Should not be reached

if __name__ == '__main__':
    # Run the Flask app
    # Using host='0.0.0.0' makes the server accessible externally
    # Port 5005 as specified in transfer.ps1
    log_message("Starting Flask app on port 5005...")
    app.run(host='0.0.0.0', port=5005, debug=False) # Set debug=True for development