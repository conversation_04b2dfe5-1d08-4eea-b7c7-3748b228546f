#!/bin/bash
# Qdrant Collection Sync Script
# This script syncs a Qdrant collection from a main to a backup server via snapshots.

# Exit on error, undefined variable, or pipe failure
set -euo pipefail

# --- Configuration ---
MAIN_QDRANT_URL="https://vectorstore1.maidalv.com:6333"
BACKUP_QDRANT_URL="https://vectorstore2.maidalv.com:6333"
QDRANT_API_KEY="2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ"
COLLECTION_NAMES=("IP_Assets_Optimized" "Product_Images")

# IMPORTANT: This directory must be mapped as a volume into your backup Qdrant container.
# Host Path: The directory on the machine running this script.
# Container Path: The path *inside* the Qdrant container that maps to the host path.
HOST_SNAPSHOT_DIR="/mnt/4tb/qdrant/snapshots"
CONTAINER_SNAPSHOT_DIR="/qdrant/snapshots"

# --- Script Setup ---
LOG_FILE="/var/log/qdrant_sync.log"

# --- Logging and Error Handling ---

# Function to log messages to both console and log file
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to handle errors, log the message, and exit
handle_error() {
    local exit_code=$2
    log "ERROR: $1 (Exit Code: $exit_code)"
    log "--- SCRIPT FAILED ---"
    exit "$exit_code"
}

# Trap ERR to call handle_error, ensuring we always log the failure
trap 'handle_error "An unexpected error occurred on line $LINENO." $?' ERR

# --- Pre-flight Checks ---

# Function to check for required command-line tools
check_dependencies() {
    log "Checking for required tools..."
    local missing_tools=0
    for tool in curl jq; do
        if ! command -v "$tool" &> /dev/null; then
            log "ERROR: Required tool '$tool' is not installed."
            missing_tools=1
        fi
    done
    if [ ! -d "$HOST_SNAPSHOT_DIR" ]; then
        log "ERROR: Snapshot directory '$HOST_SNAPSHOT_DIR' does not exist. Please create it."
        missing_tools=1
    fi
    if [ "$missing_tools" -eq 1 ]; then
        exit 1
    fi
    log "All required tools are present."
}

# Function to check Qdrant connection
check_qdrant_connection() {
    local url=$1; local server_name=$2
    log "Checking connection to $server_name Qdrant server..."
    if curl -s --fail -o /dev/null "$url/" ; then
        log "✅ Connection to $server_name Qdrant at $url successful."
    else
        handle_error "Failed to connect to $server_name Qdrant at $url." 1
    fi
}

# --- Qdrant Functions ---

# Function to get the total number of points in a Qdrant collection
get_qdrant_collection_count() {
    local url=$1
    local collection_name=$2
    local response
    response=$(curl -s -X GET "$url/collections/$collection_name" -H "api-key: $QDRANT_API_KEY")
    local count
    count=$(echo "$response" | jq -r '.result.points_count // 0')
    echo "$count"
}

# Function to wait for a collection to be in a green state
wait_for_collection_green() {
    local url=$1
    local collection_name=$2
    local polling_interval_seconds=15
    local max_attempts=40 # 10 minutes

    log "Waiting for collection '$collection_name' on $url to be 'green'..."

    local attempts=0
    while [[ $attempts -lt $max_attempts ]]; do
        local response
        response=$(curl -s "$url/collections/$collection_name" -H "api-key: $QDRANT_API_KEY")
        local status
        status=$(echo "$response" | jq -r '.result.status // "error"')

        case "$status" in
            green)
                log "✅ Collection '$collection_name' is green."
                return 0
                ;;
            yellow)
                log "⏳ Collection status is yellow (optimizing). Waiting..."
                ;;
            *)
                log "⏳ Collection status is '$status'. Waiting..."
                ;;
        esac

        attempts=$((attempts + 1))
        sleep $polling_interval_seconds
    done

    handle_error "Timed out waiting for collection '$collection_name' to become green." 1
}

# --- Main Logic ---

main() {
    # Truncate the log file to start fresh for this run
    > "$LOG_FILE"

    log "--- Starting Qdrant Sync Process ---"

    # 1. Pre-flight Checks
    log "=== Phase 1: Pre-flight Checks ==="
    check_dependencies
    check_qdrant_connection "$MAIN_QDRANT_URL" "Main"
    check_qdrant_connection "$BACKUP_QDRANT_URL" "Backup"
    log "✅ All pre-flight checks passed."

    for COLLECTION_NAME in "${COLLECTION_NAMES[@]}"; do
        log "--- Processing collection: $COLLECTION_NAME ---"

        # 2. Qdrant Sync
        log "=== Phase 2: Qdrant Sync for $COLLECTION_NAME ==="

        # Wait for the collection to be in a green state before creating a snapshot
        wait_for_collection_green "$MAIN_QDRANT_URL" "$COLLECTION_NAME"

        log "Creating a new snapshot for '$COLLECTION_NAME' on the main server..."
        
        # Create a new snapshot and capture the response
        NEW_SNAP_JSON=$(curl -s -X POST \
            "$MAIN_QDRANT_URL/collections/$COLLECTION_NAME/snapshots?wait=true" \
            -H "api-key: $QDRANT_API_KEY")
        
        # Extract the snapshot name from the JSON response
        LATEST_SNAPSHOT_NAME=$(echo "$NEW_SNAP_JSON" | jq -r '.result.name')

        if [[ -z "$LATEST_SNAPSHOT_NAME" || "$LATEST_SNAPSHOT_NAME" == "null" ]]; then
            handle_error "Failed to create or retrieve snapshot name for collection '$COLLECTION_NAME'. Response: $NEW_SNAP_JSON" 1
        fi
        log "Successfully created new snapshot: $LATEST_SNAPSHOT_NAME"

        local final_snapshot_path="$HOST_SNAPSHOT_DIR/$LATEST_SNAPSHOT_NAME"
        log "Downloading snapshot from main server to $final_snapshot_path..."
        local snapshot_url="$MAIN_QDRANT_URL/collections/$COLLECTION_NAME/snapshots/$LATEST_SNAPSHOT_NAME"

        # Use curl to download the file
        if ! curl -k -f -o "$final_snapshot_path" \
            -H "api-key: $QDRANT_API_KEY" \
            "$snapshot_url"; then
            handle_error "Failed to download snapshot from '$snapshot_url'. Check URL, API key, and network." $?
        fi

        log "✅ Snapshot successfully downloaded."
        log "Preparing for fast local restore on backup server..."

        # Delete Existing Collection on Backup Server
        log "Deleting existing collection '$COLLECTION_NAME' on backup server..."
        local delete_code
        delete_code=$(curl -k -s -w "%{http_code}" -X DELETE "$BACKUP_QDRANT_URL/collections/$COLLECTION_NAME" \
            -H "api-key: $QDRANT_API_KEY" -o /dev/null)

        if [[ "$delete_code" -ne 200 && "$delete_code" -ne 404 ]]; then
            log "⚠️ Warning: Failed to delete existing collection (HTTP $delete_code). Restore might fail."
        else
            log "Old collection removed (or did not exist). Proceeding..."
        fi
        sleep 2 # Give Qdrant a moment to process the deletion

        # Trigger the restore from the local file inside the container
        log "Requesting Qdrant to restore from local snapshot..."
        local snapshot_path_in_container="file://${CONTAINER_SNAPSHOT_DIR}/${LATEST_SNAPSHOT_NAME}"

        local http_code
        http_code=$(curl -k -w "%{http_code}" -X PUT "$BACKUP_QDRANT_URL/collections/$COLLECTION_NAME/snapshots/recover" \
            -H "Content-Type: application/json" \
            -H "api-key: $QDRANT_API_KEY" \
            -d '{
                "location": "'"$snapshot_path_in_container"'",
                "priority": "snapshot"
            }' \
            -o /dev/null)

        if [[ "$http_code" -ne 200 && "$http_code" -ne 202 ]]; then
            handle_error "Snapshot recovery request failed with HTTP code $http_code. Please check Qdrant container logs for details." 1
        fi

        # Verify Restoration
        local polling_interval_seconds=30
        local max_attempts=120

        log "✅ Recovery command accepted. Polling collection status every $polling_interval_seconds seconds..."

        local attempts=0
        local sync_successful=false
        while [[ $attempts -lt $max_attempts ]]; do
            local response
            response=$(curl -s "$BACKUP_QDRANT_URL/collections/$COLLECTION_NAME" -H "api-key: $QDRANT_API_KEY")
            local status
            status=$(echo "$response" | jq -r '.result.status // "error"')

            case "$status" in
                green)
                    log "✅ Collection '$COLLECTION_NAME' is active and ready."
                    sync_successful=true
                    break
                    ;;
                yellow)
                    log "⏳ Waiting for collection to become active. Current status: yellow (optimizing)."
                    ;;
                error)
                    log "❌ Error fetching collection status. It might not be created yet. Retrying..."
                    ;;
                *)
                    log "⏳ Collection status is '$status'. Waiting..."
                    ;;
            esac

            attempts=$((attempts + 1))
            sleep $polling_interval_seconds
        done

        if [ "$sync_successful" = false ]; then
            handle_error "Timed out after $(($max_attempts * $polling_interval_seconds)) seconds. Collection '$COLLECTION_NAME' did not become active." 1
        fi
        log "✅ Qdrant sync process for '$COLLECTION_NAME' completed."

        # 3. Verification
        log "=== Phase 3: Verification for $COLLECTION_NAME ==="
        log "Verifying Qdrant sync by comparing point counts..."
        local main_qdrant_count
        main_qdrant_count=$(get_qdrant_collection_count "$MAIN_QDRANT_URL" "$COLLECTION_NAME")
        local backup_qdrant_count
        backup_qdrant_count=$(get_qdrant_collection_count "$BACKUP_QDRANT_URL" "$COLLECTION_NAME")

        log "  -> Main server '$COLLECTION_NAME' points:   $main_qdrant_count"
        log "  -> Backup server '$COLLECTION_NAME' points:  $backup_qdrant_count"

        # A direct snapshot restore should result in an identical point count.
        if [ "$main_qdrant_count" -eq "$backup_qdrant_count" ] && [ "$backup_qdrant_count" -gt 0 ]; then
            log "✅ Verification successful for '$COLLECTION_NAME'! Qdrant point counts match."
        else
            handle_error "Verification failed for '$COLLECTION_NAME': Qdrant point counts do not match or are zero. Main: $main_qdrant_count, Backup: $backup_qdrant_count" 1
        fi
    done

    log "--- ✅ All Collections Synced Successfully ---"
    log "Log file is located at: $LOG_FILE"
}

# Execute the main function
main