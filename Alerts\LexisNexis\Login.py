import os
import time
import requests # Added for checking debugger endpoint
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import NoSuchElementException, WebDriverException, TimeoutException # Added WebDriverException
from Alerts.Chrome_Driver import send_keyboard_input, get_driver, move_mouse_to, random_delay, set_chrome_options # Consolidated imports
from logdata import log_message


# Note: to get the chrome debug browser to work, you need to :
# To control PDF behavior (plugins.always_open_pdf_externally): Look for PDF document settings, often under "Site Settings" -> "Additional content settings" -> "PDF documents". Choose "Download PDFs"
# In C:\chrome-debug\Default\Preferences edit the JSON and change 

def _check_debugger_endpoint(port: int) -> bool:
    """Checks if the Chrome debugger endpoint is active on the given port."""
    try:
        response = requests.get(f"http://localhost:{port}/json/version", timeout=1)
        response.raise_for_status() # Raise exception for bad status codes
        # Check if response is valid JSON (basic check)
        response.json()
        log_message(f"Chrome debugger endpoint found and responsive on port {port}.", level='INFO')
        return True
    except (requests.exceptions.ConnectionError, requests.exceptions.Timeout):
        log_message(f"Chrome debugger endpoint not found or unreachable on port {port}.", level='INFO')
        return False
    except Exception as e:
        log_message(f"Error checking Chrome debugger endpoint on port {port}: {e}", level='WARNING')
        return False

def get_logged_in_browser():
    """
    Gets a logged-in Selenium browser instance for LexisNexis.
    Attempts to connect to an existing Chrome instance on debug_port if running on Windows,
    otherwise creates a new driver instance.

    Returns:
        WebDriver: The Selenium WebDriver instance, hopefully logged in.
        Raises Exception: If login fails.
    """
    debug_port = 9223  # Default port for Chrome debugger
    driver=None

    # Try connecting to existing debugger session only on Windows
    if os.name == 'nt':
        log_message(f"Running on Windows (nt). Checking for Chrome debugger on port {debug_port}...", level='INFO')
        if _check_debugger_endpoint(debug_port):
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")

            try:
                log_message(f"Attempting to connect to existing Chrome session on port {debug_port}...", level='INFO')
                driver = webdriver.Chrome(options=chrome_options)
                log_message(f"Successfully connected to existing Chrome session: {driver.session_id}", level='INFO')
            except WebDriverException as e:
                log_message(f"Failed to connect to existing Chrome session on port {debug_port}: {e}", level='WARNING')
            except Exception as e:
                log_message(f"Unexpected error connecting to existing Chrome session: {e}", level='ERROR')

    # If not connected to existing (either not Windows, port not open, or connection failed), create new driver
    if driver is None:
        log_message("Creating a new Chrome driver instance.", level='INFO')
        driver = get_driver() # Pass driver path if needed by get_driver

    # Determine current page state efficiently
    on_login_page = len(driver.find_elements(By.ID, 'userid')) > 0
    on_case_page = len(driver.find_elements(By.ID, 'Header')) > 0
    has_inactive_session = len(driver.find_elements(By.CLASS_NAME, 'gvs-dialog')) > 0 or len(driver.find_elements(By.CSS_SELECTOR, '.ladialog.large .dialog-header')) > 0
    on_search_page = len(driver.find_elements(By.ID, 'docketNumberInput')) > 0

    if (not on_case_page and not on_search_page and not on_login_page) or has_inactive_session:
        driver.get('https://advance.lexis.com/courtlinksearch')
        random_delay() # Allow time for navigation
        try: 
            WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.ID, 'userid')))
            on_login_page = len(driver.find_elements(By.ID, 'userid')) > 0
        except TimeoutException:
            WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.ID, 'docketNumberInput')))
            on_search_page = len(driver.find_elements(By.ID, 'docketNumberInput')) > 0
        
    if on_login_page:
        log_message("On Login Page. Attempting login...", level='INFO')
        login_successful = login_into_platform(driver)
        if not login_successful:
            log_message("LexisNexis login failed.", level='ERROR')
            driver.quit()
            raise Exception("LexisNexis login failed")
        else:
            log_message(f'User has been logged in successfully in LexisNexis, using account {os.environ["LNusername"]}.')
    elif on_case_page:
        log_message("On a Case Page. Assuming logged in.", level='INFO')
        # Already on a content page, proceed
    elif on_search_page:
        log_message("On Search Page. Assuming logged in.", level='INFO')


    # Check if the "Ok, got it" button is present and click it if found
    try:
        ok_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Ok, got it')]"))
        )
        move_mouse_to(driver, ok_button)
        ok_button.click()
        random_delay()
        log_message("Clicked 'Ok, got it' button")
    except:
        pass

    return driver


def login_into_platform(driver):
    # Check if the login form is present
    try:
        actions = ActionChains(driver)
        
        # Move to the username input, click, and enter the username one by one
        username_input = driver.find_element(By.ID, 'userid')
        send_keyboard_input(driver, username_input, os.environ["LNusername"])

        # # Move to the "Remember Me" checkbox and click it
        remember_me_checkbox = driver.find_elements(By.ID, 'chkrmflag')
        if len(remember_me_checkbox) > 0 and remember_me_checkbox[0].is_displayed():
            actions.move_to_element(remember_me_checkbox[0]).click().perform()
 
        # Click the "Next" button
        next_button = driver.find_element(By.ID, 'signInSbmtBtn')
        actions.move_to_element(next_button).click().perform()

        # Wait for the password field to be enabled
        try:
            time.sleep(10)
            password_input = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, 'password')))
        except:
            log_message("Password input not found, trying again...")
            time.sleep(10)
            password_input = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.ID, 'password')))

        random_delay()
        password_input.send_keys('\u0001')  # Ctrl+A
        password_input.send_keys('\u0008')  # Backspace
        random_delay()

        # Move to the password input, click, and enter the password one by one
        send_keyboard_input(driver, password_input, os.environ["LNpassword"])
 
        remember_me_checkbox = driver.find_elements(By.ID, 'chkrmflag')
        if len(remember_me_checkbox) > 0 and remember_me_checkbox[0].is_displayed():
            actions.move_to_element(remember_me_checkbox[0]).click().perform()


        # Move to the "Sign In" button and click it
        
        # Click on the "rememberMe" checkbox if present
        remember_me_checkbox_new = driver.find_elements(By.ID, 'rememberMe')
        if len(remember_me_checkbox_new) > 0 and remember_me_checkbox_new[0].is_displayed():
            actions.move_to_element(remember_me_checkbox_new[0]).click().perform()
            log_message("Clicked 'rememberMe' checkbox.")

        submit_button = driver.find_elements(By.ID, 'next')
        sign_in_button = submit_button[0]
        actions.move_to_element(sign_in_button).click().perform()
        combined_locator = (By.XPATH, "//*[@id='docketNumberInput' or @id='SS_DocumentTitle']")
        WebDriverWait(driver, 60).until(EC.presence_of_element_located(combined_locator))
        return True  # Login successful

    except Exception as e:
        log_message(f"Error during LexisNexis login: {e}", level='ERROR')
        return False # Login failed


if __name__ == "__main__":
    driver_path = "C:/Program Files/Google/Chrome/Application/chrome.exe"

    # Start Chrome manually first with:
    # "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9223 --user-data-dir="C:\temp\chrome_debug_profile"
    # And log in to LexisNexis in that window.

    driver = None # Initialize driver to None
    try:
        driver = get_logged_in_browser()
        if driver:
            print("Successfully obtained browser driver.")
            print(f"Current URL: {driver.current_url}")
            # Add any test actions here if needed
            time.sleep(5) # Keep browser open for 5 seconds for verification
        else:
            print("Failed to obtain browser driver.")
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        # IMPORTANT: If connecting to an existing browser for multiple tests,
        # you typically DO NOT quit the driver here in the main script.
        # You quit it only after all tests using it are done.
        # However, for this simple __main__ test, we might quit it.
        # Decide based on how you integrate this into your testing workflow.
        if driver:
             # pass # Uncomment this line if you want the browser to stay open after this script runs
             driver.quit() # Uncomment this line to close the browser (or the connection) after this script runs
             print("Driver quit.")

