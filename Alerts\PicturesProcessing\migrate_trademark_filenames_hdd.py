import os, sys, imagehash, shutil, re, json, fitz, pickle, asyncio, time, multiprocessing, traceback
from typing import Optional, Tuple, Dict, List, Any # Added Optional, Tuple, Dict, List, Any
sys.path.append(os.getcwd())
import numpy as np
from PIL import Image
import pandas as pd
from logdata import log_message
from Common.Constants import local_case_folder, local_plaintiff_folder, sanitize_name
from Alerts.IPTrackingManager import IPTrackingManager # Added IPTrackingManager import
start_time = time.time()

from Alerts.PicturesProcessing.migrate_trademark_filenames_hdd_trademark_exhibit import process_trademark_exhibit
# from Alerts.PicturesProcessing.Trademarks_RegNo import process_trademark_regno
# from Alerts.PicturesProcessing.Trademarks_ByName import get_trademark_data_by_name

if multiprocessing.current_process().name == 'MainProcess':
    print(f"   ProcessPictures - Trademark after {time.time()-start_time:.2f} seconds") # 27 seconds!

start_time = time.time()

if multiprocessing.current_process().name == 'MainProcess':
    print(f"   ProcessPictures - TradeCopyright after {time.time()-start_time:.2f} seconds") # 2.5 sec


from Alerts.PicturesProcessing.ProcessPicturesShared import keywords
from DatabaseManagement.ImportExport import get_table_from_GZ
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
import langfuse

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


def delete_directory(directory):
    if os.path.exists(directory):
        shutil.rmtree(directory)


# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# +++ NEW FUNCTIONS FOR ITERATIVE PROCESSING ++++++++++++++++++++++++++++++++++
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

async def process_attachement_pdfs(pdf_paths: List[str], df: pd.DataFrame, index: Any, case_directory: str, step_nb: str, copyright_template1_hash: Any, copyright_template1_size: Any, certainty: str, ip_manager: IPTrackingManager) -> Tuple[Dict[str, Any], bool]:
    """
    Processes a list of downloaded PDF files for a specific case step to find IP exhibits.
    Updates the ip_manager and returns OCR data.

    Args:
        pdf_paths (list): List of absolute paths to PDF files for this step.
        df (pd.DataFrame): The main DataFrame (modified in place).
        plaintiff_df (pd.DataFrame): Plaintiff DataFrame.
        index: The index of the current case row in df.
        case_directory (str): Base directory for the case (e.g., /path/to/YYYY-MM-DD - docket).
        step_nb (str): The step number being processed.
        copyright_template1_hash: Precomputed hash for copyright template.
        copyright_template1_size: Precomputed size for copyright template.
        ip_manager (IPTrackingManager): Manager tracking the IP finding status.

    Returns:
        Tuple[Dict[str, Any], bool]: A tuple containing:
            - Dict[str, Any]: OCR data collected for the processed PDFs {pdf_path: [page_ocr_data, pages_text_json, page_images]}.
            - bool: True if a rubbish report was detected, False otherwise.
    """
    need_llm_file_paths = []
    files_to_delete = []
    log_message(f"        Processing {len(pdf_paths)} downloaded PDFs for step {step_nb}...")
    pdf_ocr_data_for_step = {}
    is_rubbish_report = False # Applies to the last PDF of pdf_paths
    case_images_directory = os.path.join(case_directory, 'images') # Define image dir path
    os.makedirs(case_images_directory, exist_ok=True) # Ensure image dir exists

    # Ensure 'images' and 'images_status' are initialized for the index
    # This should ideally happen once before the iterative loop starts
    if pd.isna(df.at[index, 'images']) or not isinstance(df.at[index, 'images'], dict):
        df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    if pd.isna(df.at[index, 'images_status']) or not isinstance(df.at[index, 'images_status'], dict):
        initialize_case_image_status(df, index) # Initialize status if not already done


    for pdf_file_path in pdf_paths:
        need_llm = True
        pdf_file = os.path.basename(pdf_file_path)
        step_directory_path = os.path.dirname(pdf_file_path) # Get the step dir from the path
        pdf_file_no_ext = os.path.splitext(pdf_file)[0]
        image_subfolder_path = os.path.join(step_directory_path, pdf_file_no_ext) # Subfolder for images from this PDF
        location_id = f"{step_nb}-{pdf_file_no_ext}" # Define location ID for IP Manager

        log_message(f"           - Processing PDF: {pdf_file}")

        try:
            pdf_document = fitz.open(pdf_file_path)
            if pdf_document.page_count == 0:
                log_message(f"           ! Skipping corrupted PDF (0 pages): {pdf_file}")
                continue

            os.makedirs(image_subfolder_path, exist_ok=True) # Ensure subfolder for extracted images exists
            
            # --- Rubbish Report Check ---
            try:
                log_message(f"           Performing preliminary rubbish check...")
                pdf_document_check = fitz.open(pdf_file_path)
                num_pages_to_check = min(4, pdf_document_check.page_count)
                if num_pages_to_check > 1:
                    # Use existing OCRProcessor, but only for first few pages
                    rubbish_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document_check, range(num_pages_to_check))
                    
                    # Do not include "civil procedure" as it is too generic and can be in a complaint
                    rubbish_keywords = [
                        "Security Imperative", "illicit trade of goods", "heavy recruitment of chinese",
                        "counterfeit and pirated goods", "office of strategy", "counterfeiting in the age of the internet",
                        "accreditation", "department of commerce", "white paper", "chamber of commerce",
                        "office of trade", "border protection", "hague convention",
                        "convention on the service", "seizure statistics", "notorious markets", " inta ",
                        "traffic report", "silk road", "state of the", "briefing papers"
                    ]
                    if any(keyword.lower() in rubbish_text.lower() for keyword in rubbish_keywords):
                        log_message(f"           🚫 Rubbish report detected in {pdf_file} based on first {num_pages_to_check} pages.")
                        is_rubbish_report = True
                        # Clean up image subfolder if it was created
                        image_subfolder_path_check = os.path.join(os.path.dirname(pdf_file_path), os.path.splitext(os.path.basename(pdf_file_path))[0])
                        if os.path.exists(image_subfolder_path_check):
                            shutil.rmtree(image_subfolder_path_check)
                        pdf_document_check.close()
                        break # Stop processing this step's PDFs

                        
                    # --- Check for Multi-Case Document ---
                    if 2 in pages_text_json:
                        top_of_page_2 = pages_text_json[2][:450]
                        case_numbers = re.findall(r"-cv-(\d+)", top_of_page_2)
                        if len(set(case_numbers)) > 1 or (len(set(case_numbers)) == 1 and case_numbers[0] != df.at[index, 'docket'].split("-cv-")[-1]):
                            log_message(f"           🔥Multi-case document detected. Skipping IP search for this PDF.")
                            shutil.rmtree(image_subfolder_path)
                            if pdf_file_path in pdf_ocr_data_for_step:
                                del pdf_ocr_data_for_step[pdf_file_path]
                            continue
                    
                    log_message(f"           Preliminary check passed for {pdf_file}.")
                else:
                    log_message(f"           Preliminary check not applicable for {pdf_file} (only 1 p.a.g.e.).")
                    pdf_document_check.close()
                    continue
                pdf_document_check.close()
            except Exception as rubbish_err:
                log_message(f"      ⚠️ Error during rubbish check for {pdf_file}: {rubbish_err}. Proceeding with caution.", level='WARNING')
                if 'pdf_document_check' in locals() and pdf_document_check and not pdf_document_check.is_closed:
                    pdf_document_check.close()
            # --- End Rubbish Report Check ---

            # --- Original Processing Starts Here ---
            try: # Re-open or use existing handle if check didn't open/close
                # Re-opening is safer in case the check had issues
                pdf_document = fitz.open(pdf_file_path)
                os.makedirs(image_subfolder_path, exist_ok=True) # Ensure subfolder for extracted images exists

                # --- Perform OCR ---
                log_message(f"           Performing OCR...")
                full_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document, range(pdf_document.page_count))
                pdf_ocr_data_for_step[pdf_file_path] = [page_ocr_data, pages_text_json, page_images]
                log_message(f"           OCR complete.")
            except Exception as e:
                log_message(f"           ❌ Error in OCR processing of PDF {pdf_file}: {e}\n{traceback.format_exc()}")
                if 'pdf_document' in locals() and pdf_document and not pdf_document.is_closed:
                    pdf_document.close()
                continue

            # --- Process for IP Exhibits ---
            is_trademark_exhibit = False

            # Trademark Check
            try: 
                if "principal register" in full_text.lower():
                    log_message(f"           💡 Trademark keyword found. Processing exhibit...")
                    # Update call to handle tuple return
                    is_trademark_exhibit, exhibit_reg_nos, files_to_delete = await process_trademark_exhibit(df, index, case_images_directory, step_directory_path, pdf_document, pdf_file, pages_text_json, page_ocr_data, page_images, ip_manager)
                    if is_trademark_exhibit:
                        log_message(f"      ✅ Found Trademark Exhibit in {pdf_file} (Reg/Ser Nos: {exhibit_reg_nos})")
                        ip_manager.record_finding('trademark', location_id, exhibit_reg_nos)
                        need_llm = False

            except Exception as e:
                log_message(f"      ❌ Error processing exhibit in {pdf_file}: {e}\n{traceback.format_exc()}")
                
            pdf_document.close() # Close the PDF document
        
        except Exception as e:
            log_message(f"      ❌ Error processing PDF {pdf_file}: {e}\n{traceback.format_exc()}")
            if 'pdf_document' in locals() and pdf_document and not pdf_document.is_closed:
                pdf_document.close()

    # After processing all PDFs in the list, return collected OCR data.
    # Sufficiency check is now handled by the calling function using ip_manager.
    log_message(f"            Finished processing PDFs for step {step_nb}. Rubbish detected: {is_rubbish_report}")

    return pdf_ocr_data_for_step, is_rubbish_report, files_to_delete


def create_resized_images(df: pd.DataFrame, index: Any, case_directory: str, case_images_directory: str) -> pd.DataFrame:
    """
    Creates resized versions of images found in the case_images_directory.
    Updates the DataFrame by removing entries for images that failed to resize.

    Args:
        df (pd.DataFrame): The main DataFrame.
        index: The index of the current case row in df.
        case_directory (str): Base directory for the case.
        case_images_directory (str): Directory containing the original images.

    Returns:
        pd.DataFrame: The modified DataFrame.
    """
    log_message(f"    Resizing images in {case_images_directory}...")
    image_files = [
        f for f in os.listdir(case_images_directory)
        if os.path.isfile(os.path.join(case_images_directory, f)) and f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx'))
    ]
    if image_files:
        log_message(f"      Found {len(image_files)} images to resize.")
        args = [(case_images_directory, filename, case_directory) for filename in image_files]
        try:
            pool = OCRProcessor.get_pool()
            if pool:
                results = pool.starmap(OCRProcessor.create_resized_image, args)
                failed_resizes = []
                for image_filename, success_resize in zip(image_files, results):
                    if not success_resize:
                        failed_resizes.append(image_filename)
                        # Safely remove from dicts
                        df.at[index, 'images'].get('trademarks', {}).pop(image_filename, None)
                        df.at[index, 'images'].get('patents', {}).pop(image_filename, None)
                        df.at[index, 'images'].get('copyrights', {}).pop(image_filename, None)
                if failed_resizes:
                    log_message(f"\033[91m      ! Failed to resize {len(failed_resizes)} images: {', '.join(failed_resizes)}\033[0m")
                else:
                    log_message(f"      Successfully resized {len(image_files) - len(failed_resizes)} images.")
            else:
                log_message("      Error: OCRProcessor pool not available for resizing images.")
        except Exception as resize_err:
            log_message(f"      Error during image resizing: {resize_err}")
    else:
        log_message("      No images found to resize.")

    return df

def initialize_case_image_status(df: pd.DataFrame, index: Any) -> None:
    df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    df.at[index, 'images_status'] = {
        'ip_manager_state': None,
        'steps_processed': [], 'number_of_pdfs': 0, 'trace_url': None,
        'copyright_status': {'exhibit': {'count': 0, 'steps': []}, 'byregno': {'ai_reg_nos': [], 'count': 0}, 'cn_website': {'cn_reg_nos': [], 'count': 0, 'sources': {}}, 'bygoogle': {'count': 0, 'search_term': ''}, 'manual': {'count': 0, 'comment': ''}},
        'trademark_status': {'exhibit': {'count': 0, 'steps': [], 'sources': {}}, 'byregno': {'ai_reg_nos': [], 'count': 0, 'steps': [], 'sources': {}}, 'cn_website': {'cn_reg_nos': [], 'count': 0, 'sources': {}}, 'byname': {'count': 0, 'search_term_used': '', 'sources': {}}, 'manual': {'count': 0, 'comment': ''}},
        'patent_status': {'exhibit': {'count': 0, 'steps': []}, 'byregno': {'ai_reg_nos': [], 'count': 0, 'steps': []}, 'cn_website': {'cn_reg_nos': [], 'count': 0}, 'byname': {'count': 0, 'search_term_used': '', 'ai_search_term': ''}, 'manual': {'count': 0, 'comment': ''}}
    }


if __name__ == "__main__":
    import asyncio
    print("Processing all cases with trademark exhibits...")
    
    async def main():
        try:
            df = get_table_from_GZ("tb_case", force_refresh=True)
            df = df[df["id"]>5648]
            
            # Count cases
            case_count = 0  # Counter for deleted files
            for index, row in df.iterrows():
                # Check if the trademark field in images column is not empty
                if (not pd.isna(row['images']) and isinstance(row['images'], dict) and 'trademarks' in row['images'] and row['images']['trademarks']):
                    
                    # Create case directory path
                    case_directory = os.path.join(local_case_folder, sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}"))
                    
                    # Check if case directory exists
                    if not os.path.exists(case_directory):
                        continue
                        
                    # Check if at least one file in the case_image_folder has the word "Exhibit" in the filename
                    case_images_directory = os.path.join(case_directory, 'images')
                    if os.path.exists(case_images_directory):
                        # Extract exhibit numbers from filenames using pattern "Exhibit_\d{1,2}"
                        exhibit_files = [f for f in os.listdir(case_images_directory) if 'Exhibit' in f]
                        if not exhibit_files:
                            continue
                        case_count = case_count + 1
            
            total_case_count = case_count
            case_count = 0

            # Process all cases that meet the criteria
            deleted_files_count = 0  # Counter for deleted files
            for index, row in df.iterrows():
                # Check if the trademark field in images column is not empty
                if (not pd.isna(row['images']) and isinstance(row['images'], dict) and 'trademarks' in row['images'] and row['images']['trademarks']):
                    
                    # Create case directory path
                    case_directory = os.path.join(local_case_folder, sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}"))
                    
                    # Check if case directory exists
                    if not os.path.exists(case_directory):
                        continue
                        
                    # Check if at least one file in the case_image_folder has the word "Exhibit" in the filename
                    case_images_directory = os.path.join(case_directory, 'images')
                    if os.path.exists(case_images_directory):
                        # Extract exhibit numbers from filenames using pattern "Exhibit_\d{1,2}"
                        exhibit_files = [f for f in os.listdir(case_images_directory) if 'Exhibit' in f]
                        if not exhibit_files:
                            continue
                        # Extract exhibit identifiers (e.g., "Exhibit_1", "Exhibit_23")
                        exhibit_identifiers = []
                        for exhibit_file in exhibit_files:
                            match = re.search(r"Exhibit_(\d{1,2})", exhibit_file)
                            if match:
                                exhibit_identifiers.append(f"Exhibit_{match.group(1)}")
                            pdf_name = exhibit_file.split("_page")[0]
                            exhibit_identifiers.append(pdf_name)
                    else:
                        continue
                    
                    log_message(f"--- Processing case {index} for docket {row['docket']} ---")
                    
                    # Initialize IPTrackingManager
                    ip_manager = IPTrackingManager(row.get('nos_description', ''), row.get('plaintiff_id', ''))

                    # Find PDF paths for steps
                    steps_data = []
                    for step_dir in os.listdir(case_directory):
                        step_path = os.path.join(case_directory, step_dir)
                        if os.path.isdir(step_path) and step_dir != 'images':
                            # Filter PDFs to only include those with exhibit identifiers in their filename
                            pdfs_in_step = [
                                os.path.join(step_path, f)
                                for f in os.listdir(step_path)
                                if f.lower().endswith('.pdf') and any(exhibit_id in f for exhibit_id in exhibit_identifiers)
                            ]
                            if pdfs_in_step:
                                steps_data.append((step_dir, pdfs_in_step))

                    if not steps_data:
                        log_message(f"No PDF steps found in {case_directory}.")
                        continue

                    log_message(f"Processing {len(steps_data)} steps found locally...")

                    # Process each step's PDFs
                    files_to_delete = []
                    
                    for step_nb, pdf_paths_for_step in steps_data:
                        log_message(f"\n--- Processing Step {step_nb} ---")
                        if not pdf_paths_for_step:
                            continue

                        # Process this step's PDFs
                        ocr_data_step, is_rubbish, step_files_to_delete = await process_attachement_pdfs(
                            pdf_paths=pdf_paths_for_step,
                            df=df,
                            index=index,
                            case_directory=case_directory,
                            step_nb=step_nb,
                            copyright_template1_hash=None,
                            copyright_template1_size=None,
                            certainty="",  # Added missing certainty parameter
                            ip_manager=ip_manager
                        )
                        
                        files_to_delete.extend(step_files_to_delete)


                    # Finalization (Always run to resize images etc.)
                    log_message("\n--- Running Finalization ---")
                    local_case_images_dir = os.path.join(case_directory, 'images')
                    os.makedirs(local_case_images_dir, exist_ok=True)
                    create_resized_images(df, index, case_directory, local_case_images_dir)
                    # for file in os.listdir(local_case_images_dir):
                    #     if file in files_to_delete:
                    #         os.remove(os.path.join(local_case_images_dir, file))
                    for file in files_to_delete:
                        if os.path.exists(os.path.join(local_case_images_dir, file)):
                            os.remove(os.path.join(local_case_images_dir, file))
                            deleted_files_count += 1
                        if os.path.exists(os.path.join(local_case_images_dir, "low", file)):
                            os.remove(os.path.join(local_case_images_dir, "low", file))
                            deleted_files_count += 1
                        if os.path.exists(os.path.join(local_case_images_dir, "high", file)):
                            os.remove(os.path.join(local_case_images_dir, "high", file))
                            deleted_files_count += 1

                    if files_to_delete:
                        log_message(f"\033[91mTotal files deleted so far: {deleted_files_count}\033[0m")

                    case_count = case_count + 1
                    log_message(f"\n\033[91m--- {case_count}/{total_case_count}: Finished processing case {index} for docket {row['docket']} ---\033[0m")

        except Exception as main_err:
            print(f"Error in example usage: {main_err}\n{traceback.format_exc()}")

    # Run the async main function
    asyncio.run(main())