import os
import json
import re
import base64
import zlib
from google.cloud import storage
from google.auth import credentials
from DatabaseManagement.ImportExport import get_table_from_GZ
from AI.GC_Credentials import get_gcs_credentials
import vertexai
from google import genai
from google.genai import types
from tqdm import tqdm
import time
import random
from FileManagement.GC_COS import upload_to_gcs

# Configuration
IMAGE_DIR = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/all_copyrights_noneardupe"
GCS_BUCKET_NAME = "trodata_bucket"
PROJECT_ID = os.environ.get("GOOGLE_CLOUD_PROJECT")
REGION = os.environ.get("GOOGLE_CLOUD_LOCATION")
OUTPUT_JSONL = "copyright_data.jsonl"
CREDENTIALS = get_gcs_credentials()
vertexai.init(project=PROJECT_ID, location=REGION, credentials=CREDENTIALS)
client = genai.Client(vertexai=True, project=PROJECT_ID, location=REGION, credentials=CREDENTIALS)


PROMPT = ("This is an intellectual property (either a trademark looking like a logo or brand name, or a copyright looking like a nice picture). "
        "Is it a trademark or a copyright? If it is a trademark, what is the name of the brand? What is the parent company of the brand? "
        "If it is a copyright, what is the name of the copyright owner (most likely the artist)? "
        'If you know the trademark registration number of the copyright registration number: add it, if not, just write "unknown". How confident are you? '
        'Return the answer in a json: {"type":"trademark", "brand":"Dior", "parent_company":"LVMH", "registration_number":"unknown", "confidence": 0.87} '
        'or {"type":"copyright", "copyright_owner":"Picasso", "registration_number":"VA 1-234-567", "confidence": 0.87}. '
        'If you do not know the brand or the copyright owner, return {"type":"unknown", "confidence": 0}')


def extract_docket_number(filename):
    """Extracts the docket number from the filename."""
    parts = filename.split("_")
    if "DIS" in filename:  #US_DIS_ILND_1_24cv1044_d104714618e5138_ 0_Exhibit_1_page9_0.webp
        docket_number = ":".join(parts[3:5]) # not including 5
        docket_number = docket_number.replace("cv", "-cv-")
        docket_number = docket_number.replace("CV", "-cv-")
        while len(docket_number) < 13:
            docket_number = docket_number.replace("-cv-", "-cv-0")
    else:  # IN_DC_1_24-cv-11628_2024-11-12_1_ 0_Exhibit_1_page3_0.webp
        docket_number = ":".join(parts[2:4]) # not including 4
    
    return docket_number

def get_plaintiff_info(filtered_df, plaintiff_df):
    """Gets plaintiff_id and plaintiff_name from the dataframe."""
    
    # Filter the dataframe to find rows where the 'docket' column contains the docket_number
    try: 
        plaintiff_id = filtered_df['plaintiff_id'].values[0]
        plaintiff_name = plaintiff_df[plaintiff_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
        return plaintiff_name
    except Exception as e:
        print(f"Error getting plaintiff info: {e}")
        return None
    
def extract_registration_number(filtered_df, filename):
    """
    Extracts the registration number from the dataframe based on docket number and filename.
    """

    # Check if any rows were found
    if not filtered_df.empty:
        # Iterate through the rows to find a match based on filename
        for index, row in filtered_df.iterrows():
            if 'images' in row and 'copyrights' in row['images']:
                for image_name, details in row['images']['copyrights'].items():
                    if filename in image_name:
                        return details['reg_no']
    
    return None

def extract_regno_and_plaintiff(filename, df, plaintiff_df):
    docket_number = extract_docket_number(filename)
    filtered_df = df[df['docket'] == docket_number]
    reg_no = extract_registration_number(filtered_df, filename)
    plaintiff_name = get_plaintiff_info(filtered_df, plaintiff_df)
    return plaintiff_name, reg_no
    

def create_training_data(df, plaintiff_df):
    """Generates training data in JSONL format."""
    gcs_client = storage.Client(credentials=CREDENTIALS)
    gcs_bucket = gcs_client.bucket(GCS_BUCKET_NAME)
    training_data = []
    existing_files = {blob.name for blob in gcs_bucket.list_blobs(prefix="copyright_training_images/")}
    for filename in tqdm(os.listdir(IMAGE_DIR), desc="Processing images"):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):
            image_path = os.path.join(IMAGE_DIR, filename)
            plaintiff_name, reg_no = extract_regno_and_plaintiff(filename, df, plaintiff_df)
            destination_blob_name = f"copyright_training_images/{filename}"

            # Skip upload if the file already exists
            if destination_blob_name in existing_files and plaintiff_name and reg_no:
                gcs_uri = f"gs://{GCS_BUCKET_NAME}/{destination_blob_name}"
            elif plaintiff_name and reg_no:
                gcs_uri = upload_to_gcs(image_path, gcs_bucket, destination_blob_name)
            else:
                continue

            training_example = {
                "contents": [
                    {
                        "role": "user",
                        "parts": [
                            {
                                "fileData": {
                                    "mimeType": "image/webp" if filename.lower().endswith('.webp') else "image/jpeg",
                                    "fileUri": gcs_uri,
                                }
                            },
                            {
                                "text": PROMPT
                            }
                        ]
                    },
                    {
                        "role": "model",
                        "parts": [
                            {
                                "text": json.dumps({"type":"copyright", "copyright_owner":plaintiff_name, "registration_number":reg_no, "confidence": 0.99})
                            }
                        ]
                    }
                ]
            }
            training_data.append(training_example)
    
    with open(OUTPUT_JSONL, "w") as f:
        for entry in training_data:
            json.dump(entry, f)
            f.write("\n")
    print(f"Training data saved to {OUTPUT_JSONL}")
    upload_to_gcs(OUTPUT_JSONL, gcs_bucket, "copyright_training_data.jsonl")

if __name__ == "__main__":
    df = get_table_from_GZ("tb_case")

    plaintiff_df = get_table_from_GZ("tb_plaintiff")
    create_training_data(df, plaintiff_df)
    # Upload the JSONL file to GCS:
    # gsutil cp copyright_data.jsonl gs://trodata-bucket/
