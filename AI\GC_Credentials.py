from google.oauth2 import service_account
from google.auth.transport.requests import Request
import os
import random
import threading
from google import genai

# Flash 2.0 EXP:
# Total free capacity from GCP: 10 RPM * 5 porject * 60 * 24 = 72,000 requests per day
# Total free capacity from Gemini API: 1000 requests per day * 7 keys = 7,000 requests per day
# Total = 79,000 requests per day

# Flash 2.0:
# Total free capacity from GCP: 0
# Total free capacity from Gemini API: 1500 requests per day * 25 keys = .... requests per day
# Total = .... requests per day

# Flash 2.5:
# Total free capacity from GCP: 0
# Total free capacity from Gemini API: 500 requests per day * 20 keys = 10,000 requests per day
# Total = 10,000 requests per day

# https://www.reddit.com/r/Bard/comments/1hvle3n/got_banned_for_abusing_the_api_key/
# You need to be smarter. I use cloudflare worker with 3 keys. It provides different IP on each request. Works pretty good as long as you dont push context over ~40-50k

credentials = {}  # Store credentials for multiple projects
project_ids = ["trodata", "trodata2", "trodata3", "trodata4", "trodata5"] # List your project IDs
# gemini_api_keys = ["GEMINI_API_KEY_JG", "GEMINI_API_KEY_SDC", "GEMINI_API_KEY_TRO", "GEMINI_API_KEY_TRO2", "GEMINI_API_KEY_TRO3", "GEMINI_API_KEY_TRO4", "GEMINI_API_KEY_TRO5"]
gemini_api_keys = ["GEMINI_API_KEY_JG", "GEMINI_API_KEY_SDC", "GEMINI_API_KEY_TRO_NO_BILLING1", "GEMINI_API_KEY_TRO_NO_BILLING2", "GEMINI_API_KEY_TRO_NO_BILLING3"]

lock = threading.Lock() # Use a lock for thread safety

def get_gcs_client_multi_project(current_project_index=None):
    """Initialize VertexAI with service account credentials, handling rotation and retries."""

    global project_ids

    if current_project_index is None:
        start_index = random.randint(0, len(project_ids) - 1)
    else:
        start_index = current_project_index % len(project_ids)

    with lock: # Acquire the lock
        for i in range(len(project_ids)):
            project_index_to_try = (start_index + i) % len(project_ids)
            project_id = project_ids[project_index_to_try]
            
            try:
                global credentials
                if project_id not in credentials:
                    print(f"Loading GCP credentials for project: {project_id}")
                    # Construct the key file path based on project_id.  VERY IMPORTANT!
                    key_file_path = os.path.join(".", "AI", "GoogleCouldServiceAccountKey", f"{project_id}-key.json") # Use f-string for clarity

                    # Try to load credentials from service account file
                    credentials[project_id] = service_account.Credentials.from_service_account_file(
                        key_file_path,
                        scopes=['https://www.googleapis.com/auth/cloud-platform']
                    )

                # Refresh token if necessary
                if not credentials[project_id].valid:
                    print(f"Refreshing GCP credentials for project: {project_id}")
                    credentials[project_id].refresh(Request())

                client = genai.Client(project=project_id, credentials=credentials[project_id])
                return client, project_index_to_try, len(project_ids)

            except Exception as e:
                print(f"Error loading GCP credentials for project {project_id}: {e}. Trying next project.")
        
        # If all projects fail
        print(f"\033[91mFailed to get GCS client for any project.\033[0m")
        return None, start_index, len(project_ids)


def get_gemini_api_key(current_key_index):
    global gemini_api_keys
    if current_key_index is None:
        current_key_index = random.randint(0, len(gemini_api_keys) - 1)
    if current_key_index >= len(gemini_api_keys):
        current_key_index = current_key_index % len(gemini_api_keys)

    return os.environ[gemini_api_keys[current_key_index]], current_key_index, len(gemini_api_keys)



def get_gcs_credentials():
    """Initialize VertexAI with service account credentials"""
    try:
        global credentials
        project_id = project_ids[0]
        if project_id not in credentials:
            print("Loading GCP credentials")
            # Try to load credentials from service account file
            credentials[project_id] = service_account.Credentials.from_service_account_file(
                './AI/GoogleCouldServiceAccountKey/trodata-key.json',
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

        # Refresh token if necessary
        if not credentials[project_id].valid:
            print("Refreshing GCP credentials")
            credentials[project_id].refresh(Request())

        return credentials[project_id]
    except Exception as e:
        print(f"Error loading GCP credentials: {e}")
        return None