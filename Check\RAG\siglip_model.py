# d:\Documents\Programing\TRO\USSideRefactoring\Check\RAG\siglip_model.py

import os
os.environ.setdefault("HF_HOME", os.path.join(os.getcwd(),"hf_cache"))
os.environ.setdefault("HF_HUB_CACHE", os.path.join(os.getcwd(),"hf_cache/hub"))
os.environ.setdefault("HF_XET_CACHE", os.path.join(os.getcwd(),"hf_cache/xet"))
os.environ.setdefault("TRANSFORMERS_CACHE", os.path.join(os.getcwd(),"hf_cache/transformers"))
os.environ.setdefault("HF_DATASETS_CACHE", os.path.join(os.getcwd(),"hf_cache/datasets"))
import logging
import torch
import numpy as np
from PIL import Image
from transformers import AutoModel, AutoProcessor
from transformers.image_utils import load_image # For image loading
import io # Added for handling image bytes
import cv2

logger = logging.getLogger(__name__)

# Requires on linux and windows: transformers==4.51.3


class SiglipModel:
    """
    Image and Text embedding model using Hugging Face transformers with a Siglip model.
    """
    def __init__(self, model_id: str, config: dict):
        """
        Initializes the SiglipModel.

        Args:
            model_id (str): The unique identifier for the model instance.
            config (dict): Configuration parameters. Expected keys:
                                - 'vector_size'
                                - 'model_name_or_path'
        """
        super().__init__()
        self.model_id = model_id
        self._vector_size = config.get('vector_size')
        self.model_name_or_path = config.get('model_name_or_path')

        if not self._vector_size:
            raise ValueError(f"Missing required parameter 'vector_size' for SiglipModel {model_id}")
        if not self.model_name_or_path:
            raise ValueError(f"Missing required parameter 'model_name_or_path' for SiglipModel {model_id}")

        self._vector_size = int(self._vector_size)
        self.model = None
        self.processor = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Initialized SiglipModel (ID: {self.model_id}) "
                    f"for Model: {self.model_name_or_path}, Vector Size: {self._vector_size}, Device: {self.device}")

    def load(self):
        """
        Loads the Siglip model and processor.
        """
        if self.model is not None and self.processor is not None:
            logger.info(f"Model {self.model_id} ({self.model_name_or_path}) is already loaded.")
            return

        try:
            logger.info(f"Loading Siglip model: {self.model_name_or_path} to device: {self.device}")
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            torch.set_float32_matmul_precision("high")
            self.model = AutoModel.from_pretrained(
                self.model_name_or_path, trust_remote_code=True, torch_dtype=torch.float16,
            ).eval().to(self.device)
            self.processor = AutoProcessor.from_pretrained(
                self.model_name_or_path, use_fast=True, trust_remote_code=True
            )
            logger.info(f"Successfully loaded model {self.model_id} ({self.model_name_or_path})")

        except Exception as e:
            logger.error(f"Error loading model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            self.model = None
            self.processor = None
            raise

    def unload(self):
        """
        Unloads the Siglip model and processor.
        """
        if self.model is not None or self.processor is not None:
            logger.info(f"Unloading model {self.model_id} ({self.model_name_or_path})...")
            self.model = None
            self.processor = None
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            logger.info(f"Model {self.model_id} unloaded.")
        else:
            logger.info(f"Model {self.model_id} is not loaded, nothing to unload.")

    @property
    def vector_size(self) -> int:
        if self._vector_size is None:
             raise ValueError(f"Vector size not set for model {self.model_id}")
        return self._vector_size

    def _preprocess_images(self, image_data_list: list): # Changed type hint to accept mixed types
        if self.processor is None:
             raise ValueError(f"Model {self.model_id} processor not initialized. Load the model first.")
        try:
            images = []
            for item in image_data_list:
                if isinstance(item, str): # Assume it's a path
                    images.append(load_image(item).convert("RGB"))
                elif isinstance(item, bytes): # Assume it's image bytes
                    # PIL decoding (we used PIL for encoding)
                    images.append(Image.open(io.BytesIO(item)).convert("RGB"))
                elif isinstance(item, Image.Image):
                    images.append(item.convert("RGB"))
                elif isinstance(item, np.ndarray):  # NumPy (H×W or H×W×C, usually from OpenCV)
                    arr = item
                    # ensure 3-channel RGB HWC
                    if arr.ndim == 2 or (arr.ndim == 3 and arr.shape[2] == 1):
                        arr = cv2.cvtColor(arr, cv2.COLOR_GRAY2RGB)
                    elif arr.ndim == 3 and arr.shape[2] == 3:
                        # assume BGR if it came from cv2; flip to RGB
                        arr = arr[..., ::-1]
                    elif arr.ndim == 3 and arr.shape[2] == 4:
                        arr = cv2.cvtColor(arr, cv2.COLOR_BGRA2RGB)
                    else:
                        raise ValueError(f"Unsupported ndarray shape {arr.shape}; need HxW or HxWx{1,3,4}.")
                    if arr.dtype != np.uint8:
                        arr = arr.astype(np.uint8, copy=False)
                    images.append(np.ascontiguousarray(arr))                    
                      
                else:
                    raise ValueError(f"Unsupported image data type: {type(item)}. Must be path (str), bytes, or PIL Image.")
            
            inputs = self.processor(images=images, return_tensors="pt", padding=True, truncation=True).to(self.device)
            
            for im in images:
                try: im.close()
                except: pass
            del images
            
            return inputs
        except FileNotFoundError as e:
             logger.error(f"Image file not found: {e.filename}")
             raise
        except Exception as e:
            logger.error(f"Unexpected error during image preprocessing: {e}", exc_info=True)
            raise

    def _preprocess_texts(self, texts: list[str]):
        if self.processor is None:
             raise ValueError(f"Model {self.model_id} processor not initialized. Load the model first.")
        try:
            inputs = self.processor(text=texts, return_tensors="pt", padding=True, truncation=True).to(self.device)
            return inputs
        except Exception as e:
            logger.error(f"Unexpected error during text preprocessing: {e}", exc_info=True)
            raise

    def compute_features(self, data_list: list[str], data_type: str = "image") -> np.ndarray:
        """
        Computes features (embeddings) for a list of images or texts.

        Args:
            data_list (list[str]): List of image paths or text strings.
            data_type (str): "image" or "text".

        Returns:
            np.ndarray: Array of embeddings.
        """
        if self.model is None:
            logger.error(f"Model {self.model_id} ({self.model_name_or_path}) is not loaded.")
            raise ValueError(f"Model {self.model_id} must be loaded before computing features.")

        try:
            if data_type == "image":
                inputs = self._preprocess_images(data_list)
                with torch.inference_mode():
                    if self.device.type == "cuda":
                        with torch.autocast(device_type="cuda", dtype=torch.float16):
                            features = self.model.get_image_features(**inputs)
                    else:
                        features = self.model.get_image_features(**inputs)
            elif data_type == "text":
                inputs = self._preprocess_texts(data_list)
                with torch.no_grad():
                    features = self.model.get_text_features(**inputs)
            else:
                raise ValueError(f"Unsupported data_type: {data_type}. Must be 'image' or 'text'.")

            del inputs
            features_np = features.cpu().numpy()
            del features 
            
            # Siglip models typically output normalized embeddings, so no explicit normalization here.
            # If batch size is 1, squeeze might remove the batch dimension.
            # We want to ensure a 2D array [n_samples, n_features].
            # features_np = np.squeeze(features_np) # Avoid squeeze if it makes it 1D for single input
            if features_np.ndim == 1 and len(data_list) == 1:
                 features_np = np.expand_dims(features_np, axis=0)


            if features_np.ndim > 1 and features_np.shape[1] != self.vector_size:
                 logger.warning(f"Model {self.model_id} ({self.model_name_or_path}) produced vector of size {features_np.shape[1]}, "
                                 f"but expected {self.vector_size}. Check model/config.")
            return features_np
        except Exception as e:
            logger.error(f"Error computing features with model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            raise
        

if __name__ == "__main__":
    # Production
    test_picture1 = r"D:\Documents\Programing\TRO\ModelTestsWorkbenchData\pictures\copyright\product\Screenshot_2025-07-01_234711.png"
    test_picture2 = r"D:\Documents\Programing\TRO\Documents\IP-TRO\copyrights\Production\1_24-cv-13065_IN_DC_1_24-cv-13065_2024-12-19_2_ 0_Exhibit_Exh_1_Copyright_Vau_1_380_912_page3_0.webp"

    siglip_model = SiglipModel(model_id="siglip2_large_patch16_512", config={"vector_size": 1024, "model_name_or_path": "google/siglip2-large-patch16-512"})
    siglip_model.load()
    test_pictures_vector = siglip_model.compute_features(data_list=[test_picture1, test_picture2], data_type="image") 
