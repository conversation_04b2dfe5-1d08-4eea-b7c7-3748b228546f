name = "gena<PERSON><PERSON><PERSON>"  # Replace with your desired Worker name
main = "worker.py"  # Specifies the entry point (your Python file)
compatibility_date = "2025-03-25"  # Use a date that supports Python Workers
compatibility_flags = ["python_workers"]

account_id = "c075c432122df9bcff22ddef39df0170"

#[build] #Added build context
#  command = "pip install -r requirements.txt -t ./build && cp worker.py ./build"  # Install dependencies
#  watch_dir = "./"  # Watch this directory for changes

#[build.upload]
#  format = "modules" # Required for python.
#  main = "./build/worker.py"


[vars]
#  No environment variables needed for the proxy

[build]
command = "" # No build command needed for plain JS files
watch_dir = ""

[observability.logs]
enabled = true