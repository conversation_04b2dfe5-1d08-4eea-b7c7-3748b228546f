import os
from qdrant_client import QdrantClient
from IP.Trademarks_Bulk.trademark_db import get_db_connection

QDRANT_API_URL = os.getenv("QDRANT_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", None) # Add your API key if Qdrant is secured
QDRANT_COLLECTION_IP_ASSETS = "IP_Assets"  # Unified collection for all IP types
QDRANT_COLLECTION_PRODUCT_IMAGES = "Product_Images"  # Collection for product images

qdrant_client = QdrantClient(url=QDRANT_API_URL, api_key=QDRANT_API_KEY)

def cleanup_ip_assets_patents():
    """
    Cleans up the IP_Assets collection by removing points that match IDs in the Patents table.
    """
    try:
        # Get database connection
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Fetch all patent IDs from the Patents table
        cursor.execute("SELECT id FROM Patents")
        patent_ids = [str(row[0]) for row in cursor.fetchall()]
        
        if not patent_ids:
            print("No patent IDs found in the database")
            return
        
        # Delete points from Qdrant collection
        deleted = qdrant_client.delete(
            collection_name=QDRANT_COLLECTION_IP_ASSETS,
            points_selector=patent_ids
        )
        
        print(f"Successfully deleted {len(patent_ids)} points from {QDRANT_COLLECTION_IP_ASSETS}")
        return deleted
        
    except Exception as e:
        print(f"Error during IP_Assets cleanup: {str(e)}")
        raise
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    cleanup_ip_assets_patents()
