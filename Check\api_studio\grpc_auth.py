import os
import grpc
from functools import wraps

def validate_bearer_token(func):
    @wraps(func)
    def wrapper(self, request, context):
        metadata = dict(context.invocation_metadata())
        expected_token = os.getenv("API_BEARER_TOKEN")
        
        authorization = metadata.get('authorization')
        
        if not authorization:
            context.abort(grpc.StatusCode.UNAUTHENTICATED, "Missing authorization metadata")
            return None
            
        try:
            auth_type, token = authorization.split()
            if auth_type.lower() != 'bearer' or token != expected_token:
                context.abort(grpc.StatusCode.UNAUTHENTICATED, "Invalid bearer token")
                return None
        except ValueError:
            context.abort(grpc.StatusCode.UNAUTHENTICATED, "Invalid authorization header format")
            return None

        return func(self, request, context)
    return wrapper