{% extends 'layout_ui.html' %}

{% block title %}Plaintiffs Management{% endblock %}

{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='visualizer/visualizer.css') }}"> {# Assuming common styles are needed #}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='plaintiffs/plaintiffs.css') }}">
{% endblock %}

{% block content %}
    <div class="app-container">
        <div class="filters-container">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="search_term">Search:</label>
                    <input type="text" id="search_term" placeholder="Enter plaintiff name">
                </div>
                <div class="filter-group">
                    <label for="sort_by">Sort By:</label>
                    <select id="sort_by">
                        <option value="id">Plaintiff ID</option>
                        <option value="plaintiff_name">Plaintiff Name</option>
                        <option value="total_cases">Number of Cases</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sort_order">Order:</label>
                    <select id="sort_order">
                        <option value="asc">Ascending</option>
                        <option value="desc">Descending</option>
                    </select>
                </div>
            </div>

            <div class="actions-row">
                <div class="results-info">
                    <span id="total-results">0 plaintiffs</span>
                    (<span id="last-refresh">Last refresh: Never</span>)
                </div>
                <div class="action-buttons">
                    <button id="apply-filters" class="btn primary">Apply</button>
                    <button id="refresh-data" class="btn secondary">Refresh</button>
                </div>
            </div>
        </div>

        <div class="filters-container">
            <h3>Cleanup Actions</h3>
            <div class="cleanup-actions">
                <button id="delete-without-cases" class="btn secondary">Delete Plaintiffs Without Cases</button>
                <button id="fix-auto" class="btn secondary">Auto Fix Duplicate Names</button>
                <button id="fix-manual" class="btn secondary">Manual Fix Duplicate Names</button>
            </div>
            <div id="action-result" class="action-result"></div>
        </div>

        <div class="table-container">
            <div class="loading">Loading plaintiffs...</div>
            <table id="plaintiffs-table" class="common-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th class="col-plaintiff-name">Plaintiff Name</th>
                        <th class="col-overview">Overview (English)</th>
                        <th class="col-chinese-overview">Chinese (5)</th>
                        <th class="col-cases-count">Cases Count (with NOS / without NOS)</th>
                        <th class="col-actions">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Plaintiffs will be populated here dynamically -->
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script src="{{ url_for('static', filename='plaintiffs/plaintiffs.js') }}"></script>
    <script>
        // Add active class to menu item
        const menuItem = document.getElementById('menu-plaintiffs');
        if (menuItem) {
            menuItem.classList.add('active');
        }
    </script>
{% endblock %}