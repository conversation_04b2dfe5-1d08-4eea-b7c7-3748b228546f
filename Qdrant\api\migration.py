import os
from qdrant_client import QdrantClient, models

# --- Configuration ---
QDRANT_URL = os.getenv("QDRANT_URL", "https://vectorstore1.maidalv.com:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

OLD_COLLECTION_NAME = "IP_Assets"
NEW_COLLECTION_NAME = "IP_Assets_Optimized"

# --- <PERSON>ript ---

def create_optimized_collection(client: QdrantClient):
    """
    Creates the new, optimized collection with HNSW indexing, quantization,
    and rescoring enabled.
    """
    print(f"Creating new collection: {NEW_COLLECTION_NAME}")
    
    client.recreate_collection(
        collection_name=NEW_COLLECTION_NAME,
        on_disk_payload=True,
        vectors_config={
            "siglip_vector": models.VectorParams(
                size=1024,
                distance=models.Distance.COSINE,
                on_disk=True,                     # Store original vectors on disk for rescoring
                quantization_config=models.ScalarQuantization(
                    scalar=models.ScalarQuantizationConfig(
                        type=models.ScalarType.INT8,
                        always_ram=True          # Keep quantized vectors in RAM for speed
                        # optional: quantile=0.99
                    )
                ),
            )
        },
        hnsw_config=models.HnswConfigDiff(
            m=32,
            ef_construct=200,
            full_scan_threshold=10_000,
            on_disk=False                       # Keep the HNSW index in RAM
        )
    )
    
    
    # Create a payload index on the 'ip_type' field
    client.create_payload_index(
        collection_name=NEW_COLLECTION_NAME,
        field_name="ip_type",
        field_schema=models.PayloadSchemaType.KEYWORD,
    )
    print("Collection created and payload index on 'ip_type' is set.")


def migrate_data(client: QdrantClient, ip_types: list[str] | None = None):
    """
    Migrates data from the old collection to the new one with filtering and efficiency.
    - Fetches only IDs from the source collection first.
    - Filters by `ip_type` if provided.
    - Retrieves full point data only for the points that need to be migrated.
    - Skips points that already exist in the new collection.
    """
    print("Starting data migration...")

    # 1. Build the filter to be used for both collections.
    scroll_filter = None
    if ip_types:
        print(f"Filtering for ip_types: {ip_types}")
        scroll_filter = models.Filter(
            should=[
                models.FieldCondition(
                    key="ip_type",
                    match=models.MatchValue(value=ip_type)
                ) for ip_type in ip_types
            ]
        )

    # 2. Fetch existing point IDs from the NEW collection (with filtering).
    print(f"Fetching existing point IDs from '{NEW_COLLECTION_NAME}' to resume migration...")
    existing_ids = set()
    next_id_offset = None
    try:
        while True:
            points, next_id_offset = client.scroll(
                collection_name=NEW_COLLECTION_NAME,
                scroll_filter=scroll_filter,
                limit=10_000, # Use a larger batch size for fetching IDs
                offset=next_id_offset,
                with_payload=False,  # We only need the IDs
                with_vectors=False,
            )
            if not points:
                # Break if the scroll returns no points, as a safeguard.
                break
            for point in points:
                existing_ids.add(point.id)
            if next_id_offset is None:
                # Break if Qdrant indicates there are no more pages.
                break
            print(f"Fetched {len(existing_ids)} existing IDs from target collection...", end="\r")
    except Exception as e:
        print(f"\nCould not fetch existing points (collection might not exist yet): {e}")

    if existing_ids:
        print(f"\nFound {len(existing_ids)} existing points in '{NEW_COLLECTION_NAME}' for the selected types. Will skip them.")
    else:
        print(f"\nCollection '{NEW_COLLECTION_NAME}' has no points for the selected types. Starting a fresh migration.")

    # 3. Scroll through the OLD collection to get only the IDs of points to migrate.
    print("Fetching point IDs from source collection...")
    source_ids_to_migrate = []
    next_page_offset = None
    while True:
        points, next_page_offset = client.scroll(
            collection_name=OLD_COLLECTION_NAME,
            scroll_filter=scroll_filter,
            limit=10_000,
            offset=next_page_offset,
            with_payload=False,
            with_vectors=False,
        )
        if not points:
            break
        
        for point in points:
            if point.id not in existing_ids:
                source_ids_to_migrate.append(point.id)
        
        if next_page_offset is None:
            break
            
    if not source_ids_to_migrate:
        print("\nNo new points to migrate for the selected types. Migration complete.")
        return

    print(f"\nFound {len(source_ids_to_migrate)} new points to migrate.")

    # 4. Retrieve full data for the filtered points and upsert them in batches.
    total_migrated_this_run = 0
    batch_size = 1000  # Batch size for retrieving and upserting
    
    for i in range(0, len(source_ids_to_migrate), batch_size):
        batch_ids = source_ids_to_migrate[i:i + batch_size]
        
        # Retrieve the full point data for the batch
        points_to_upsert_full = client.retrieve(
            collection_name=OLD_COLLECTION_NAME,
            ids=batch_ids,
            with_payload=True,
            with_vectors=True
        )

        points_to_upsert_struct = []
        for point in points_to_upsert_full:
            vector = point.vector
            if isinstance(vector, dict) and 'siglip_vector' in vector:
                vector_data = vector['siglip_vector']
            else:
                print(f"\nWarning: Unexpected vector format for point {point.id}. Skipping.")
                continue
            
            points_to_upsert_struct.append(
                models.PointStruct(
                    id=point.id,
                    vector={"siglip_vector": vector_data},
                    payload=point.payload
                )
            )

        if points_to_upsert_struct:
            client.upsert(
                collection_name=NEW_COLLECTION_NAME,
                points=points_to_upsert_struct,
                wait=True
            )
            total_migrated_this_run += len(points_to_upsert_struct)
        
        print(f"Migrating... Progress: {total_migrated_this_run}/{len(source_ids_to_migrate)}", end="\r")

    print(f"\nMigration complete. Total new points migrated: {total_migrated_this_run}")


def get_ip_type_selection():
    """Prompts the user to select IP types for migration."""
    print("\nPlease select the ip_type to migrate:")
    print("  1: Trademark")
    print("  2: Copyright")
    print("  3: Patent")
    print("  (Enter numbers separated by commas, e.g., '1,3', or leave blank for all)")

    ip_type_map = {"1": "Trademark", "2": "Copyright", "3": "Patent"}
    
    while True:
        choice = input("> ").strip()
        if not choice:
            return None  # User wants to migrate all types

        selected_numbers = [c.strip() for c in choice.split(',')]
        
        if all(c in ip_type_map for c in selected_numbers):
            return [ip_type_map[c] for c in selected_numbers]
        else:
            print("Invalid input. Please enter only the numbers 1, 2, or 3, separated by commas.")


if __name__ == "__main__":
    qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
    
    print("--- Qdrant Data Migration Script ---")
    print(f"This script will migrate data from '{OLD_COLLECTION_NAME}' to '{NEW_COLLECTION_NAME}'.")

    # Get user input for IP types
    selected_ip_types = get_ip_type_selection()

    # Step 1: Create the new collection if it doesn't exist
    try:
        collection_info = qdrant_client.get_collection(collection_name=NEW_COLLECTION_NAME)
        print(f"\nCollection '{NEW_COLLECTION_NAME}' already exists. Skipping creation.")
    except Exception:
        create_optimized_collection(qdrant_client)

    # Step 2: Migrate the data with the selected filter
    migrate_data(qdrant_client, ip_types=selected_ip_types)

    print("\n--- Migration Process Finished ---")
    print("Please update your application to use the new collection name:")
    print(f"'{NEW_COLLECTION_NAME}'")
