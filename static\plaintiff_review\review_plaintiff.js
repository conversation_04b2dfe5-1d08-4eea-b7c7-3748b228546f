document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const reviewTable = document.getElementById('review-table');
    const reviewTableBody = document.getElementById('review-table-body');
    const totalResults = document.getElementById('total-results');
    const refreshButton = document.getElementById('refresh-data');
    const add9AndLen2Button = document.getElementById('add-9-and-len2');
    const deleteWithoutCasesButton = document.getElementById('delete-plaintiff-without-cases');
    const deleteOverlay = document.getElementById('delete-overlay');
    const plaintiffsToDeleteList = document.getElementById('plaintiffs-to-delete-list');
    const cancelDeleteButton = document.getElementById('cancel-delete');
    const confirmDeleteButton = document.getElementById('confirm-delete');

    // Review data
    let reviewData = [];
    let plaintiffsToDelete = [];

    // Event listeners
    refreshButton.addEventListener('click', () => loadReviewData(true));
    add9AndLen2Button.addEventListener('click', runImprove9AndLen2);
    deleteWithoutCasesButton.addEventListener('click', showPlaintiffsToDelete);
    cancelDeleteButton.addEventListener('click', () => deleteOverlay.style.display = 'none');
    confirmDeleteButton.addEventListener('click', confirmDeletePlaintiffs);

    // Load data on page load
    loadReviewData();

    function loadReviewData(forceRefresh = false) {
        document.querySelector('.loading').style.display = 'block';
        reviewTable.style.display = 'none';

        fetch(`/api/plaintiff/reviews?force_refresh=${forceRefresh}`)
            .then(response => response.json())
            .then(data => {
                reviewData = data.reviews || [];
                totalResults.textContent = `${reviewData.length} reviews`;

                renderData();
                document.querySelector('.loading').style.display = 'none';
                reviewTable.style.display = 'table';
            })
            .catch(error => {
                console.error('Error loading review data:', error);
                document.querySelector('.loading').textContent = 'Error loading data. Please try again.';
            });
    }

    function renderData() {
        // Clear table
        reviewTableBody.innerHTML = '';

        // Render all rows
        reviewData.forEach(review => {
            const row = document.createElement('tr');
            row.dataset.caseId = review.case_id;

            // Format the plaintiff_names from JSON if needed
            let plaintiffNames = review.plaintiff_names;
            if (plaintiffNames && typeof plaintiffNames === 'string') {
                try {
                    const names = JSON.parse(plaintiffNames);
                    plaintiffNames = Array.isArray(names) ? names.join(', ') : plaintiffNames;
                } catch (e) {
                    // Keep as is if not valid JSON
                }
            }

            // Format date
            const formattedDate = review.date_filed ? new Date(review.date_filed).toLocaleDateString() : '';
            const formattedUpdateTime = review.update_time ? new Date(review.update_time).toLocaleString() : '';

            row.innerHTML = `
                <td>${review.case_id}</td>
                <td>${formattedDate}</td>
                <td>${review.docket || ''}</td>
                <td>${review.current_plaintiff_name || ''}</td>
                <td class="plaintiff-names-cell truncate">${plaintiffNames || ''}</td>
                <td class="proposed-name-cell">${review.proposed_name || ''}</td>
                <td class="method-info-cell truncate">${review.method_info || ''}</td>
                <td>${formattedUpdateTime}</td>
                <td class="action-cell">
                    <button class="btn primary approve-btn" data-case-id="${review.case_id}">Approve</button>
                    <button class="btn secondary reject-btn" data-case-id="${review.case_id}">Reject</button>
                </td>
            `;

            reviewTableBody.appendChild(row);
        });

        // Add event listeners to the new buttons
        document.querySelectorAll('.approve-btn').forEach(btn => {
            btn.addEventListener('click', handleApproveClick);
        });

        document.querySelectorAll('.reject-btn').forEach(btn => {
            btn.addEventListener('click', handleRejectClick);
        });
    }

    function handleApproveClick(event) {
        const caseId = event.target.dataset.caseId;
        showCaseSelectionModal(caseId);
    }

    function handleRejectClick(event) {
        const caseId = event.target.dataset.caseId;

        // Directly submit rejection
        const decisions = [{
            case_id: caseId,
            action: 'reject'
        }];

        submitDecisions(decisions);
    }

    function showCaseSelectionModal(caseId) {
        // Fetch cases for this plaintiff
        fetch(`/api/plaintiff/cases/${caseId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    createCaseSelectionModal(caseId, data.cases, data.proposed_name);
                } else {
                    alert('Error loading cases: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error loading cases:', error);
                alert('Error loading cases. Please try again.');
            });
    }

    function createCaseSelectionModal(reviewCaseId, cases, proposedName) {
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.className = 'overlay case-selection-overlay';
        overlay.style.display = 'flex';

        // Create modal content
        const content = document.createElement('div');
        content.className = 'overlay-content';
        content.style.maxWidth = '800px';
        content.style.maxHeight = '80vh';

        // Modal header
        const header = document.createElement('div');
        header.className = 'overlay-header';

        const title = document.createElement('div');
        title.className = 'overlay-title';
        title.textContent = `Approve Plaintiff Name: "${proposedName}"`;

        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-overlay';
        closeBtn.innerHTML = '&times;';
        closeBtn.addEventListener('click', () => document.body.removeChild(overlay));

        header.appendChild(title);
        header.appendChild(closeBtn);

        // Modal body
        const body = document.createElement('div');
        body.className = 'overlay-body';
        body.style.overflowY = 'auto';

        const instruction = document.createElement('p');
        instruction.textContent = 'Select which cases to reprocess with the new plaintiff name:';
        instruction.style.marginBottom = '15px';
        body.appendChild(instruction);

        // Cases table
        const table = document.createElement('table');
        table.className = 'common-table';
        table.style.fontSize = '14px';

        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th style="width: 50px;">
                    <input type="checkbox" id="select-all-cases" />
                </th>
                <th>Case ID</th>
                <th>Filed Date</th>
                <th>Docket</th>
                <th>Plaintiff Names</th>
                <th>Files</th>
            </tr>
        `;
        table.appendChild(thead);

        const tbody = document.createElement('tbody');

        cases.forEach(caseData => {
            const row = document.createElement('tr');

            // Format plaintiff names
            let plaintiffNames = caseData.plaintiff_names;
            if (plaintiffNames && typeof plaintiffNames === 'string') {
                try {
                    const names = JSON.parse(plaintiffNames);
                    plaintiffNames = Array.isArray(names) ? names.join(', ') : plaintiffNames;
                } catch (e) {
                    // Keep as is if not valid JSON
                }
            }

            // Format date
            const formattedDate = caseData.date_filed ? new Date(caseData.date_filed).toLocaleDateString() : '';

            // Create file link using the same logic as visualizer.js
            const fileLink = createFileLink(caseData.date_filed, caseData.docket);

            row.innerHTML = `
                <td>
                    <input type="checkbox" class="case-checkbox" value="${caseData.id}" />
                </td>
                <td>${caseData.id}</td>
                <td>${formattedDate}</td>
                <td>${caseData.docket || ''}</td>
                <td class="truncate" style="max-width: 200px;">${plaintiffNames || ''}</td>
                <td>${fileLink}</td>
            `;

            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        body.appendChild(table);

        // Modal footer
        const footer = document.createElement('div');
        footer.className = 'overlay-footer';

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'btn secondary';
        cancelBtn.textContent = 'Cancel';
        cancelBtn.addEventListener('click', () => document.body.removeChild(overlay));

        const approveBtn = document.createElement('button');
        approveBtn.className = 'btn primary';
        approveBtn.textContent = 'Approve & Reprocess Selected';
        approveBtn.addEventListener('click', () => {
            const selectedCaseIds = Array.from(document.querySelectorAll('.case-checkbox:checked'))
                .map(cb => parseInt(cb.value));

            if (selectedCaseIds.length === 0) {
                alert('Please select at least one case to reprocess.');
                return;
            }

            // Submit approval with selected case IDs
            const decisions = [{
                case_id: reviewCaseId,
                action: 'approve',
                selected_case_ids: selectedCaseIds
            }];

            document.body.removeChild(overlay);
            submitDecisions(decisions);
        });

        footer.appendChild(cancelBtn);
        footer.appendChild(approveBtn);

        // Assemble modal
        content.appendChild(header);
        content.appendChild(body);
        content.appendChild(footer);
        overlay.appendChild(content);
        document.body.appendChild(overlay);

        // Add select all functionality
        const selectAllCheckbox = document.getElementById('select-all-cases');
        const caseCheckboxes = document.querySelectorAll('.case-checkbox');

        selectAllCheckbox.addEventListener('change', function() {
            caseCheckboxes.forEach(cb => cb.checked = this.checked);
        });

        // Update select all when individual checkboxes change
        caseCheckboxes.forEach(cb => {
            cb.addEventListener('change', function() {
                const allChecked = Array.from(caseCheckboxes).every(checkbox => checkbox.checked);
                const noneChecked = Array.from(caseCheckboxes).every(checkbox => !checkbox.checked);

                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
            });
        });
    }

    function createFileLink(dateField, docket) {
        if (!dateField || !docket) {
            return '<span class="disabled">No link</span>';
        }

        try {
            // Format date for path (same logic as visualizer.js)
            const date = new Date(dateField);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDateForPath = `${year}-${month}-${day}`;

            const formattedDocket = docket.replace(/:/g, '_');
            const filePath = `/Maidalv/IP TRO Data/Case Files/${formattedDateForPath} - ${formattedDocket}/`;

            // URL encoding (same as visualizer.js)
            const singleEncodedPath = encodeURIComponent(filePath);
            const paramValue = `openfile=${singleEncodedPath}`;
            const fullyEncodedParam = encodeURIComponent(paramValue);
            const fileUrl = `https://synology.jslawlegal.com/index.cgi?launchApp=SYNO.SDS.App.FileStation3.Instance&launchParam=${fullyEncodedParam}`;

            return `<a href="${fileUrl}" target="_blank" class="action-link">Files</a>`;
        } catch (e) {
            return '<span class="disabled">Invalid date</span>';
        }
    }

    function submitDecisions(decisions) {
        // Send decisions to server
        fetch('/api/plaintiff/reviews/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ decisions })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully processed ${data.processed_count} reviews.`);
                loadReviewData(); // Refresh the data
            } else {
                alert('Error processing reviews: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error submitting reviews:', error);
            alert('Error submitting reviews. Please try again.');
        });
    }

    
    function runImprove9AndLen2() {
        // Show loading state
        add9AndLen2Button.disabled = true;
        add9AndLen2Button.textContent = 'Processing...';

        // Call the API to run the improve_plaintiff_9_and_2_letter_names function
        fetch('/api/plaintiff/improve-9-and-len2', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully processed ${data.processed_count} cases. Added to review queue.`);
                loadReviewData(); // Refresh the data to show new reviews
            } else {
                alert('Error processing cases: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error running improve function:', error);
            alert('Error processing cases. Please try again.');
        })
        .finally(() => {
            // Reset button state
            add9AndLen2Button.disabled = false;
            add9AndLen2Button.textContent = 'Add #9 and len=2';
        });
    }

    function showPlaintiffsToDelete() {
        // Show loading state
        deleteWithoutCasesButton.disabled = true;
        deleteWithoutCasesButton.textContent = 'Loading...';

        // Get the list of plaintiffs without cases
        fetch('/api/plaintiff/without-cases')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                plaintiffsToDelete = data.plaintiffs || [];

                if (plaintiffsToDelete.length === 0) {
                    alert('No plaintiffs without cases found.');
                    return;
                }

                // Populate the overlay list
                plaintiffsToDeleteList.innerHTML = '';
                const listHTML = document.createElement('ul');

                plaintiffsToDelete.forEach(plaintiff => {
                    const item = document.createElement('li');
                    item.textContent = `ID: ${plaintiff.id} - ${plaintiff.plaintiff_name}`;
                    listHTML.appendChild(item);
                });

                plaintiffsToDeleteList.appendChild(listHTML);

                // Show the overlay
                deleteOverlay.style.display = 'flex';
            } else {
                alert('Error getting plaintiffs: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error getting plaintiffs without cases:', error);
            alert('Error getting plaintiffs. Please try again.');
        })
        .finally(() => {
            // Reset button state
            deleteWithoutCasesButton.disabled = false;
            deleteWithoutCasesButton.textContent = 'Delete plaintiff without cases';
        });
    }

    function confirmDeletePlaintiffs() {
        // Show loading state
        confirmDeleteButton.disabled = true;
        confirmDeleteButton.textContent = 'Deleting...';

        // Get the IDs of plaintiffs to delete
        const plaintiffIds = plaintiffsToDelete.map(p => p.id);

        // Call the API to delete the plaintiffs
        fetch('/api/plaintiff/delete-without-cases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ plaintiff_ids: plaintiffIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully deleted ${data.deleted_count} plaintiffs.`);
                deleteOverlay.style.display = 'none';
            } else {
                alert('Error deleting plaintiffs: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error deleting plaintiffs:', error);
            alert('Error deleting plaintiffs. Please try again.');
        })
        .finally(() => {
            // Reset button state
            confirmDeleteButton.disabled = false;
            confirmDeleteButton.textContent = 'Confirm Delete';
        });
    }
});
// Menu activation is handled by common.js activateMenuItem('menu-review-plaintiff')