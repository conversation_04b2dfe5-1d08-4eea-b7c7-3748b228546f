from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse
import os
from Check.api_studio.kong_util import get_consumer
from Qdrant.api.utils.db import get_db_connection as get_pg_connection
from DatabaseManagement.ImportExport import execute_gz_query, get_table_from_GZ
import psycopg2.extras
import json
from pydantic import BaseModel
from datetime import datetime

class JsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super(JsonEncoder, self).default(obj)
    
# Create APIRouter
history_router = APIRouter()



@history_router.post('/get_check_dates')
async def get_check_dates(http_request: Request):
    consumer_data = get_consumer(http_request)
    if not consumer_data:
        raise HTTPException(status_code=401, detail="Invalid API Key")

    api_key = consumer_data["api_key"]
    admin_api_key = os.getenv("ADMIN_API_KEY")
    is_admin = admin_api_key and api_key == admin_api_key

    connection = None
    try:
        connection = get_pg_connection()
        cursor = connection.cursor()
        
        if is_admin:
            # 1. Get dates from the primary PG database
            query_pg = """
                SELECT DISTINCT DATE(create_time)
                FROM check_api_requests
            """
            cursor.execute(query_pg)
            dates_pg = {row[0].strftime('%Y-%m-%d') for row in cursor.fetchall()}

            # 2. Get dates from the secondary GZ (MySQL) database
            try:
                query_gz = "SELECT DISTINCT DATE(create_time) FROM tb_case_check"
                gz_results = execute_gz_query(query_gz)
                dates_gz = {row[0].strftime('%Y-%m-%d') for row in gz_results if row[0]}
            except Exception as e:
                print(f"⚠️ Could not fetch dates from GZ database: {e}")
                dates_gz = set()

            # 3. Merge, sort, and return
            all_dates = sorted(list(dates_pg.union(dates_gz)), reverse=True)
            return JSONResponse(content={"dates": all_dates})

        else:
            query = """
                SELECT DISTINCT DATE(create_time)
                FROM check_api_requests
                WHERE user_name = %s
                ORDER BY DATE(create_time) DESC
            """
            cursor.execute(query, (consumer_data["username"],))
            dates = [row[0].strftime('%Y-%m-%d') for row in cursor.fetchall()]
            return JSONResponse(content={"dates": dates})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if connection:
            cursor.close()
            connection.close()

@history_router.post('/get_checks_for_date')
async def get_checks_for_date(http_request: Request):
    consumer_data = get_consumer(http_request)
    if not consumer_data:
        raise HTTPException(status_code=401, detail="Invalid API Key")

    api_key = consumer_data["api_key"]
    client_name = consumer_data["username"]
    
    try:
        body = await http_request.json()
        date = body.get("date")
        if not date:
            raise HTTPException(status_code=400, detail="Date is required")
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid JSON body")
    admin_api_key = os.getenv("ADMIN_API_KEY")
    is_admin = admin_api_key and api_key == admin_api_key

    connection = None
    try:
        connection = get_pg_connection()
        cursor = connection.cursor()
        
        if is_admin:
            # 1. Get checks from the primary PG database
            query_pg = """
                SELECT id, create_time, api_key
                FROM check_api_requests
                WHERE DATE(create_time) = %s
                ORDER BY create_time DESC
            """
            cursor.execute(query_pg, (date,))
            checks_from_db = cursor.fetchall()
            
            checks = []
            for row in checks_from_db:
                check_id, timestamp, api_key_from_db = row
                client_name = "Admin" if is_admin else client_name
                checks.append({
                    "check_id": str(check_id),
                    "timestamp": timestamp.isoformat(),
                    "client_name": client_name
                })

            # 2. Get checks from the secondary GZ (MySQL) database
            try:
                where_clause = f"DATE(create_time) = '{date}'"
                df_gz = get_table_from_GZ('tb_case_check', force_refresh=True, where_clause=where_clause)
                
                for index, row in df_gz.iterrows():
                    checks.append({
                        "check_id": f"gz_{row['id']}",
                        "timestamp": row['create_time'].isoformat(),
                        "client_name": "GZ_User"  # Or another identifier
                    })
            except Exception as e:
                print(f"⚠️ Could not fetch checks from GZ database for date {date}: {e}")

            # 3. Sort combined checks
            checks.sort(key=lambda x: x['timestamp'], reverse=True)
            checks.sort(key=lambda x: x['client_name'])

        else:
            query = """
                SELECT id, create_time
                FROM check_api_requests
                WHERE DATE(create_time) = %s
                AND user_name = %s
                ORDER BY create_time DESC
            """
            cursor.execute(query, (date, consumer_data["username"]))
            checks = [{"check_id": str(row[0]), "timestamp": row[1].isoformat()} for row in cursor.fetchall()]
            
        return JSONResponse(content={"checks": checks})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if connection:
            cursor.close()
            connection.close()

@history_router.post('/get_check_details')
async def get_check_details(http_request: Request):
    consumer_data = get_consumer(http_request)
    if not consumer_data:
        raise HTTPException(status_code=401, detail="Invalid API Key")

    api_key = consumer_data["api_key"]
    
    try:
        body = await http_request.json()
        check_id = body.get("check_id")
        if not check_id:
            raise HTTPException(status_code=400, detail="Check ID is required")
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid JSON body")
    admin_api_key = os.getenv("ADMIN_API_KEY")
    is_admin = admin_api_key and api_key == admin_api_key

    # Handle checks from the GZ (MySQL) database
    if is_admin and isinstance(check_id, str) and check_id.startswith('gz_'):
        try:
            gz_check_id = check_id.split('gz_')[1]
            
            # Fetch request data from tb_case_check
            where_clause_request = f"id = {gz_check_id}"
            df_request = get_table_from_GZ('tb_case_check', force_refresh=True, where_clause=where_clause_request)
            
            if df_request.empty:
                raise HTTPException(status_code=404, detail="GZ check not found")
            
            request_data = df_request.iloc[0].to_dict()
            
            # Fetch result data from tb_case_check_result
            where_clause_result = f"check_id = {gz_check_id}"
            df_result = get_table_from_GZ('tb_case_check_result', force_refresh=True, where_clause=where_clause_result)
            
            result_data = df_result.iloc[0].to_dict() if not df_result.empty else {}

            # Map GZ data to the expected format
            # This is a sample mapping, adjust as per actual column names
            request_dict = {
                'id': f"gz_{request_data.get('id')}",
                'create_time': request_data.get('create_time').isoformat() if request_data.get('create_time') else None,
                'product_category': request_data.get('product_category'),
                'description': request_data.get('description'),
                'ip_keywords': request_data.get('ip_keywords'),
                'reference_text': request_data.get('reference_text'),
                'main_product_image': request_data.get('main_product_image'),
                'other_product_images': request_data.get('other_product_images'),
                'ip_images': request_data.get('ip_images'),
                'reference_images': request_data.get('reference_images'),
                'api_key': "GZ_DB" # Placeholder for API key
            }

            return JSONResponse(content={
                "request": request_dict,
                "result": result_data.get('result') if result_data and result_data.get('result') and isinstance(result_data.get('result'), str) else result_data.get('result')
            })

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error fetching GZ check details: {str(e)}")

    # Existing logic for PG database
    connection = None
    try:
        connection = get_pg_connection()
        cursor = connection.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Fetch request data
        if is_admin:
            query_request = "SELECT * FROM check_api_requests WHERE id = %s"
            cursor.execute(query_request, (int(check_id),))
        else:
            query_request = "SELECT * FROM check_api_requests WHERE id = %s AND user_name = %s"
            cursor.execute(query_request, (int(check_id), consumer_data["username"]))
        
        request_data = cursor.fetchone()

        # Fetch result data
        query_result = "SELECT result FROM check_api_results WHERE check_id = %s"
        cursor.execute(query_result, (int(check_id),))
        result_data = cursor.fetchone()

        if not request_data:
            raise HTTPException(status_code=404, detail="Check not found or access denied")

        request_dict = dict(request_data) if request_data else None
        if request_dict and request_dict.get('id'):
            request_dict['id'] = str(request_dict['id'])

        return JSONResponse(content={
            "request": json.loads(json.dumps(request_dict, cls=JsonEncoder)),
            "result": result_data['result'] if result_data and result_data.get('result') else None
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if connection:
            cursor.close()
            connection.close()