On Linux I had spawn, but it was not working when processing a case from the UI due to the processing happening in a thread






You’ve got a classic “fork + threads = hang” issue.

### What’s going on

* You start a **daemon thread** (`threading.Thread(..., daemon=True)`).
* Inside that thread you call `asyncio.run(...)`, which is fine.
* During that async work you create a **multiprocessing Pool** in `OCRProcessor.get_pool()`.

On  **Linux/Unix** , your code chooses `get_context('fork')`. Forking a process that already has threads (your parent process does, because you’re in a thread) is unsafe: C libs (NumPy, OpenCV, PyMuPDF/fitz, Tesseract’s wrappers) may hold internal locks. After `fork()`, only the calling thread survives; other threads vanish, but their locks stay locked. Result: the pool workers or the parent side of `starmap` can block forever, so `process_pdf(...)` “doesn’t return”.

When you “run reprocess_case directly”, you’re running in the  **main thread** , so the `fork` happens from a single-threaded state and it works—hence the confusing difference.

---

## Fixes (pick one; first is simplest)

### 1) Always use `spawn` (or `forkserver`) for the Pool

`spawn` is thread-safe: the child starts a fresh interpreter (no inherited locks).

**Changes:**

* Force spawn on all platforms.
* Make the worker function top-level (not a class staticmethod) to ensure picklability on Windows/macOS.
* Join the pool on shutdown to avoid zombies.
* (Optional) Use `starmap_async(...).get(timeout=...)` to fail fast if something goes wrong.

<pre class="overflow-visible!" data-start="1427" data-end="5445"><div class="contain-inline-size rounded-2xl relative bg-token-sidebar-surface-primary"><div class="sticky top-9"><div class="absolute end-0 bottom-0 flex h-9 items-center pe-2"><div class="bg-token-bg-elevated-secondary text-token-text-secondary flex items-center gap-4 rounded-sm px-2 font-sans text-xs"><span class="" data-state="closed"></span></div></div></div><div class="overflow-y-auto p-4" dir="ltr"><code class="whitespace-pre! language-python"><span><span># ocr_processor.py</span><span>
</span><span>import</span><span> multiprocessing </span><span>as</span><span> mp
</span><span>import</span><span> platform
</span><span>import</span><span> os
</span><span>import</span><span> cv2
</span><span>import</span><span> fitz
</span><span>import</span><span> numpy </span><span>as</span><span> np
</span><span>import</span><span> pytesseract

</span><span>def</span><span></span><span>_text_in_page_worker</span><span>(</span><span>pdf_source, page_num, save_image=False</span><span>):
    </span><span>try</span><span>:
        pdf_document = fitz.</span><span>open</span><span>(pdf_source)
        </span><span>if</span><span> page_num >= </span><span>len</span><span>(pdf_document):
            pdf_document.close()
            </span><span>return</span><span></span><span>""</span><span>, {}, </span><span>None</span><span>

        page = pdf_document[page_num]
        text = page.get_text()

        img = convert_page_number_to_image(pdf_document, page_num + </span><span>1</span><span>)
        pdf_path = pdf_source
        pdf_folder = os.path.splitext(pdf_path)[</span><span>0</span><span>]
        pdf_name_no_ext = os.path.splitext(os.path.basename(pdf_path))[</span><span>0</span><span>]
        img_path = </span><span>None</span><span>

        </span><span>if</span><span> save_image:
            os.makedirs(pdf_folder, exist_ok=</span><span>True</span><span>)
            img_path = os.path.join(pdf_folder, </span><span>f"{pdf_name_no_ext}</span><span>_page</span><span>{page_num+1</span><span>}_0.jpg")
            cv2.imwrite(img_path, img)

        pdf_document.close()

        </span><span>if</span><span> img.shape[</span><span>1</span><span>] > img.shape[</span><span>0</span><span>]:
            img = np.rot90(img, k=</span><span>1</span><span>)

        ocr_text, ocr_data = OCRProcessor.text_in_image(img)
        log_message(</span><span>f"_text_in_page: {pdf_path}</span><span>: page </span><span>{page_num+1</span><span>} done.")
        </span><span>return</span><span> text + </span><span>" "</span><span> + ocr_text, ocr_data, img_path
    </span><span>except</span><span> Exception </span><span>as</span><span> e:
        log_message(</span><span>f"Error in _text_in_page_worker: {e}</span><span>")
        </span><span>return</span><span></span><span>""</span><span>, {}, </span><span>None</span><span>


</span><span>class</span><span></span><span>OCRProcessor</span><span>:
    _pool = </span><span>None</span><span>
    _pool_pid = </span><span>None</span><span>
    _num_processes = </span><span>None</span><span>

    THUMBNAIL_MAX_SIZE = </span><span>40</span><span> * </span><span>1024</span><span>
    HIGH_RES_MAX_SIZE = </span><span>300</span><span> * </span><span>1024</span><span>
    THUMBNAIL_WIDTH = </span><span>300</span><span>
    HIGH_RES_WIDTH = </span><span>1600</span><span>

</span><span>    @classmethod</span><span>
    </span><span>def</span><span></span><span>get_pool</span><span>(</span><span>cls</span><span>):
        current_pid = os.getpid()
        </span><span>if</span><span> cls._pool </span><span>is</span><span></span><span>None</span><span></span><span>or</span><span> cls._pool_pid != current_pid:
            cls.shutdown()

            ctx = mp.get_context(</span><span>'spawn'</span><span>)  </span><span># <- ALWAYS spawn</span><span>
            cls._num_processes = get_allocated_cpus()
            cls._pool = ctx.Pool(
                processes=cls._num_processes,
                initializer=cls.init_worker
            )
            cls._pool_pid = current_pid
            log_message(</span><span>f"📊 Created {cls._num_processes}</span><span> worker processes (spawn)")
        </span><span>return</span><span> cls._pool

</span><span>    @classmethod</span><span>
    </span><span>def</span><span></span><span>shutdown</span><span>(</span><span>cls</span><span>):
        </span><span>if</span><span> cls._pool </span><span>is</span><span></span><span>not</span><span></span><span>None</span><span>:
            log_message(</span><span>"🔌 Shutting down OCR process pool"</span><span>)
            </span><span>try</span><span>:
                cls._pool.close()
                cls._pool.join()   </span><span># <- ensure clean exit</span><span>
            </span><span>finally</span><span>:
                cls._pool = </span><span>None</span><span>

</span><span>    @staticmethod</span><span>
    </span><span>def</span><span></span><span>init_worker</span><span>():
        </span><span>if</span><span> platform.system() == </span><span>"Windows"</span><span>:
            pytesseract.pytesseract.tesseract_cmd = </span><span>r'C:\Program Files\Tesseract-OCR\tesseract.exe'</span><span>
        </span><span>else</span><span>:
            pytesseract.pytesseract.tesseract_cmd = </span><span>'tesseract'</span><span>
        log_message(</span><span>f"🛠️ Initialized OCR worker (PID: {os.getpid()}</span><span>)")

</span><span>    @classmethod</span><span>
    </span><span>def</span><span></span><span>process_pdf</span><span>(</span><span>cls, pdf_document, page_numbers=None</span><span>):
        </span><span>if</span><span></span><span>isinstance</span><span>(pdf_document, </span><span>str</span><span>):
            pdf_path = pdf_document
            doc = fitz.</span><span>open</span><span>(pdf_document)
        </span><span>else</span><span>:
            doc = pdf_document
            pdf_path = pdf_document.name

        </span><span>if</span><span> page_numbers </span><span>is</span><span></span><span>None</span><span>:
            page_numbers = </span><span>range</span><span>(doc.page_count)

        log_message(</span><span>f"🔍 Processing {len</span><span>(page_numbers)} page(s) in </span><span>{os.path.basename(pdf_path)}</span><span>")

        page_text, page_ocr_data, page_images = {}, {}, {}
        </span><span>if</span><span></span><span>not</span><span></span><span>isinstance</span><span>(pdf_document, </span><span>str</span><span>):  </span><span># if we opened above, don’t leak</span><span>
            doc.close()

        pool = cls.get_pool()
        </span><span># use async + timeout to avoid silent hangs</span><span>
        async_result = pool.starmap_async(
            _text_in_page_worker,
            [(pdf_path, page_num, </span><span>True</span><span>) </span><span>for</span><span> page_num </span><span>in</span><span> page_numbers]
        )
        results = async_result.get(timeout=</span><span>600</span><span>)  </span><span># adjust as needed</span><span>

        </span><span>for</span><span> page_number, (text, ocr_data, img_path) </span><span>in</span><span></span><span>zip</span><span>(page_numbers, results):
            page_text[page_number + </span><span>1</span><span>] = text
            page_ocr_data[page_number + </span><span>1</span><span>] = ocr_data
            page_images[page_number + </span><span>1</span><span>] = img_path

        full_text = </span><span>" "</span><span>.join(page_text.values())
        </span><span>return</span><span> full_text, page_text, page_ocr_data, page_images
</span></span></code></div></div></pre>

> If you ever run this on Windows/macOS as a script: make sure your **program’s entry point** (where threads or pools are started) is under:

<pre class="overflow-visible!" data-start="5588" data-end="5636"><div class="contain-inline-size rounded-2xl relative bg-token-sidebar-surface-primary"><div class="sticky top-9"><div class="absolute end-0 bottom-0 flex h-9 items-center pe-2"><div class="bg-token-bg-elevated-secondary text-token-text-secondary flex items-center gap-4 rounded-sm px-2 font-sans text-xs"><span class="" data-state="closed"></span></div></div></div><div class="overflow-y-auto p-4" dir="ltr"><code class="whitespace-pre! language-python"><span><span>if</span><span> __name__ == </span><span>"__main__"</span><span>:
    ...
</span></span></code></div></div></pre>

This prevents recursive child launches with `spawn`.

---

### 2) Use a `ProcessPoolExecutor` and integrate with `asyncio`

This plays even nicer with your `asyncio.run(...)` and avoids `multiprocessing.Pool` quirks:

<pre class="overflow-visible!" data-start="5854" data-end="6797"><div class="contain-inline-size rounded-2xl relative bg-token-sidebar-surface-primary"><div class="sticky top-9"><div class="absolute end-0 bottom-0 flex h-9 items-center pe-2"><div class="bg-token-bg-elevated-secondary text-token-text-secondary flex items-center gap-4 rounded-sm px-2 font-sans text-xs"><span class="" data-state="closed"></span></div></div></div><div class="overflow-y-auto p-4" dir="ltr"><code class="whitespace-pre! language-python"><span><span># module-level</span><span>
</span><span>from</span><span> concurrent.futures </span><span>import</span><span> ProcessPoolExecutor

_executor = </span><span>None</span><span>

</span><span>def</span><span></span><span>get_executor</span><span>():
    </span><span>global</span><span> _executor
    </span><span>if</span><span> _executor </span><span>is</span><span></span><span>None</span><span>:
        _executor = ProcessPoolExecutor(
            max_workers=get_allocated_cpus(),
            initializer=OCRProcessor.init_worker,
            mp_context=mp.get_context(</span><span>'spawn'</span><span>),   </span><span># ensure spawn</span><span>
        )
    </span><span>return</span><span> _executor

</span><span>async</span><span></span><span>def</span><span></span><span>process_pdf_async</span><span>(</span><span>pdf_path, page_numbers=None</span><span>):
    </span><span>if</span><span></span><span>isinstance</span><span>(pdf_path, fitz.Document):
        path = pdf_path.name
        pdf_path.close()
    </span><span>else</span><span>:
        path = pdf_path

    </span><span>if</span><span> page_numbers </span><span>is</span><span></span><span>None</span><span>:
        </span><span>with</span><span> fitz.</span><span>open</span><span>(path) </span><span>as</span><span> d:
            page_numbers = </span><span>range</span><span>(</span><span>len</span><span>(d))

    loop = asyncio.get_running_loop()
    tasks = [
        loop.run_in_executor(get_executor(), _text_in_page_worker, path, n, </span><span>True</span><span>)
        </span><span>for</span><span> n </span><span>in</span><span> page_numbers
    ]
    results = </span><span>await</span><span> asyncio.gather(*tasks)
    </span><span># assemble dicts as before...</span><span>
</span></span></code></div></div></pre>

Then call this from `reprocess_cases` with `await process_pdf_async(...)`.

---

## Small but important extras

* **Don’t reuse one Pool across multiple threads** without a lock; it’s not thread-safe. If you plan parallel case processing from several threads, either:
  * Create one global Pool/Executor in the **main thread** and submit all work to it from other threads (use a `threading.Lock` around submission), or
  * Create a separate Pool per thread (more overhead), or
  * Funnel jobs to a single worker thread that talks to the Pool.
* In your current `shutdown()`, call **`join()`** after `close()` so workers fully exit.
* Make `_text_in_page` a **top-level function** (as above). It’s more robust for pickling across platforms.

---

### TL;DR

Your Pool is created with `fork` from inside a thread; that’s what makes `process_pdf(...)` hang. Switch the Pool’s start method to  **`spawn` (or `forkserver`)** , move the worker to a top-level function, and join the pool on shutdown. Or, swap to `ProcessPoolExecutor` with `spawn` and use it via `asyncio`—that’s the cleanest path with your current architecture.

##### You said:

Tell me more about forkserver, what is it? how is it different from fork/spawn? how does it fix the problem?

###### ChatGPT said:

**Thought for 54s**

Here’s the quick, practical tour.

## What is `forkserver`?

A Python multiprocessing **start method** (Unix-only) where Python first launches a small, single-threaded  **server process** . Whenever you create a `Process`/`Pool`, the main process asks that server (over a Unix socket) to **fork** a new child. So your workers are forked  **from the server** , not from your thread-rich main process.

## How it differs from `fork` and `spawn`

* **fork** (Linux default): child is a copy-on-write clone of the **current** process. Fast, minimal overhead. **Unsafe** if the parent currently has threads (common C-library locks can be inherited in a “locked” state → deadlocks).
* **spawn** (Windows default; macOS default since Py3.8): starts a **fresh Python interpreter** and imports your module. Safest, but **slowest** (no copy-on-write, everything is re-imported).
* **forkserver** (Unix): a middle ground. Children are  **forked from a clean, single-threaded server** , so you avoid “forking from a multi-threaded state.” You still get copy-on-write speed and lower overhead than `spawn`.

## Why `forkserver` fixes your issue

Your hang is caused by **forking from inside a thread** (your OCR pool is created from a worker thread). With `forkserver`, the  **actual fork happens in the server** , which has **no extra threads** and no poisoned internal locks. That sidesteps the classic “fork + threads = deadlock” problem, while keeping much of `fork`’s performance.

## Other benefits / trade-offs

* **Safer file descriptor handling** : only fds explicitly passed to children are inherited; fewer leaks.
* **Faster than `spawn`** for many cases (copy-on-write).
* **Preloading** : you can ask the forkserver to import heavy modules **once** so children inherit them cheaply (COW).

  Trade-offs:
* Unix-only (Linux/macOS). Not on Windows.
* You still need **picklable** callables (top-level functions, not closures/inner funcs).
* Don’t rely on inheriting random global state; initialize what you need in an `initializer`.

## How to use it (drop-in changes)

**Option A — set global start method (early, in main):**

<pre class="overflow-visible!" data-start="2118" data-end="2531"><div class="contain-inline-size rounded-2xl relative bg-token-sidebar-surface-primary"><div class="sticky top-9"><div class="absolute end-0 bottom-0 flex h-9 items-center pe-2"><div class="bg-token-bg-elevated-secondary text-token-text-secondary flex items-center gap-4 rounded-sm px-2 font-sans text-xs"><span class="" data-state="closed"></span></div></div></div><div class="overflow-y-auto p-4" dir="ltr"><code class="whitespace-pre! language-python"><span><span># main.py</span><span>
</span><span>import</span><span> multiprocessing </span><span>as</span><span> mp

</span><span>if</span><span> __name__ == </span><span>"__main__"</span><span>:
    </span><span>try</span><span>:
        mp.set_start_method(</span><span>"forkserver"</span><span>)  </span><span># do this BEFORE starting threads/pools</span><span>
    </span><span>except</span><span> RuntimeError:
        </span><span>pass</span><span></span><span># already set elsewhere</span><span>

    </span><span># Optional: preload heavy modules into the forkserver for COW benefits</span><span>
    mp.set_forkserver_preload([</span><span>"fitz"</span><span>, </span><span>"cv2"</span><span>, </span><span>"numpy"</span><span>, </span><span>"pytesseract"</span><span>, </span><span>"your_ocr_module"</span><span>])

    main()
</span></span></code></div></div></pre>

**Then create pools normally** (but ideally from the main thread), or keep your current code and it will route through the forkserver.

**Option B — use a context explicitly where you build the Pool/Executor:**

<pre class="overflow-visible!" data-start="2744" data-end="2876"><div class="contain-inline-size rounded-2xl relative bg-token-sidebar-surface-primary"><div class="sticky top-9"><div class="absolute end-0 bottom-0 flex h-9 items-center pe-2"><div class="bg-token-bg-elevated-secondary text-token-text-secondary flex items-center gap-4 rounded-sm px-2 font-sans text-xs"><span class="" data-state="closed"></span></div></div></div><div class="overflow-y-auto p-4" dir="ltr"><code class="whitespace-pre! language-python"><span><span>import</span><span> multiprocessing </span><span>as</span><span> mp

ctx = mp.get_context(</span><span>"forkserver"</span><span>)
pool = ctx.Pool(processes=n, initializer=init_worker)
</span></span></code></div></div></pre>

**ProcessPoolExecutor with forkserver (nice with asyncio):**

<pre class="overflow-visible!" data-start="2939" data-end="3159"><div class="contain-inline-size rounded-2xl relative bg-token-sidebar-surface-primary"><div class="sticky top-9"><div class="absolute end-0 bottom-0 flex h-9 items-center pe-2"><div class="bg-token-bg-elevated-secondary text-token-text-secondary flex items-center gap-4 rounded-sm px-2 font-sans text-xs"><span class="" data-state="closed"></span></div></div></div><div class="overflow-y-auto p-4" dir="ltr"><code class="whitespace-pre! language-python"><span><span>from</span><span> concurrent.futures </span><span>import</span><span> ProcessPoolExecutor
</span><span>import</span><span> multiprocessing </span><span>as</span><span> mp

executor = ProcessPoolExecutor(
    max_workers=n,
    mp_context=mp.get_context(</span><span>"forkserver"</span><span>),
    initializer=init_worker
)
</span></span></code></div></div></pre>

## Practical tips for your OCR pipeline

* Make the worker target a **top-level function** (not a class staticmethod) to avoid pickling hiccups across platforms.
* Keep `init_worker()` to (re)configure Tesseract path, OpenCV, env vars (e.g., limit OpenBLAS/OMP threads if needed).
* If multiple threads submit to one Pool/Executor, guard submissions with a `threading.Lock` (Pool is not inherently thread-safe for concurrent `starmap` calls).
* If you stay on Linux and really want to keep `fork`, another workaround is to **create the Pool in the main thread before starting any threads** and reuse it. But `forkserver`/`spawn` is more robust.

**Bottom line:** `forkserver` gives you (most of) fork’s speed without the “forking from threads” foot-gun. It’s the safest high-performance choice on Unix for apps like yours that use threads + heavy C libraries (NumPy, OpenCV, PyMuPDF) and still want multiprocessing.
