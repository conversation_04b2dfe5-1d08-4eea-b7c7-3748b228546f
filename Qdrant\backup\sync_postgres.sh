#!/bin/bash
# PostgreSQL Database Sync Script
# This script syncs PostgreSQL tables from a main to a backup server.

# Exit on error, undefined variable, or pipe failure
set -euo pipefail

# --- Configuration ---

# Main (Source) PostgreSQL Database
MAIN_DB_HOST="vectorstore1.maidalv.com"
MAIN_DB_PORT="5432"
MAIN_DB_USER="maidalv"
MAIN_DB_PASSWORD="QFfzPivrwiDqkDyW"
MAIN_DB_NAME="maidalv_db"

# Backup (Destination) PostgreSQL Database
BACKUP_DB_HOST="localhost"
BACKUP_DB_PORT="5432"
BACKUP_DB_USER="maidalv"
BACKUP_DB_PASSWORD="QFfzPivrwiDqkDyW"
BACKUP_DB_NAME="maidalv_db"

# --- Script Setup ---
LOG_FILE="/var/log/postgres_sync.log"
TMP_DIR="/tmp/db_sync_$(date +%s)" # Create a unique temp directory for this run

# --- Logging and Error Handling ---

# Function to log messages to both console and log file
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to handle errors, log the message, and exit
handle_error() {
    local exit_code=$2
    log "ERROR: $1 (Exit Code: $exit_code)"
    log "--- SCRIPT FAILED ---"
    # Cleanup before exiting
    if [ -d "$TMP_DIR" ]; then
        rm -rf "$TMP_DIR"
    fi
    exit "$exit_code"
}

# Trap ERR to call handle_error, ensuring we always log the failure
trap 'handle_error "An unexpected error occurred on line $LINENO." $?' ERR

# --- Pre-flight Checks ---

# Function to check for required command-line tools
check_dependencies() {
    log "Checking for required tools..."
    local missing_tools=0
    for tool in psql pg_dump; do
        if ! command -v "$tool" &> /dev/null; then
            log "ERROR: Required tool '$tool' is not installed."
            missing_tools=1
        fi
    done
    if [ "$missing_tools" -eq 1 ]; then
        exit 1
    fi
    log "All required tools are present."
}

# Function to check PostgreSQL connection
check_pg_connection() {
    local host=$1; local port=$2; local user=$3; local password=$4; local dbname=$5; local server_name=$6
    log "Checking connection to $server_name PostgreSQL server..."
    if PGPASSWORD="$password" psql -h "$host" -p "$port" -U "$user" -d "$dbname" -c "SELECT 1;" &> /dev/null; then
        log "✅ Connection to $server_name PostgreSQL successful."
    else
        handle_error "Failed to connect to $server_name PostgreSQL at $host:$port." 1
    fi
}

# --- PostgreSQL Functions ---

# Function to check if a table exists in a database
table_exists() {
    local host=$1; local port=$2; local user=$3; local password=$4; local dbname=$5; local table=$6
    local exists
    exists=$(PGPASSWORD="$password" psql -h "$host" -p "$port" -U "$user" -d "$dbname" -tAc \
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');")
    echo "$exists"
}

# Function to get the latest built_at date from a table
get_latest_built_at() {
    local host=$1; local port=$2; local user=$3; local password=$4; local dbname=$5; local table=$6
    local built_at
    built_at=$(PGPASSWORD="$password" psql -h "$host" -p "$port" -U "$user" -d "$dbname" -tAc \
        "SELECT COALESCE(MAX(built_at), '1900-01-01'::timestamp) FROM $table;")
    echo "$built_at"
}

# Function to sync a single table (structure + data) - FASTEST
sync_table() {
    local table=$1
    log "Syncing table: $table"

    # Drop and recreate the table structure
    log "  -> Dropping table '$table' on backup server if it exists..."
    PGPASSWORD="$BACKUP_DB_PASSWORD" psql -v ON_ERROR_STOP=1 -h "$BACKUP_DB_HOST" -p "$BACKUP_DB_PORT" -U "$BACKUP_DB_USER" -d "$BACKUP_DB_NAME" \
        -c "DROP TABLE IF EXISTS public.$table CASCADE;" >> "$LOG_FILE" 2>&1

    log "  -> Dumping and restoring structure for '$table'..."
    PGPASSWORD="$MAIN_DB_PASSWORD" pg_dump --schema-only --no-owner -h "$MAIN_DB_HOST" -p "$MAIN_DB_PORT" -U "$MAIN_DB_USER" -d "$MAIN_DB_NAME" -t "public.$table" | \
        PGPASSWORD="$BACKUP_DB_PASSWORD" psql -v ON_ERROR_STOP=1 -h "$BACKUP_DB_HOST" -p "$BACKUP_DB_PORT" -U "$BACKUP_DB_USER" -d "$BACKUP_DB_NAME" >> "$LOG_FILE" 2>&1

    # Stream data directly from the main server to the backup server using COPY
    log "  -> Streaming data for '$table' from main to backup via COPY..."
    PGPASSWORD="$MAIN_DB_PASSWORD" psql -h "$MAIN_DB_HOST" -p "$MAIN_DB_PORT" -U "$MAIN_DB_USER" -d "$MAIN_DB_NAME" \
        -c "\copy (SELECT * FROM public.$table) to stdout with binary" | \
    PGPASSWORD="$BACKUP_DB_PASSWORD" psql -h "$BACKUP_DB_HOST" -p "$BACKUP_DB_PORT" -U "$BACKUP_DB_USER" -d "$BACKUP_DB_NAME" \
        -c "\copy public.$table from stdin with binary" >> "$LOG_FILE" 2>&1

    log "✅ Successfully synced table: $table"
}


# --- Main Logic ---

main() {
    # Truncate the log file to start fresh for this run
    > "$LOG_FILE"

    log "--- Starting PostgreSQL Sync Process ---"
    mkdir -p "$TMP_DIR"

    # 1. Pre-flight Checks
    log "=== Phase 1: Pre-flight Checks ==="
    check_dependencies
    check_pg_connection "$MAIN_DB_HOST" "$MAIN_DB_PORT" "$MAIN_DB_USER" "$MAIN_DB_PASSWORD" "$MAIN_DB_NAME" "Main"
    check_pg_connection "$BACKUP_DB_HOST" "$BACKUP_DB_PORT" "$BACKUP_DB_USER" "$BACKUP_DB_PASSWORD" "$BACKUP_DB_NAME" "Backup"
    log "✅ All pre-flight checks passed."

    # 2. PostgreSQL Sync
    log "=== Phase 2: PostgreSQL Sync ==="

    # Define table lists
    local FULL_COPY_TABLES=(
                # copyrights
                "copyrights"
                "copyrights_files"
                "cn_websites"
                "cn_websites_files"
                "maijiazhichi_clone"
                # patents (definitions and parent tables first)
                "patents_cpc_ipc_definitions"
                "patents_cpc_ipc_definitions_full"
                "patents_uspc_definitions"
                "patents_loc_definitions"
                "patents"
                "patents_all"
                # patents (assignment/child tables last)
                "patents_cpc_ipc_assignments"
                "patents_cpc_ipc_assignments_all"
                "patents_uspc_assignments"
                "patents_uspc_assignments_all"
                "tro_cpc_classifications"
                # trademarks
                "trademarks"
                "trademarks_clone"
                "trademarks_int_cls_definitions"
                # misc
                "statistics_log"
                "check_client_api_keys"
            )
    local CONDITIONAL_SYNC_TABLE="trademarks_precomputed_marks"

    # Check if this is the first run by checking for a known table
    local backup_db_initialized
    backup_db_initialized=$(table_exists "$BACKUP_DB_HOST" "$BACKUP_DB_PORT" "$BACKUP_DB_USER" "$BACKUP_DB_PASSWORD" "$BACKUP_DB_NAME" "copyrights")

    if [ "$backup_db_initialized" != "t" ]; then
        log "First run detected: Backup database appears empty. Performing full initial sync."

        log "Dumping full schema from main database..."
        local schema_sql="$TMP_DIR/main_schema.sql"
        if ! PGPASSWORD="$MAIN_DB_PASSWORD" pg_dump -v --schema-only --no-owner --no-privileges \
            -h "$MAIN_DB_HOST" -p "$MAIN_DB_PORT" -U "$MAIN_DB_USER" -d "$MAIN_DB_NAME" \
            > "$schema_sql" 2>> "$LOG_FILE"; then
            handle_error "Failed to dump main schema. Check log for details from pg_dump." $?
        fi

        log "Restoring full schema to backup database..."
        PGPASSWORD="$BACKUP_DB_PASSWORD" psql -v ON_ERROR_STOP=1 -h "$BACKUP_DB_HOST" -p "$BACKUP_DB_PORT" -U "$BACKUP_DB_USER" -d "$BACKUP_DB_NAME" \
            -f "$schema_sql" >> "$LOG_FILE" 2>&1

        log "Syncing data for all required tables..."
        for table in "${FULL_COPY_TABLES[@]}"; do
            sync_table "$table"
        done
        sync_table "$CONDITIONAL_SYNC_TABLE"

    else
        log "Daily sync detected: Backup database is initialized. Syncing changes."

        log "Syncing always-sync tables..."
        for table in "${FULL_COPY_TABLES[@]}"; do
            sync_table "$table"
        done

        log "Checking conditional sync table: $CONDITIONAL_SYNC_TABLE"
        local main_built_at
        main_built_at=$(get_latest_built_at "$MAIN_DB_HOST" "$MAIN_DB_PORT" "$MAIN_DB_USER" "$MAIN_DB_PASSWORD" "$MAIN_DB_NAME" "$CONDITIONAL_SYNC_TABLE")
        local backup_built_at
        backup_built_at=$(get_latest_built_at "$BACKUP_DB_HOST" "$BACKUP_DB_PORT" "$BACKUP_DB_USER" "$BACKUP_DB_PASSWORD" "$BACKUP_DB_NAME" "$CONDITIONAL_SYNC_TABLE")

        log "  -> Main server latest 'built_at':   $main_built_at"
        log "  -> Backup server latest 'built_at':  $backup_built_at"

        if [ "$main_built_at" != "$backup_built_at" ]; then
            log "'built_at' dates differ. Syncing $CONDITIONAL_SYNC_TABLE..."
            sync_table "$CONDITIONAL_SYNC_TABLE"
        else
            log "'built_at' dates match. Skipping sync for $CONDITIONAL_SYNC_TABLE."
        fi
    fi
    log "✅ PostgreSQL sync completed successfully."


    # 3. Verification
    log "=== Phase 3: Verification ==="
    log "Verifying PostgreSQL sync..."
    local backup_table_count
    backup_table_count=$(PGPASSWORD="$BACKUP_DB_PASSWORD" psql -h "$BACKUP_DB_HOST" -p "$BACKUP_DB_PORT" -U "$BACKUP_DB_USER" -d "$BACKUP_DB_NAME" -tAc \
        "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")

    log "  -> Backup database contains $backup_table_count tables."
    if [ "$backup_table_count" -lt 5 ]; then # Arbitrary low number to indicate a likely problem
         handle_error "Verification failed: Low number of tables in backup DB." 1
    fi
    log "✅ PostgreSQL table count looks reasonable."

    # 4. Cleanup
    log "=== Phase 4: Cleanup ==="
    rm -rf "$TMP_DIR"
    log "Temporary directory '$TMP_DIR' cleaned up."

    log "--- ✅ Sync Process Completed Successfully ---"
    log "Log file is located at: $LOG_FILE"
}

# Execute the main function
main