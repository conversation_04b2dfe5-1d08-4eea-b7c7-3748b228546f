"""
Unified Qdrant-based implementation for IP asset similarity search.
This module provides functions to find similar IP assets using Qdrant vector database.
"""

import os, re, aiohttp, asyncio, time
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from langfuse import observe
import langfuse

# Import the embedding functions from gRPC client
from Check.gRPC_Client import get_siglip_embeddings_grpc
from Common.Constants import local_ip_folder
from Check.Do_Check_Download import download_from_url
from Check.Utils import create_ip_url

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_API_URL = os.getenv("QDRANT_API_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

# Global aiohttp session
aiohttp_session = None

async def init_aiohttp_session():
   """Initializes the shared aiohttp client session."""
   global aiohttp_session
   if aiohttp_session is None:
       timeout = aiohttp.ClientTimeout(total=600)  # 10 minutes timeout
       aiohttp_session = aiohttp.ClientSession(timeout=timeout)

async def close_aiohttp_session():
   """Closes the shared aiohttp client session."""
   global aiohttp_session
   if aiohttp_session:
       await aiohttp_session.close()
       aiohttp_session = None

@observe(capture_input=False, capture_output=False)
async def find_similar_assets_qdrant(
    query_image_paths: List[str],
    check_id: str,
    client_id: str,
    ip_type: str,
    temp_dir: str,
    cases_df=None,
    plaintiff_df=None,
    plaintiff_id: Optional[str] = None,
    top_n: int = 1,
    similarity_threshold: float = 0.8,
    similarity_threshold_text: float = 0.25,
    embedding_task: Optional[asyncio.Task] = None
) -> List[Dict[str, Any]]:
    metadata = {}
    langfuse.get_client().update_current_span(input={
        "query_image_paths": query_image_paths,
        "check_id": check_id, "client_id": client_id, "ip_type": ip_type,
        "plaintiff_id": plaintiff_id, "top_n": top_n,
        "similarity_threshold": similarity_threshold,
        "similarity_threshold_text": similarity_threshold_text
    })
    """
    Find similar IP assets using Qdrant vector database and prepare downloads.

    Args:
        query_image_paths (List[str]): Paths to the query images
        check_id (str): The identifier for this specific check/batch
        client_id (str): The identifier for the client submitting the batch
        ip_type (str): The type of IP assets to search for ("Copyright", "Patent", "Trademark")
        temp_dir (str): Temporary directory for downloads
        plaintiff_df: DataFrame containing plaintiff information
        plaintiff_id (str, optional): Optional plaintiff ID to filter results
        top_n (int): Number of top similar images to return
        similarity_threshold (float): Minimum cosine similarity score to consider a match for images
        similarity_threshold_text (float): Minimum cosine similarity score to consider a match for text
        embedding_task (asyncio.Task, optional): A task that resolves to a map of pre-computed embeddings.

    Returns:
        List[Dict[str, Any]]: List of match information with download URLs and local paths prepared
    """
    if not query_image_paths:
        langfuse.get_client().update_current_span(output=[])
        return []

    # Await the pre-computed embeddings. If the task is already done, this returns immediately.
    precomputed_embeddings_map = await embedding_task if embedding_task else {}

    # Separate images that need embeddings from those that have them
    prep_start_time = time.time()
    paths_to_embed = [path for path in query_image_paths if path not in precomputed_embeddings_map]
    final_embeddings_map = {path: emb for path, emb in precomputed_embeddings_map.items()}

    # If there are images to embed, run them concurrently in a thread pool
    if paths_to_embed:
        embedding_start_time = time.time()
        # Run embedding generation for all needed images in a single thread
        new_embeddings_map = await get_siglip_embeddings_grpc(paths_to_embed, "image")
        metadata["embedding_time"] = time.time() - embedding_start_time

        for path, new_embedding in new_embeddings_map.items():
            final_embeddings_map[path] = new_embedding
            
    # Re-create the payload with the correct order and embeddings
    products = []
    for path in query_image_paths:
        products.append({
            "siglip_vector": final_embeddings_map[path].tolist(),
            "filename": os.path.basename(path)
        })
    
    payload = {
        "client_id": str(client_id),
        "check_id": check_id,
        "products": products,
        "search_ip_type": ip_type,
        "threshold": similarity_threshold*2-1,
        "threshold_text": similarity_threshold_text,
        "plaintiff_id": plaintiff_id,
        "top_n": top_n
    }
    metadata["prep_time"] = time.time() - prep_start_time
    
    # Make the API request to the forward_check endpoint
    try:
       # Ensure the session is initialized
       await init_aiohttp_session()
       global aiohttp_session

       qdrant_start_time = time.time()
       async with aiohttp_session.post(
           f"{QDRANT_API_URL}/forward_check",
                json=payload,
                headers={
                    "Authorization": f"Bearer {QDRANT_API_KEY}",
                    "Content-Type": "application/json"
                }
       ) as response:
           if response.status != 200:
               metadata["qdrant_search_time"] = time.time() - qdrant_start_time
               error_details = await response.text()
               print(f"Error from Qdrant API: {response.status} - {error_details}")
               langfuse.get_client().update_current_span(output={"error": error_details, "status": response.status}, metadata=metadata)
               return []
           
           result = await response.json()
       metadata["qdrant_search_time"] = time.time() - qdrant_start_time
    except aiohttp.ClientError as e:
        print(f"Network error during Qdrant API call: {e}")
        langfuse.get_client().update_current_span(output={"error": str(e), "status": "Network Error"}, level="ERROR", metadata=metadata)
        return []
    
    # Process the results
    all_matches = []
    download_truples = []
    
    processing_start_time = time.time()
    for infringement in result.get("results", []):
        product_filename = infringement.get("query_filename")
        query_image_path = next((path for path in query_image_paths if os.path.basename(path) == product_filename), None)
        
        if not query_image_path:
            continue
        
        if infringement.get("ip_type") != ip_type:
            continue
        
        qdrant_metadata = infringement.get("qdrant_metadata", {}) or {}
        db_metadata = infringement.get("db_metadata", {}) or {}
        
        if qdrant_metadata and "plaintiff_id" in qdrant_metadata: # TRO infringement
            result_plaintiff_id = qdrant_metadata.get("plaintiff_id", [])
            plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id, 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id].empty else ""
            
            match_info = {
                "product_local_path": query_image_path,
                "similarity": str(infringement.get("score", 0)/2+0.5),
                'plaintiff_id': result_plaintiff_id,
                "ip_type": infringement.get("ip_type", ""),
                "ip_owner": plaintiff_name,
                "plaintiff_name": plaintiff_name,
                # "docket": qdrant_metadata.get("docket", ""),
                # "number_of_cases": qdrant_metadata.get("number_of_cases", 0),
                "reg_no": qdrant_metadata.get("reg_no", "") or qdrant_metadata.get("patent_number", ""),
                "int_cls_list": qdrant_metadata.get("int_cls", []),  # Trademark
                "text": qdrant_metadata.get("trademark_text", "") or qdrant_metadata.get("text", ""),  # trademark_text for trademark and "text" for patent however: !!!! Trademark_text is not in NPY and not in Qdrant !!!! It is in database, field mark_text. Does trademark report use it?
                "ip_asset_urls": [],  # Needed to then addpend
                "ip_local_paths": []  # Needed to then addpend
            }
            
            if infringement.get("ip_type") == "Copyright": # It is key to have the image first, then the certificate, because Do_Check_Copyright.py assumes it.
                download_truples.append(create_task_download_ip_old_cos_plaintiff(match_info, temp_dir, result_plaintiff_id, qdrant_metadata.get("filename", "")))
                download_truples.append(create_task_download_ip_old_cos_plaintiff(match_info, temp_dir, result_plaintiff_id, qdrant_metadata.get("full_filename", "")))
            elif infringement.get("ip_type") == "Trademark":
                download_truples.append(create_task_download_ip_old_cos_plaintiff(match_info, temp_dir, result_plaintiff_id, qdrant_metadata.get("filename", "")))
                for full_filename in qdrant_metadata.get("full_filename", ""):
                    download_truples.append(create_task_download_ip_old_cos_plaintiff(match_info, temp_dir, result_plaintiff_id, full_filename))
            elif infringement.get("ip_type") == "Patent":
                if cases_df is None:
                    raise ValueError("cases_df is required for patent search")
                ip_image_filenames = get_tro_patent_pages(cases_df, result_plaintiff_id, qdrant_metadata.get("filename", ""), qdrant_metadata.get("full_filename", ""), qdrant_metadata.get("docket", ""))
                for ip_image_filename in ip_image_filenames:
                    download_truples.append(create_task_download_ip_old_cos_plaintiff(match_info, temp_dir, result_plaintiff_id, ip_image_filename))
            
            # Add specific fields based on IP type
            
            all_matches.append(match_info)
            
        else: # Non-TRO infringement / All infringement: ultimatly we will only use this when we have theTRO flag right.
            if not db_metadata:
                continue
            
            plaintiff_name = None
            if db_metadata.get("plaintiff_id", None):
                plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == db_metadata.get("plaintiff_id", None), 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == db_metadata.get("plaintiff_id", None)].empty else ""
            
            ip_type = infringement.get("ip_type", "")
            
            match_info = {
                "product_local_path": query_image_path,
                "similarity": str(infringement.get("score", 0)/2+0.5),
                "plaintiff_id": db_metadata.get("plaintiff_id", None),
                "plaintiff_name": plaintiff_name,
                "ip_type": ip_type,
                # "docket": cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)].sort_values(by='date_filed', ascending=False)['docket'].iloc[0] if not cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)].empty else "",
                # "number_of_cases": len(cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)]),
                "reg_no": db_metadata.get("reg_no", "") or db_metadata.get("document_id", ""),
                "int_cls_list": db_metadata.get("int_cls", []),  # Trademark
                "text": db_metadata.get("mark_text", "") or db_metadata.get("patent_title", ""),
                "ip_asset_urls": [],
                "ip_local_paths": []
            }

            ip_owner_val = ""
            if ip_type == "Patent":
                assignee = db_metadata.get("assignee")
                applicant = db_metadata.get("applicant")
                inventors = db_metadata.get("inventors")

                owner_source = ""
                if assignee and assignee not in ('[]', '""', "''"):
                    owner_source = assignee
                elif applicant and applicant not in ('[]', '""', "''"):
                    owner_source = applicant
                elif inventors and inventors not in ('[]', '""', "''"):
                    owner_source = inventors
                
                if owner_source:
                    # Handles "['Name 1', 'Name 2']" or "(Name)"
                    ip_owner_val = str(owner_source).strip("[]'\" ")
                    if ip_owner_val.startswith("(") and ip_owner_val.endswith(")"):
                        ip_owner_val = ip_owner_val[1:-1].strip()

            else: # Copyright, Trademark
                owner_source = db_metadata.get("applicant_name", "") or db_metadata.get("names", "") or db_metadata.get("copyright_claimant", "")
                if owner_source:
                    ip_owner_val = str(owner_source)
            
            match_info["ip_owner"] = ip_owner_val
            
            if ip_type == "Copyright":
                download_truples.append(create_task_download_ip_new_cos(match_info, temp_dir, ip_type, seed=db_metadata["reg_no"]))
            elif ip_type == "Trademark":
                download_truples.append(create_task_download_ip_new_cos(match_info, temp_dir, ip_type, seed=db_metadata["ser_no"]))
            elif ip_type == "Patent":
                fig_files = db_metadata["fig_files"]
                fig_files = sorted(fig_files)
                for fig_file in fig_files:
                    download_truples.append(create_task_download_ip_new_cos(match_info, temp_dir, ip_type, seed=fig_file.split(".")[0]))

            all_matches.append(match_info)
        
    download_truples = list(set(download_truples)) # Remove duplicates
    metadata["processing_time"] = time.time() - processing_start_time
    download_tasks = [download_from_url(ip_url, ip_local_path) for ip_url, ip_local_path in download_truples] # Prepare download tasks
    download_start_time = time.time()
    download_statuses = await asyncio.gather(*download_tasks)
    metadata["download_time"] = time.time() - download_start_time

    # Log URL not found
    for download_truple, download_status in zip(download_truples, download_statuses):
        if not download_status:
            await _log_download_error(check_id, download_truple[0])
    
    # Return top N results with download information
    langfuse.get_client().update_current_span(output=all_matches, metadata=metadata)
    return all_matches

def create_task_download_ip_new_cos(match_info, temp_dir, ip_type, seed):
    IP_Url = create_ip_url(ip_type, seed)
    match_info["ip_asset_urls"].append(IP_Url)
    if ip_type == "Patent":
        extension = "png"
    else:
        extension = "webp"
        
    match_info["ip_local_paths"].append(os.path.join(temp_dir, seed + "." + extension))
        
    return (IP_Url, os.path.join(temp_dir, seed + "." + extension))

def create_task_download_ip_old_cos_plaintiff(match_info, temp_dir, plaintiff_id, ip_image_filename):
    plaintiff_id_int = int(float(plaintiff_id))
    IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id_int}/high/{ip_image_filename}"
    ip_local_path = os.path.join(temp_dir, ip_image_filename)
    match_info["ip_asset_urls"].append(IP_Url)
    match_info["ip_local_paths"].append(ip_local_path)
    return (IP_Url, ip_local_path)


async def _log_download_error(check_id: str, IP_Url: str):
    """Helper function to log download errors."""
    os.makedirs(os.path.join(os.getcwd(), "Errors"), exist_ok=True)
    with open(os.path.join(os.getcwd(), "Errors", "IP_Download_Error.txt"), "a") as f:
        f.write(f"{check_id} - {IP_Url}\n")
        
        
def get_tro_patent_pages(cases_df, plaintiff_id, filename, full_filename, docket):
    # Helper function to extract page number robustly using regex
    def extract_page_number(filename_str):
        match = re.search(r'page(\d+)', filename_str)
        if match:
            return int(match.group(1))
        elif "_full" in filename_str: # the certificate comes first
            return 0
        
        # Fallback if page number is not found. This might indicate an unexpected filename format.
        # Depending on requirements, you could raise an error or return a default (e.g., 0 or float('inf')).
        print(f"Warning: Could not extract page number from '{filename_str}'. Defaulting to 0 for sorting.")
        return 99

    if isinstance(filename,str):
        row = cases_df[(cases_df['docket'] == docket) & (cases_df['plaintiff_id'] == plaintiff_id)].iloc[0]
        all_pages = [key for key in row['images']['patents'].keys() if row['images']['patents'][key]['full_filename'][0] == full_filename]
        full_patent = all_pages + [full_filename]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    else:
        full_patent = filename + [full_filename]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    return full_patent
