<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maidalv IP 检测 API 文档</title>
    <link rel="shortcut icon" href="{{ url_path_for('static', path='images/favicon.ico') }}" type="image/x-icon">
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/inter-fonts.css') }}">
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/prism.min.css') }}">
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/prism-tomorrow.min.css') }}" id="prism-dark-theme" disabled>
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/documentation.css') }}">
</head>
<body>
    <div class="container">
        <!-- 侧边栏导航 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">Maidalv API</div>
                <button class="theme-toggle" onclick="toggleTheme()" title="切换主题">
                    🌙
                </button>
            </div>
            <nav class="nav-menu">
                <a href="#overview" class="nav-item active">概览</a>
                <a href="#authentication" class="nav-item">认证</a>
                <a href="#submit-check" class="nav-item">提交检测</a>
                <a href="#retrieve-results" class="nav-item">获取结果</a>
                <a href="#errors" class="nav-item">错误处理</a>
                <a href="#rate-limiting" class="nav-item">速率限制</a>
                <a href="#faq" class="nav-item">FAQ</a>
            </nav>
        </aside>

        <!-- 主内容 -->
        <main class="main-content">
            <div class="content-header">
                <h1>IP 检测 API 文档</h1>
                <p>集成 Maidalv 知识产权检测 API 的完整指南</p>
            </div>

            <!-- 概览 -->
            <section id="overview" class="section">
                <h2>概览</h2>
                <p>本 API 服务帮助电商卖家快速判断其产品是否可能侵犯他方的知识产权（IP），覆盖版权、商标和专利。</p>

                <h3>可提交的内容</h3>
                <ul>
                    <li><strong>产品图片</strong> —— 需要检测的产品图片</li>
                    <li><strong>IP 图片</strong> —— 参与产品设计的相关知识产权图片</li>
                    <li><strong>参考图片</strong> —— 与产品设计相似或启发来源的图片</li>
                    <li><strong>产品描述</strong> —— 产品的文字描述</li>
                    <li><strong>IP 关键词</strong> —— 与知识产权相关的关键词</li>
                </ul>

                <h3>返回的结果</h3>
                <ul>
                    <li><strong>侵权分析</strong> —— 可能的侵权项</li>
                    <li><strong>IP 证据</strong> —— 被疑似侵权的知识产权图片</li>
                    <li><strong>诉讼信息</strong> —— 该 IP 是否出现在法院案件中</li>
                    <li><strong>法律意见报告</strong> —— 对潜在侵权严重程度的评估</li>
                    <li><strong>风险评估</strong> —— 总体风险等级（高/中/低）</li>
                </ul>

                <p>这有助于卖家在上架或销售前做出更明智的决策。</p>

                <div class="alert info">
                    <strong>基础 URL：</strong> <code>https://api.maidalv.com</code>
                </div>
            </section>

            <!-- 认证 -->
            <section id="authentication" class="section">
                <h2>认证</h2>
                <p>所有 API 请求均需要使用 API Key 进行认证。请在请求头中以 <code>apikey</code> 字段提交您的密钥。</p>

                <div class="alert warning">
                    <strong>安全提示：</strong> 请妥善保管您的 API Key，不要在客户端代码中暴露。务必从您的服务器侧发起 API 调用。
                </div>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests

# Your API key
API_KEY = "{YOUR_API_KEY}"

# Include in request headers
headers = {
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# Example request
response = requests.post(url, headers=headers, json=data)</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Your API key
const API_KEY = "{YOUR_API_KEY}";

// Include in request headers
const headers = {
    "apikey": API_KEY,
    "Content-Type": "application/json"
};

// Example request
fetch(url, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(data)
});</code></pre>
                    </div>
                </div>
            </section>

            <!-- 提交检测 -->
            <section id="submit-check" class="section">
                <h2>提交商品进行 IP 检测</h2>
                <p><span class="badge post">POST</span> <code>/check_api</code></p>

                <p>提交一个商品以进行知识产权分析。该端点接收产品图片、描述和相关信息，并执行全面的 IP 检测。</p>

                <h3>请求参数</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>api_key</code></td>
                                <td>string</td>
                                <td><span class="badge required">必填</span></td>
                                <td>您的 API 认证密钥</td>
                            </tr>
                            <tr>
                                <td><code>main_product_image</code></td>
                                <td>string</td>
                                <td><span class="badge required">必填</span></td>
                                <td>带有数据 URI 头的 Base64 图片（如 <code>data:image/jpeg;base64,{base64_data}</code>）或图片 URL</td>
                            </tr>
                            <tr>
                                <td><code>other_product_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">可选</span></td>
                                <td>额外产品图片数组（带数据 URI 头的 Base64 或 URL）。最多 5 张。</td>
                            </tr>
                            <tr>
                                <td><code>ip_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">可选</span></td>
                                <td>与 IP 相关的图片数组（带数据 URI 头的 Base64 或 URL）。最多 3 张。</td>
                            </tr>
                            <tr>
                                <td><code>reference_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">可选</span></td>
                                <td>相似产品的参考图片数组（带数据 URI 头的 Base64 或 URL）。最多 3 张。</td>
                            </tr>
                            <tr>
                                <td><code>description</code></td>
                                <td>string</td>
                                <td><span class="badge optional">可选</span></td>
                                <td>产品描述。为获得最佳效果，建议仅使用产品标题。</td>
                            </tr>
                            <tr>
                                <td><code>ip_keywords</code></td>
                                <td>array</td>
                                <td><span class="badge optional">可选</span></td>
                                <td>与知识产权相关的关键词数组。最多 20 个。</td>
                            </tr>
                            <tr>
                                <td><code>reference_text</code></td>
                                <td>string</td>
                                <td><span class="badge optional">可选</span></td>
                                <td>来自相似产品的文本（如产品标题）</td>
                            </tr>
                            <tr>
                                <td><code>language</code></td>
                                <td>string</td>
                                <td><span class="badge optional">可选</span></td>
                                <td>响应语言（<code>'en'</code> 或 <code>'zh'</code>，默认 <code>'zh'</code>）。仅影响法律意见报告的语言。</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="alert warning">
                    <strong>⚠️ 重要：</strong> 发送 Base64 图片时，必须包含数据 URI 头部格式：<code>data:image/jpeg;base64,{base64_data}</code> 或 <code>data:image/png;base64,{base64_data}</code>。后端要求此格式，不带头部的纯 Base64 字符串会被拒绝。
                </div>

                <h3>示例请求</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import base64
import json

# Your API configuration
API_KEY = "{YOUR_API_KEY}"
BASE_URL = "https://api.maidalv.com"

# Function to encode image to base64 with data URI header
def encode_image_to_base64(image_path):
    # Determine MIME type based on file extension
    if image_path.lower().endswith(('.jpg', '.jpeg')):
        mime_type = 'image/jpeg'
    elif image_path.lower().endswith('.png'):
        mime_type = 'image/png'
    elif image_path.lower().endswith('.webp'):
        mime_type = 'image/webp'
    else:
        mime_type = 'image/jpeg'  # default

    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        # Return with data URI header as required by the API
        return f"data:{mime_type};base64,{encoded_string}"

# Prepare the request data
data = {
    "api_key": API_KEY,
    "main_product_image": encode_image_to_base64("product.jpg"),  # Returns data:image/jpeg;base64,{base64_data}
    "other_product_images": [
        encode_image_to_base64("product2.jpg"),
        encode_image_to_base64("product3.jpg")
    ],
    "ip_images": [
        encode_image_to_base64("ip_reference.jpg")
    ],
    "description": "Wireless bluetooth headphones",  # Product title recommended
    "ip_keywords": ["bluetooth", "wireless", "headphones", "noise cancellation"],
    "reference_text": "Similar to popular brand headphones",
    "language": "en"
}

# Submit the check request
try:
    response = requests.post(
        f"{BASE_URL}/check_api",
        json=data,
        headers={"apikey": API_KEY, "Content-Type": "application/json"},
        timeout=30
    )

    if response.status_code == 200:
        result = response.json()
        check_id = result.get("check_id")
        status = result.get("status")

        print(f"Check submitted successfully!")
        print(f"Check ID: {check_id}")
        print(f"Status: {status}")

        if "estimated_completion_time" in result:
            print(f"Estimated completion time: {result['estimated_completion_time']} seconds")

    else:
        error_data = response.json()
        print(f"Error: {error_data}")

except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Your API configuration
const API_KEY = "{YOUR_API_KEY}";
const BASE_URL = "https://api.maidalv.com";

// Function to convert file to base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = error => reject(error);
    });
}

// Example with file inputs (browser environment)
async function submitCheck() {
    try {
        // Get files from input elements
        const mainImageFile = document.getElementById('mainImage').files[0];
        const otherImageFiles = document.getElementById('otherImages').files;

        // Convert to base64
        const mainImageBase64 = await fileToBase64(mainImageFile);
        const otherImagesBase64 = [];

        for (let file of otherImageFiles) {
            const base64 = await fileToBase64(file);
            otherImagesBase64.push(base64);
        }

        // Prepare request data
        const data = {
            api_key: API_KEY,
            main_product_image: mainImageBase64,
            other_product_images: otherImagesBase64,
            description: "Wireless bluetooth headphones",  // Product title recommended
            ip_keywords: ["bluetooth", "wireless", "headphones", "noise cancellation"],
            reference_text: "Similar to popular brand headphones",
            language: "en"
        };

        // Submit the request
        const response = await fetch(`${BASE_URL}/check_api`, {
            method: 'POST',
            headers: {
                'apikey': API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Check submitted successfully!');
            console.log('Check ID:', result.check_id);
            console.log('Status:', result.status);

            if (result.estimated_completion_time) {
                console.log('Estimated completion time:', result.estimated_completion_time, 'seconds');
            }

            return result;
        } else {
            const errorData = await response.json();
            console.error('Error:', errorData);
            throw new Error(errorData.error?.message || 'Request failed');
        }

    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
}

// Call the function
submitCheck()
    .then(result => {
        // Handle successful submission
        console.log('Submission result:', result);
    })
    .catch(error => {
        // Handle errors
        console.error('Submission failed:', error);
    });</code></pre>
                    </div>
                </div>

                <div class="disclosure">
                    <div class="disclosure-header" onclick="toggleDisclosure(this)">
                        <span>显示 cURL 示例</span>
                        <span class="disclosure-arrow">▶</span>
                    </div>
                    <div class="disclosure-content">
                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-tabs">
                                    <button class="code-tab active" data-lang="bash">cURL</button>
                                </div>
                                <button class="copy-button" onclick="copyCode(this)">复制</button>
                            </div>
                            <div class="code-block active" data-lang="bash">
                                <pre><code class="language-bash">curl -X POST "https://api.maidalv.com/check_api" \
  -H "Content-Type: application/json" \
  -H "apikey: {YOUR_API_KEY}" \
  -d '{
    "main_product_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "other_product_images": [
      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
    ],
    "description": "Wireless bluetooth headphones",
    "ip_keywords": ["bluetooth", "wireless", "headphones"],
    "language": "en"
  }'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>响应</h3>
                <p>API 将返回包含唯一 <code>check_id</code> 的 JSON，用于后续查询结果。</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">响应</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "check_id": "1234567890123456",
  "status": "queued",
  "message": "Analysis has been queued. Use the check_status endpoint to poll for results.",
  "estimated_completion_time": 45
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- 获取结果 -->
            <section id="retrieve-results" class="section">
                <h2>获取结果</h2>
                <p><span class="badge get">GET</span> <code>/check_status/{check_id}</code></p>

                <p>获取已提交检测的分析结果。由于分析可能需要时间，您需要轮询此端点，直到状态变为 "completed"。可根据提交响应中的 <code>estimated_completion_time</code>（单位：秒）来设置合适的轮询间隔。</p>

                <h3>请求参数</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>check_id</code></td>
                                <td>string</td>
                                <td><span class="badge required">必填</span></td>
                                <td>提交端点返回的检测 ID</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>状态值说明</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>状态</th>
                                <th>描述</th>
                                <th>建议操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>queued</code></td>
                                <td>请求正在排队等待处理</td>
                                <td>20 秒后再轮询</td>
                            </tr>
                            <tr>
                                <td><code>processing</code></td>
                                <td>分析处理中</td>
                                <td>3 秒后再轮询</td>
                            </tr>
                            <tr>
                                <td><code>completed</code></td>
                                <td>分析完成，结果可用</td>
                                <td>读取并处理结果</td>
                            </tr>
                            <tr>
                                <td><code>error</code></td>
                                <td>分析失败</td>
                                <td>查看错误详情后按需重试</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>示例请求</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import time

def poll_for_results(check_id, base_url, max_attempts=120):
    """
    Poll for results with exponential backoff
    """
    attempt = 0

    while attempt &lt; max_attempts:
        try:
            response = requests.get(f"{base_url}/check_status/{check_id}")

            if response.status_code == 200:
                result = response.json()
                status = result.get("status")

                print(f"Attempt {attempt + 1}: Status = {status}")

                if status == "completed":
                    print("Analysis completed!")
                    return result.get("result")

                elif status == "error":
                    print(f"Analysis failed: {result.get('message')}")
                    return None

                elif status in ["queued", "processing"]:
                    # Wait based on status
                    wait_time = 20 if status == "queued" else 3
                    print(f"Waiting {wait_time} seconds...")
                    time.sleep(wait_time)

            else:
                print(f"HTTP Error: {response.status_code}")
                break

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            break

        attempt += 1

    print("Max attempts reached or error occurred")
    return None

# Usage example
BASE_URL = "https://api.maidalv.com"
check_id = "1234567890123456"  # From submit response

results = poll_for_results(check_id, BASE_URL)
if results:
    print("Final results:", results)
else:
    print("Failed to get results")</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">const BASE_URL = "https://api.maidalv.com";

async function pollForResults(checkId, maxAttempts = 120) {
    let attempt = 0;

    while (attempt &lt; maxAttempts) {
        try {
            const response = await fetch(`${BASE_URL}/check_status/${checkId}`);

            if (response.ok) {
                const result = await response.json();
                const status = result.status;

                console.log(`Attempt ${attempt + 1}: Status = ${status}`);

                if (status === "completed") {
                    console.log("Analysis completed!");
                    return result.result;

                } else if (status === "error") {
                    console.log(`Analysis failed: ${result.message}`);
                    return null;

                } else if (status === "queued" || status === "processing") {
                    // Wait based on status
                    const waitTime = status === "queued" ? 20000 : 3000;
                    console.log(`Waiting ${waitTime/1000} seconds...`);
                    await new Promise(resolve =&gt; setTimeout(resolve, waitTime));
                }

            } else {
                console.log(`HTTP Error: ${response.status}`);
                break;
            }

        } catch (error) {
            console.log(`Request failed: ${error}`);
            break;
        }

        attempt++;
    }

    console.log("Max attempts reached or error occurred");
    return null;
}

// Usage example
const checkId = "1234567890123456"; // From submit response

pollForResults(checkId)
    .then(results =&gt; {
        if (results) {
            console.log("Final results:", results);
        } else {
            console.log("Failed to get results");
        }
    })
    .catch(error =&gt; {
        console.error("Polling failed:", error);
    });</code></pre>
                    </div>
                </div>

                <h3>成功响应示例</h3>
                <p>当分析完成后，您将收到包含侵权详情的完整响应：</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">响应</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "status": "completed",
  "result": {
    "check_id": "1234567890123456",
    "status": "success",
    "risk_level": "High Risk",
    "results": [
      {
        "ip_type": "Trademark",
        "ip_owner": "Apple Inc.",
        "risk_level": "High Risk",
        "risk_score": 8.5,
        "text": "AirPods Pro",
        "reg_no": "US87654321",
        "ip_asset_urls": ["http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/12345/high/trademark.jpg"],
        "plaintiff_id": 12345,
        "plaintiff_name": "Apple Inc.",
        "number_of_cases": 15,
        "last_case_docket": "1:23-cv-00123",
        "last_case_date_filed": "2023-08-15",
        "risk_description": "High similarity to IP of a known TRO plaintiff. There is a high risk of litigation.",
        "report": "**Trademark Risk Assessment Report**\n\n**1. Mark Information:**\nThe analyzed product shows high similarity to registered trademark 'AirPods Pro'..."
      },
      {
        "ip_type": "Patent",
        "ip_owner": "Sony Corporation",
        "risk_level": "Medium Risk",
        "risk_score": 6.2,
        "text": "Noise cancellation technology for wireless headphones",
        "reg_no": "US10123456",
        "ip_asset_urls": ["http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/67890/high/patent_page1.jpg"],
        "plaintiff_id": 67890,
        "plaintiff_name": "Sony Corporation",
        "number_of_cases": 8,
        "last_case_docket": "1:22-cv-00456",
        "last_case_date_filed": "2022-12-03",
        "risk_description": "Moderate similarity to IP of a known TRO plaintiff. There is a medium risk of litigation.",
        "report": "**Patent Risk Assessment Report**\n\n**1. Patent Information:**\nThe analyzed product may infringe on patent US10123456..."
      },
      {
        "ip_type": "Copyright",
        "ip_owner": "Design Studio LLC",
        "risk_level": "Low Risk",
        "risk_score": 3.1,
        "text": "Wireless Headphone Design",
        "reg_no": "VA0002345678",
        "ip_asset_urls": ["https://api.maidalv.com/ip/copyright/VA0002345678.webp"]
      }
    ]
  }
}</code></pre>
                    </div>
                </div>

                <h3>响应字段说明</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>字段</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>check_id</code></td>
                                <td>string</td>
                                <td>本次检测的唯一标识</td>
                            </tr>
                            <tr>
                                <td><code>status</code></td>
                                <td>string</td>
                                <td>分析的整体状态</td>
                            </tr>
                            <tr>
                                <td><code>risk_level</code></td>
                                <td>string</td>
                                <td>总体风险评估（High/Medium/Low）</td>
                            </tr>
                            <tr>
                                <td><code>results</code></td>
                                <td>array</td>
                                <td>发现的潜在侵权项数组</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_type</code></td>
                                <td>string</td>
                                <td>IP 类型（trademark、patent、copyright）</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_owner</code></td>
                                <td>string</td>
                                <td>该知识产权的权利人</td>
                            </tr>
                            <tr>
                                <td><code>results[].risk_score</code></td>
                                <td>number</td>
                                <td>数值风险分（0–10）</td>
                            </tr>
                            <tr>
                                <td><code>results[].risk_description</code></td>
                                <td>string</td>
                                <td>风险级别的文字说明</td>
                            </tr>
                            <tr>
                                <td><code>results[].text</code></td>
                                <td>string</td>
                                <td>IP 文本内容（商标文字、专利标题或版权标题）</td>
                            </tr>
                            <tr>
                                <td><code>results[].reg_no</code></td>
                                <td>string</td>
                                <td>注册号（商标序列号、专利号或版权登记号）</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_asset_urls</code></td>
                                <td>array</td>
                                <td>查看 IP 证据图片的 URL 列表</td>
                            </tr>
                            <tr>
                                <td><code>results[].report</code></td>
                                <td>string</td>
                                <td>AI 生成的法律评估报告（可选）</td>
                            </tr>
                            <tr>
                                <td><code>results[].plaintiff_id</code></td>
                                <td>number</td>
                                <td>原告 ID（仅当该 IP 出现在 TRO 案件中时存在）</td>
                            </tr>
                            <tr>
                                <td><code>results[].plaintiff_name</code></td>
                                <td>string</td>
                                <td>原告名称（仅当该 IP 出现在 TRO 案件中时存在）</td>
                            </tr>
                            <tr>
                                <td><code>results[].number_of_cases</code></td>
                                <td>number</td>
                                <td>该原告提起的案件数量（仅当出现在 TRO 案件中时存在）</td>
                            </tr>
                            <tr>
                                <td><code>results[].last_case_docket</code></td>
                                <td>string</td>
                                <td>最近案件的法院案号（仅当出现在 TRO 案件中时存在）</td>
                            </tr>
                            <tr>
                                <td><code>results[].last_case_date_filed</code></td>
                                <td>string</td>
                                <td>最近案件的立案日期（仅当出现在 TRO 案件中时存在）</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert info">
                    <strong>关于 TRO 案件的说明：</strong> 若某 IP 未出现在法院的 TRO（临时限制令）案件中，则响应中不会包含与案件相关的字段（如 <code>plaintiff_name</code>、<code>number_of_cases</code>、<code>last_case_docket</code>、<code>last_case_date_filed</code>）。由于公司结构或个人/公司权属差异，<code>plaintiff_name</code> 可能与 <code>ip_owner</code> 不一致。
                </div>
            </section>

            <!-- 错误处理 -->
            <section id="errors" class="section">
                <h2>错误处理</h2>
                <p>API 使用标准的 HTTP 状态码，并返回统一格式的错误信息。</p>

                <h3>错误响应格式</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">错误响应</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "error": {
    "error_code": "INVALID_API_KEY",
    "message": "The provided API Key is invalid or does not exist.",
    "details": "Please check your API key and ensure it's correctly included in the request."
  }
}</code></pre>
                    </div>
                </div>

                <h3>常见错误码</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>HTTP 状态码</th>
                                <th>错误码</th>
                                <th>描述</th>
                                <th>解决方案</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>401</td>
                                <td><code>INVALID_API_KEY</code></td>
                                <td>API Key 无效或缺失</td>
                                <td>检查 API Key 是否正确并已包含在请求中</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td><code>MISSING_MAIN_IMAGE</code></td>
                                <td>缺少主产品图片</td>
                                <td>在请求中加入 <code>main_product_image</code></td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td><code>MISSING_REQUIRED_FIELD</code></td>
                                <td>缺少必填字段</td>
                                <td>根据错误详情补全对应字段</td>
                            </tr>
                            <tr>
                                <td>429</td>
                                <td><code>RATE_LIMIT_MINUTE_EXCEEDED</code></td>
                                <td>超过每分钟速率限制</td>
                                <td>等待 60 秒后再请求</td>
                            </tr>
                            <tr>
                                <td>429</td>
                                <td><code>RATE_LIMIT_DAILY_EXCEEDED</code></td>
                                <td>超过每日速率限制</td>
                                <td>等待至次日或升级套餐</td>
                            </tr>
                            <tr>
                                <td>404</td>
                                <td><code>RESULTS_NOT_FOUND</code></td>
                                <td>未找到对应的检测 ID</td>
                                <td>确认 <code>check_id</code> 是否正确</td>
                            </tr>
                            <tr>
                                <td>500</td>
                                <td><code>SERVER_ERROR</code></td>
                                <td>服务器内部错误</td>
                                <td>重试或联系支持</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>错误处理示例</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import time

def handle_api_request(url, data, max_retries=3):
    """
    Handle API request with proper error handling and retries
    """
    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, timeout=30)

            if response.status_code == 200:
                return response.json()

            elif response.status_code == 429:
                # Rate limit exceeded
                error_data = response.json()
                error_code = error_data.get('error', {}).get('error_code')

                if error_code == 'RATE_LIMIT_MINUTE_EXCEEDED':
                    print("Rate limit exceeded. Waiting 60 seconds...")
                    time.sleep(60)
                    continue
                elif error_code == 'RATE_LIMIT_DAILY_EXCEEDED':
                    print("Daily limit exceeded. Please try tomorrow.")
                    return None

            elif response.status_code == 401:
                print("Authentication failed. Check your API key.")
                return None

            else:
                # Other errors
                error_data = response.json()
                print(f"API Error: {error_data}")
                return None

        except requests.exceptions.Timeout:
            print(f"Request timeout (attempt {attempt + 1})")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None

    print("Max retries exceeded")
    return None</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">async function handleApiRequest(url, data, maxRetries = 3) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                return await response.json();

            } else if (response.status === 429) {
                // Rate limit exceeded
                const errorData = await response.json();
                const errorCode = errorData.error?.error_code;

                if (errorCode === 'RATE_LIMIT_MINUTE_EXCEEDED') {
                    console.log('Rate limit exceeded. Waiting 60 seconds...');
                    await new Promise(resolve => setTimeout(resolve, 60000));
                    continue;
                } else if (errorCode === 'RATE_LIMIT_DAILY_EXCEEDED') {
                    console.log('Daily limit exceeded. Please try tomorrow.');
                    return null;
                }

            } else if (response.status === 401) {
                console.log('Authentication failed. Check your API key.');
                return null;

            } else {
                // Other errors
                const errorData = await response.json();
                console.log('API Error:', errorData);
                return null;
            }

        } catch (error) {
            console.log(`Request failed (attempt ${attempt + 1}):`, error);
            if (attempt < maxRetries - 1) {
                // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
        }
    }

    console.log('Max retries exceeded');
    return null;
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- 速率限制 -->
            <section id="rate-limiting" class="section">
                <h2>速率限制与重试</h2>
                <p>API 实施速率限制以确保公平使用与系统稳定性。</p>

                <h3>速率限制</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>限制类型</th>
                                <th>默认限制</th>
                                <th>重置周期</th>
                                <th>错误码</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>每分钟请求数</td>
                                <td>按合同约定</td>
                                <td>60 秒</td>
                                <td><code>RATE_LIMIT_MINUTE_EXCEEDED</code></td>
                            </tr>
                            <tr>
                                <td>每日请求数</td>
                                <td>按合同约定</td>
                                <td>24 小时</td>
                                <td><code>RATE_LIMIT_DAILY_EXCEEDED</code></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert info">
                    <strong>注意：</strong> 具体速率限制由您的 Maidalv 客户经理/销售与您沟通并确认。
                </div>

                <h3>最佳实践</h3>
                <ul>
                    <li><strong>实现指数退避</strong> —— 重试间隔逐步拉长</li>
                    <li><strong>处理限流响应</strong> —— 按提示等待后再重试</li>
                    <li><strong>缓存结果</strong> —— 对已完成的分析结果进行缓存，避免重复请求</li>
                    <li><strong>批量处理</strong> —— 多个商品建议顺序处理而非并发</li>
                    <li><strong>监控用量</strong> —— 跟踪 API 用量以保持在配额内</li>
                </ul>

                <h3>重试策略示例</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import time
import random

def exponential_backoff_retry(func, max_retries=5, base_delay=1):
    """
    Retry function with exponential backoff and jitter
    """
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e

            # Calculate delay with exponential backoff and jitter
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"Attempt {attempt + 1} failed. Retrying in {delay:.2f} seconds...")
            time.sleep(delay)

    raise Exception("Max retries exceeded")

# Usage
def submit_check():
    # Your API call here
    response = requests.post(url, json=data)
    if response.status_code != 200:
        raise Exception(f"API call failed: {response.status_code}")
    return response.json()

try:
    result = exponential_backoff_retry(submit_check)
    print("Success:", result)
except Exception as e:
    print("Failed after retries:", e)</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">async function exponentialBackoffRetry(func, maxRetries = 5, baseDelay = 1000) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            return await func();
        } catch (error) {
            if (attempt === maxRetries - 1) {
                throw error;
            }

            // Calculate delay with exponential backoff and jitter
            const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
            console.log(`Attempt ${attempt + 1} failed. Retrying in ${delay/1000:.2f} seconds...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw new Error("Max retries exceeded");
}

// Usage
async function submitCheck() {
    const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
    }

    return await response.json();
}

try {
    const result = await exponentialBackoffRetry(submitCheck);
    console.log("Success:", result);
} catch (error) {
    console.log("Failed after retries:", error);
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- 常见问题 -->
            <section id="faq" class="section">
                <h2>常见问题</h2>

                <h3>通用问题</h3>

                <h4>分析需要多长时间？</h4>
                <p>通常为 30–90 秒，具体取决于产品复杂度与当前队列长度。提交请求时 API 会返回预计完成时间。</p>

                <h4>支持哪些图片格式？</h4>
                <p>支持 JPEG、PNG、WebP 和 GIF。图片可采用 Base64 编码或提供可访问的 URL。单张图片最大 10MB。</p>

                <h4>IP 分析的准确性如何？</h4>
                <p>我们的 AI 分析结合视觉相似度检测与全面的 IP 数据库。尽管准确率很高，但最终决策仍建议由法律专业人士审核。</p>

                <h4>可以一次检测多个产品吗？</h4>
                <p>每次 API 调用仅分析一个产品。若需分析多个产品，请分别提交并顺序处理，以避免触发限流。</p>

                <h4>服务器部署在哪里？</h4>
                <p>API 检测端点（https://api.maidalv.com）部署在中国境外。</p>

                <h4>IP 图片托管在哪里？</h4>
                <p>IP 证据图片（<code>results[].ip_asset_urls</code>）托管在中国境内，以提升本地区访问速度。</p>

                <h4>为何原告名称与 IP 权利人不同？</h4>
                <p>可能由于公司结构差异，或个人/公司权属不同（例如版权中艺术家为个人，而原告为公司）。</p>

                <h3>技术问题</h3>

                <h4>请求超时怎么办？</h4>
                <p>请实现合理的超时与重试逻辑。若分析时间较长，请持续轮询状态端点，而非重复提交。</p>

                <h4>如何进行 Base64 编码？</h4>
                <p>大多数编程语言都内置 Base64 编码方法。请确保编码的是原始图片字节，而非包含数据 URL 前缀的字符串。</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python"># Correct base64 encoding
import base64

with open("image.jpg", "rb") as f:
    image_data = base64.b64encode(f.read()).decode('utf-8')

# Use image_data in your API request</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Correct base64 encoding (browser)
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // Remove data URL prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
    });
}</code></pre>
                    </div>
                </div>

                <h4>IP 图片与参考图片有何区别？</h4>
                <ul>
                    <li><strong>IP 图片：</strong> 现有知识产权的图片，您认为可能与产品相关</li>
                    <li><strong>参考图片：</strong> 与您的产品相似或启发来源的产品图片</li>
                    <li><strong>产品图片：</strong> 您要检测的实际产品图片</li>
                </ul>

                <h3>故障排查</h3>

                <h4>常见问题与解决方案</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>问题</th>
                                <th>原因</th>
                                <th>解决方案</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Base64 无效错误</td>
                                <td>编码不正确或包含数据 URL 前缀</td>
                                <td>确保正确的 Base64 编码并移除前缀</td>
                            </tr>
                            <tr>
                                <td>请求超时</td>
                                <td>图片过大或网络不稳定</td>
                                <td>压缩图片或提高超时设置</td>
                            </tr>
                            <tr>
                                <td>结果为空</td>
                                <td>数据库中未找到相似 IP</td>
                                <td>属正常情况——表示未发现显著匹配</td>
                            </tr>
                            <tr>
                                <td>限流错误</td>
                                <td>短时间内请求过多</td>
                                <td>实现合理延迟与重试策略</td>
                            </tr>
                            <tr>
                                <td>图片 URL 不可访问</td>
                                <td>我们的服务器无法访问该 URL</td>
                                <td>确保 URL 公开可访问，或改用 Base64</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert success">
                    <strong>需要帮助？</strong> 若遇到本文未覆盖的问题，请联系支持团队，并提供您的检测 ID 及错误详情。
                </div>
            </section>
        </main>
    </div>

    <!-- Prism.js 语法高亮 -->
    <script src="{{ url_path_for('static', path='js/prism-core.min.js') }}" defer></script>
    <script src="{{ url_path_for('static', path='js/prism-autoloader.min.js') }}" defer></script>

    <!-- 文档脚本 -->
    <script src="{{ url_path_for('static', path='js/documentation.js') }}" defer></script>
</body>
</html>