apisix:
  node_listen:
    - 9080
  enable_http2: true
  enable_admin: true
  ssl:
    enable: true
  stream_proxy: {}
  proxy_cache: {}

deployment:
  admin:
    admin_listen:
      ip: 0.0.0.0
      port: 9180
    allow_admin:
      - *********/24     # loopback
      - **********/16    # docker bridge (adjust if different)
      - 0.0.0.0/0        # Everyone
    admin_key:
      - name: "admin"
        key: "2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ"
        role: admin

etcd:
  host:
    - "http://etcd:2379"
  prefix: /apisix
  timeout: 30

# NGINX-level knobs (client_max_body_size equivalent)
nginx_config:
  http:
    client_max_body_size: 10485760
# (You can also use *client_control* plugin per-route later if needed.) 
# See docs on nginx_config and client-control. 

plugins:
  - key-auth
  - limit-count
  - prometheus
  - consumer-restriction
  - proxy-rewrite
  - traffic-split
  # (plus the default bundled ones)

plugin_attr:
  prometheus:
    export_addr:
      ip: "0.0.0.0"
      port: 9091
