"""
Unified Qdrant-based implementation for IP asset similarity search.
This module provides functions to find similar IP assets using Qdrant vector database.
"""

import os, re, aiohttp, time
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from langfuse import observe
import langfuse

from Check.Utils import create_ip_url

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_API_URL = os.getenv("QDRANT_API_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

@observe(capture_input=False, capture_output=False)
async def find_similar_assets_qdrant(
    query_image_paths: List[str],
    check_id: str,
    client_id: str,
    ip_type: str,
    temp_dir: str,
    cases_df=None,
    plaintiff_df=None,
    plaintiff_id: Optional[str] = None,
    top_n: int = 1,
    similarity_threshold: float = 0.8,
    similarity_threshold_text: float = 0.25,
    precomputed_embeddings_map: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    metadata = {}
    langfuse.get_client().update_current_span(input={
        "query_image_paths": query_image_paths,
        "check_id": check_id, "client_id": client_id, "ip_type": ip_type,
        "plaintiff_id": plaintiff_id, "top_n": top_n,
        "similarity_threshold": similarity_threshold,
        "similarity_threshold_text": similarity_threshold_text
    })
    """
    Find similar IP assets using Qdrant vector database and prepare downloads.

    Args:
        query_image_paths (List[str]): Paths to the query images
        check_id (str): The identifier for this specific check/batch
        client_id (str): The identifier for the client submitting the batch
        ip_type (str): The type of IP assets to search for ("Copyright", "Patent", "Trademark")
        temp_dir (str): Temporary directory for downloads
        plaintiff_df: DataFrame containing plaintiff information
        plaintiff_id (str, optional): Optional plaintiff ID to filter results
        top_n (int): Number of top similar images to return
        similarity_threshold (float): Minimum cosine similarity score to consider a match for images
        similarity_threshold_text (float): Minimum cosine similarity score to consider a match for text
        precomputed_embeddings_map (Optional[Dict[str, Any]], optional): A map of pre-computed embeddings.

    Returns:
        List[Dict[str, Any]]: List of match information with download URLs and local paths prepared
    """
    
    if not query_image_paths:
        langfuse.get_client().update_current_span(output=[])
        return []

    # Separate images that need embeddings from those that have them
    prep_start_time = time.time()
    missing_embeddings = [path for path in query_image_paths if path not in precomputed_embeddings_map]
    if missing_embeddings:
        langfuse.get_client().update_current_span(output=f"MISSING EMBEDDING: {missing_embeddings}")
        return []
    
    final_embeddings_map = {path: emb for path, emb in precomputed_embeddings_map.items()}
    
    # Re-create the payload with the correct order and embeddings
    products = []
    for path in query_image_paths:
        products.append({
            "siglip_vector": final_embeddings_map[path].tolist(),
            "filename": os.path.basename(path)
        })
    
    payload = {
        "client_id": str(client_id),
        "check_id": check_id,
        "products": products,
        "search_ip_type": ip_type,
        "threshold": similarity_threshold*2-1,
        "threshold_text": similarity_threshold_text,
        "plaintiff_id": plaintiff_id,
        "top_n": top_n
    }
    metadata["prep_time"] = time.time() - prep_start_time
    
    # Make the API request to the forward_check endpoint
    qdrant_start_time = time.time()
    try:
        timeout = aiohttp.ClientTimeout(total=600)  # 10 minutes timeout
        async with aiohttp.ClientSession(timeout=timeout) as session:
            
            async with session.post(
                f"{QDRANT_API_URL}/forward_check",
                json=payload,
                headers={
                    "Authorization": f"Bearer {QDRANT_API_KEY}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status != 200:
                    metadata["qdrant_search_time"] = time.time() - qdrant_start_time
                    error_details = await response.text()
                    print(f"Error from Qdrant API: {response.status} - {error_details}")
                    langfuse.get_client().update_current_span(output={"error": error_details, "status": response.status}, metadata=metadata)
                    return []
                
                result = await response.json()
            
    except aiohttp.ClientError as e:
        print(f"Network error during Qdrant API call: {e}")
        langfuse.get_client().update_current_span(output={"error": str(e), "status": "Network Error"}, level="ERROR", metadata=metadata)
        return []
    metadata["qdrant_search_time"] = time.time() - qdrant_start_time
    
    # Process the results
    processing_start_time = time.time()
    all_matches = []
    
    for infringement in result.get("results", []):
        product_filename = infringement.get("query_filename")
        query_image_path = next((path for path in query_image_paths if os.path.basename(path) == product_filename), None)
        
        if not query_image_path:
            continue
        
        if infringement.get("ip_type") != ip_type:
            continue
        
        qdrant_metadata = infringement.get("qdrant_metadata", {}) or {}
        db_metadata = infringement.get("db_metadata", {}) or {}
        
        if qdrant_metadata and "plaintiff_id" in qdrant_metadata: # TRO infringement
            result_plaintiff_id = qdrant_metadata.get("plaintiff_id", [])
            plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id, 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == result_plaintiff_id].empty else ""
            
            match_info = {
                "product_local_path": query_image_path,
                "similarity": str(infringement.get("score", 0)/2+0.5),
                'plaintiff_id': result_plaintiff_id,
                "ip_type": infringement.get("ip_type", ""),
                "ip_owner": plaintiff_name,
                "plaintiff_name": plaintiff_name,
                # "docket": qdrant_metadata.get("docket", ""),
                # "number_of_cases": qdrant_metadata.get("number_of_cases", 0),
                "reg_no": qdrant_metadata.get("reg_no", "") or qdrant_metadata.get("patent_number", ""),
                "int_cls_list": qdrant_metadata.get("int_cls", []),  # Trademark
                "text": qdrant_metadata.get("trademark_text", "") or qdrant_metadata.get("text", ""),  # trademark_text for trademark and "text" for patent however: !!!! Trademark_text is not in NPY and not in Qdrant !!!! It is in database, field mark_text. Does trademark report use it?
                "ip_asset_urls": [],  # Needed to then addpend
            }
            
            plaintiff_id_int = int(float(result_plaintiff_id))
            if infringement.get("ip_type") == "Copyright": # It is key to have the image first, then the certificate, because Do_Check_Copyright.py assumes it.
                IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id_int}/high/{qdrant_metadata.get('filename', '')}"
                match_info["ip_asset_urls"].append(IP_Url)
                IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id_int}/high/{qdrant_metadata.get('full_filename', '')}"
                match_info["ip_asset_urls"].append(IP_Url)
                
            elif infringement.get("ip_type") == "Trademark":
                IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id_int}/high/{qdrant_metadata.get('filename', '')}"
                match_info["ip_asset_urls"].append(IP_Url)
                for full_filename in qdrant_metadata.get("full_filename", ""):
                    IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id_int}/high/{full_filename}"
                    match_info["ip_asset_urls"].append(IP_Url)
                    
            elif infringement.get("ip_type") == "Patent":
                if cases_df is None:
                    raise ValueError("cases_df is required for patent search")
                ip_image_filenames = get_tro_patent_pages(cases_df, result_plaintiff_id, qdrant_metadata.get("filename", ""), qdrant_metadata.get("full_filename", ""), qdrant_metadata.get("docket", ""))
                for ip_image_filename in ip_image_filenames:
                    IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id_int}/high/{ip_image_filename}"
                    match_info["ip_asset_urls"].append(IP_Url)
            
            # Add specific fields based on IP type
            all_matches.append(match_info)
            
        else: # Non-TRO infringement / All infringement: ultimatly we will only use this when we have theTRO flag right.
            if not db_metadata:
                continue
            
            plaintiff_name = None
            if db_metadata.get("plaintiff_id", None):
                plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == db_metadata.get("plaintiff_id", None), 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == db_metadata.get("plaintiff_id", None)].empty else ""
            
            ip_type = infringement.get("ip_type", "")
            
            match_info = {
                "product_local_path": query_image_path,
                "similarity": str(infringement.get("score", 0)/2+0.5),
                "plaintiff_id": db_metadata.get("plaintiff_id", None),
                "plaintiff_name": plaintiff_name,
                "ip_type": ip_type,
                # "docket": cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)].sort_values(by='date_filed', ascending=False)['docket'].iloc[0] if not cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)].empty else "",
                # "number_of_cases": len(cases_df[cases_df['plaintiff_id'] == db_metadata.get("plaintiff_id", None)]),
                "reg_no": db_metadata.get("reg_no", "") or db_metadata.get("document_id", ""),
                "int_cls_list": db_metadata.get("int_cls", []),  # Trademark
                "text": db_metadata.get("mark_text", "") or db_metadata.get("patent_title", ""),
                "ip_asset_urls": [],
            }

            ip_owner_val = ""
            if ip_type == "Patent":
                assignee = db_metadata.get("assignee")
                applicant = db_metadata.get("applicant")
                inventors = db_metadata.get("inventors")

                owner_source = ""
                if assignee and assignee not in ('[]', '""', "''"):
                    owner_source = assignee
                elif applicant and applicant not in ('[]', '""', "''"):
                    owner_source = applicant
                elif inventors and inventors not in ('[]', '""', "''"):
                    owner_source = inventors
                
                if owner_source:
                    # Handles "['Name 1', 'Name 2']" or "(Name)"
                    ip_owner_val = str(owner_source).strip("[]'\" ")
                    if ip_owner_val.startswith("(") and ip_owner_val.endswith(")"):
                        ip_owner_val = ip_owner_val[1:-1].strip()

            else: # Copyright, Trademark
                owner_source = db_metadata.get("applicant_name", "") or db_metadata.get("names", "") or db_metadata.get("copyright_claimant", "")
                if owner_source:
                    ip_owner_val = str(owner_source)
            
            match_info["ip_owner"] = ip_owner_val
            
            if ip_type == "Copyright":
                IP_Url = create_ip_url(ip_type, db_metadata["reg_no"])
                match_info["ip_asset_urls"].append(IP_Url)
            elif ip_type == "Trademark":
                IP_Url = create_ip_url(ip_type, db_metadata["ser_no"])
                match_info["ip_asset_urls"].append(IP_Url)
            elif ip_type == "Patent":
                fig_files = db_metadata["fig_files"]
                fig_files = sorted(fig_files)
                for fig_file in fig_files:
                    IP_Url = create_ip_url(ip_type, fig_file.split(".")[0])
                    match_info["ip_asset_urls"].append(IP_Url)

            all_matches.append(match_info)
    
    metadata["processing_time"] = time.time() - processing_start_time
    
    # Return top N results with download information
    langfuse.get_client().update_current_span(output=all_matches, metadata=metadata)
    return all_matches

        
        
def get_tro_patent_pages(cases_df, plaintiff_id, filename, full_filename, docket):
    # Helper function to extract page number robustly using regex
    def extract_page_number(filename_str):
        match = re.search(r'page(\d+)', filename_str)
        if match:
            return int(match.group(1))
        elif "_full" in filename_str: # the certificate comes first
            return 0
        
        # Fallback if page number is not found. This might indicate an unexpected filename format.
        # Depending on requirements, you could raise an error or return a default (e.g., 0 or float('inf')).
        print(f"Warning: Could not extract page number from '{filename_str}'. Defaulting to 0 for sorting.")
        return 99

    if isinstance(filename,str):
        row = cases_df[(cases_df['docket'] == docket) & (cases_df['plaintiff_id'] == plaintiff_id)].iloc[0]
        all_pages = [key for key in row['images']['patents'].keys() if row['images']['patents'][key]['full_filename'][0] == full_filename]
        full_patent = all_pages + [full_filename]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    else:
        full_patent = filename + [full_filename]
        # Sort by page number
        full_patent = sorted(full_patent, key=extract_page_number)
    return full_patent
