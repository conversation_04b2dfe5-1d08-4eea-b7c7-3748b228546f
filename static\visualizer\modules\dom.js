// DOM Elements
export const applyFiltersBtn = document.getElementById('apply-filters');
export const refreshAllDataBtn = document.getElementById('refresh-all-data');
export const refreshSelectionBtn = document.getElementById('refresh-selection-data');
export const casesContainer = document.getElementById('cases-container');
export const totalResultsEl = document.getElementById('total-results');
export const lastRefreshEl = document.getElementById('last-refresh');
export const prevPageBtn = document.getElementById('prev-page');
export const nextPageBtn = document.getElementById('next-page');
export const pageInfoEl = document.getElementById('page-info');
export const caseTypeCheckboxes = document.querySelectorAll('input[name="case_type"]');
export const caseTypeDropdownHeader = document.getElementById('case-type-dropdown-header');
export const caseTypeDropdownContent = document.getElementById('case-type-dropdown-content');

// Filter inputs
export const caseNumberInput = document.getElementById('case_number');
export const plaintiffNameInput = document.getElementById('plaintiff_name');
export const plaintiffIdInput = document.getElementById('plaintiff_id');
export const validationStatusInput = document.getElementById('validation_status');
export const ipSourceInput = document.getElementById('ip_source');
export const pictureTypeInput = document.getElementById('picture_type');
export const dateFromInput = document.getElementById('date_from');
export const dateToInput = document.getElementById('date_to');
export const sortByInput = document.getElementById('sort_by');
export const sortOrderInput = document.getElementById('sort_order');
export const limitInput = document.getElementById('limit');