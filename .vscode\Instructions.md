You need to add this to C:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json  (File -> preference -> key binding -> icon on top right = key bingind json)

    {

    "key": "f5", "command": "-workbench.action.debug.start"
    },
    {
        "key": "f5",
        "command": "debug.startFromConfig",
        "when": "editorTextFocus && editorLangId == 'python' && debugState == 'inactive'",
        "args": {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        }
    },
    {
        "key": "f5",
        "command": "PowerShell.Debug.Start",
        "when": "editorTextFocus && editorLangId == 'powershell' && debugState == 'inactive'"
    }
