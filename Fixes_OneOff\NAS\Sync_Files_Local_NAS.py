import os
from FileManagement.NAS import NASConnection
from Common.Constants import local_case_folder, nas_case_folder

def sync_local_to_nas():
    nas = NASConnection()
    
    # Example usage:
    # sftp, transport = connect_to_nas()
    for case_name in os.listdir(local_case_folder):
        if os.path.isdir(os.path.join(local_case_folder, case_name)):
            nb_file = len(os.listdir(os.path.join(local_case_folder, case_name)))
            if nb_file > 0:
                local_folder = os.path.join(local_case_folder, case_name)
                remote_folder = f"{nas_case_folder}/{case_name}"

                if not nas.ssh_exists(remote_folder):
                    print(f"Folder {case_name} does not exist, copying it over")
                    nas.ssh_local_to_nas(local_folder, remote_folder)
            else: 
                print(f"Folder {case_name} has only no file, skipping")


def sync_nas_to_local():
    nas = NASConnection()
    remote_cases = nas.list_remote_directory(nas_case_folder)
    for i, case_name in enumerate(remote_cases):
        print(f"Processing {i+1}/{len(remote_cases)}: {case_name}")
        if nas.ssh_is_dir(f"{nas_case_folder}/{case_name}"):
            nb_file = len(nas.ssh_get_folder_files_names(f"{nas_case_folder}/{case_name}"))
            if nb_file > 0:
                remote_folder = f"{nas_case_folder}/{case_name}"
                local_folder = os.path.join(local_case_folder, case_name)

                if not os.path.exists(local_folder):
                    print(f"  Folder {case_name} does not exist locally, copying it over")
                    nas.ssh_nas_to_local(remote_folder, local_folder)
                else:
                    print(f"  Folder {case_name} exists locally, skipping")
            else: 
                print(f"  Folder {case_name} has only no file, skipping")

if __name__ == "__main__":
    # sync_local_to_nas()
    sync_nas_to_local()

