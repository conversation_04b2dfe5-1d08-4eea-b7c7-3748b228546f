from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.lib.colors import HexColor
from reportlab.lib.units import mm

# ---- Color palette (shared across report) ----
colors = {
    # Core
    'navy': HexColor('#043057'),
    'white': HexColor('#FFFFFF'),
    'black': HexColor('#000000'),
    'muted': HexColor('#6B7280'),
    'light_gray': HexColor('#E7E7E7'),
    'whitesmoke': HexColor('#F5F5F5'),
    # Accents
    'accent_bg': HexColor('#F3F4F6'),
    'line': HexColor('#D1D5DB'),
    # Risk badges (kept for compatibility)
    'risk_high_bg': HexColor('#FDE2E2'),
    'risk_high_text': HexColor('#B91C1C'),
    'risk_high_border': HexColor('#F19999'),
    'risk_med_bg': HexColor('#FEF3C7'),
    'risk_med_text': HexColor('#B45309'),
    'risk_med_border': HexColor('#F59E0B'),
    'risk_low_bg': HexColor('#DCFCE7'),
    'risk_low_text': HexColor('#065F46'),
    'risk_low_border': HexColor('#6EE7B7'),
}

def build_styles():
    """Create and return a dict of ParagraphStyle objects."""
    styles = getSampleStyleSheet()
    
    # ---- PDF pages styles ----
    # Modify existing Title style
    title_style = styles['Title']
    title_style.fontName = 'Helvetica-Bold'
    title_style.fontSize = 16
    title_style.leading = 20
    title_style.textColor = colors['white']
    title_style.alignment = TA_CENTER
    
    # Modify existing h2 style for SectionTitle
    section_title_style = styles['h2']
    section_title_style.fontName = 'Helvetica-Bold'
    section_title_style.fontSize = 13
    section_title_style.textColor = colors['navy']
    section_title_style.alignment = TA_CENTER
    
    
    styles.add(ParagraphStyle(
        name='BrandMeta',
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=12,
        textColor=colors['navy'],
        alignment=TA_CENTER
    ))

    styles.add(ParagraphStyle(
        name='MetaGridLabelCentered',
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=10,
        textColor=colors['muted'],
        alignment=TA_CENTER
    ))

    styles.add(ParagraphStyle(
        name='MetaGridValueCentered',
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=11,
        textColor=colors['navy'],
        alignment=TA_CENTER
    ))
    
    styles.add(ParagraphStyle(
        name='RiskBadge',
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=11,
        alignment=TA_CENTER,
    ))
    
    
    styles.add(ParagraphStyle(
        name='SectionTitle',
        parent=section_title_style
    ))

    styles.add(ParagraphStyle(
        name='SectionTitleLeft',
        parent=section_title_style,
        alignment=TA_LEFT
    ))
    
    styles.add(ParagraphStyle(
        name='SummaryHeader',
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=9.5,
        textColor=colors['navy'],
        alignment=TA_LEFT
    ))
    
    styles.add(ParagraphStyle(
        name='SummaryBody',
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        fontSize=9.5,
        textColor=colors['navy'],
        alignment=TA_CENTER
    ))
    
    styles.add(ParagraphStyle(
        name='ReportBody',
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=9.5,
        textColor=colors['navy'],
        alignment=TA_LEFT
    ))

    # ---- Legal Report styles ----
    # Title (H1) – centered, generous spacing, keep with next
    styles.add(ParagraphStyle(
        name='H1',
        parent=styles['Heading1'],
        fontName='Helvetica-Bold',
        fontSize=16,
        leading=20,
        alignment=TA_CENTER,
        textColor=colors['navy'],
        spaceBefore=12,
        spaceAfter=8,
        keepWithNext=1,
    ))

    # H2 – left aligned
    styles.add(ParagraphStyle(
        name='H2',
        parent=styles['Heading2'],
        fontName='Helvetica-Bold',
        fontSize=13,
        leading=16,
        alignment=TA_LEFT,
        textColor=colors['navy'],
        spaceBefore=8,
        spaceAfter=4,
        keepWithNext=1,
    ))

    # H3 – left aligned
    styles.add(ParagraphStyle(
        name='H3',
        parent=styles['Heading3'],
        fontName='Helvetica-Bold',
        fontSize=11.5,
        leading=14,
        alignment=TA_LEFT,
        textColor=colors['navy'],
        spaceBefore=6,
        spaceAfter=2,
        keepWithNext=1,
    ))

    # Body – increase leading for readability, small paragraph spacing
    styles.add(ParagraphStyle(
        name='Body',
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=9.5,
        leading=13,
        spaceBefore=0,
        spaceAfter=2,
    ))

    # List bullet paragraph (for items inside ListFlowable) with hanging indent
    styles.add(ParagraphStyle(
        name='ListBullet',
        parent=styles['Body'],
        leftIndent=12,
        firstLineIndent=-6,  # hanging indent
        spaceBefore=0,
        spaceAfter=2,
    ))

    # Callout – for Overall Statement / Conclusion
    styles.add(ParagraphStyle(
        name='Callout',
        parent=styles['Body'],
        backColor=colors['accent_bg'],
        borderColor=colors['line'],
        borderWidth=0.5,
        borderPadding=4,
        spaceBefore=6,
        spaceAfter=6,
    ))

    # Quote / Blockquote
    styles.add(ParagraphStyle(
        name='Quote',
        parent=styles['Body'],
        leftIndent=10,
        textColor=colors['muted'],
    ))

    # Code
    styles.add(ParagraphStyle(
        name='myCode',
        fontName='Courier',
        fontSize=8.5,
        leading=11,
        backColor=colors['whitesmoke'],
        borderColor=colors['line'],
        borderWidth=0.5,
        borderPadding=4,
        leftIndent=6,
        rightIndent=6,
        spaceBefore=4,
        spaceAfter=4,
    ))

    # Table cell / header
    styles.add(ParagraphStyle(
        name='TableCell',
        parent=styles['Body'],
        spaceBefore=0,
        spaceAfter=0,
    ))
    styles.add(ParagraphStyle(
        name='TableHeader',
        parent=styles['Body'],
        fontName='Helvetica-Bold',
        textColor=colors['navy'],
    ))

    # Key/value (Mark Information table)
    styles.add(ParagraphStyle(
        name='MetaKey',
        parent=styles['Body'],
        fontName='Helvetica-Bold',
        textColor=colors['muted'],
    ))
    styles.add(ParagraphStyle(
        name='MetaValue',
        parent=styles['Body'],
    ))

    return styles

# Export a global styles dict for convenience
myStyles = build_styles()