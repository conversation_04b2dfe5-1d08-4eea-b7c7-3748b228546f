import asyncio, contextlib, io, json, re, sys, zlib, base64, copy
from datetime import datetime
import langfuse
import pandas as pd
import numpy as np

# Import the functions that the task will need
import Check.Do_Check as Do_Check
import CheckLite.Do_Check_Lite as Do_Check_Lite
from Check.Do_Check_Download import ImageDownloadError
from DatabaseManagement.ImportExport import get_gz_connection, insert_and_update_df_to_GZ_batch
from Check.Utils import insert_check_api_result_to_pg

# Define custom exception for tasks

# Moved from app_apistudio.py
def ansi_to_html(text):
    """Convert ANSI color codes to more readable format with color circle emojis for Langfuse UI"""
    ansi_to_emoji = {
        '\033[0m': '',
        '\033[31m': '🔴 ',  # Red
        '\033[32m': '🟢 ',  # Green
        '\033[33m': '🟠 ',  # Orange
        '\033[91m': '🔴 ',  # Light Red
        '\033[92m': '🟢 ',  # Light Green
        '\033[93m': '🟡 ',  # Light Yellow
    }
    # A simple regex to find all ANSI color codes
    ansi_escape = re.compile(r'(\033\[[0-9;]*m)')
    
    # Replace each code with its emoji equivalent
    for code, emoji in ansi_to_emoji.items():
        text = text.replace(code, emoji)
        
    # Remove any remaining unmapped codes
    text = ansi_escape.sub('', text)
    return text


# Moved from app_apistudio.py
@contextlib.contextmanager
def capture_stdout():
    """Context manager to capture stdout and return it as a string while still printing to console."""
    captured_output = io.StringIO()
    original_stdout = sys.stdout
    
    class TeeOutput:
        def write(self, data):
            captured_output.write(data)
            original_stdout.write(data)
            
        def flush(self):
            captured_output.flush()
            original_stdout.flush()
    
    sys.stdout = TeeOutput()
    try:
        yield captured_output
    finally:
        sys.stdout = original_stdout

# This is the ARQ task. The `bind=True` argument makes the task instance (`self`) available.
async def process_check_task(ctx, check_id, client_id, client_name, langfuse_trace_id, product_category, api_key, language='zh', **kwargs):
    """
    ARQ task to perform the check and store results in the database.
    This function is based on the original process_check_with_db.
    """
    captured_output = None
    connection = None
    
    # Wrap the entire task in a Langfuse span to ensure continuity
    with langfuse.get_client().start_as_current_span(
        name="arq-process-check-task",
        trace_context={"trace_id": langfuse_trace_id}
    ) as task_span:
        # Sanitize kwargs for logging to avoid storing large base64 images
        log_kwargs = copy.deepcopy(kwargs)
        image_keys = ['main_product_image', 'other_product_images', 'client_ip_images', 'reference_images']
        for key in image_keys:
            if key in log_kwargs:
                value = log_kwargs[key]
                if isinstance(value, list):
                    for i in range(len(value)):
                        if isinstance(value[i], str) and len(value[i]) > 500:
                            log_kwargs[key][i] = f"[base64 image: {len(value[i])} chars]"
                elif isinstance(value, str) and len(value) > 500:
                    log_kwargs[key] = f"[base64 image: {len(value)} chars]"
        task_span.update(input=log_kwargs)
        
        try:
            # Capture all stdout during the entire execution
            with capture_stdout() as captured_output:
                print(f"📥 Starting download_and_check for check_id: {check_id}")
                
                # The `download_and_check` is an async function, so we can await it directly.
                json_results = await Do_Check.download_and_check(
                    check_id=check_id,
                    client_id=client_id,
                    client_name=client_name,
                    api_key=api_key,
                    product_category=product_category,
                    language=language,
                    **kwargs
                )

                # Store result in database
                print(f"📤 Storing result in database for check_id: {check_id}")
                
                # The result data now needs to be converted to a DataFrame for insertion.
                if client_name in ["MiniApp", "H5", "MiniAppDev", "H5Dev"]:
                    db_result_data = {
                        'check_id': int(check_id),
                        'check_date': datetime.now().date(),
                        'result': json.dumps(json_results)  # Serialize JSON results to string
                    }
                    if client_name in ["MiniApp", "H5"]:
                        insert_and_update_df_to_GZ_batch(pd.DataFrame([db_result_data]), 'tb_case_check_result', 'check_id')
                    else: # MiniAppDev, H5Dev
                        connection = get_gz_connection(host="maidalv.com", port=3307)
                        insert_and_update_df_to_GZ_batch(pd.DataFrame([db_result_data]), 'tb_case_check_result', 'check_id', conn=connection)
                else:
                    # For API clients, insert into the new PostgreSQL table
                    pg_result_data = {
                        'check_id': int(check_id),
                        'result': json_results, # Store as JSONB
                        'check_status': 1 # 1 for completed
                    }
                    insert_check_api_result_to_pg(pg_result_data)
                
                print(f"✅ Successfully processed and stored result for check_id: {check_id}")

                # Log success inside the try block
                log_output = captured_output.getvalue()
                with langfuse.get_client().start_as_current_span(name="Success Logs") as log_span:
                    log_span.update(input=ansi_to_html(log_output), output="success")

                return json_results

        except ImageDownloadError as e:
            error_reason = str(e)  # This will now be the specific reason, e.g., 'invalid_url'
            error_msg = f"Image download failed. Reason: {error_reason}"
            print(f"🔥 {error_msg} for check_id {check_id}")
            task_span.update(output={"error": error_msg}, level='ERROR')
            
            # Pass the specific error reason in the details
            failure_meta = {
                'exc_type': 'ImageDownloadError',
                'exc_message': "Failed to download one or more images.", # Generic message
                'error_code': 'IMAGE_DOWNLOAD_FAILED',
                'details': error_reason # Pass the specific failure reason here
            }
            # With ARQ, we just raise the exception. The result backend will store it.
            raise Exception(failure_meta)
            
        except Exception as e:
            import traceback
            error_msg = f"An unexpected error occurred in task for check_id {check_id}: {str(e)}"
            tb = traceback.format_exc()
            print(f"🔥 {error_msg}\n{tb}")
            
            task_span.update(output={"error": error_msg, "traceback": tb}, level='ERROR')
            
            # Use the new, simplified generic job failure code
            failure_meta = {
                'exc_type': type(e).__name__,
                'exc_message': error_msg,
                'error_code': 'JOB_FAILED'
            }
            # With ARQ, we just raise the exception. The result backend will store it.
            raise Exception(failure_meta)
        finally:
            # Ensure the database connection is closed
            if connection and connection.is_connected():
                connection.close()
            print(f"Task for check_id {check_id} finished.")


async def process_check_lite_task(ctx, check_id, client_id, client_name, api_key, product_category, main_product_image, ip_keywords, description, langfuse_trace_id, language='zh'):
    """
    ARQ task to perform the lite check.
    """
    try:
        with langfuse.get_client().start_as_current_span(
            name="arq-process-check-task",
            trace_context={"trace_id": langfuse_trace_id}
        ) as task_span:
            task_span.update(input=f"check_id: {check_id}, client_id: {client_id}, client_name: {client_name}, api_key: {api_key}, product_category: {product_category}, main_product_image: {main_product_image}, ip_keywords: {ip_keywords}, description: {description}, language: {language}")  # Add input data to the span
            
            # The `download_and_check` is an async function, so we can await it directly.
            json_results = await Do_Check_Lite.download_and_check(
                check_id=check_id,
                client_id=client_id,
                client_name=client_name,
                api_key=api_key,
                product_category=product_category,
                main_product_image=main_product_image,
                ip_keywords=ip_keywords,
                description=description,
                language=language
            )
            return json_results
    except Exception as e:
        import traceback
        error_msg = f"An unexpected error occurred in lite task for check_id {check_id}: {str(e)}"
        tb = traceback.format_exc()
        print(f"🔥 {error_msg}\n{tb}")
        
        failure_meta = {
            'exc_type': type(e).__name__,
            'exc_message': error_msg,
            'error_code': 'JOB_FAILED'
        }
        task_span.update(output=failure_meta)
        raise Exception(failure_meta)
