from multiprocessing import Pool, get_context, Manager
import os
import atexit
import platform
import pytesseract
import cv2
import numpy as np
from logdata import log_message
from Alerts.PicturesProcessing.ProcessPicturesShared import convert_page_number_to_image, crop_white_space
import fitz
from PIL import Image
import contextlib
import sys
import fitz # Added for merge_pdfs
import os # Added for merge_pdfs
from Common.cpu_count import get_allocated_cpus

os.environ['OMP_THREAD_LIMIT'] = '1'
# OMP_THREAD_LIMIT=1 setting only affects OpenMP parallelization, which is used internally by Tesseract OCR. It is critical for Tesseract to run at ok speed in a container. It won't affect:
# 1. Python's threading (threading module)
# 2. Python's multiprocessing (multiprocessing module)
# 3. <PERSON>lask's thread handling
# 4. Your start_task endpoint or any other threading in your application

if os.name == "nt":
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
else:
    pytesseract.pytesseract.tesseract_cmd = 'tesseract'
    

class OCRProcessor:
    _pool = None
    _pool_pid = None
    _num_processes = None

    # Add resizing constants
    THUMBNAIL_MAX_SIZE = 40 * 1024  # 40 KB
    HIGH_RES_MAX_SIZE = 300 * 1024  # 300 KB
    THUMBNAIL_WIDTH = 300
    HIGH_RES_WIDTH = 1600


    ### Pool related methods
    @classmethod
    def get_pool(cls):
        """OS-aware pool creation with PID check"""
        current_pid = os.getpid()
        if cls._pool is None or cls._pool_pid != current_pid:
            cls.shutdown()
            
            # Platform-specific configuration
            cls._num_processes = get_allocated_cpus()
            if platform.system() == "Windows":
                ctx = get_context('spawn')
            else:
                ctx = get_context('spawn')

            cls._pool = ctx.Pool(
                processes=cls._num_processes,
                initializer=cls.init_worker
            )
            cls._pool_pid = current_pid  # Store current PID after pool creation
            log_message(f"            📊 Created {cls._num_processes} worker processes ({platform.system()})")
        return cls._pool
    
    @classmethod
    def shutdown(cls):
        """Clean up the process pool without resetting PID"""
        if cls._pool is not None:
            log_message("🔌 Shutting down OCR process pool")
            cls._pool.close()
            cls._pool = None
        # Keep _pool_pid until new pool is created

    @staticmethod
    def init_worker():
        """Initialize worker processes"""
        # Set Tesseract path
        if platform.system() == "Windows":
            pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        else:
            pytesseract.pytesseract.tesseract_cmd = 'tesseract'
        log_message(f"            🛠️ Initialized OCR worker (PID: {os.getpid()})")



    ### PDF processing methods  
    @classmethod
    def process_pdf(cls, pdf_document, page_numbers=None):
        """Main entry point for PDF OCR processing using multiprocessing"""
        if isinstance(pdf_document, str):
            pdf_document = fitz.open(pdf_document)

        if page_numbers is None:
            page_numbers = range(pdf_document.page_count)
            
        log_message(f"            🔍 Processing {len(page_numbers)} p.a.g.e(s) in {os.path.basename(pdf_document.name)} using {cls._num_processes} workers")
        page_text = {}
        page_ocr_data = {}
        page_images = {}
        pdf_path = pdf_document.name

        pool = cls.get_pool()
        results = pool.starmap(cls._text_in_page, 
                             [(pdf_path, page_num, True) for page_num in page_numbers])

        for page_number, (text, ocr_data, img_path) in zip(page_numbers, results):
            page_text[page_number+1] = text
            page_ocr_data[page_number+1] = ocr_data
            page_images[page_number+1] = img_path

        full_text = " ".join(page_text.values())
        return full_text, page_text, page_ocr_data, page_images

    @staticmethod
    def process_single_page(pdf_document, page_num, save_image=False):
        """Direct single-page processing without multiprocessing"""
        return OCRProcessor._text_in_page(pdf_document.name, page_num, save_image)

    @staticmethod
    def _text_in_page(pdf_source, page_num, save_image=False):
        """Internal method for processing a single page"""
        try: 
            # Open the PDF document in this process
            if isinstance(pdf_source, fitz.Document):
                pdf_document = pdf_source  # Use existing document
                should_close = False  # Don't close document we didn't open
                pdf_path = pdf_document.name  # Get path from document
            else:
                pdf_document = fitz.open(pdf_source)  # Open new document
                should_close = True  # We should close this document
                pdf_path = pdf_source
            
            if page_num >= len(pdf_document):
                if should_close:
                    pdf_document.close()
                return "", {}, None
            
            # Extract text directly from PDF
            page = pdf_document[page_num]
            text = page.get_text()
            
            # Render page as image for OCR
            img = convert_page_number_to_image(pdf_document, page_num + 1)
            pdf_folder = os.path.splitext(pdf_path)[0]
            pdf_name_no_ext = os.path.splitext(os.path.basename(pdf_path))[0]
            img_path = None
            
            if save_image:
                os.makedirs(pdf_folder, exist_ok=True)
                img_path = os.path.join(pdf_folder, f"{pdf_name_no_ext}_page{page_num+1}_0.jpg")
                cv2.imwrite(img_path, img)

            # Close the document
            if should_close:
                pdf_document.close()
            
            if img.shape[1] > img.shape[0]:
                img = np.rot90(img, k=1)
            
            ocr_text, ocr_data = OCRProcessor.text_in_image(img)

            log_message(f"_text_in_page: {pdf_path}: page {page_num+1} done.")

        except Exception as e:
            log_message(f"Error in _text_in_page: {e}")
            return "", {}, None

        return text + " " + ocr_text, ocr_data, img_path

    @staticmethod
    def text_in_image(img):
        """Public static method for single image processing"""
        try: 
            if len(img.shape) == 3: 
                img_bw = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                img_bw = img
            
            cropped_image_bw, (x_offset, y_offset) = crop_white_space(img_bw)

            ocr_data = pytesseract.image_to_data(cropped_image_bw, output_type=pytesseract.Output.DICT)  # 125 sec (79 pages on server)
            # ocr_data = pytesseract.image_to_data(cropped_image_bw, lang='eng', output_type=pytesseract.Output.DICT) # 118 sec
            # ocr_data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)  # 134 sec

            # Adjust coordinates in ocr_data using actual offsets
            ocr_data['left'] = [x + x_offset for x in ocr_data['left']]
            ocr_data['top'] = [y + y_offset for y in ocr_data['top']]

            ocr_text = ' '.join([word for word in ocr_data['text'] if word.strip()])
            return ocr_text, ocr_data
        except Exception as e: # This fails if the image is too too big
            log_message(f"Error in text_in_image: {e}")
            return "", {}



    ### Image resize related methods
    @staticmethod
    @contextlib.contextmanager
    def suppress_stderr():
        old_stderr = sys.stderr
        devnull_file = None
        old_stderr_fd = None
        null_fd = None

        try:
            # Try file descriptor approach first (works in normal environments)
            if hasattr(sys.stderr, 'fileno'):
                try:
                    null_fd = os.open(os.devnull, os.O_RDWR)
                    old_stderr_fd = os.dup(sys.stderr.fileno())
                    os.dup2(null_fd, sys.stderr.fileno())
                    yield
                    return
                except (AttributeError, OSError):
                    # Fall through to file object approach
                    pass
            
            # Fallback: file object approach (works with Celery's LoggingProxy)
            devnull_file = open(os.devnull, 'w')
            sys.stderr = devnull_file
            yield
            
        finally:
            # Restore original stderr
            sys.stderr = old_stderr
            
            # Clean up file descriptor resources
            if old_stderr_fd is not None:
                try:
                    os.dup2(old_stderr_fd, sys.stderr.fileno())
                    os.close(old_stderr_fd)
                except OSError:
                    pass
            
            if null_fd is not None:
                try:
                    os.close(null_fd)
                except OSError:
                    pass
            
            # Clean up file object
            if devnull_file is not None:
                try:
                    devnull_file.close()
                except OSError:
                    pass
                
    # Add resizing methods
    @staticmethod
    def create_resized_image(images_directory, image_filename, output_path):
        try:
            image_path = os.path.join(images_directory, image_filename)
            with OCRProcessor.suppress_stderr():
                img = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
            
            if img is None:
                raise ValueError(f"Failed to read image: {image_path}")

            low_path = os.path.join(output_path, "images", "low", os.path.splitext(image_filename)[0] + '.webp')
            high_path = os.path.join(output_path, "images", "high", os.path.splitext(image_filename)[0] + '.webp')
            
            os.makedirs(os.path.dirname(low_path), exist_ok=True)
            os.makedirs(os.path.dirname(high_path), exist_ok=True)
            
            OCRProcessor.process_image(img, OCRProcessor.THUMBNAIL_WIDTH, OCRProcessor.THUMBNAIL_MAX_SIZE, low_path)
            OCRProcessor.process_image(img, OCRProcessor.HIGH_RES_WIDTH, OCRProcessor.HIGH_RES_MAX_SIZE, high_path)

            return os.path.splitext(image_filename)[0] + '.webp'
        except Exception as e:
            print(f"Failed to process image {image_path}: {e}")
            return None

    @staticmethod
    def process_image(img, target_width, max_size, output_path):
        height, width = img.shape[:2]
        if width > target_width:
            scale = target_width / width
            new_height = int(height * scale)
            img = cv2.resize(img, (target_width, new_height), interpolation=cv2.INTER_AREA)

        for quality in range(95, 0, -10):
            encode_param = [int(cv2.IMWRITE_WEBP_QUALITY), quality]
            result, encoded_img = cv2.imencode('.webp', img, encode_param)
            
            if not result:
                continue

            size = len(encoded_img)
            if size <= max_size or quality <= 25:
                output_path = os.path.splitext(output_path)[0] + '.webp'
                with open(output_path, 'wb') as f:
                    f.write(encoded_img)
                break

    @classmethod
    def save_pdf_pages_batch(cls, save_pdf_page_tasks):
        """
        Process a batch of image saving tasks in parallel
        
        Args:
            image_tasks: List of tuples (image, output_path, options)
                - image: CV2 image object or path to image file
                - output_path: Path where to save the processed image
                - options: List of CV2 image writing options
                
        Returns:
            List of paths to saved images
        """
        print(f"🔄 Processing batch of {len(save_pdf_page_tasks)} images in parallel")
        
        # Get the process pool and map the tasks
        pool = cls.get_pool()
        results = pool.map(cls._save_single_pdf_page_image, save_pdf_page_tasks)
        
        return results


    @staticmethod
    def _save_single_pdf_page_image(task):
        """
        Helper function to save a single image from a PDF
        
        Args:
            task: Tuple of (pdf_path, page_num, output_path, options)
        
        Returns:
            Path to saved image or None on error
        """
        pdf_path, page_num, output_path, options = task
        try:
            # If img is a string (path), load the image
            if isinstance(pdf_path, str):
                pdf_document = fitz.open(pdf_path)
                img = convert_page_number_to_image(pdf_document, page_num)
                pdf_document.close()
                
            # Ensure the directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save the image
            cv2.imwrite(output_path, img, options)
            return output_path
        except Exception as e:
            log_message(f"Error saving image to {output_path}: {e}")
            return None
        

    @staticmethod
    def merge_pdfs(pdf_list, output_path):
        """
        Merges multiple PDF files into a single PDF file.

        Args:
            pdf_list (list): A list of file paths for the PDFs to merge.
            output_path (str): The file path for the merged output PDF.
        """
        merged_pdf = fitz.open()  # Create a new empty PDF
        total_pages_merged = 0

        try:
            for pdf_path in pdf_list:
                source_pdf = fitz.open(pdf_path)
                pages_in_source = source_pdf.page_count

                if pages_in_source > 0:
                    merged_pdf.insert_pdf(source_pdf) # Append all pages from source_pdf
                    total_pages_merged += pages_in_source
                    # print(f"Appended {pages_in_source} pages from: {os.path.basename(pdf_path)} (Total: {total_pages_merged})")

                source_pdf.close()

            if merged_pdf.page_count > 0:
                merged_pdf.save(output_path, garbage=4, deflate=True) # Save the merged PDF with optimization
                print(f"\nSuccessfully merged {merged_pdf.page_count} pages into {output_path}")

        except Exception as e:
            print(f"\nAn error occurred during merging: {e}")
        finally:
            # Ensure merged_pdf is closed
            with contextlib.suppress(NameError, AttributeError):
                if 'merged_pdf' in locals() and merged_pdf and not merged_pdf.is_closed:
                    merged_pdf.close()


# Clean up when process exits
atexit.register(OCRProcessor.shutdown)