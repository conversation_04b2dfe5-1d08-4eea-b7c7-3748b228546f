import os
import re
import sys
from datetime import datetime, timedelta
import asyncio
from bs4 import BeautifulSoup
from dateutil.parser import parse
from pytz import timezone

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from Scraper.ChineseWebsite_OutOfTheFlow.common_scraper import run_scraper, setup_logger, CHINA_TZ

def _parse_date_maijiazhichi(date_str: str) -> datetime | None:
    """
    Parses a date string from maijiazhichi.com, which can be relative (e.g., '7小时前') or absolute (e.g., '2025年8月5日').
    """
    if not date_str:
        return None
    now = datetime.now(CHINA_TZ)
    try:
        if '前' in date_str:
            if '天' in date_str:
                days = int(re.search(r'\d+', date_str).group())
                return now - timedelta(days=days)
            elif '小时' in date_str:
                hours = int(re.search(r'\d+', date_str).group())
                return now - timedelta(hours=hours)
            elif '分钟' in date_str:
                minutes = int(re.search(r'\d+', date_str).group())
                return now - timedelta(minutes=minutes)
        else:
            return CHINA_TZ.localize(parse(date_str.replace('年', '-').replace('月', '-').replace('日', '')))
    except (ValueError, TypeError, AttributeError):
        return None

def _extract_case_numbers_maijiazhichi(text: str) -> list[str]:
    """
    Extracts case numbers (e.g., '2025-cv-09493') from a given text.
    """
    if not text:
        return []
    return re.findall(r'\d+-cv-\d+', text, re.IGNORECASE)

def parse_html_maijiazhichi(html_content: str) -> list[dict]:
    """
    Parses the HTML content from maijiazhichi.com and extracts data from each article.
    """
    if not html_content:
        return []
    soup = BeautifulSoup(html_content, 'html.parser')
    articles = []
    for item in soup.select('li.item'):
        title_element = item.select_one('h2.item-title a')
        date_element = item.select_one('span.item-meta-li.date')
        views_element = item.select_one('span.item-meta-li.views')

        if not all([title_element, date_element, views_element]):
            continue

        title = title_element.get_text(strip=True)
        url = title_element.get('href')
        date_str = date_element.get_text(strip=True)
        views_str = views_element.get_text(strip=True)

        case_numbers_title = _extract_case_numbers_maijiazhichi(title)
        if not case_numbers_title:
            continue

        posting_date = _parse_date_maijiazhichi(date_str)
        views = int(re.search(r'\d+', views_str.replace('K', '000').replace('W', '0000')).group()) if views_str else 0

        articles.append({
            'posting_date': posting_date.date() if posting_date else None,
            'docket_in_title': case_numbers_title[0] if case_numbers_title else None,
            'views': views,
            'url': url,
        })
    return articles

MAIJIAZHICHI_CONFIG = {
    'site_name': "Maijiazhichi",
    'base_url': "https://maijiazhichi.com/news/page/{}",
    'log_file': os.path.join(os.path.dirname(__file__), 'logs', 'maijiazhichi_scraper.log'),
    'image_dir': os.path.abspath(os.path.join(os.getcwd(), "..", 'Documents', 'IP', 'Maijiazhichi')),
    'parse_html_func': parse_html_maijiazhichi,
    'last_page_check': lambda html: "404 - 页面未找到" in html,
}
MAIJIAZHICHI_CONFIG['logger'] = setup_logger(MAIJIAZHICHI_CONFIG['log_file'])

async def get_new_case_from_maijiazhichi():
    """Main async function to run the maijiazhichi.com scraper."""
    return await run_scraper(MAIJIAZHICHI_CONFIG)

if __name__ == '__main__':
    asyncio.run(get_new_case_from_maijiazhichi())