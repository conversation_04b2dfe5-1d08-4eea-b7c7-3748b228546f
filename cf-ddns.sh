#!/usr/bin/env bash
###############################################################################
# Cloudflare DDNS updater – minimal, bash-only, no systemd required
# Checks current public IPv4 and updates api.maidalv.com when it changes.
# Requirements: apt-get update && apt-get install -y jq
###############################################################################

set -euo pipefail

# --- USER SETTINGS -----------------------------------------------------------
CF_KEY="e01bafbad17f59fb2a7b2e14ac229e159f63d"      #* API token with “Zone DNS Edit”
CF_EMAIL="<EMAIL>"
ZONE_NAME="maidalv.com"                 # your root zone
RECORD_NAME="vast.maidalv.com"           # record to update
TTL=120                                 # Cloudflare minimum TTL (seconds)

# --- HELPER ------------------------------------------------------------------
cf_api () {
  curl -sS \
       -H "X-Auth-Email: $CF_EMAIL" \
       -H "X-Auth-Key:   $CF_KEY" \
       -H "Content-Type: application/json" \
       "$@"
}

# --- 1. discover current public IP ------------------------------------------
PUBLIC_IP=$(curl -s https://ipv4.icanhazip.com || true)
[[ $PUBLIC_IP =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]] || {
  echo "[DDNS] Cannot determine public IPv4" >&2; exit 1; }

# --- 2. get zone- and record-ids (cached locally for speed) ------------------
CF_CACHE="/var/cache/cf-ddns"
mkdir -p "$CF_CACHE"
if [[ -f "$CF_CACHE/ids" ]]; then
    source "$CF_CACHE/ids"
else
    ZONE_ID=$(cf_api "https://api.cloudflare.com/client/v4/zones?name=${ZONE_NAME}" \
              | jq -r '.result[0].id')
    RECORD_ID=$(cf_api "https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/dns_records?type=A&name=${RECORD_NAME}" \
                | jq -r '.result[0].id')
    echo "ZONE_ID=${ZONE_ID}"   > "$CF_CACHE/ids"
    echo "RECORD_ID=${RECORD_ID}" >> "$CF_CACHE/ids"
fi

# --- 3. read last ip from cache ---------------------------------------------
LAST_IP_FILE="$CF_CACHE/last_ip"
LAST_IP=$(cat "$LAST_IP_FILE" 2>/dev/null || echo "0.0.0.0")

if [[ "$PUBLIC_IP" == "$LAST_IP" ]]; then
    echo "[DDNS] IP unchanged ($PUBLIC_IP) – nothing to do"
    exit 0
fi

# --- 4. push new IP to Cloudflare -------------------------------------------
UPDATE=$(cf_api -X PUT \
        --data "{\"type\":\"A\",\"name\":\"$RECORD_NAME\",\"content\":\"$PUBLIC_IP\",\"ttl\":$TTL,\"proxied\":false}" \
        "https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/dns_records/${RECORD_ID}")

if echo "$UPDATE" | jq -e '.success' >/dev/null; then
    echo "[DDNS] Updated $RECORD_NAME ➜ $PUBLIC_IP"
    echo "$PUBLIC_IP" > "$LAST_IP_FILE"
else
    echo "[DDNS] Cloudflare update failed:" >&2
    echo "$UPDATE"   >&2
    exit 1
fi
