
from DatabaseManagement.ImportExport import get_table_from_GZ
import json
import zlib
import base64
from FileManagement import NAS_module
from Common.Constants import nas_case_folder
import pandas as pd
from Common.Constants import sanitize_name
import os
from AI.GC_VertexAI import vertex_genai_multi
from AI.LLM_shared import get_json


# Gemini call
prompt_list = [
    ("text", 'What is this picture? And when was it regirsted. Answer in this format: {"description": "....", "regirsted_date": "...."}'),
    ("image_path", "D:/Documents/Programing/TRO/USside/Documents/IP/Trademarks/Certificates/0000005.webp")
]
ai_answer = vertex_genai_multi(prompt_list)
print(ai_answer)
json_answer = get_json(ai_answer)
print(json_answer)



df_cases = get_table_from_GZ("tb_case", force_refresh=False)
df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=False)

df_patent = df_cases[df_cases["images"]["patents"].apply(lambda x: len(x.keys()) > 0)]

df_patent = df_patent.head(50)

nas = NAS_module()
local_case_folder = "C:/Users/<USER>/OneDrive/Documents/GitHub/IP_Project/Cases"
for index, row in df_patent.iterrows():
    sanitized_case_name = sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}")
    next_case_dir_nas = f"{nas_case_folder}/{sanitized_case_name}"
    next_case_dir_local = os.path.join(local_case_folder, sanitized_case_name)
    nas.ssh_nas_to_local(next_case_dir_nas, next_case_dir_local, "*.pdf")

