import grpc
from fastapi import APIRouter, File, UploadFile, HTTPException
from typing import List
import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
from Protos import embedding_pb2
from Protos import embedding_pb2_grpc

### Embedding Routes to be able to call the gRPC service from FastAPI, not really needed, but can be handy

embedding_router = APIRouter()

def get_embedding_service_stub():
    channel = grpc.insecure_channel('localhost:5001')
    return embedding_pb2_grpc.EmbeddingServiceStub(channel)

@embedding_router.post("/get_image_embeddings")
async def get_image_embeddings(images: List[UploadFile] = File(...)):
    image_bytes_list = [await image.read() for image in images]
    
    stub = get_embedding_service_stub()
    request = embedding_pb2.GetImageEmbeddingsRequest()
    for img_bytes in image_bytes_list:
        request.images.add().image_data = img_bytes
        
    metadata = [('authorization', f'Bearer {os.getenv("API_BEARER_TOKEN")}')]
    try:
        response = stub.GetImageEmbeddings(request, metadata=metadata)
        return {"embeddings": [list(e.embedding.embedding) for e in response.embeddings]}
    except grpc.RpcError as e:
        raise HTTPException(status_code=500, detail=f"gRPC error: {e.details()}")

@embedding_router.post("/get_image_split_with_embeddings")
async def get_image_split_with_embeddings(image: UploadFile = File(...)):
    image_bytes = await image.read()
    
    stub = get_embedding_service_stub()
    request = embedding_pb2.GetImageSplitWithEmbeddingsRequest()
    request.image.image_data = image_bytes
    
    metadata = [('authorization', f'Bearer {os.getenv("API_BEARER_TOKEN")}')]
    try:
        response = stub.GetImageSplitWithEmbeddings(request, metadata=metadata)
        return {"image_splits": [{"image_data": s.image_data.hex(), "embedding": list(s.embedding)} for s in response.image_splits]}
    except grpc.RpcError as e:
        raise HTTPException(status_code=500, detail=f"gRPC error: {e.details()}")