import shlex # For quoting paths in shell commands
import hashlib # For local file hashing
import asyncio
import os
from pathlib import Path
import logging
from FileManagement.NAS import NASConnection
from Common.Constants import nas_ip_folder

logger = logging.getLogger(__name__)

# NAS paths (mirroring structure from weekly_patent.py for grant and cpc)
NAS_BASE_DIR = f"{nas_ip_folder}/Patents"
NAS_GRANT_ZIP_DIR = f"{NAS_BASE_DIR}/USPTO_Grants/Zip"
NAS_GRANT_EXTRACT_BASE = f"{NAS_BASE_DIR}/USPTO_Grants/Extracted"
NAS_CPC_ZIP_DIR = f"{NAS_BASE_DIR}/USPTO_CPC/Zip"
NAS_CPC_EXTRACT_BASE = f"{NAS_BASE_DIR}/USPTO_CPC/Extracted"

# Path for the NAS utility script on the NAS
NAS_UTIL_SCRIPT_DIR_ON_NAS = f"{nas_ip_folder}/Patents"
NAS_UNZIP_SCRIPT_NAME = "NAS_Patent_Unzip.py"
# Full path for SSH commands (prefixed with /volume1)
NAS_UNZIP_SCRIPT_PATH_ON_NAS_SSH = f"/volume1{NAS_UTIL_SCRIPT_DIR_ON_NAS}/{NAS_UNZIP_SCRIPT_NAME}"
# Path for SCP (typically relative to NAS user's home or without /volume1)
NAS_UNZIP_SCRIPT_PATH_ON_NAS_SCP = f"{NAS_UTIL_SCRIPT_DIR_ON_NAS}/{NAS_UNZIP_SCRIPT_NAME}"
# Local path to the script
LOCAL_NAS_UNZIP_SCRIPT_PATH = os.path.join(os.getcwd(), "FileManagement", "NAS_module", NAS_UNZIP_SCRIPT_NAME)


def get_local_file_hash(local_file_path, algorithm='md5'):
    """Computes the hash of a local file."""
    h = hashlib.new(algorithm)
    if not Path(local_file_path).exists():
        logger.warning(f"Local file for hashing not found: {local_file_path}")
        return None
    with open(local_file_path, 'rb') as f:
        while True:
            chunk = f.read(8192)
            if not chunk:
                break
            h.update(chunk)
    return h.hexdigest()

async def get_remote_file_hash_async(nas_connection, remote_file_path_ssh, hash_command_template="md5sum {}"):
    """
    Gets the hash of a remote file on the NAS (e.g., using md5sum).
    remote_file_path_ssh should be the /volume1 prefixed path for the command.
    """
    command = hash_command_template.format(shlex.quote(remote_file_path_ssh))
    logger.debug(f"Getting remote file hash for: {remote_file_path_ssh} using command: {command}")
    # md5sum might return error code 1 if file not found.
    # suppress_stderr_codes=[1] for "No such file or directory"
    stdout, exit_code = await asyncio.to_thread(
        nas_connection.ssh_execute_command,
        command,
        expected_exit_codes=[0, 1], # 0 for success, 1 for file not found by md5sum
        suppress_stderr_codes=[1]
    )
    if exit_code == 0 and stdout:
        return stdout.split()[0].strip() # Output is "hash  filename"
    logger.info(f"Could not get remote hash for {remote_file_path_ssh} (exit: {exit_code}). File might not exist.")
    return None


async def transfer_script_to_nas_if_needed_async(nas_connection, local_script_path, nas_script_ssh_path, nas_script_scp_path):
    """
    Transfers the NAS utility script to the NAS if it's not present or if the local version has been updated.
    nas_script_ssh_path is for hash checking (e.g., /volume1/path/to/script.py)
    nas_script_scp_path is for scp transfer (e.g., /path/to/script.py)
    """
    logger.info(f"Checking NAS utility script: Local: {local_script_path}, NAS Target (SSH for hash): {nas_script_ssh_path}")
    if not os.path.exists(local_script_path):
        logger.error(f"Local NAS utility script not found: {local_script_path}. Cannot transfer.")
        return False

    os.path.exists('D:\\Documents\\Programing\\TRO\\USSidePatent2\\IP\\FileManagement\\NAS_module\\NAS_Patent_Unzip.py')


    local_hash = get_local_file_hash(local_script_path)
    if not local_hash:
        logger.error(f"Could not compute hash for local script {local_script_path}.")
        return False

    remote_hash = await get_remote_file_hash_async(nas_connection, nas_script_ssh_path)

    if local_hash == remote_hash:
        logger.info(f"NAS utility script {os.path.basename(local_script_path)} is up-to-date on NAS. Hash: {local_hash}")
        return True
    
    logger.info(f"NAS utility script {os.path.basename(local_script_path)} outdated/not found on NAS (Local: {local_hash}, Remote: {remote_hash}). Transferring...")
    
    # Ensure parent directory exists on NAS. nas_connection.create_remote_directory expects path without /volume1
    nas_script_dir_for_create = str(Path(nas_script_scp_path).parent)
    await asyncio.to_thread(nas_connection.create_remote_directory, nas_script_dir_for_create)

    # transfer_file_with_scp expects remote_path without /volume1 prefix (this is nas_script_scp_path)
    transfer_success = await asyncio.to_thread(
        nas_connection.transfer_file_with_scp, # Assuming this method exists from a mixin
        local_path=local_script_path,
        remote_path=nas_script_scp_path,
        to_nas=True
    )
    if transfer_success:
        logger.info(f"Successfully transferred {os.path.basename(local_script_path)} to {nas_script_scp_path} on NAS.")
        # Optionally verify hash after transfer
        new_remote_hash = await get_remote_file_hash_async(nas_connection, nas_script_ssh_path)
        if new_remote_hash == local_hash:
            logger.info(f"Hash verification successful for {os.path.basename(local_script_path)} on NAS post-transfer.")
            return True
        else:
            logger.error(f"Hash mismatch for {os.path.basename(local_script_path)} after transfer! Local: {local_hash}, New Remote: {new_remote_hash}")
            return False # Indicates a problem with the transfer or hashing
    else:
        logger.error(f"Failed to transfer {os.path.basename(local_script_path)} to NAS.")
        return False


async def perform_nas_backup_operations_internal(zip_filename, zip_url, headers):
    """
    Performs NAS backup operations based on record_type:
    1. Ensures NAS_Patent_Unzip.py is on NAS and up-to-date.
    2. Downloads the archive directly to NAS via curl.
    3. Extracts the archive on NAS using NAS_Patent_Unzip.py.
    """

    nas_zip_path_for_ssh_cmd = f"/volume1{NAS_GRANT_ZIP_DIR}/{zip_filename}"
    nas_extract_base_for_ssh_cmd = f"/volume1{NAS_GRANT_EXTRACT_BASE}"
    try:
        with NASConnection() as nas:
            logger.info(f"NAS Backup ({zip_filename}): Initiating operations.")

            # 1. Ensure NAS_Patent_Unzip.py is on NAS and up-to-date
            script_transfer_ok = await transfer_script_to_nas_if_needed_async(
                nas, LOCAL_NAS_UNZIP_SCRIPT_PATH, NAS_UNZIP_SCRIPT_PATH_ON_NAS_SSH, NAS_UNZIP_SCRIPT_PATH_ON_NAS_SCP
            )
            if not script_transfer_ok:
                logger.error(f"NAS Backup ({zip_filename}): Failed to ensure {NAS_UNZIP_SCRIPT_NAME} is on NAS. Aborting NAS backup for this file.")
                return

            # 2. Download archive directly to NAS via curl
            nas_zip_dir_for_create_cmd = str(Path(nas_zip_path_for_ssh_cmd).parent).replace("/volume1", "", 1)
            await asyncio.to_thread(nas.create_remote_directory, nas_zip_dir_for_create_cmd)

            api_key_value = headers.get("X-API-KEY") if headers else None
            api_key_curl_header = f'-H "X-API-KEY: {api_key_value}"' if api_key_value else ""

            # Path for ssh_exists check (without /volume1)
            nas_zip_path_for_exists_check = nas_zip_path_for_ssh_cmd.replace("/volume1", "", 1)

            if await asyncio.to_thread(nas.ssh_exists, nas_zip_path_for_exists_check):
                logger.info(f"NAS Backup ({zip_filename}): Archive already exists at {nas_zip_path_for_ssh_cmd}. Skipping NAS download.")
            else:
                curl_command = f'curl -L --fail -o {shlex.quote(nas_zip_path_for_ssh_cmd)} {api_key_curl_header} {shlex.quote(zip_url)}'
                logger.info(f"NAS Backup ({zip_filename}): Starting direct download to NAS: {curl_command}")
                curl_stdout, curl_exit_code = await asyncio.to_thread(nas.ssh_execute_command, curl_command)
                if curl_exit_code != 0:
                    logger.error(f"NAS Backup ({zip_filename}): curl download to NAS failed. Exit: {curl_exit_code}, Stdout: {curl_stdout}")
                    return # Abort further NAS backup for this file
                logger.info(f"NAS Backup ({zip_filename}): Successfully downloaded to {nas_zip_path_for_ssh_cmd} on NAS.")

            # 3. Extract archive on NAS using NAS_Patent_Unzip.py
            nas_extract_base_for_create_cmd = nas_extract_base_for_ssh_cmd.replace("/volume1", "", 1)
            await asyncio.to_thread(nas.create_remote_directory, nas_extract_base_for_create_cmd)

            unzip_script_command_on_nas = (
                f"/bin/python {shlex.quote(NAS_UNZIP_SCRIPT_PATH_ON_NAS_SSH)} "
                f"{shlex.quote(nas_zip_path_for_ssh_cmd)} {shlex.quote(nas_extract_base_for_ssh_cmd)}"
            )
            logger.info(f"NAS Backup ({zip_filename}): Starting extraction on NAS: {unzip_script_command_on_nas}")
            unzip_stdout, unzip_exit_code = await asyncio.to_thread(nas.ssh_execute_command, unzip_script_command_on_nas)
            if unzip_exit_code != 0:
                logger.error(f"NAS Backup ({zip_filename}): {NAS_UNZIP_SCRIPT_NAME} execution failed on NAS. Exit: {unzip_exit_code}, Stdout: {unzip_stdout}")
            else:
                logger.info(f"NAS Backup ({zip_filename}): Successfully initiated extraction on NAS using {NAS_UNZIP_SCRIPT_NAME}.")
                logger.debug(f"NAS Unzip Script Output ({zip_filename}): {unzip_stdout}")
    except Exception as e_nas:
        logger.error(f"NAS Backup ({zip_filename}): Unhandled exception during NAS backup operations: {e_nas}", exc_info=True)