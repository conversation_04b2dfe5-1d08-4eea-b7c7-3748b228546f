:root {
    --primary-color: #1a73e8;
    --primary-hover: #1557b0;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --border-color: #dadce0;
    --background-light: #f8f9fa;
    --background-white: #ffffff;
    --code-background: #f8f9fa;
    --shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-hover: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

[data-theme="dark"] {
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --border-color: #3c4043;
    --background-light: #202124;
    --background-white: #303134;
    --code-background: #2d2d30;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-light);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
    width: 280px;
    background: var(--background-white);
    border-right: 1px solid var(--border-color);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
    transition: background-color 0.3s ease;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 6px 8px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background: var(--background-light);
    color: var(--text-primary);
}

.nav-menu {
    padding: 16px 0;
}

.nav-item {
    display: block;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: var(--background-light);
    color: var(--text-primary);
}

.nav-item.active {
    color: var(--primary-color);
    background: rgba(26, 115, 232, 0.08);
    border-left-color: var(--primary-color);
}

/* Main Content */
.main-content {
    margin-left: 280px;
    flex: 1;
    padding: 40px;
    max-width: calc(100vw - 280px);
}

.content-header {
    margin-bottom: 40px;
}

.content-header h1 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.content-header p {
    font-size: 18px;
    color: var(--text-secondary);
    max-width: 600px;
}

/* Sections */
.section {
    margin-bottom: 48px;
}

.section h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
    scroll-margin-top: 20px;
}

.section h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 32px 0 16px 0;
    color: var(--text-primary);
    scroll-margin-top: 20px;
}

.section h4 {
    font-size: 16px;
    font-weight: 600;
    margin: 24px 0 12px 0;
    color: var(--text-primary);
}

.section p {
    margin-bottom: 16px;
    color: var(--text-secondary);
    line-height: 1.7;
}

.section ul {
    margin: 16px 0;
    padding-left: 24px;
}

.section li {
    margin-bottom: 8px;
    color: var(--text-secondary);
}

/* Code Blocks */
.code-container {
    position: relative;
    margin: 24px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.code-header {
    background: var(--background-white);
    border-bottom: 1px solid var(--border-color);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.code-tabs {
    display: flex;
    gap: 8px;
}

.code-tab {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--background-light);
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.code-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.copy-button {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--background-light);
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.copy-button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.copy-button.copied {
    background: #34a853;
    color: white;
    border-color: #34a853;
}

pre[class*="language-"] {
    margin: 0;
    border-radius: 0;
    background: var(--code-background) !important;
}

code[class*="language-"] {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.code-block {
    display: none;
}

.code-block.active {
    display: block;
}

/* Tables */
.table-container {
    overflow-x: auto;
    margin: 24px 0;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

table {
    width: 100%;
    border-collapse: collapse;
    background: var(--background-white);
}

th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--background-light);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

td {
    color: var(--text-secondary);
    font-size: 14px;
}

tr:last-child td {
    border-bottom: none;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.badge.post {
    background: #e8f5e8;
    color: #137333;
}

.badge.get {
    background: #e3f2fd;
    color: #1565c0;
}

.badge.required {
    background: #fce8e6;
    color: #d93025;
}

.badge.optional {
    background: #f3f4f6;
    color: #6b7280;
}

/* Alert Boxes */
.alert {
    padding: 16px;
    border-radius: 8px;
    margin: 24px 0;
    border-left: 4px solid;
}

.alert.info {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #0d47a1;
}

.alert.warning {
    background: #fff3e0;
    border-color: #ff9800;
    color: #e65100;
}

.alert.success {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #2e7d32;
}

/* Disclosure styles */
.disclosure {
    margin: 16px 0;
}

.disclosure-header {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;
}

.disclosure-header:hover {
    background: var(--background-white);
}

.disclosure-content {
    display: none;
    margin-top: 8px;
}

.disclosure.open .disclosure-content {
    display: block;
}

.disclosure-arrow {
    transition: transform 0.2s ease;
    font-family: monospace;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.nav-item:focus,
.code-tab:focus,
.copy-button:focus,
.theme-toggle:focus,
.disclosure-header:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        max-width: 100vw;
        padding: 20px;
    }

    .content-header h1 {
        font-size: 24px;
    }

    .content-header p {
        font-size: 16px;
    }

    .code-container {
        margin: 16px -20px;
        border-radius: 0;
    }

    .table-container {
        margin: 16px -20px;
        border-radius: 0;
    }
}
