import io
import requests
import threading
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor
from PIL import Image as PILImage
from reportlab.platypus import Paragraph, Spacer, Image, Table, TableStyle, HRFlowable, Flowable
from reportlab.lib.units import mm
from reportlab.lib.utils import ImageReader
from reportlab.pdfbase.pdfmetrics import stringWidth
from Check.PDF_ReportLab.styles import myStyles, colors

_session = requests.Session()
_adapter = requests.adapters.HTTPAdapter(pool_connections=8, pool_maxsize=8, max_retries=2)
_session.mount('http://', _adapter)
_session.mount('https://', _adapter)

def _para(text, style):
    return Paragraph(str(text) if text is not None else '—', style)

def _sp(h_mm):
    return Spacer(1, h_mm * mm)

def _hr(width='80%'):
    return HRFlowable(width=width, thickness=0.3*mm, color=colors['navy'], hAlign='CENTER')

def _fit_image(src, max_w, max_h, img_cache):
    """
    Fits an image within max_w and max_h, using pre-fetched and cached data.
    Downscales and re-encodes the image based on the target display size.
    """
    DPI = 200
    # Convert mm dimensions to pixels for resizing
    target_px_w = max_w * DPI / (25.4 * mm)
    target_px_h = max_h * DPI / (25.4 * mm)

    img_bytes = None
    if isinstance(src, str) and src.startswith(('http://', 'https://')):
        cached = img_cache.get(src)
        if cached:
            img_bytes = cached.get('bytes')

    if img_bytes is None:
        # Fallback for local files or URLs not pre-fetched
        try:
            with open(src, 'rb') as f:
                img_bytes = f.read()
        except (IOError, TypeError):
            # Not a local file path, treat as raw data/BytesIO if possible
            if hasattr(src, 'read'):
                img_bytes = src.read()
            else: # Or just hope ImageReader can handle it
                img_bytes = src

    try:
        pil_img = PILImage.open(io.BytesIO(img_bytes))
        
        if pil_img.mode in ('RGBA', 'LA', 'P'):
            pil_img = pil_img.convert('RGB')

        # Only resize if the image is significantly larger than the target
        if pil_img.width > target_px_w * 1.1 or pil_img.height > target_px_h * 1.1:
            pil_img.thumbnail((target_px_w, target_px_h))

        buffer = io.BytesIO()
        pil_img.save(buffer, format='JPEG', quality=90)
        buffer.seek(0) # Rewind buffer to the beginning
        processed_bytes_io = buffer
        ir = ImageReader(processed_bytes_io)
    except Exception:
        # If processing fails, use original bytes
        processed_bytes_io = io.BytesIO(img_bytes)
        ir = ImageReader(processed_bytes_io)

    iw, ih = ir.getSize()
    scale = min(max_w / float(iw or 1), max_h / float(ih or 1))
    w, h = iw * scale, ih * scale
    
    processed_bytes_io.seek(0) # Ensure buffer is at the start before Image reads it
    img = Image(processed_bytes_io, width=w, height=h)
    img.hAlign = 'CENTER'
    return img

def _label_value_rows(pairs, styles):
    rows = []
    for label, value in pairs:
        rows.append([
            _para(label, styles['SummaryHeader']),
            _para(value, styles['ReportBody'])
        ])
    return rows

def _simple_kv_table(pairs, styles, col_widths=None):
    t = Table(
        _label_value_rows(pairs, styles),
        colWidths=col_widths or [40*mm, None]
    )
    t.setStyle(TableStyle([
        ('ALIGN',        (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN',       (0, 0), (-1, -1), 'MIDDLE'),
        ('TEXTCOLOR',    (0, 0), (-1, -1), colors['navy']),
        ('INNERGRID',    (0, 0), (-1, -1), 0.25, colors['line']),
        ('BOX',          (0, 0), (-1, -1), 0.25, colors['line']),
        ('LEFTPADDING',  (0, 0), (-1, -1), 4),
        ('RIGHTPADDING', (0, 0), (-1, -1), 4),
        ('TOPPADDING',   (0, 0), (-1, -1), 4),
        ('BOTTOMPADDING',(0, 0), (-1, -1), 4),
    ]))
    return t

def _images_grid(image_urls, img_cache, thumb_w_mm=38, thumb_h_mm=38, gap=3*mm, fixed_cols=None):
    """
    Make a tight grid of thumbnails (kept proportionally scaled).
    It can now handle dynamic columns based on the number of images.
    """
    num_images = len(image_urls)
    if num_images == 0:
        return None

    if fixed_cols:
        cols = fixed_cols
    elif num_images >= 7:
        cols = 4
    elif num_images >= 5:
        cols = 3
    else:
        cols = num_images

    # Build rows of flowables sized to thumb box
    rows = []
    row = []
    for i, url in enumerate(image_urls):
        img = _fit_image(url, thumb_w_mm*mm, thumb_h_mm*mm, img_cache)
        row.append(img)
        if (i + 1) % cols == 0:
            rows.append(row)
            row = []
    if row:
        # pad final row to full width so grid sizes nicely
        while len(row) < cols:
            row.append(Spacer(thumb_w_mm*mm, thumb_h_mm*mm))
        rows.append(row)

    col_widths = [thumb_w_mm*mm] * cols
    t = Table(rows, colWidths=col_widths, hAlign='CENTER', spaceBefore=0, spaceAfter=0)
    t.setStyle(TableStyle([
        ('LEFTPADDING',  (0, 0), (-1, -1), gap/2),
        ('RIGHTPADDING', (0, 0), (-1, -1), gap/2),
        ('TOPPADDING',   (0, 0), (-1, -1), gap/2),
        ('BOTTOMPADDING',(0, 0), (-1, -1), gap/2),
        ('INNERGRID',    (0, 0), (-1, -1), 0.25, colors['line']),
        ('BOX',          (0, 0), (-1, -1), 0.25, colors['line']),
        ('ALIGN',        (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN',       (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    return t

def _field(res, *keys, fallback='—'):
    for k in keys:
        if k in res and res[k] not in (None, '', []):
            return res[k]
    return fallback

def _as_joined(val, sep=", "):
    if isinstance(val, (list, tuple, set)):
        return sep.join(str(x) for x in val)
    return str(val) if val not in (None, '') else '—'


class RiskBadge(Flowable):
    def __init__(self, text, risk_level):
        super().__init__()
        self.text = text
        self._level_raw = (risk_level or "").lower().strip()
        
        level = self._norm_level()
        self.bg_name, self.txt_name, self.border_name = {
            'high':   ('risk_high_bg', 'risk_high_text', 'risk_high_border'),
            'medium': ('risk_med_bg',  'risk_med_text',  'risk_med_border'),
            'low':    ('risk_low_bg',  'risk_low_text',  'risk_low_border'),
        }[level]
        
        self.styles = myStyles
        style = self.styles['RiskBadge'].clone('RiskBadgeDyn')
        style.textColor = colors[self.txt_name]
        self.p = Paragraph(self.text, style)
        self.h_pad = 3*mm
        self.v_pad = 1.3*mm
        self.radius = 2.5*mm

    def _norm_level(self):
        s = self._level_raw
        if 'high' in s:    return 'high'
        if 'med' in s:     return 'medium'
        if 'low' in s:     return 'low'
        return 'low'  # sensible default

    def wrap(self, availW, availH):
        style = self.p.style
        # measure the plain text with the current font + size
        text_w = stringWidth(self.p.getPlainText(), style.fontName, style.fontSize)
        self.width  = min(availW, text_w + 2*self.h_pad)   # don’t exceed the frame
        # now ask the Paragraph to wrap to *our* width so height is accurate
        _, para_h = self.p.wrap(self.width - 2*self.h_pad, availH)
        self.height = para_h + 2*self.v_pad
        return self.width, self.height

    def draw(self):
        c = self.canv
        c.saveState()
        c.setStrokeColor(colors[self.border_name])
        c.setFillColor(colors[self.bg_name])
        c.setLineWidth(1)
        c.roundRect(0, -1, self.width, self.height, self.radius, stroke=1, fill=1) # -1 to bring the box down a little

        # Draw the text
        self.p.drawOn(c, self.h_pad, self.v_pad)
        c.restoreState()

def _fetch_ir_for_url(url, img_cache, lock, timeout=(3, 10)):
    """Fetches image bytes and stores them in the cache."""
    with lock:
        if url in img_cache:
            return

    try:
        r = _session.get(url, timeout=timeout, stream=True)
        r.raise_for_status()
        img_bytes = r.content
        with lock:
            img_cache[url] = {'bytes': img_bytes}
    except requests.exceptions.RequestException as e:
        print(f"Failed to fetch {url}: {e}")
        with lock:
            img_cache[url] = {'bytes': None} # Cache failure

def prefetch_images(urls, img_cache, max_workers=8):
    urls = [u for u in set(urls) if isinstance(u, str) and u.startswith(('http://','https://'))]
    if not urls: return
    
    lock = threading.Lock()
    
    with ThreadPoolExecutor(max_workers=max_workers) as ex:
        # Use a lambda to pass the extra arguments to the map function
        ex.map(lambda url: _fetch_ir_for_url(url, img_cache, lock), urls)