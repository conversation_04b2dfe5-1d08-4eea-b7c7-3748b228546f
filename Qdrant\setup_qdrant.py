#!/usr/bin/env python3
"""
Setup script for Qdrant collections and indexes.
This script configures the Qdrant instance with the required collections and indexes
for the IP Infringement Detection system.
"""

import os
from qdrant_client import QdrantClient
from qdrant_client.http import models
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Constants for vector dimensions - Using single SigLIP model for all embeddings
SIGLIP_DIMENSION = 1024

def setup_qdrant():
    """
    Set up Qdrant collections and indexes.
    """
    # Get Qdrant connection details from environment variables
    qdrant_url = os.getenv("QDRANT_URL")
    qdrant_api_key = os.getenv("QDRANT_API_KEY")

    # Initialize Qdrant client
    client = QdrantClient(url=qdrant_url, api_key=qdrant_api_key)

    # Create IP_Assets collection
    create_ip_assets_collection(client)

    # Create Product_Images collection
    create_product_images_collection(client)

    # Create payload index for Product_Images collection
    create_product_images_index(client)

    print("Qdrant setup completed successfully.")

def create_ip_assets_collection(client):
    """
    Create the IP_Assets collection in Qdrant.
    """
    print("Creating IP_Assets collection...")
    client.recreate_collection(
        collection_name="IP_Assets",

        # --- Vector Parameters (Named Vectors) ---
        vectors_config={
            # Universal SigLIP vector for all IP types (image and text)
            "siglip_vector": models.VectorParams(
                size=SIGLIP_DIMENSION,  # Dimension of the SigLIP2 large patch16-512 embedding
                distance=models.Distance.COSINE,  # Similarity metric. COSINE recommended for normalized embeddings.
                on_disk=True,  # Store raw vectors on disk using memory mapping. CRITICAL for SSDs & large datasets.
                datatype=models.Datatype.FLOAT32  # Data type for vector elements.
            ),
        },

        # --- Sparse Vector Parameters ---
        sparse_vectors_config=None,  # No sparse vectors are required for this use case.

        # --- HNSW Index Parameters (Global for collection) ---
        hnsw_config=models.HnswConfigDiff(
            m=32,  # Max connections per node per HNSW layer. Higher = better recall, more RAM/disk, slower build.
            ef_construct=200,  # Size of the dynamic list for intermediate search results during index build.
            full_scan_threshold=10000,  # (In KiloBytes) Segments larger than this threshold benefit from payload indexes.
            on_disk=True  # Store HNSW graph structure on disk using memory mapping. CRITICAL for SSDs & large indexes.
        ),

        # --- Optimizer Parameters ---
        optimizers_config=models.OptimizersConfigDiff(
            deleted_threshold=0.2,  # Minimum fraction of deleted points in a segment to trigger vacuum optimization.
            vacuum_min_vector_number=1000,  # Minimum number of vectors in a segment required for vacuum optimization.
            default_segment_number=0,  # Target number of segments per shard. 0 = auto-configure based on CPU cores.
            indexing_threshold=20000 * 1024,  # (In KiloBytes converted to Bytes) Minimum segment size to build a vector index.
            memmap_threshold=20000 * 1024,  # (In KiloBytes converted to Bytes) Minimum segment size to store vectors in memory-mapped files.
        ),

        # --- Write-Ahead-Log Parameters ---
        wal_config=models.WalConfigDiff(
            wal_capacity_mb=32  # Size of the Write-Ahead-Log file per shard.
        ),

        # --- Quantization Parameters ---
        quantization_config=None,  # Disable quantization initially.

        # --- Payload Storage Parameters ---
        on_disk_payload=True  # Store payload data primarily on disk (using RocksDB).
    )
    print("IP_Assets collection created successfully.")

def create_product_images_collection(client):
    """
    Create the Product_Images collection in Qdrant.
    """
    print("Creating Product_Images collection...")
    client.recreate_collection(
        collection_name="Product_Images",

        # --- Vector Parameters (Named Vectors) ---
        vectors_config={
            # Universal SigLIP vector for product images
            "siglip_vector": models.VectorParams(
                size=SIGLIP_DIMENSION,  # Dimension of the SigLIP2 large patch16-512 embedding
                distance=models.Distance.COSINE,
                on_disk=True,  # CRITICAL for SSD setup.
                datatype=models.Datatype.FLOAT32  # Default precision.
            ),
        },

        # --- Sparse Vector Parameters ---
        sparse_vectors_config=None,  # No sparse vectors required.

        # --- HNSW Index Parameters ---
        hnsw_config=models.HnswConfigDiff(
            m=32,  # Max connections. Default=16. Start with 32.
            ef_construct=200,  # Build-time search scope. Default=100. Start with 200.
            full_scan_threshold=10000,  # KB threshold for HNSW planning optimization. Default=10000. Keep default.
            on_disk=True  # Store HNSW index on disk. CRITICAL for SSD setup.
        ),

        # --- Optimizer Parameters ---
        optimizers_config=models.OptimizersConfigDiff(
            deleted_threshold=0.2,  # Default=0.2. Keep default.
            vacuum_min_vector_number=1000,  # Default=1000. Keep default.
            default_segment_number=0,  # 0 = auto based on CPU cores. Keep default.
            indexing_threshold=20000 * 1024,  # Default=20000 KB (converted to Bytes). Keep default.
            memmap_threshold=20000 * 1024,  # Align with indexing_threshold (converted to Bytes). Set explicitly to 20000 KB.
        ),

        # --- Write-Ahead-Log Parameters ---
        wal_config=models.WalConfigDiff(
            wal_capacity_mb=32  # Default=32. Keep default.
        ),

        # --- Quantization Parameters ---
        quantization_config=None,  # Disable quantization initially.

        # --- Payload Storage Parameters ---
        on_disk_payload=True  # Store payload on disk (RocksDB). Recommended.
    )
    print("Product_Images collection created successfully.")

def create_product_images_index(client):
    """
    Create payload index for the Product_Images collection.
    """
    print("Creating payload index for Product_Images collection...")
    client.create_payload_index(
        collection_name="Product_Images",
        field_name="client_id",
        field_schema=models.PayloadSchemaType.KEYWORD,
        wait=True  # Ensure the index creation task is acknowledged by Qdrant before the script proceeds.
    )
    print("Payload index created successfully.")


def get_status_of_qdrant_server():
    qdrant_url = os.getenv("QDRANT_URL")
    qdrant_api_key = os.getenv("QDRANT_API_KEY")

    # Initialize Qdrant client
    client = QdrantClient(url=qdrant_url, api_key=qdrant_api_key)

    try:
        # Get a list of all collection names
        collections_response = client.get_collections()
        
        if not collections_response.collections:
            print("No collections found.")
        else:
            print(f"Found {len(collections_response.collections)} collection(s):\n")
            for collection_description in collections_response.collections:
                collection_name = collection_description.name
                
                # Get detailed information for each collection
                collection_info = client.get_collection(collection_name=collection_name)
                
                print(f"Collection: {collection_name}")
                print(f"  Status: {collection_info.status}")
                print(f"  Points count: {collection_info.points_count}")
                print(f"  Vectors count: {collection_info.vectors_count}") # Sum of vectors across all named vectors
                print(f"  Segments count: {collection_info.segments_count}")
                if hasattr(collection_info, 'disk_data_size'): # Available in newer versions
                    print(f"  Disk data size (bytes): {collection_info.disk_data_size}")
                if hasattr(collection_info, 'ram_data_size'): # Available in newer versions
                    print(f"  RAM data size (bytes): {collection_info.ram_data_size}")
                print(f"  Configuration: {collection_info.config}")
                print("-" * 30)

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        client.close()


if __name__ == "__main__":
    setup_qdrant()
    get_status_of_qdrant_server()
