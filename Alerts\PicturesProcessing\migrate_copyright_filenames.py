#!/usr/bin/env python3
"""
Migration script to convert copyright filenames from reg_no based to a standardized format.
Converts existing filenames to {reg_no}_{method}.webp or {reg_no}_{Exhibit}.webp.

This script:
1. Processes all cases with copyright images in the dataframe.
2. Cleans and formats reg_no, or generates a new one if missing.
3. Updates the 'copyrights' table in the PostgreSQL database.
4. Copies files to new names based on the standardized format.
5. Updates dataframe with new filenames and reg_no information.
"""

import os
import sys
import re
import asyncio
import json
import copy
from typing import Dict, Optional, Tuple
from datetime import datetime, date
import traceback

USER_ANSWERS_CACHE_FILE = os.path.join(os.path.dirname(__file__), "user_answers_cache.json")

sys.path.append(os.getcwd())

import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from IP.Patents_Bulk.patent_db_grant import get_db_connection
from FileManagement.Tencent_COS import get_cos_client, async_copy_file_with_retry
from IP.Copyrights.Copyright_USCO import get_info_from_USCO_using_reg_no
from AI.GC_VertexAI import vertex_genai_multi_async
from FileManagement.Tencent_COS import download_from_cos
from Common import Constants
import tempfile
from Alerts.Chrome_Driver import get_driver
from datetime import datetime
import hashlib

def date_serializer(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")

def parse_publication_date(value):
    if not value:
        return None

    try:
        # Try full date first
        return datetime.strptime(value, "%Y-%m-%d").date()
    except:
        pass

    try:
        # Try year-month
        return datetime.strptime(value, "%Y-%m").date()
    except:
        pass

    try:
        # Try year only
        if len(value) == 4 and value.isdigit():
            return datetime.strptime(value, "%Y").date()
    except:
        pass

    return None  # Fallback if parsing fails

COPYRIGHT_COLUMNS = [
    "tro", "registration_number", "registration_date", "type_of_work", "title",
    "date_of_creation", "date_of_publication", "copyright_claimant",
    "authorship_on_application", "rights_and_permissions", "description",
    "nation_of_first_publication", "names", "plaintiff_id"
]

# def reset_copyright_database():
#     """Reset the copyrights table in the database."""
#     db_conn = get_db_connection()
#     cursor = db_conn.cursor()
#     try:
#         cursor.execute("DELETE FROM copyrights")
#         db_conn.commit()
#         print("✅ Copyrights table reset successfully.")
#     except Exception as e:
#         print(f"❌ Error resetting copyrights table: {e}")
#     finally:
#         cursor.close()
#         db_conn.close()

def split_reg(reg_no: str) -> list:
    """Split the registration number into its components.

    Args:
        reg_no (str): The registration number to split.

    Returns:
        list: A list containing the components of the registration number.
    """
    formated_reg_no = re.sub(r'[^a-zA-Z0-9]', '', reg_no)
    return re.findall(r'([a-zA-Z]+|[0-9]+)', formated_reg_no)

def zero_pad_reg_no(chart: str, number: str) -> str:
    """Zero pad the registration number based on its chart type.
    Args:
        chart (str): The chart type (e.g., 'MD', 'VA').
        number (str): The number to zero pad.
    Returns:
        str: The zero-padded registration number.
    """
    if chart == 'MD':  # Internally generated number: MD + 4 digit plaintiff id + 4 digit copyright count
        formated_reg_no = chart + number.zfill(8)
    elif len(chart) == 3:  # e.g. VAu123456789
        if len(number) > 9:
            number = number[-9:]  # Ensure we only take the last 9 digits
        if chart[2] == 'U':
            chart = chart[:2] + 'u'
    
        formated_reg_no = chart + number.zfill(9)
    else:  # e.g. VA0123456789
        if len(number) > 10:
            number = number[-10:]  # Ensure we only take the last 9 digits        
        formated_reg_no = chart + number.zfill(10)
    
    return formated_reg_no

def get_related_reg_nos(reg_no: str) -> list:
    """Get related registration numbers based on the provided reg_no.
    
    Args:
        reg_no (str): The standardized registration number.
    
    Returns:
        list: A list of related registration numbers.
    """
    if reg_no.startswith('VA', 0, 2):
        char, code = split_reg(reg_no.replace('VA', 'VAu'))
        return [reg_no, zero_pad_reg_no(char, str(int(code)))]
    elif reg_no.startswith('TX', 0, 2):
        char, code = split_reg(reg_no.replace('TX', 'TXu'))
        return [reg_no, zero_pad_reg_no(char, str(int(code)))]
    elif reg_no.startswith('PA', 0, 2):
        char, code = split_reg(reg_no.replace('PA', 'PAu'))
        return [reg_no, zero_pad_reg_no(char, str(int(code)))]
    elif reg_no.startswith('SR', 0, 2):
        char, code = split_reg(reg_no.replace('SR', 'SRu'))
        return [reg_no, zero_pad_reg_no(char, str(int(code)))]
    elif reg_no.startswith('RE', 0, 2):
        char, code = split_reg(reg_no.replace('RE', 'REu'))
        return [reg_no, zero_pad_reg_no(char, str(int(code)))]
    else:
        return [reg_no]  # Return the original reg_no if no related ones found

def getting_migration_cases() -> Tuple[pd.DataFrame, set[str]]:
    """Get cases from tb_case that have copyrights and return a deep copied DataFrame and a set of existing MD registration numbers.
    
    Args:
        None
    
    Returns:
        pd.DataFrame: DataFrame containing cases with copyrights images.
        set: Set of existing MD registration numbers.
    """
    cases_with_copyrights = []

    df = get_table_from_GZ("tb_case", force_refresh=True).sort_values(by='date_filed', ascending=True)
    existing_md_regs = set()

    for index, row in df.iterrows():
        images_data = row.get('images', {})
        if images_data and 'copyrights' in images_data and images_data['copyrights']:
            cases_with_copyrights.append(index)
            for _, related_info in images_data['copyrights'].items():
                unparsed_reg_nos = related_info.get("reg_no", "")

                # update the oringinal data with standardized list of reg_no
                if not unparsed_reg_nos: # handle "", None, []
                    unparsed_reg_nos = []
                elif isinstance(unparsed_reg_nos, str):
                    unparsed_reg_nos = [unparsed_reg_nos]
                elif isinstance(unparsed_reg_nos, list) and not unparsed_reg_nos[0]:
                    unparsed_reg_nos = []
                related_info['reg_no'] = unparsed_reg_nos

                # identify formated MD registration numbers
                if unparsed_reg_nos:
                    unparsed_reg_no = unparsed_reg_nos[0]

                    # save the formated reg_no to the existing_md_copyright_list
                    split_str = split_reg(unparsed_reg_no)
                    if len(split_str) == 2:  # e.g. ********** or VA0123456789
                        formated_reg_no = zero_pad_reg_no(split_str[0], split_str[1])  # This will zero pad the reg_no based on its type
                        if formated_reg_no.startswith('MD'):
                            existing_md_regs.add(formated_reg_no)

    if not cases_with_copyrights:
        print("⚠️ No cases with copyrights found.")
        return pd.DataFrame(), set()

    return df.loc[cases_with_copyrights].copy(), existing_md_regs

class CopyrightFilenameMigrator:
    def __init__(self, existing_md_copyright_list: set):
        self.db_conn = None
        self.cos_client = None
        self.cos_bucket = None
        self.driver = None
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
        self.copyright_md_list = existing_md_copyright_list
        self.copyrights_cache = {}
        # Add semaphore to limit concurrent copy operations
        self.copy_semaphore = asyncio.Semaphore(15)  # Limit to 15 concurrent copy operations

    async def check_file_exists_in_cos(self, key: str) -> bool:
        """Checks if a file with the given key exists in the COS bucket."""
        try:
            await self.cos_client.head_object(Bucket=self.cos_bucket, Key=key)
            return True
        except Exception:
            return False

    async def download_and_hash_file_from_cos(self, key: str) -> Optional[str]:
        """Downloads a file from COS to a temporary location and calculates its SHA256 hash."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            success = download_from_cos(self.cos_client, self.cos_bucket, key, temp_path)
            if not success:
                return None
            
            hasher = hashlib.sha256()
            with open(temp_path, 'rb') as f:
                while chunk := f.read(8192):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except Exception as e:
            print(f"Error downloading or hashing file {key}: {e}")
            return None
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)

    async def _load_copyrights_cache(self):
        """Load the copyrights and copyrights_files tables into an in-memory cache."""
        try:
            cursor = self.db_conn.cursor()
            
            # Load copyrights table
            cursor.execute("SELECT * FROM copyrights")
            columns = [desc[0] for desc in cursor.description]
            for row in cursor.fetchall():
                row_dict = dict(zip(columns, row))
                row_dict['filenames'] = []  # Initialize filenames list
                self.copyrights_cache[row_dict['registration_number']] = row_dict

            # Load copyrights_files table and append filenames
            cursor.execute("SELECT registration_number, filename FROM copyrights_files")
            for reg_no, filename in cursor.fetchall():
                if reg_no in self.copyrights_cache:
                    self.copyrights_cache[reg_no]['filenames'].append(filename)

            print(f"✅ Copyrights cache loaded with {len(self.copyrights_cache)} records.")
        except Exception as e:
            print(f"❌ Error loading copyrights cache: {e}")
        finally:
            cursor.close()

    async def __aenter__(self):
        self.db_conn = get_db_connection()
        await self._load_copyrights_cache()
        self.cos_client, self.cos_bucket = get_cos_client()
        try:
            self.driver = get_driver()
        except Exception as e:
            print(f"Error getting driver: {e}")
            self.driver = None
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()
        if self.driver:
            self.driver.quit()

    async def copy_file_to_new_name(self, old_filename: str, new_filename: str, plaintiff_id: int) -> bool:
        """Copy file from old name to new name using async_copy_file_with_retry
        This is also going to handle the overwriting issue
        """

        if old_filename == new_filename:
            print(f"Skipping copy for {old_filename} as it is the same as {new_filename}")
            return True

        try:
            if "exhibit" in new_filename.lower() and "_full" not in old_filename:
                # check if the key already exists in the low and high folders
                new_key_low = f"plaintiff_images/{plaintiff_id}/low/{new_filename}"
                new_key_high = f"plaintiff_images/{plaintiff_id}/high/{new_filename}"
                old_key_low = f"plaintiff_images/{plaintiff_id}/low/{old_filename}"
                old_key_high = f"plaintiff_images/{plaintiff_id}/high/{old_filename}"

                new_key_low_full = f"plaintiff_images/{plaintiff_id}/low/{new_filename.replace('.webp', '_full.webp')}"

                new_key_low_exists = await self.check_file_exists_in_cos(new_key_low)

                if not new_key_low_exists:
                    await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key_low, old_key_low)
                    await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key_high, old_key_high)
                else: # comparing the hashes are necessary to avoid overwriting
                    # getting the existing files's from the cos
                    hash_new_low = await self.download_and_hash_file_from_cos(new_key_low)
                    hash_new_low_full = await self.download_and_hash_file_from_cos(new_key_low_full)

                    # compare the hashes of the existing low and high files
                    # if the hash of the existing files is same, overwrite
                    # if the has not same, skip overwrite
                    if hash_new_low == hash_new_low_full and hash_new_low is not None:
                        await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key_low, old_key_low)
                        await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key_high, old_key_high)
                    else:
                        print(f"Skipping overwrite for exhibit file {new_filename} due to hash mismatch or missing full file.")
                        return True
            else:
                # Original logic for non-exhibit files
                if "_full" not in old_filename:
                    old_key = f"plaintiff_images/{plaintiff_id}/low/{old_filename}"
                    new_key = f"plaintiff_images/{plaintiff_id}/low/{new_filename}"
                    await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key, old_key)

                old_key = f"plaintiff_images/{plaintiff_id}/high/{old_filename}"
                new_key = f"plaintiff_images/{plaintiff_id}/high/{new_filename}"
                await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key, old_key)

            return True
        except Exception as e:
            print(f"File copy error from {old_filename} to {new_filename}: {e}")
            return False

    async def throttled_copy(self, old_filename: str, new_filename: str, plaintiff_id: int) -> bool:
        """A wrapper for copy_file_to_new_name that uses a semaphore to limit concurrency."""
        async with self.copy_semaphore:
            return await self.copy_file_to_new_name(old_filename, new_filename, plaintiff_id)

    async def getting_matched_reg_no_from_ai(self, reg_nos_and_meta_info: dict, plaintiff_id: str, certificate_basepath: str) -> str:
        """Get the most accurate registration number from AI based on the provided reg_nos and certificate_basepath.

        Args:
            reg_nos_and_meta_info (dict): A dictionary where keys are registration numbers and values are their metadata.
            certificate_basepath (str): The base path of the certificate image.
        
        Returns:
            str: The most accurate registration number as determined by the AI. if ai can't decide or 
            the parsing is unsuccessful, return the first reg_no in the list.
        """
        prompt = f"""
        Looking at this copyright certificate image, and considering the following registration numbers
        {list(reg_nos_and_meta_info.keys())}
        and their metadata:
        {json.dumps(reg_nos_and_meta_info, default=date_serializer, indent=2)}

        Please identify the most accurate registration number from the list
        
        Please respond in this exact format:
        REG_NO: [number]

        If you cannot find either number, return the first reg_no in the list.
        """

        # Download the certificate image locally to a temporary path
        cos_key = f"plaintiff_images/{plaintiff_id}/high/{certificate_basepath}"

        # Download the certificate image from the COS bucket
        with tempfile.NamedTemporaryFile(delete=False, suffix='.webp') as temp_file:
            certificate_path = temp_file.name

        downlaod_success = download_from_cos(
            self.cos_client, self.cos_bucket, cos_key, certificate_path
        )

        if not downlaod_success:
            raise RuntimeError(f"Failed to download certificate image from COS: {cos_key}")

        prompt_list = [("text", prompt), ("image_path", certificate_path)]
        ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE, useVertexAI=Constants.IMAGE_MODEL_FREE_VERTEX)

        os.remove(certificate_path)

        # Parse LLM response
        reg_match = re.search(r'REG_NO:\s*(.*)', ai_answer)
        if reg_match is not None:
            found_reg_no = reg_match.group(1)
            reg_no = found_reg_no if found_reg_no in reg_nos_and_meta_info.keys() else reg_nos_and_meta_info.keys()[0]
        else:
            reg_no = reg_nos_and_meta_info.keys()[0]  # If parsing fails, return the first reg_no
        
        return reg_no

    async def getting_reg_no_from_ai(self, related_reg_nos: list, plaintiff_id: str, certificate_basepath: str) -> str:
        """Get the registration number from AI based on the provided certificate_basepath.

        Args:
            related_reg_nos (list): A list of related registration numbers.
            plaintiff_id (str): The ID of the plaintiff.
            certificate_basepath (str): The base path of the certificate image.
        
        Returns:
            str: The registration number as determined by the AI. if the parsing is unsuccessful, return None.
        """
        prompt = f"""
        Looking at this copyright certificate image, please identify:
        1. The registration number (usually 6-7 digits start with two English characters), usually it starts with VA or has the word copyright before the number
        
        The current reg_nos we have is: {related_reg_nos}
        
        Please respond in this exact format:
        REG_NO: [number]
        
        If you cannot find either number, respond with "NOT_FOUND" for that field.
        """

        cos_key = f"plaintiff_images/{plaintiff_id}/high/{certificate_basepath}"

        with tempfile.NamedTemporaryFile(delete=False, suffix='.webp') as temp_file:
            certificate_path = temp_file.name

        download_success = download_from_cos(
            self.cos_client, self.cos_bucket, cos_key, certificate_path
        )

        if not download_success:
            raise RuntimeError(f"Failed to download certificate image from COS: {cos_key}")

        prompt_list = [("text", prompt), ("image_path", certificate_path)]
        ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE, useVertexAI=Constants.IMAGE_MODEL_FREE_VERTEX)

        os.remove(certificate_path)

        # Parse LLM response
        reg_match = re.search(r'REG_NO:\s*(.*|NOT_FOUND)', ai_answer)
        reg_no = reg_match.group(1) if reg_match and reg_match.group(1) != 'NOT_FOUND' else None
        if reg_no:
            split_str = split_reg(reg_no)
            if len(split_str) == 2:
                # If we have both parts, we can use them
                return zero_pad_reg_no(split_str[0], split_str[1])
            
        return None



    def generate_md_registration_number(self, plaintiff_id) -> str:
        """
        Generate a new MD registration number for unknown copyrights.
        Format: MDxxxxyyyy where xxxx is plaintiff_id 0-padded to 4 digits, yyyy is consecutive number.
        """
        # Get existing MD numbers for this plaintiff
        plaintiff_str = f"{plaintiff_id:04d}"
        pattern = f"MD{plaintiff_str}"

        existing_numbers = []
        if len(self.copyright_md_list) > 0:

            # Extract the consecutive numbers
            for reg_no in self.copyright_md_list:
                if len(reg_no) == 10 and reg_no.startswith(pattern):  # MD + 4 digits + 4 digits
                    try:
                        consecutive_num = int(reg_no[-4:])
                        existing_numbers.append(int(consecutive_num))
                    except ValueError:
                        continue

        # Find next consecutive number
        next_num = 1
        if existing_numbers:
            next_num = max(existing_numbers) + 1

        return f"MD{plaintiff_str}{next_num:04d}"
    
    def query_copyrights_table(self, reg_nos: list) -> list:
        """Query the copyrights table for specific registration numbers.

        Args:
            reg_nos (list): List of registration numbers to query.

        Returns:
            list: List of dictionaries containing the queried data in the same order as the input reg_nos.
            If a reg_no is not found, its corresponding entry in the list will be None.
        """
        results = [self.copyrights_cache.get(reg_no) for reg_no in reg_nos]
        return results

    def upsert_copyrights_table(self, plaintiff_id: str, upsert_data: dict) -> bool:
        """Upsert a record into the copyrights table."""
        try:
            clean_date_of_creation = int(upsert_data.get("date_of_creation", None))
        except (ValueError, TypeError):
            clean_date_of_creation = None

        raw_pub_date = upsert_data.get("date_of_publication", None)
        clean_date_of_publication = parse_publication_date(str(raw_pub_date)) if raw_pub_date else None

        upsert_data = {k: v for k, v in upsert_data.items() if v is not None and v != ""}

        try:
            cursor = self.db_conn.cursor()

            insert_value = [
                True,  # tro
                upsert_data['registration_number'],
                upsert_data.get("registration_date", None),
                upsert_data.get("type_of_work", None),
                upsert_data.get("title", None),
                clean_date_of_creation,
                clean_date_of_publication,
                upsert_data.get("copyright_claimant", None),
                upsert_data.get("authorship_on_application", None),
                upsert_data.get("rights_and_permissions", None),
                upsert_data.get("description", None),
                upsert_data.get("nation_of_first_publication", None),
                upsert_data.get("names", None),
                plaintiff_id
            ]

            insert_columns_str = ", ".join(COPYRIGHT_COLUMNS)
            placeholders = ", ".join(["%s"] * len(COPYRIGHT_COLUMNS))
            update_set_str = ", ".join([f'"{col}" = EXCLUDED."{col}"' for col in COPYRIGHT_COLUMNS if col != 'registration_number'])
            update_set_str += ", update_time = NOW()"

            upsert_query = f"""
                INSERT INTO copyrights ({insert_columns_str})
                VALUES ({placeholders})
                ON CONFLICT (registration_number) DO UPDATE SET
                    {update_set_str}
            """
            cursor.execute(upsert_query, tuple(insert_value))
            self.db_conn.commit()
            
            # Update cache
            cached_item = self.copyrights_cache.get(upsert_data['registration_number'], {})
            cached_item.update(dict(zip(COPYRIGHT_COLUMNS, insert_value)))
            self.copyrights_cache[upsert_data['registration_number']] = cached_item

            return True
        except Exception as e:
            print(f"Error upserting to copyrights table: {e}")
            return False
        finally:
            cursor.close()

    def upsert_copyright_file(self, registration_number: str, filename: str, method: str):
        """Upsert a file record into the copyrights_files table."""
        try:
            cursor = self.db_conn.cursor()
            query = """
                INSERT INTO copyrights_files (registration_number, filename, method)
                VALUES (%s, %s, %s)
                ON CONFLICT (filename) DO UPDATE SET
                    registration_number = EXCLUDED.registration_number,
                    method = EXCLUDED.method,
                    update_time = NOW();
            """
            cursor.execute(query, (registration_number, filename, method))
            self.db_conn.commit()

            # Update cache
            if registration_number in self.copyrights_cache:
                if 'filenames' not in self.copyrights_cache[registration_number]:
                    self.copyrights_cache[registration_number]['filenames'] = []
                if filename not in self.copyrights_cache[registration_number]['filenames']:
                    self.copyrights_cache[registration_number]['filenames'].append(filename)
        except Exception as e:
            print(f"Error upserting to copyrights_files table: {e}")
        finally:
            cursor.close()

    async def find_valid_reg_no_and_meta_info(self, reg_no: str, plaintiff_id: int, method: str, old_certificate_basepath: str) -> Tuple[bool, str]:
        """Find a valid registration number and its metadata from the database or generate a new one
        the function will first check the related registration numbers based on the provided reg_no,
        ...
        Finally the function will upsert the info to the database

        Args:
            reg_no (str): The registration number to validate or generate.
            plaintiff_id (int): The ID of the plaintiff.
            method (str): The method used for processing the copyright.

        Returns:
            Tuple[str, Dict]: A tuple containing either the procedure is successful and the standardized registration number,
                            or False and an empty string if something goes wrong.
        """
        related_reg_nos = get_related_reg_nos(reg_no)
        new_reg_no = self.generate_md_registration_number(plaintiff_id)

        # Query the copyrights table for the related registration numbers
        related_reg_nos_meta_info = self.query_copyrights_table(related_reg_nos)
        found_elements = {reg_no: meta for reg_no, meta in zip(related_reg_nos, related_reg_nos_meta_info) if meta}

        if len(found_elements) == 1: # when only one reg_no is found in the database
            found_reg_no = next(iter(found_elements.keys()))
            return True, found_reg_no
        elif len(found_elements) > 1:
            # asked AI to find the most accurate reg_no
            matched_reg_no_ai = await self.getting_matched_reg_no_from_ai(found_elements, plaintiff_id, old_certificate_basepath)
            found_elements[matched_reg_no_ai]['registration_number'] = matched_reg_no_ai
            return True, matched_reg_no_ai
        else: # when no reg_no is found in the database
            # search reg_no in UCSO
            for reg_no in related_reg_nos:
                if not reg_no.startswith('MD'):
                    search_info = await get_info_from_USCO_using_reg_no(reg_no, existing_driver=self.driver, include_canceled=True, max_click_attempts=1)
                    if search_info:
                        found_elements[reg_no] = search_info

            if len(found_elements) == 1:
                found_reg_no = next(iter(found_elements.keys()))
                found_elements[found_reg_no]['registration_number'] = found_reg_no
                self.upsert_copyrights_table(plaintiff_id, found_elements[found_reg_no])
                self.upsert_copyright_file(found_reg_no, f"{found_reg_no}_{method}.webp", method)
                return True, found_reg_no
            elif len(found_elements) > 1:
                # asked AI to find the most accurate reg_no
                matched_reg_no_ai = await self.getting_matched_reg_no_from_ai(found_elements, plaintiff_id, old_certificate_basepath)
                found_elements[matched_reg_no_ai]['registration_number'] = matched_reg_no_ai
                self.upsert_copyrights_table(plaintiff_id, found_elements[matched_reg_no_ai])
                self.upsert_copyright_file(matched_reg_no_ai, f"{matched_reg_no_ai}_{method}.webp", method)
                return True, matched_reg_no_ai
            else: # when reg_no hasn't been found in the database and USCO, review the certificate image using AI
                reg_no_ai = await self.getting_reg_no_from_ai(related_reg_nos, plaintiff_id, old_certificate_basepath)
                if reg_no_ai:
                    meta_info = self.query_copyrights_table([reg_no_ai])[0]  # Check if the AI found reg_no exists in the database
                    if meta_info:
                        return True, reg_no_ai
                    else:
                        meta_info = await get_info_from_USCO_using_reg_no(reg_no_ai, existing_driver=self.driver, include_canceled=True, max_click_attempts=1)
                        if meta_info:
                            meta_info['registration_number'] = reg_no_ai
                            self.upsert_copyrights_table(plaintiff_id, meta_info)
                            self.upsert_copyright_file(reg_no_ai, f"{reg_no_ai}_{method}.webp", method)
                            return True, reg_no_ai
                        else:
                            self.upsert_copyrights_table(plaintiff_id, {"registration_number": new_reg_no})
                            self.upsert_copyright_file(new_reg_no, f"{new_reg_no}_{method}.webp", method)
                            return True, new_reg_no
                else:
                     # check if the original reg_no starts with MD
                    if reg_no.startswith('MD'):
                        self.upsert_copyrights_table(plaintiff_id, {"registration_number": reg_no})
                        self.upsert_copyright_file(reg_no, f"{reg_no}_{method}.webp", method)
                        return True, reg_no
                    else:
                        self.upsert_copyrights_table(plaintiff_id, {"registration_number": new_reg_no})
                        self.upsert_copyright_file(new_reg_no, f"{new_reg_no}_{method}.webp", method)
                        return True, new_reg_no

    async def process_copyright_entry(self, copyright_entry: Dict, plaintiff_id: int) -> Tuple[bool, Dict]:
        """Process a single copyright entry and return updated data
        
        Args:
            copyright_entry (Dict): The copyright entry to process.
            plaintiff_id (int): The ID of the plaintiff.

        Returns:
            Tuple[bool, Dict]: A tuple containing a success flag and the updated copyright data.
        
        """
        original_reg_nos = copyright_entry.get('reg_no', [])
        original_full_filenames = copyright_entry.get('full_filename', [])

        updated_data = copyright_entry.copy()

        # double check our assumption
        # fullname list only contains one item
        # reg_no list only contains one item
        if original_full_filenames is None or len(original_full_filenames) != 1:
            print(f"\nEntry{'='*100}")
            print(f"Skipping copyright entry due to missing or mismatched full_filename: {copyright_entry}")
            return False, copyright_entry

        # Standarize the reg_no
        if original_reg_nos:
            original_reg_no = original_reg_nos[0]

            # Apply format fixes
            # original_reg_no = fix_reg_no_format(original_reg_no)
            # original_reg_no = handle_13_char_reg_no(original_reg_no)

            split_str = split_reg(original_reg_no)

            if len(split_str) == 2:
                formated_reg_no = zero_pad_reg_no(split_str[0], split_str[1])
            else:
                print(f"\033[91mError formated_reg_no not expected: {original_reg_no} \033[0m")
                reg_no_ai = await self.getting_reg_no_from_ai(original_reg_no, plaintiff_id, original_full_filenames[0])

                if reg_no_ai:
                    split_str = split_reg(reg_no_ai)
                    if len(split_str) == 2:
                        formated_reg_no = zero_pad_reg_no(split_str[0], split_str[1])
                    else:
                        formated_reg_no = self.generate_md_registration_number(plaintiff_id)
                else:
                    formated_reg_no = self.generate_md_registration_number(plaintiff_id)
        else:
            formated_reg_no = self.generate_md_registration_number(plaintiff_id)

        # Find the method name from the original full_filenames
        matched_format = re.match(r'^[A-Za-z0-9]+_([A-Za-z]+)?(?:_\d+)?(_full)?\.webp$', original_full_filenames[0])
        if matched_format:
            method = matched_format.group(1)
        else:
            method = 'Exhibit'

        # Handle special case: MD files with "Exhibit" in the name - use AI to find registration number
        if original_full_filenames[0].startswith('MD') and 'exhibit' in original_full_filenames[0].lower():
            print(f"🤖 MD file with 'Exhibit' detected: {original_full_filenames[0]}. Using AI to find registration number.")
            try:
                reg_no_ai = await self.getting_reg_no_from_ai([], plaintiff_id, original_full_filenames[0])
                if reg_no_ai:
                    # Use the AI-found registration number instead of the original
                    split_str = split_reg(reg_no_ai)
                    if len(split_str) == 2:
                        formated_reg_no = zero_pad_reg_no(split_str[0], split_str[1])
                    print(f"   -> AI found registration number: {reg_no_ai}")
                else:
                    print(f"   -> AI could not find registration number in certificate")
            except Exception as e:
                print(f"   -> Error using AI for MD Exhibit file: {e}")

        success_upsert, formated_reg_no = await self.find_valid_reg_no_and_meta_info(formated_reg_no, plaintiff_id, method, original_full_filenames[0])
        if formated_reg_no.startswith('MD'):
            self.copyright_md_list.add(formated_reg_no)  # Add the new MD reg_no to the existing list


        if not success_upsert:
            print(f"Failed to update database for reg_no {formated_reg_no} and plaintiff_id {plaintiff_id}")
            return False, copyright_entry
        else:
            updated_data['reg_no'] = [formated_reg_no]  # Update the reg_no in the data 

        # Update the full filenames
        final_full_filenames = []
        for original_full_filename in original_full_filenames:
            matched_pattern = re.match(r'^[A-Za-z0-9]+_([A-Za-z]+)(?:_\d+)?_full\.webp$', original_full_filename)
            if matched_pattern: # Format VA0002445774_GenAI_full.webp. Same as countring the number of _   => this case is a single _
                final_full_filenames.append(f"{formated_reg_no}_{matched_pattern.group(1)}_full.webp")
            else:  # Format US_DIS_XXX_XXXX_XXXX_XXXX.web. Same as countring the number of _  => this case is multiple _
                final_full_filenames.append(f"{formated_reg_no}_Exhibit_full.webp")
        updated_data['full_filename'] = final_full_filenames

        return True, updated_data

    async def migrate_case_copyrights(self, case_row: pd.Series) -> bool:
        """Migrate all copyrights for a single case
        
        Args:
            case_row (pd.Series): A row from the DataFrame containing case information.
        
        Returns:
            bool: True if migration was successful, False otherwise.
        """
        updated_copyrights = {}
        files_to_copy = []  # List of (old_filename, new_filename) tuples

        append_new_filename_key = set()  # To track new filenames that are already added
        
        case_id = case_row['id']
        plaintiff_id = int(float(case_row['plaintiff_id']))
        images_data = case_row['images']
        copyrights_data = images_data['copyrights']

        # process each copyright entry of a single case
        for old_filename, copyright_entry in copyrights_data.items():
            # Process this copyright entry
            success, updated_data = await self.process_copyright_entry(copyright_entry, plaintiff_id)

            if not success:
                # Keep original data if processing failed
                updated_copyrights[old_filename] = copyright_entry
                self.error_count += 1
                continue
            
            if not updated_data:
                print(f"Removing copyright {old_filename} from case {case_id}")
                self.processed_count += 1 # Count as processed, but removed
                continue
            
            registration_number = updated_data['reg_no']

            # Extract reg_no from old_filename and apply format fixes
            old_filename_reg_no = old_filename.split('_')[0]

            if re.match(r'^[A-Za-z0-9]+_[A-Za-z]+(?:_\d+)?\.webp$', old_filename) and old_filename_reg_no == registration_number[0]: # Format VA0002445774_GenAI.webp. Same as countring the number of _   => this case is a single _
                # new_filename_reg_no = fix_reg_no_format(old_filename_reg_no)
                # new_filename_reg_no = handle_13_char_reg_no(new_filename_reg_no)
                
                new_filename_key = old_filename
                if registration_number[0] in old_filename: # correct format:
                    new_filename_key = old_filename
                else: # wrong format:  VAu00123456 in VAu000123456 is False
                    print(f"⚠️ Incorrect registration number format in {old_filename}. Will attempt to fix it.")
                    new_filename_key = old_filename.replace(old_filename.split("_")[0], registration_number[0])
            else: # Format US_DIS_XXX_XXXX_XXXX_XXXX.web. Same as countring the number of _  => this case is multiple _
                new_filename_key = f"{registration_number[0]}_Exhibit.webp"
            
            # Add the main filename to files to copy
            if new_filename_key in append_new_filename_key:
                # Handle filename collision
                base, ext = os.path.splitext(new_filename_key)
                i = 1
                while True:
                    new_filename_key_attempt = f"{base}_{i}{ext}"
                    if new_filename_key_attempt not in append_new_filename_key:
                        new_filename_key = new_filename_key_attempt
                        break
                    i += 1
                
                # Update the filenames in the database
                self.upsert_copyright_file(registration_number[0], new_filename_key, method='Exhibit')

            if old_filename != new_filename_key:
                files_to_copy.append((old_filename, new_filename_key))

            # Handle full_filename(s)
            original_full_filenames = copyright_entry.get('full_filename', [])
            new_full_filenames = updated_data.get('full_filename', [])

            if len(original_full_filenames) == len(new_full_filenames):
                for i in range(len(original_full_filenames)):
                    old_full = original_full_filenames[i]
                    new_full = new_full_filenames[i]
                    if old_full and new_full:
                        if old_full != new_full:
                            # also change the new_full name to have the suffix
                            if new_filename_key.rsplit('_', 1)[-1].isdigit():
                                base, ext = os.path.splitext(new_full)
                                new_full = f"{base}_{new_filename_key.rsplit('_', 1)[-1]}{ext}"
                            files_to_copy.append((old_full, new_full))
            else:
                print(f"Mismatched original and new full_filenames for {old_filename}. Skipping full_filename copies.")

            # Store with new filename as key
            updated_copyrights[new_filename_key] = updated_data
            self.processed_count += 1
            append_new_filename_key.add(new_filename_key)

        # Copy all files asynchronously using throttled copy to limit concurrency
        copy_tasks = []
        for old_filename, new_filename in files_to_copy:
            task = self.throttled_copy(old_filename, new_filename, plaintiff_id)
            copy_tasks.append(task)

        if copy_tasks:
            copy_results = await asyncio.gather(*copy_tasks, return_exceptions=True)
            failed_copies = [i for i, result in enumerate(copy_results) if isinstance(result, Exception) or not result]

            if failed_copies:
                print(f"Failed to copy {len(failed_copies)} files for case {case_id}")

        # Update the case data
        case_row['images']['copyrights'] = updated_copyrights

        return True


    async def migrate_all_cases(self, df: pd.DataFrame) -> None:
        """Migrate all cases in the dataframe
        
        Args:
            df (pd.DataFrame): DataFrame containing cases with copyrights images.
        
        Returns:
            None
        """
        print(f"ℹ️ Starting migration of {len(df)} cases")

        for i, (index, row) in enumerate(df.iterrows()):
            case_id = row.get('id', 'unknown')
            try:
                old_copyright_data = copy.deepcopy(row['images']['copyrights'])
                await self.migrate_case_copyrights(row)
                new_copyright_data = row['images']['copyrights']

                # Update the case info in the tb_case directly impact the rendering of the case images
                if json.dumps(old_copyright_data, sort_keys=True) != json.dumps(new_copyright_data, sort_keys=True):
                    
                    insert_and_update_df_to_GZ_batch(df.loc[[index]], "tb_case", key_column="id")
                    print(f"ℹ️ Updated table tb_case for case {case_id}: changes detected.")
                # else:
                    # print(f"ℹ️ Skipping DB update for case {case_id}: no changes detected in copyrights data.")

                if (i + 1) % 10 == 0:
                    print(f"\033[91mProcessed {i + 1}/{len(df)} cases\033[0m")
                    # Save user answers cache
                    # if self.user_answers_cache:
                    #     self._save_user_answers_cache()
            except Exception as e:
                # log the case to the file
                log_file = os.path.join("Alerts/PicturesProcessing/mcf_error_log.txt")
                os.makedirs(os.path.dirname(log_file), exist_ok=True)
                try:
                    with open(log_file, "a", encoding="utf-8") as f:
                        f.write(f"Error processing case {case_id}: {e}, traceback: {traceback.format_exc()}\n")
                except Exception as e:
                    print(f"Error logging case {case_id}: {e}")


                print(f"❌ Error processing case {case_id}: {e}, traceback: {traceback.format_exc()}")
                self.error_count += 1

        print(f"✅ Migration completed. Processed: {self.processed_count}, Skipped: {self.skipped_count}, Errors: {self.error_count}")

# Perform migration
async def run_migration(df_to_migrate: pd.DataFrame, existing_md_copyright_list: set) -> None:
    """Run the migration process for all cases in the DataFrame.

    Args:
        df_to_migrate (pd.DataFrame): DataFrame containing cases with copyrights images(must have at least one copyright).
        existing_md_copyright_list (set): Set of existing MD registration numbers.
    """
    if df_to_migrate['images'].apply(lambda x: x.get('copyrights')).isnull().any():
        raise ValueError("DataFrame contains cases without copyright images.")

    async with CopyrightFilenameMigrator(existing_md_copyright_list) as migrator:
        await migrator.migrate_all_cases(df_to_migrate)

def main():
    """Main migration function"""
    print("Starting Copyright Filename Migration")
    print("=" * 50)

    # Reset the copyright database table
    # TODO: feel free to put the reset func here

    # get case with copyrights and existing MD registration numbers
    df_to_migrate, existing_md_copyright_list = getting_migration_cases()

    if df_to_migrate.empty:
        return

    # TODO 
    df_to_migrate = df_to_migrate
    # df_to_migrate = df_to_migrate[df_to_migrate['id'] == 2956]
    
    # # save the df_to_migrate.docket to a txt file
    # log_file = os.path.join("Alerts/PicturesProcessing/testing_logName.txt")
    # try:
    #     with open(log_file, "w", encoding="utf-8") as f:
    #             for docket in df_to_migrate['docket']:
    #                 f.write(f"{docket}\n")
    # except Exception as e:
    #     print(f"Error logging docket numbers: {e}")

    # df_to_migrate = df_to_migrate[df_to_migrate['docket'] == '1:22-cv-04189']
    
    # Perform migration
    asyncio.run(run_migration(df_to_migrate, existing_md_copyright_list))


if __name__ == "__main__":
    main()