import os
from Check.PDF_ReportLab.generate_pdf_report import generate_pdf_report

async def generate_pdf_task(ctx, data):
    """
    ARQ task to generate a PDF report.
    """
    check_id = data.get('check_id', 'unknown')
    print(f"[{check_id}] generate_pdf_task started.")
    try:
        print(f"[{check_id}] Calling generate_pdf_report...")
        pdf_path = generate_pdf_report(data)
        print(f"[{check_id}] generate_pdf_report finished successfully. Path: {pdf_path}")
        return {"status": "completed", "path": pdf_path}
    except Exception as e:
        print(f"[{check_id}] An error occurred in generate_pdf_task: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}