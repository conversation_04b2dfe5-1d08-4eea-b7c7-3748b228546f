# queue_manager.py
import threading
import queue
import time
from typing import Dict, Any, Optional, List, Tuple

from logdata import log_message, task_context, create_reprocessing_run_and_step, update_step_status, finish_run
import ui_controllers.cache_manager as cache_manager # Import cache_manager
from DatabaseManagement.ImportExport import get_table_from_GZ # For fetching single case

# --- Global Queue Management Variables ---
update_progress_queues: Dict[int, queue.Queue] = {} # For SSE streaming progress to clients
case_update_queue: List[Dict[str, Any]] = [] # The actual job queue
case_processing_active: bool = False # Flag indicating if a job is currently being processed
_currently_processing_job_details: Optional[Dict[str, Any]] = None # Stores details of the job being processed
_lock = threading.RLock() # Reentrant Lock for thread-safe operations on shared queue variables, allows multiple acquisitions by same thread

def get_progress_queue_for_case(case_id: int) -> queue.Queue:
    """
    Retrieves or creates a progress queue for a specific case_id.
    Used for SSE streaming.
    """
    with _lock:
        if case_id not in update_progress_queues:
            update_progress_queues[case_id] = queue.Queue()
        return update_progress_queues[case_id]

def enqueue_case_processing_job(case_id: int, processing_options: Dict[str, Any], initiated_by: str) -> Tuple[bool, str, Optional[int]]:
    """
    Adds a case processing job to the central queue.

    Args:
        case_id: The ID of the case.
        processing_options: Dictionary of options for the processing task.
        initiated_by: A string describing the origin of the request.

    Returns:
        Tuple (success: bool, message: str, queue_position: int or None)
    """
    global case_update_queue # Keep global for now, could be encapsulated in a class later

    log_message(f"QueueManager: Enqueueing job for case {case_id} by {initiated_by}. Options: {processing_options}", level="INFO")

    with _lock:
        q = get_progress_queue_for_case(case_id) # Ensures SSE queue exists for this case_id
        for queued_job in case_update_queue:
            # This check is inside the lock, should be quick
            if queued_job['case_id'] == case_id:
                # More sophisticated duplicate checking might be needed if re-queuing the same case
                # with different options is a valid scenario while it's pending.
                msg = f"Case {case_id} already in processing queue. Request by {initiated_by} ignored."
                log_message(f"QueueManager: {msg}", level="INFO")
                return False, msg, None

        case_job = {
            'case_id': case_id,
            'processing_options': processing_options,
            'initiated_by': initiated_by,
            'queued_time': time.time()
        }
        case_update_queue.append(case_job)
        current_queue_len = len(case_update_queue) # Number of items waiting
    
    added_message = f"Case {case_id} added by {initiated_by}. Queue pos: {current_queue_len}."
    q.put(added_message) # Put message into the specific case's progress queue
    log_message(f"QueueManager: {added_message}", level="INFO")

    _trigger_processing_if_idle() # Attempt to start processing if idle
    
    # Inform about waiting position if processing is active and this job isn't the one starting
    with _lock:
        if case_processing_active and current_queue_len > 0 : 
            is_current_job_the_one_processing = (_currently_processing_job_details and 
                                                 _currently_processing_job_details['case_id'] == case_id)
            if not is_current_job_the_one_processing:
                actual_pos_in_waiting_queue = 0
                for i, job_in_q in enumerate(case_update_queue): # case_update_queue only has waiting jobs
                    if job_in_q['case_id'] == case_id:
                        actual_pos_in_waiting_queue = i + 1 # 1-based index for waiting items
                        break
                if actual_pos_in_waiting_queue > 0:
                    waiting_message = f"Processing active. Case {case_id} (from {initiated_by}) is at waiting queue position: {actual_pos_in_waiting_queue}."
                    q.put(waiting_message)
                    log_message(f"QueueManager: {waiting_message}", level="INFO")

    return True, "Task queued successfully.", current_queue_len


def _trigger_processing_if_idle():
    """Checks if processing is idle and starts the next job if the queue is not empty."""
    print("DEBUG: QueueManager: _trigger_processing_if_idle called.") # Use print for pre-lock logging
    should_start_processing = False
    current_job_details_for_log = "None" # Default for logging

    with _lock:
        current_job_details_for_log = _currently_processing_job_details # Capture for logging

        if not case_processing_active and case_update_queue: # If not active and queue has items
            log_message("QueueManager: Trigger: Idle and queue has items. Attempting to start processing.", level="DEBUG")
            should_start_processing = True
            # case_processing_active will be set to True by _process_next_case_in_queue
        elif case_processing_active:
            log_message(f"QueueManager: Trigger: Processing is already active for job: {_currently_processing_job_details}. New job will wait.", level="INFO")
        elif not case_update_queue:
            log_message("QueueManager: Trigger: Queue is empty. Nothing to process.", level="INFO")

    print("DEBUG: QueueManager: _trigger_processing_if_idle released _lock.") # Use print

    if should_start_processing:
        log_message("QueueManager: Trigger: Calling _process_next_case_in_queue.", level="DEBUG")
        _process_next_case_in_queue()
    else:
        log_message(f"QueueManager: Trigger: Not starting new processing. Active: {case_processing_active}, Queue empty: {not case_update_queue}. Current job: {current_job_details_for_log}", level="DEBUG")

def _process_next_case_in_queue():
    """Internal: Picks the next case from the queue and starts processing it in a new thread."""
    global case_update_queue, case_processing_active, _currently_processing_job_details

    job_to_process = None
    can_process_now = False

    with _lock:
        print("DEBUG: QueueManager: _process_next_case_in_queue acquired _lock.") # Use print
        if not case_processing_active and case_update_queue:
            can_process_now = True
            case_processing_active = True # Mark as active
            job_to_process = case_update_queue.pop(0) # Dequeue the job
            _currently_processing_job_details = job_to_process
            
            # This log_message is inside the lock. If it blocks, the lock is held.
            log_message(f"QueueManager: Dequeued job for case {job_to_process['case_id']}. Initiated by: {job_to_process['initiated_by']}. Remaining in queue: {len(case_update_queue)}", level="INFO")

        elif case_processing_active:
            log_message(f"QueueManager: ProcessNext: Attempted to process next, but a job is already active. Currently processing: {_currently_processing_job_details}", level="INFO")
        elif not case_update_queue:
            log_message("QueueManager: ProcessNext: Queue is empty. Ensuring processing flag is clear.", level="INFO")
            case_processing_active = False # Ensure flag is correct if queue emptied
    
    print("DEBUG: QueueManager: _process_next_case_in_queue released _lock.") # Use print

    if can_process_now and job_to_process:
        log_message(f"QueueManager: Preparing to start thread for case {job_to_process['case_id']}.", level="DEBUG")
        thread = threading.Thread(
            target=_process_case_in_thread,
            args=(
                job_to_process['case_id'],
                job_to_process['processing_options'],
                job_to_process.get('initiated_by', "Unknown")
            )
        )
        thread.daemon = True # Ensure thread doesn't block program exit
        thread.start()
        log_message(f"QueueManager: Started processing thread for case {job_to_process['case_id']}.", level="INFO")
    elif not job_to_process and can_process_now: # Should not happen if can_process_now is true
        log_message("QueueManager: ProcessNext: Logic error - can_process_now was true but no job was dequeued.", level="ERROR")


def _process_case_in_thread(case_id: int, processing_options: Dict[str, Any], initiated_by: str):
    """Worker function that processes a single case. Runs in a separate thread."""
    global case_processing_active, _currently_processing_job_details
    import asyncio
    from Alerts.ReprocessCases import reprocess_cases # Import heavy modules in thread

    q = get_progress_queue_for_case(case_id) # Get the SSE progress queue for this case
    db_run_id = None # Initialize
    start_time_thread = time.time()

    # --- Pre-fetch and update the single case in cache ---
    log_message(f"QueueManager: Thread for case {case_id}: Pre-fetching and updating case in cache.", level="INFO")
    try:
        # Ensure global caches are loaded (if not already)
        if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
            log_message(f"QueueManager: Thread for case {case_id}: Global cache not loaded. Refreshing.", level="INFO")
            if not cache_manager.refresh_cached_data(force=True): # Force load if None
                raise Exception("Failed to load global cache for reprocessing.")

        # Fetch the specific case data from GZ
        where_clause_refresh = f"id = {int(case_id)}"
        single_case_fresh_df = get_table_from_GZ("tb_case", where_clause=where_clause_refresh, force_refresh=True)

        if single_case_fresh_df is not None and not single_case_fresh_df.empty:
            fresh_case_data_row = single_case_fresh_df.iloc[0]
            # Find the index in the cached_cases_df
            idx_in_cache_list = cache_manager.cached_cases_df.index[cache_manager.cached_cases_df['id'] == case_id].tolist()
            if idx_in_cache_list:
                idx_in_cache = idx_in_cache_list[0]
                for col_name in fresh_case_data_row.index:
                    if col_name in cache_manager.cached_cases_df.columns:
                        cache_manager.cached_cases_df.at[idx_in_cache, col_name] = fresh_case_data_row[col_name]
                log_message(f"QueueManager: Thread for case {case_id}: Successfully refreshed case data from GZ into cache_manager.cached_cases_df.", "INFO")
            else:
                log_message(f"QueueManager: Thread for case {case_id}: Case ID {case_id} not found in cache_manager.cached_cases_df after fetching. This might be a new case or an issue.", "WARNING")
        else:
            log_message(f"QueueManager: Thread for case {case_id}: No data found in GZ for case ID {case_id}. Proceeding with existing cached data if any.", "WARNING")
    except Exception as e_prefetch:
        log_message(f"QueueManager: Thread for case {case_id}: Error during pre-fetch/cache update: {e_prefetch}. Processing will continue with potentially stale cache.", "ERROR")
    # --- End of pre-fetch ---

    log_message(f"QueueManager: Thread for case {case_id} (by {initiated_by}) starting. Options: {processing_options}", level="INFO")
    q.put(f"Job for case {case_id} (by {initiated_by}) started...")

    try:
        # Create a run and step in the database for this reprocessing task
        db_run_id, db_step_name = create_reprocessing_run_and_step(case_id_as_run_id=case_id)
        update_step_status(db_run_id, db_step_name, 'Running')

        op_type = processing_options.get('operation_type', 'reprocess') # Default if not specified
        trace_name = f"{initiated_by} - {op_type} - Case:{case_id}"

        # Set the task context for log_message to use the db_run_id and the progress queue
        with task_context(run_id=db_run_id, step_name=db_step_name, progress_queue_for_case=q):
            success, tracking_dict = asyncio.run(reprocess_cases(
                cases_to_reprocess=case_id,
                processing_options=processing_options,
                trace_name=trace_name,
                full_cases_df=cache_manager.cached_cases_df, # Pass the updated cache
                plaintiff_df=cache_manager.cached_plaintiff_df  # Pass the cache
            ))

        duration = time.time() - start_time_thread
        msg_status = "successfully" if success else "with warnings/errors"
        q.put(f"Case {case_id} processing completed {msg_status} in {duration:.2f}s.")
        log_message(f"QueueManager: Thread for case {case_id} completed {msg_status} in {duration:.2f}s.", level="INFO" if success else "WARNING")
        if db_run_id:
            update_step_status(db_run_id, db_step_name, 'Completed' if success else 'Failed')
            finish_run(db_run_id, 'Completed' if success else 'Failed')

    except Exception as e:
        duration = time.time() - start_time_thread
        error_message = f"Critical error in job for case {case_id} (by {initiated_by}) after {duration:.2f}s: {str(e)}"
        q.put(f"Error: {error_message}")
        log_message(f"QueueManager: {error_message}", level="CRITICAL", exc_info=True) # Use exc_info
        if db_run_id: # db_run_id might be None if create_reprocessing_run_and_step failed
            update_step_status(db_run_id, db_step_name, 'Failed')
            finish_run(db_run_id, 'Failed')
    finally:
        q.put("DONE") # Signal to SSE stream that this case is finished
        with _lock:
            _currently_processing_job_details = None # Clear the currently processing job
            case_processing_active = False # Mark processing as inactive *before* trying to start next
        _trigger_processing_if_idle() # Attempt to process next item in queue

def get_queue_status() -> Dict[str, Any]:
    """Returns the current status of the processing queue."""
    with _lock: # Ensure thread-safe access to shared variables
        queue_snapshot = list(case_update_queue) # Create a snapshot of the waiting queue
        is_processing = case_processing_active
        # Create a snapshot of the job currently being processed
        current_job_details_snapshot = dict(_currently_processing_job_details) if _currently_processing_job_details else None

    currently_processing_info = None
    if current_job_details_snapshot:
        currently_processing_info = {
            "case_id": current_job_details_snapshot.get('case_id'),
            "initiated_by": current_job_details_snapshot.get('initiated_by'),
            "queued_time": time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime(current_job_details_snapshot.get('queued_time', 0))),
            "processing_options": current_job_details_snapshot.get('processing_options') # Optional: for more detail
        }
        
    next_in_queue_info = None
    if queue_snapshot: # If there are items waiting in the queue
        next_job_details = queue_snapshot[0]
        next_in_queue_info = {
            "case_id": next_job_details.get('case_id'),
            "initiated_by": next_job_details.get('initiated_by'),
            "queued_time": time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime(next_job_details.get('queued_time', 0)))
        }

    return {
        "is_processing_active": is_processing,
        "currently_processing": currently_processing_info,
        "waiting_queue_size": len(queue_snapshot), # Number of items waiting
        "next_in_waiting_queue": next_in_queue_info, # First item in the waiting list
        "waiting_jobs_summary": [ # Summary of all jobs currently waiting in the queue
            {
                "case_id": job.get('case_id'),
                "initiated_by": job.get('initiated_by'),
                "queued_time": time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime(job.get('queued_time', 0)))
            } for job in queue_snapshot
        ]
    }