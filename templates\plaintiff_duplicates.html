{% extends 'layout_ui.html' %}

{% block title %}Duplicate Plaintiffs{% endblock %}

{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='visualizer/visualizer.css') }}"> {# Assuming common styles are needed #}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='plaintiffs/plaintiff_duplicates.css') }}">
{% endblock %}

{% block content %}
    <div class="app-container">
        <div class="duplicates-header">
            <h2>Manual Duplicate Detection</h2>
            <p>Review potential duplicate plaintiffs and choose which one to keep.</p>
            <button id="back-to-plaintiffs" class="btn secondary">Back to Plaintiffs</button>
        </div>

        <div class="duplicates-container">
            <div class="loading">Analyzing plaintiffs for duplicates. This may take a few minutes...</div>
            <div id="duplicates-list"></div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script src="{{ url_for('static', filename='plaintiffs/plaintiff_duplicates.js') }}"></script>
    <script>
        // Add active class to menu item if it exists
        const menuItem = document.getElementById('menu-plaintiffs'); // Assuming it shares the main plaintiffs menu item
        if (menuItem) {
            menuItem.classList.add('active');
        }
        // Add specific active class if needed for sub-navigation
        const subMenuItem = document.getElementById('menu-plaintiff-duplicates');
         if (subMenuItem) {
            subMenuItem.classList.add('active');
        }
    </script>
{% endblock %}