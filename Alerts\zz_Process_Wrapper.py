# Removed logging import
import time
import multiprocessing
import pandas as pd
from logdata import log_message, create_new_run
from email_sender import send_email_report
import pytz
from datetime import datetime

# Import the new WorkflowManager and potentially DatabaseHandler
from . import zz_WorkflowManager
# Removed: from DatabaseManagement.database_handler import <PERSON>Handler
from DatabaseManagement.ImportExport import get_table_from_GZ # Added
# from Common.Constants import FILE_TYPE_STRATEGY_IP_ONLY # Import default strategy if needed

# This File is used to wrap the WorkflowManager processes into a separate process (spawned)
# The process wrapper are called from the API in app.py

# Removed configure_logging_for_process and cleanup_process_resources functions

def monitor_process(process, run_id):
    """Monitor process status and update database accordingly"""
    # Note: Using print here as it's monitoring the *external* process from the main thread.
    # log_message might only log within the spawned process context depending on setup.
    max_wait = 30  # seconds to wait for process to start
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        if process.is_alive():
            print(f"Process {process.pid} is running for run_id {run_id}")
            return True
        time.sleep(0.5)
    
    print(f"Process for run_id {run_id} failed to start within {max_wait:.1f} seconds")
    return False


def process_wrapper_get_alerts(run_id, selected_date, loop_back_days, force_redo):
    # file_handler = None # Removed logging setup
    try:
        # Ensure spawn method is set (might be redundant if set globally, but safe)
        # multiprocessing.set_start_method('spawn', force=True) # Can cause issues if called multiple times

        start_time = time.time()
        log_message(f"🚀 Starting daily fetch process {run_id}...", level='INFO')

        # Define default options for the workflow
        default_options = {
            'file_type_strategy': "IP_ONLY", 
            'force_refresh': force_redo, # Pass the specific flag
            'update_steps': True, # Example default
            'process_pictures': True,
            'upload_files_nas': True,
            'upload_files_cos': True,
            'run_plaintiff_overview': True, # Enable AI for daily fetch
            'run_summary_translation': True,
            'run_step_translation': True,
            'refresh_cache_after_ai': True,
            'max_retries': 3,
            'delay_between_retries': 5
        }

        # Run the actual process using WorkflowManager
        zz_WorkflowManager.process_daily_fetch(
            run_id=run_id,
            selected_date=selected_date,
            loop_back_days=loop_back_days,
            force_redo=force_redo, # Pass force_redo flag
            options=default_options
        )

        duration = time.time() - start_time
        log_message(f"🏁 Daily fetch process {run_id} completed via WorkflowManager in {duration:.2f} seconds", level='INFO')
        send_email_report(run_id)

    except Exception as e:
        log_message(f"💥 Daily fetch process {run_id} failed: {str(e)}", level='CRITICAL', exc_info=True)
        # Re-raising might be necessary depending on how the caller handles it
        raise

    # finally: # Removed logging cleanup
    #     cleanup_process_resources(file_handler)
    #     pass

def run_fetch_task(selected_date=None, loop_back_days=None, force_redo=False):
    """Generic function that can be called both by scheduler and API endpoint"""
    try:
        print(f"Running task with selected_date: {selected_date} and loop_back_days: {loop_back_days}")
        # If no date provided, use NY timezone's current date
        if selected_date is None:
            selected_date = (datetime.now(pytz.timezone('America/New_York'))).strftime('%Y-%m-%d')
            loop_back_days = 1

        run_id = create_new_run()
        print(f"Main process creating new process for run_id {run_id}")
        
        process = multiprocessing.Process(target=process_wrapper_get_alerts, args=(run_id, selected_date, loop_back_days, force_redo))
        process.start()

        # Monitor process and return status
        if monitor_process(process, run_id):
            return {'run_id': run_id, 'process_id': process.pid, 'status': 'started'}
        else:
            return {'run_id': run_id, 'status': 'failed_to_start'}
            
    except Exception as e:
        print(f"Error in run_task: {str(e)}")
        return {'error': str(e)}
    


def process_wrapper_update_all_subscribed_cases(run_id, threshold_days):
    # file_handler = None # Removed logging setup
    try:
        # Ensure spawn method is set (might be redundant if set globally, but safe)
        # multiprocessing.set_start_method('spawn', force=True) # Can cause issues if called multiple times

        start_time = time.time()
        log_message(f"🔄 Starting update subscribed cases process {run_id}...", level='INFO')

        # Define default options for the workflow
        default_options = {
            'file_type_strategy': FILE_TYPE_STRATEGY_IP_ONLY, # Example default
            'force_refresh': False, # Usually false for updates unless specified
            'update_steps': True, # Usually true for updates
            'process_pictures': True,
            'upload_files_nas': True,
            'upload_files_cos': True,
            'run_plaintiff_overview': True, # Enable AI for updates
            'run_summary_translation': True,
            'run_step_translation': True,
            'refresh_cache_after_ai': True,
            'max_retries': 3,
            'delay_between_retries': 5
        }

        # Fetch the list of subscribed case IDs using DataFrames
        subscribed_case_ids = []
        try:
            log_message("💾 Loading cases and user_case data to find subscribed cases...", level='INFO')
            cases_df = get_table_from_GZ("tb_case")
            user_case_df = get_table_from_GZ("tb_user_case")

            # Filter for open cases
            open_cases_df = cases_df[cases_df['class_code'] == 'Open']

            # Merge to find subscribed open cases
            subscribed_open_cases = pd.merge(open_cases_df, user_case_df, left_on='id', right_on='case_id')

            # Get unique IDs
            subscribed_case_ids = subscribed_open_cases['id'].unique().tolist()
            log_message(f"🔍 Found {len(subscribed_case_ids)} subscribed open cases to update for run_id {run_id}.", level='INFO')

        except Exception as data_err:
             log_message(f"❌ Error loading or filtering data to find subscribed cases: {data_err}", level='ERROR', exc_info=True)
             # Decide if this should halt the process or continue with an empty list

        if subscribed_case_ids:
            # Run the actual process using WorkflowManager
            zz_WorkflowManager.process_specific_cases(
                run_id=run_id,
                case_list=subscribed_case_ids, # Pass the list of IDs
                options=default_options
            )
        else:
            log_message(f"✅ No subscribed cases found needing update for run_id {run_id}.", level='INFO')


        duration = time.time() - start_time
        log_message(f"🏁 Update subscribed cases process {run_id} completed via WorkflowManager in {duration:.2f} seconds", level='INFO')
        send_email_report(run_id)

    except Exception as e:
        log_message(f"💥 Update subscribed cases process {run_id} failed: {str(e)}", level='CRITICAL', exc_info=True)
        raise

    # finally: # Removed logging cleanup
    #     cleanup_process_resources(file_handler)

def run_update_task(threshold_days=2):
    """Similar to run_task but for updating subscribed cases"""
    try:
        run_id = create_new_run()
        print(f"Main process creating new process for update task run_id {run_id}, at {datetime.now()}")
        
        process = multiprocessing.Process(target=process_wrapper_update_all_subscribed_cases, args=(run_id, threshold_days))
        process.start()

        # Monitor process and return status
        if monitor_process(process, run_id):
            return {'run_id': run_id, 'process_id': process.pid, 'status': 'started'}
        else:
            return {'run_id': run_id, 'status': 'failed_to_start'}
            
    except Exception as e:
        print(f"Error in run_update_task: {str(e)}")
        return {'error': str(e)}
    


def process_wrapper_fix_cases_no_ip(run_id):
    # file_handler = None # Removed logging setup
    try:
        # Ensure spawn method is set (might be redundant if set globally, but safe)
        # multiprocessing.set_start_method('spawn', force=True) # Can cause issues if called multiple times

        start_time = time.time()
        log_message(f"🔨 Starting fix cases (no IP) process {run_id}...", level='INFO')

        # Define default options for the workflow
        default_options = {
            'file_type_strategy': FILE_TYPE_STRATEGY_IP_ONLY, # Focus on IP files
            'force_refresh': True, # Force refresh might be needed to get missing IP
            'update_steps': True,
            'process_pictures': True,
            'upload_files_nas': True,
            'upload_files_cos': True,
            'run_plaintiff_overview': False, # Disable AI for fix runs
            'run_summary_translation': False,
            'run_step_translation': False,
            'refresh_cache_after_ai': False, # No AI ran, so no refresh needed
            'max_retries': 3,
            'delay_between_retries': 5
        }

        # Fetch the list of case IDs needing IP fix using DataFrames
        fix_case_ids = []
        try:
            log_message("💾 Loading cases data to find cases needing IP fix...", level='INFO')
            cases_df = get_table_from_GZ("tb_case")

            # Define conditions for needing IP fix (example: status is 'Completed - No IP')
            # Adjust the filter based on the actual logic/status used
            needs_fix_df = cases_df[cases_df['file_status'] == 'Completed - No IP'] # Updated status check

            fix_case_ids = needs_fix_df['id'].tolist()
            log_message(f"🔍 Found {len(fix_case_ids)} cases needing IP fix for run_id {run_id}.", level='INFO')

        except Exception as data_err:
            log_message(f"❌ Error loading or filtering data to find cases needing IP fix: {data_err}", level='ERROR', exc_info=True)
            # Decide if this should halt the process

        if fix_case_ids:
            # Run the actual process using WorkflowManager
            zz_WorkflowManager.process_specific_cases(
                run_id=run_id,
                case_list=fix_case_ids, # Pass the list of IDs
                options=default_options
            )
        else:
            log_message(f"✅ No cases found needing IP fix for run_id {run_id}.", level='INFO')

        duration = time.time() - start_time
        log_message(f"🏁 Fix cases (no IP) process {run_id} completed via WorkflowManager in {duration:.2f} seconds", level='INFO')
        send_email_report(run_id)

    except Exception as e:
        log_message(f"💥 Fix cases (no IP) process {run_id} failed: {str(e)}", level='CRITICAL', exc_info=True)
        raise

    # finally: # Removed logging cleanup
    #     cleanup_process_resources(file_handler)

def run_fix_task():
    """Similar to run_task but for updating subscribed cases"""
    try:
        run_id = create_new_run()
        print(f"Main process creating new process for update task run_id {run_id}, at {datetime.now()}")
        
        process = multiprocessing.Process(target=process_wrapper_fix_cases_no_ip, args=(run_id))
        process.start()

        # Monitor process and return status
        if monitor_process(process, run_id):
            return {'run_id': run_id, 'process_id': process.pid, 'status': 'started'}
        else:
            return {'run_id': run_id, 'status': 'failed_to_start'}
            
    except Exception as e:
        print(f"Error in run_update_task: {str(e)}")
        return {'error': str(e)}
    








