# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from Protos import embedding_pb2 as embedding__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in embedding_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class EmbeddingServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetEmbeddings = channel.unary_unary(
                '/embedding.EmbeddingService/GetEmbeddings',
                request_serializer=embedding__pb2.GetEmbeddingsRequest.SerializeToString,
                response_deserializer=embedding__pb2.GetEmbeddingsResponse.FromString,
                _registered_method=True)
        self.GetImageSplitWithEmbeddings = channel.unary_unary(
                '/embedding.EmbeddingService/GetImageSplitWithEmbeddings',
                request_serializer=embedding__pb2.GetImageSplitWithEmbeddingsRequest.SerializeToString,
                response_deserializer=embedding__pb2.GetImageSplitWithEmbeddingsResponse.FromString,
                _registered_method=True)
        self.GetAllMatches = channel.unary_unary(
                '/embedding.EmbeddingService/GetAllMatches',
                request_serializer=embedding__pb2.GetAllMatchesRequest.SerializeToString,
                response_deserializer=embedding__pb2.GetAllMatchesResponse.FromString,
                _registered_method=True)


class EmbeddingServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetEmbeddings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetImageSplitWithEmbeddings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAllMatches(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EmbeddingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetEmbeddings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEmbeddings,
                    request_deserializer=embedding__pb2.GetEmbeddingsRequest.FromString,
                    response_serializer=embedding__pb2.GetEmbeddingsResponse.SerializeToString,
            ),
            'GetImageSplitWithEmbeddings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetImageSplitWithEmbeddings,
                    request_deserializer=embedding__pb2.GetImageSplitWithEmbeddingsRequest.FromString,
                    response_serializer=embedding__pb2.GetImageSplitWithEmbeddingsResponse.SerializeToString,
            ),
            'GetAllMatches': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAllMatches,
                    request_deserializer=embedding__pb2.GetAllMatchesRequest.FromString,
                    response_serializer=embedding__pb2.GetAllMatchesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'embedding.EmbeddingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('embedding.EmbeddingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class EmbeddingService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetEmbeddings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/embedding.EmbeddingService/GetEmbeddings',
            embedding__pb2.GetEmbeddingsRequest.SerializeToString,
            embedding__pb2.GetEmbeddingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetImageSplitWithEmbeddings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/embedding.EmbeddingService/GetImageSplitWithEmbeddings',
            embedding__pb2.GetImageSplitWithEmbeddingsRequest.SerializeToString,
            embedding__pb2.GetImageSplitWithEmbeddingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAllMatches(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/embedding.EmbeddingService/GetAllMatches',
            embedding__pb2.GetAllMatchesRequest.SerializeToString,
            embedding__pb2.GetAllMatchesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
