import os, sys
sys.path.append(os.getcwd())

from Alerts.PicturesProcessing.RemoveDuplicates import compute_md5
import os
import imagehash
from PIL import Image
import shutil
import re
import asyncio
from Check.Do_Check_Download import download_from_url
from Common.Constants import sanitize_name, sem_task, local_ip_tro_folder
from tqdm.asyncio import tqdm_asyncio
from tqdm import tqdm
from AI.LLM_shared import get_json
from AI.llm_caching import vertex_genai_multi_with_cache
import pandas as pd

async def collect_all_images(df, target_folder_path, case_type, existing_folder_path=None):
    if not os.path.exists(target_folder_path):
        os.makedirs(target_folder_path)
    tasks = []

    semaphore = asyncio.Semaphore(20)
    # df = df.head(40)
    for index, row in df.iterrows():
        if row['images'] and case_type in row['images']:
            # Sanitize the docket format for use in filenames
            docket_sanitized = row['docket'].replace(':', '_')
            for image in row['images'][case_type].keys():
                # Ignore images that are the same as the certificate
                if "full_filename" in row['images'][case_type][image] and row['images'][case_type][image]['full_filename'] == image:
                    continue

                plaintiff_id = int(row['plaintiff_id'])
                # For Patent, add the text to the filename
                if "product_name" in row['images'][case_type][image] and row['images'][case_type][image]['product_name'] != "":
                    file_name = f"{plaintiff_id}_{row['images'][case_type][image]['product_name']}_{image}"
                else:
                    file_name = f"{plaintiff_id}_{image}"

                file_name = sanitize_name(file_name)
                target_path = f"{target_folder_path}/{file_name}"

                # Avoid redownloading pictures which we have already downloaded
                if os.path.exists(target_path):
                    continue
                if existing_folder_path: 
                    existing_path = f"{existing_folder_path}/{file_name}"
                    if os.path.exists(existing_path):
                        continue
                
                url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{int(row['plaintiff_id'])}/high/{image}"
                tasks.append(sem_task(semaphore, download_from_url(url, target_path)))  

    # Process all downloads concurrently
    if tasks:
        await tqdm_asyncio.gather(*tasks)
    else:
        print("No new images to download.")




def classify_trademark_images(df, folder_path):
    # This function is used to fix the trademark images that are in the text folder but should be in the logo folder.
    # It is used after the images have been downloaded and the text folder has been created.

    os.makedirs(f"{folder_path}/Logo", exist_ok=True)
    os.makedirs(f"{folder_path}/Text", exist_ok=True)
    os.makedirs(f"{folder_path}/Unknown", exist_ok=True)
    
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        if os.path.isdir(file_path):
            continue
        
        case_row, case_image, original_image_key = get_case_image_from_df(df, None, filename, "trademarks")

        if case_row is None or case_image is None:
            # get_case_image_from_df might print its own detailed messages
            print(f"No case/image data in df for {filename}. Skipping.")
            continue

        file_destination_path = f"{folder_path}/Unknown" # Default destination
        if "trademark_text" in case_image:
            trademark_text = case_image['trademark_text'][0]
            if trademark_text == "":
                file_destination_path = f"{folder_path}/Logo"
            else:
                # count the number of special characters in the trademark_text
                count_special_characters = sum(1 for c in trademark_text if not c.isalnum() and not c.isspace() and c not in {'-', "'", '.'})
                if count_special_characters == 0:
                    file_destination_path = f"{folder_path}/Text"
                else: # ask AI

                    prompt_list = [
                        ("text", 'This is a registered trademark. You need to classify it as either a Text or a Logo. You answer in this format: {"type": "text"} or {"type": "logo"}'),
                        ("image_path", file_path),
                    ]
                    ai_answer = vertex_genai_multi_with_cache(
                        case_type="trademarks",
                        cache_filename="classify_trademark.json",
                        prompt_list=prompt_list,
                        image_path1=file_path
                    )
                    json_answer = get_json(ai_answer)
                    if "type" in json_answer and json_answer["type"] == "text":
                        file_destination_path = f"{folder_path}/Text"
                    elif "type" in json_answer and json_answer["type"] == "logo":
                        file_destination_path = f"{folder_path}/Logo"
        
        shutil.move(file_path, file_destination_path)


# Remove duplicates by md5 hash
def remove_duplicates(folder_path, reference_folder=None):
    hash_dict = {}

    if reference_folder:
        for filename in os.listdir(reference_folder):
            file_path = os.path.join(reference_folder, filename)
            file_hash = compute_md5(file_path)
            hash_dict[file_hash] = file_path

    
    with tqdm(total=len(os.listdir(folder_path)), desc="Removing Exact Duplicates") as pbar:
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            file_hash = compute_md5(file_path)
            if file_hash in hash_dict:
                # original_file = os.path.basename(hash_dict[file_hash])
                os.remove(file_path)
            else:
                hash_dict[file_hash] = file_path
            pbar.update(1)


# Remove duplicates by filename
def remove_duplicates_by_filename_with_different_extension(folder_path):
    filename_dict = {}
    for full_filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, full_filename)
        filename = full_filename.split(".")[0]
        if filename in filename_dict:
            if full_filename.split(".")[1] == ".webp":
                os.remove(file_path)
            else:
                original_file = filename_dict[filename]
                os.remove(original_file)
        else:
            filename_dict[filename] = file_path

# Remove duplicates by patent name and page number
def remove_patent_duplicates(folder_path):
    filename_dict = {}
    for full_filename in os.listdir(folder_path):
        parts = full_filename.split("_")
        if len(parts) < 2:
            continue
        # plaintiff_id = parts[0] # We don't need it, but it's there
        patent_name = parts[1]
        page_match = re.search(r'page\d+', full_filename)
        if not page_match:
            continue
        page_number = page_match.group(0)
        patent_number_profile = r"US-D?\d{6,8}" # Example: US-D1234567 or US-D12345678
        patent_number_match = re.search(patent_number_profile, full_filename)
        if not patent_number_match:
            continue
        patent_number = patent_number_match.group(0)
        file_path = os.path.join(folder_path, full_filename)
        if (patent_name, patent_number, page_number) in filename_dict:
            os.remove(file_path)
            print(f"Removed duplicate file: {full_filename} and kept {os.path.basename(filename_dict[(patent_name, patent_number, page_number)])}")
        else:
            filename_dict[(patent_name, patent_number, page_number)] = file_path

def remove_text_from_patent_filename(folder_path):
    for filename in os.listdir(folder_path):
        new_filename = "_".join(filename.split("_")[1:])
        os.rename(os.path.join(folder_path, filename), os.path.join(folder_path, new_filename))

# Remove duplicates by perceptual hashing
# def remove_near_duplicates(folder_path, hash_size=16, threshold=10):
#     """
#     Removes near-duplicate images in the specified directory based on perceptual hashing.
#     """
#     hashes = {}
#     for filename in os.listdir(folder_path):
#         file_path = os.path.join(folder_path, filename)
#         try:
#             with Image.open(file_path) as img:
#                 img_hash = imagehash.phash(img, hash_size=hash_size)
#             # Compare with existing hashes
#             duplicate_found = False
#             for existing_hash, existing_file in hashes.items():
#                 if img_hash - existing_hash < threshold:
#                     print(f"Removing near duplicate: {img_hash - existing_hash} threshold")
#                     os.remove(file_path)
#                     duplicate_found = True
#                     break
#             if not duplicate_found:
#                 hashes[img_hash] = file_path
#         except Exception as e:
#             print(f"Error processing {file_path}: {e}")


# Remove duplicates by perceptual hashing
def remove_near_duplicates(target_folder, reference_folder=None, hash_size=16, threshold=60, llm_threshold=30):
    """
    Removes images in target_folder that are near duplicates of any image in reference_folder.
    Uses perceptual hashing via imagehash.
    """
    # Build a dictionary of perceptual hashes for images in the reference folder.
    hashes = {}

    if reference_folder:
        filenames = os.listdir(reference_folder)
        with tqdm(total=len(filenames), desc="Building Hashes of Reference Folder") as pbar:
            for filename in filenames:
                ref_path = os.path.join(reference_folder, filename)
                try:
                    with Image.open(ref_path) as img:
                        ref_hash = imagehash.phash(img, hash_size=hash_size)
                    hashes[ref_hash] = ref_path
                except Exception as e:
                    print(f"Error processing {ref_path}: {e}")
                pbar.update(1)

    # Prepare the folder to store near duplicates.
    parent_dir = os.path.dirname(target_folder)
    near_duplicate_folder = os.path.join(parent_dir, "RemovedNearDuplicate")
    os.makedirs(near_duplicate_folder, exist_ok=True)

    # Process each image in the target folder.

    
    previous_folder_size = 99999999999999
    while previous_folder_size > len(os.listdir(target_folder)): # it is very hard to edit the folder and the hash as we loop through, so instead we break after the first match, and redo untill no file is moved anymore
        filenames = os.listdir(target_folder)
        previous_folder_size = len(filenames)
        with tqdm(total=len(filenames), desc="Processing Target Folder") as pbar:
            for filename in filenames:
                file_path = os.path.join(target_folder, filename)
                try:
                    if file_path in hashes.values():
                        file_hash = list(hashes.keys())[list(hashes.values()).index(file_path)]
                    else:
                        with Image.open(file_path) as img:
                            file_hash = imagehash.phash(img, hash_size=hash_size)

                    # Compare with every hash from the reference folder.
                    duplicate_found = False
                    for existing_hash, existing_file_path in hashes.items():
                        if existing_file_path == file_path:
                            continue                        
                        
                        if file_hash - existing_hash < threshold:
                            if file_hash - existing_hash > llm_threshold and os.path.exists(existing_file_path) and os.path.exists(file_path):
                                prompt_list = [
                                    ("text", 'These two images have been collected from different documents. I used a hash function to find that they are near duplicates. Your job is to determine if they are really duplicates (incl. the color is the same) and which one to keep (we keep the cleanest, without artifacts, e.g. bad borders). You answer in this format: {"is_duplicates"": "yes or no", "image_to_keep": "image1 or image2"}'),
                                    ("text", '\n\nimage1:'),
                                    ("image_path", file_path),
                                    ("text", '\n\nimage2:'),
                                    ("image_path", existing_file_path),
                                ]
                                ai_answer = vertex_genai_multi_with_cache(
                                    case_type="copyrights", # This function is only used for copyrights
                                    cache_filename="near_duplicates.json",
                                    prompt_list=prompt_list,
                                    image_path1=file_path,
                                    image_path2=existing_file_path
                                )
                                json_answer = get_json(ai_answer)
                                if "is_duplicates" in json_answer and json_answer["is_duplicates"] == "yes":
                                    if "image_to_keep" in json_answer and "1" in json_answer["image_to_keep"]:
                                        shutil.move(file_path, os.path.join(near_duplicate_folder, filename))
                                        duplicate_found = True
                                        break
                                    elif "image_to_keep" in json_answer and "2" in json_answer["image_to_keep"]:
                                        shutil.move(existing_file_path, os.path.join(near_duplicate_folder, filename))
                                        # !!! We need to remove the hash from the hashes dictionary for existing_file_path and add file_path
                                        # This is to dangerous to change the dictionary (hashes) as we loop through, so we just edit that hash and replace it's filepath
                                        hashes[existing_hash] = file_path
                                        duplicate_found = True
                                        break
                            
                            else:
                                shutil.move(file_path, os.path.join(near_duplicate_folder, filename))
                                duplicate_found = True
                                break

                    if not duplicate_found:
                        hashes[file_hash] = file_path

                except Exception as e:
                    print(f"Error processing {file_path}: {e}")
                    continue
                
                pbar.update(1)


def classify_near_duplicates(folder_path, reference_folder=None, hash_size=16, threshold=60, llm_threshold=30):
    # Classify near-duplicate images in folders based on the similarity of the hash.

    classified_folder = os.path.basename(folder_path) + "_classified"
    parent_dir = os.path.dirname(folder_path)
    classified_folder_path = f"{parent_dir}/{classified_folder}/"

    classified_folder_path = folder_path + "_classified"
    os.makedirs(classified_folder_path, exist_ok=True)
    hashes = {}

    if reference_folder:
        filenames = os.listdir(reference_folder)
        with tqdm(total=len(filenames), desc="Building Hashes of Reference Folder") as pbar:
            for filename in filenames:
                ref_path = os.path.join(reference_folder, filename)
                try:
                    with Image.open(ref_path) as img:
                        ref_hash = imagehash.phash(img, hash_size=hash_size)
                    hashes[ref_hash] = ref_path
                except Exception as e:
                    print(f"Error processing {ref_path}: {e}")
                pbar.update(1)

    i = 1
    filenames = os.listdir(folder_path)
    with tqdm(total=len(filenames), desc="Classifying near duplicates") as pbar:
        for filename in filenames:
            file_path = os.path.join(folder_path, filename)
            try:
                with Image.open(file_path) as img:
                    img_hash = imagehash.phash(img, hash_size=hash_size)

                # Compare with existing hashes
                duplicate_found = False
                for existing_hash, existing_file in hashes.items():
                    if img_hash - existing_hash < threshold:
                        if img_hash - existing_hash > llm_threshold and os.path.exists(existing_file) and os.path.exists(file_path):
                            prompt_list = [
                                ("text", 'These two images have been collected from different documents. I used a hash function to find that they are near duplicates. Your job is to determine if they are really duplicates (incl. the color is the same) and which one to keep (we keep the cleanest, without artifacts, e.g. bad borders). You answer in this format: {"is_duplicates"": "yes or no", "image_to_keep": "image1 or image2"}'),
                                ("text", '\n\nimage1:'),
                                ("image_path", file_path),
                                ("text", '\n\nimage2:'),
                                ("image_path", existing_file),
                            ]
                            ai_answer = vertex_genai_multi_with_cache(
                                case_type="copyrights", # This function is only used for copyrights
                                cache_filename="near_duplicates.json",
                                prompt_list=prompt_list,
                                image_path1=file_path,
                                image_path2=existing_file
                            )
                            json_answer = get_json(ai_answer)
                            if "is_duplicates" in json_answer and json_answer["is_duplicates"] == "yes":
                                if "image_to_keep" in json_answer and "1" in json_answer["image_to_keep"]:
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_1_keep.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_2_remove.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                                elif "image_to_keep" in json_answer and "2" in json_answer["image_to_keep"]:
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_1_remove.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_2_keep.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                            elif "is_duplicates" in json_answer and json_answer["is_duplicates"] == "no":
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}/LLM_no", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_no/{i}_1.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_no/{i}_2.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                            else:
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_1.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_2.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                        
                        else:
                            os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}", exist_ok=True)
                            shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_1.webp")
                            shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_2.webp")
                            duplicate_found = True
                            i += 1
                            break
                if not duplicate_found:
                    hashes[img_hash] = file_path
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
            pbar.update(1)



def get_case_image_from_df(cases_df, docket_from_file: str, filename_from_disk: str, case_type: str):
    """
    Finds the case row, specific image data, and the original image key from the DataFrame
    that corresponds to a filename found on disk. It does this by reconstructing potential
    sanitized filenames for images associated with the given docket and comparing them.
    """
    
    try:
        plaintiff_id_from_file = int(filename_from_disk.split('_')[0])
    except (ValueError, IndexError):
        print(f"Could not parse plaintiff_id from filename: {filename_from_disk}")
        return None, None, None

    # Filter cases for the given plaintiff_id
    plaintiff_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id_from_file].copy()
    if plaintiff_cases.empty:
        return None, None, None

    # Sort cases by filing_date to get the most recent one first
    plaintiff_cases['date_filed'] = pd.to_datetime(plaintiff_cases['date_filed'])
    plaintiff_cases = plaintiff_cases.sort_values(by='date_filed', ascending=False)

    # Iterate through the plaintiff's cases, starting with the most recent
    for _, case_row in plaintiff_cases.iterrows():
        if not isinstance(case_row.get('images'), dict) or case_type not in case_row['images']:
            continue

        images_for_case_type = case_row['images'][case_type]
        if not isinstance(images_for_case_type, dict):
            continue

        for original_image_key, image_data_val in images_for_case_type.items():
            # Reconstruct the filename as it would have been saved
            if "product_name" in image_data_val and image_data_val['product_name'] != "":
                potential_filename_stem = f"{plaintiff_id_from_file}_{image_data_val['product_name']}_{original_image_key}"
            else:
                potential_filename_stem = f"{plaintiff_id_from_file}_{original_image_key}"
            
            potential_filename_sanitized = sanitize_name(potential_filename_stem)

            # Check if the sanitized filename from the DataFrame matches the filename from disk
            if potential_filename_sanitized == filename_from_disk:
                return case_row, image_data_val, original_image_key

    return None, None, None


def standardize_extracted_docket_for_filename(raw_docket_str):
    """
    Standardizes a raw docket string extracted from a filename to the format D_YY-cv-NNNNN.
    Example: "1_24cv9709" -> "1_24-cv-09709"
             "1_25-cv-05993" -> "1_25-cv-05993"
    """
    # Normalize "cv" to "-cv-"
    d_normalized_cv = raw_docket_str.replace("cv", "-cv-") if "cv" in raw_docket_str and "-cv-" not in raw_docket_str else raw_docket_str

    parts = d_normalized_cv.split('_', 1)  # Split only on the first underscore
    if len(parts) != 2:
        print(f"Warning: Unexpected docket format for standardization: {raw_docket_str}")
        return None

    district_court = parts[0]
    year_and_case_part = parts[1]  # e.g., "24-cv-9709" or "25-cv-05993"

    sub_parts = year_and_case_part.split('-cv-')
    if len(sub_parts) != 2:
        print(f"Warning: Unexpected year/case format for standardization: {year_and_case_part} from {raw_docket_str}")
        return None

    year = sub_parts[0]
    case_number_str = sub_parts[1]

    # Pad case number to 5 digits with leading zeros
    padded_case_number = case_number_str.zfill(5)

    return f"{district_court}_{year}-cv-{padded_case_number}"


def fix_filenames_in_folder(folder_path):
    """
    Renames files in the given folder from old formats to a new standardized format.
    New Format: [docket_sanitized]_[original_filename_part].webp
    Old Format 1: US_DIS_ILND_[docket]_[original_part].webp
    Old Format 2: IN_DC_[docket]_[original_part].webp
    """
    renamed_count = 0
    skipped_count = 0

    # Define a list of regex patterns to try.
    # These patterns are designed to capture the docket and the rest of the filename.
    regex_patterns = [
        # Matches US_DIS_XXXX_docket_rest.webp (e.g., US_DIS_ILND_..., US_DIS_NYSD_...)
        # Group 1: Prefix (e.g., US_DIS_NYSD)
        # Group 2: Raw Docket (e.g., 1_24cv380 or 1_24-cv-00380)
        # Group 3: Remainder of the filename (e.g., d98110939e302_..._0.webp)
        re.compile(r"^(US_DIS_[A-Z]{2,4})_((?:\d+_\d+cv\d+)|(?:\d+_\d+-cv-\d+))_(.*\.webp)$", re.IGNORECASE),
        # Matches XX_DC_docket_rest.webp or XXX_DC_docket_rest.webp (e.g., IN_DC_..., CN_DC_..., NYS_DC_...)
        # Group 1: Prefix (e.g., CN_DC)
        # Group 2: Raw Docket (e.g., 3_23-cv-04716)
        # Group 3: Remainder of the filename (e.g., 2023-09-13_..._0.webp)
        re.compile(r"^([A-Z]{2,3}_DC)_((?:\d+_\d+cv\d+)|(?:\d+_\d+-cv-\d+))_(.*\.webp)$", re.IGNORECASE),
    ]

    for filename in os.listdir(folder_path):
        original_path = os.path.join(folder_path, filename)
        if not os.path.isfile(original_path):
            continue

        raw_docket_part = None
        # original_filename_suffix is not strictly needed for the new name construction,
        # but its extraction confirms the pattern matched as expected.
        original_filename_suffix_part = None 
        matched_pattern = False

        for regex in regex_patterns:
            match = regex.match(filename)
            if match:
                raw_docket_part = match.group(2)  # Docket is group 2 in the generalized patterns
                original_filename_suffix_part = match.group(3) # Remainder is group 3
                matched_pattern = True
                break

        if matched_pattern:
            standardized_docket = standardize_extracted_docket_for_filename(raw_docket_part)
            if standardized_docket:
                # Construct the new filename by prepending the standardized docket to the original filename
                new_filename_stem = f"{standardized_docket}_{filename}"
                # Use the existing sanitize_name function for consistency
                new_filename = sanitize_name(new_filename_stem)

                if new_filename != filename: # Check if sanitization changed the name or if it's already correct
                    new_path = os.path.join(folder_path, new_filename)
                    try:
                        shutil.move(original_path, new_path)
                        print(f"Renamed: '{filename}' to '{new_filename}'")
                        renamed_count += 1
                    except Exception as e:
                        print(f"Error renaming '{filename}' to '{new_filename}': {e}")
                        skipped_count += 1
                else:
                    # This case can happen if the filename was already in the new format and matched a pattern,
                    # or if sanitize_name didn't change anything.
                    print(f"Skipped (no change needed or already correct): '{filename}'")
                    # skipped_count += 1 # Not an error, so don't increment error/skipped count
            else:
                print(f"Skipped (docket standardization failed for {raw_docket_part}): '{filename}'")
                skipped_count += 1
        # Check for files that start with known prefixes but didn't match the full pattern (e.g., malformed docket)
        elif filename.lower().endswith('.webp') and \
             (filename.upper().startswith("US_DIS_") or re.match(r"^[A-Z]{2,3}_DC_", filename.upper())):
            print(f"Skipped (prefix matched but full pattern did not, possibly malformed docket/suffix): '{filename}'")
            skipped_count += 1

    print(f"\nRenaming complete. Renamed: {renamed_count}, Skipped/Errors: {skipped_count}")


if __name__ == "__main__":
    fix_filenames_in_folder(os.path.join(local_ip_tro_folder, "copyrights", "Production"))