import json
import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ

# Module-level variables initialized to None
patent_df = None
trademark_df = None
copyright_df = None

def safe_json_loads(x):
    """Safely decode JSON strings, handling NaNs and non-string types."""
    if isinstance(x, str) and x.strip().startswith('['):
        try:
            return json.loads(x)
        except json.JSONDecodeError:
            # Handle case where string starts with '[' but isn't valid JSON
            print(f"Warning: Could not decode supposed JSON string: {x}")
            return [] # Or return x, or None, depending on desired behavior
    # Check if x is a scalar before using pd.isna in a boolean context
    elif pd.api.types.is_scalar(x):
        # If scalar, check if it's NaN/None, return [] if it is, else return the scalar
        return [] if pd.isna(x) else x
    else:
        # If x is not a string starting with '[' and not a scalar (e.g., already a list/array),
        # return it as is.
        return x

def normalize_array_column(x):
    """Ensure values are lists. Replace None/NaN with empty list."""
    if isinstance(x, list):
        return x
    elif pd.isna(x):
        return []
    else:
        return [x]  # fallback
    
    
def get_patent_df():
    """Get the patent dataframe, loading from database if necessary."""
    global patent_df
    if patent_df is None:
        print("Loading patent dataframe from database...")
        patent_df = get_table_from_GZ("tb_patent")

        # Ensure DataFrame is loaded before proceeding
        if patent_df is None:
            print("Error: Failed to load patent dataframe.")
            return None

        # Convert JSON string columns back to lists/objects
        for col in ["design_page_numbers", "plaintiff_ids"]:
            if col in patent_df.columns:
                patent_df[col] = patent_df[col].apply(safe_json_loads)
                print(f"Converted column '{col}' using safe_json_loads.")

    return patent_df

def get_trademark_df():
    """Get the trademark dataframe, loading from database if necessary."""
    global trademark_df
    if trademark_df is None:
        print("Loading trademark dataframe from database...")
        trademark_df = get_table_from_GZ("tb_trademark")

        # Ensure DataFrame is loaded before proceeding
        if trademark_df is None:
            print("Error: Failed to load trademark dataframe.")
            return None

        # Convert JSON string columns back to lists/objects
        for col in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
            if col in trademark_df.columns:
                trademark_df[col] = trademark_df[col].apply(safe_json_loads)
                print(f"Converted column '{col}' using safe_json_loads.")

    return trademark_df

def get_copyright_df():
    """Get the copyright dataframe, loading from database if necessary."""
    global copyright_df
    if copyright_df is None:
        print("Loading copyright dataframe from database...")
        copyright_df = get_table_from_GZ("tb_copyright")
    return copyright_df

# Optional: Add ability to refresh the data when needed
def refresh_patent_df():
    """Force refresh the patent dataframe from the database."""
    global patent_df
    patent_df = get_table_from_GZ("tb_patent")
    return patent_df

# Similar refresh functions for other dataframes 