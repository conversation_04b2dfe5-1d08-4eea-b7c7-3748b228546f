# Use an ARM-compatible base image
FROM python:3.12-slim-bullseye

# Install system dependencies
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    libtesseract-dev \
    libleptonica-dev \
    pkg-config \
    poppler-utils \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    wget \
    build-essential \
    default-libmysqlclient-dev \
    default-mysql-client \
    chromium \
    chromium-driver \
    unzip \
    libaio1 \    
    curl \
    net-tools \  
    redis-server \
    apt-transport-https \
    ca-certificates \
    gnupg \
    tmux \ 
    openssh-client \
    git


# Install SSH server
RUN apt-get install -y openssh-server \
    && mkdir /var/run/sshd \
    && echo 'root:trosdc2024' | chpasswd \
    && sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
    && sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config \
    && sed -i 's/#ListenAddress 0.0.0.0/ListenAddress 0.0.0.0/' /etc/ssh/sshd_config \ 
    && sed -i 's/#Port 22/Port 2223/' /etc/ssh/sshd_config \
    && rm -rf /var/lib/apt/lists/*

# Install Google Cloud CLI
RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list && curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg && apt-get update -y && apt-get install google-cloud-cli -y

# Install VS Code Server and extensions
# This will download the latest stable version of VS Code Server for the container's architecture (amd64 or arm64)
# and install the specified Python extensions.
# RUN ARCH=$(dpkg --print-architecture) &amp;&amp; \
#     if [ "$ARCH" = "amd64" ]; then VSCODE_ARCH="linux-x64-server"; else VSCODE_ARCH="linux-arm64-server"; fi &amp;&amp; \
#     wget -qO- "https://code.visualstudio.com/sha/download?build=stable&amp;os=${VSCODE_ARCH}" | tar -xz -C /usr/local/bin --strip-components=1 &amp;&amp; \
#     code --install-extension ms-python.python --force &amp;&amp; \
#     code --install-extension ms-python.debugpy --force &amp;&amp; \
#     code --install-extension ms-python.vscode-pylance --force

# Set Chrome options for Selenium
ENV CHROME_BIN=/usr/bin/chromium
ENV CHROME_DRIVER=/usr/bin/chromedriver

# Set the working directory
WORKDIR /app
# ENV PYTHONPATH=/app:$PYTHONPATH 
ENV PYTHONPATH=/app 
# By appending :$PYTHONPATH, we are preserving any existing paths that were already in the PYTHONPATH variable.

# Copy requirements.txt first, so that if the code has changed, the dependencies are not re-installed (different stage)
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt


# Copy the application code
COPY . .

# Declare a build argument for the SSH public key (SSH key to log into the container)
ARG SSH_PUBLIC_KEY
# Create the .ssh directory and set correct permissions
RUN mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh
# Add the public key to authorized_keys and set correct permissions
RUN echo "${SSH_PUBLIC_KEY}" > /root/.ssh/authorized_keys && \
    chmod 600 /root/.ssh/authorized_keys
    
# Set strict permissions for the private key
# RUN chmod 600 /app/.ssh/id_rsa && \
# chmod 700 /app/.ssh
    
# Set ownership to root (or whichever user is running SSH commands)
# RUN chown -R root:root /app/.ssh

# Create directories for the chrome profile and database
RUN mkdir -p /app/data/chrome_user_data
RUN mkdir -p /app/data/db

# Copy the chromedriver to a directory where it can be modified by undetected_chromedriver
RUN mkdir -p /root/.local/share/undetected_chromedriver
RUN cp /usr/bin/chromedriver /root/.local/share/undetected_chromedriver/chromedriver_copy
RUN chmod +x /root/.local/share/undetected_chromedriver/chromedriver_copy

# Expose the port 5000 for flask and 2223 for SSH and 5555 for Flower
EXPOSE 5000 2223

# Run the application
# CMD ["python", "app.py"]
RUN chmod +x startno.sh
CMD ["./startno.sh"]