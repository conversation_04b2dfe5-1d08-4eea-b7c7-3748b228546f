# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: embedding.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'embedding.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0f\x65mbedding.proto\x12\tembedding\"\x1e\n\tEmbedding\x12\x11\n\tembedding\x18\x01 \x03(\x02\"n\n\nImageSplit\x12\x12\n\nimage_data\x18\x01 \x01(\x0c\x12\x11\n\tembedding\x18\x02 \x03(\x02\x12\'\n\trectangle\x18\x03 \x01(\x0b\x32\x14.embedding.Rectangle\x12\x10\n\x08\x66ilename\x18\x04 \x01(\t\"\x1b\n\tRectangle\x12\x0e\n\x06points\x18\x01 \x03(\x02\"4\n\rRectangleList\x12#\n\x05rects\x18\x01 \x03(\x0b\x32\x14.embedding.Rectangle\":\n\tImageInfo\x12\x12\n\nimage_data\x18\x01 \x01(\x0c\x12\x19\n\x11original_filename\x18\x02 \x01(\t\"J\n\"GetImageSplitWithEmbeddingsRequest\x12$\n\x06images\x18\x01 \x03(\x0b\x32\x14.embedding.ImageInfo\"\xe3\x01\n\x10ImageSplitResult\x12+\n\x0cimage_splits\x18\x01 \x03(\x0b\x32\x15.embedding.ImageSplit\x12\x19\n\x11original_filename\x18\x02 \x01(\t\x12<\n\trects_map\x18\x03 \x03(\x0b\x32).embedding.ImageSplitResult.RectsMapEntry\x1aI\n\rRectsMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.embedding.RectangleList:\x02\x38\x01\"S\n#GetImageSplitWithEmbeddingsResponse\x12,\n\x07results\x18\x01 \x03(\x0b\x32\x1b.embedding.ImageSplitResult\"C\n\x0e\x45mbeddingInput\x12\x14\n\nimage_data\x18\x01 \x01(\x0cH\x00\x12\x13\n\ttext_data\x18\x02 \x01(\tH\x00\x42\x06\n\x04\x64\x61ta\"A\n\x14GetEmbeddingsRequest\x12)\n\x06inputs\x18\x01 \x03(\x0b\x32\x19.embedding.EmbeddingInput\"A\n\x15GetEmbeddingsResponse\x12(\n\nembeddings\x18\x01 \x03(\x0b\x32\x14.embedding.Embedding\"\xbe\x01\n\x11TrademarkMetadata\x12\x11\n\tmark_text\x18\x01 \x01(\t\x12\x19\n\x0cplaintiff_id\x18\x02 \x01(\x05H\x00\x88\x01\x01\x12\x0e\n\x06reg_no\x18\x03 \x01(\t\x12\x0e\n\x06ser_no\x18\x04 \x01(\t\x12\x16\n\x0e\x61pplicant_name\x18\x05 \x01(\t\x12\x0f\n\x07int_cls\x18\x06 \x03(\x05\x12!\n\x19goods_services_text_daily\x18\x07 \x01(\tB\x0f\n\r_plaintiff_id\"$\n\x14GetAllMatchesRequest\x12\x0c\n\x04text\x18\x01 \x01(\t\"S\n\x05Match\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\x12.\n\x08metadata\x18\x03 \x01(\x0b\x32\x1c.embedding.TrademarkMetadata\"N\n\x15GetAllMatchesResponse\x12!\n\x07matches\x18\x01 \x03(\x0b\x32\x10.embedding.Match\x12\x12\n\nis_perfect\x18\x02 \x01(\x08\x32\xb8\x02\n\x10\x45mbeddingService\x12R\n\rGetEmbeddings\x12\x1f.embedding.GetEmbeddingsRequest\x1a .embedding.GetEmbeddingsResponse\x12|\n\x1bGetImageSplitWithEmbeddings\x12-.embedding.GetImageSplitWithEmbeddingsRequest\x1a..embedding.GetImageSplitWithEmbeddingsResponse\x12R\n\rGetAllMatches\x12\x1f.embedding.GetAllMatchesRequest\x1a .embedding.GetAllMatchesResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'embedding_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_IMAGESPLITRESULT_RECTSMAPENTRY']._loaded_options = None
  _globals['_IMAGESPLITRESULT_RECTSMAPENTRY']._serialized_options = b'8\001'
  _globals['_EMBEDDING']._serialized_start=30
  _globals['_EMBEDDING']._serialized_end=60
  _globals['_IMAGESPLIT']._serialized_start=62
  _globals['_IMAGESPLIT']._serialized_end=172
  _globals['_RECTANGLE']._serialized_start=174
  _globals['_RECTANGLE']._serialized_end=201
  _globals['_RECTANGLELIST']._serialized_start=203
  _globals['_RECTANGLELIST']._serialized_end=255
  _globals['_IMAGEINFO']._serialized_start=257
  _globals['_IMAGEINFO']._serialized_end=315
  _globals['_GETIMAGESPLITWITHEMBEDDINGSREQUEST']._serialized_start=317
  _globals['_GETIMAGESPLITWITHEMBEDDINGSREQUEST']._serialized_end=391
  _globals['_IMAGESPLITRESULT']._serialized_start=394
  _globals['_IMAGESPLITRESULT']._serialized_end=621
  _globals['_IMAGESPLITRESULT_RECTSMAPENTRY']._serialized_start=548
  _globals['_IMAGESPLITRESULT_RECTSMAPENTRY']._serialized_end=621
  _globals['_GETIMAGESPLITWITHEMBEDDINGSRESPONSE']._serialized_start=623
  _globals['_GETIMAGESPLITWITHEMBEDDINGSRESPONSE']._serialized_end=706
  _globals['_EMBEDDINGINPUT']._serialized_start=708
  _globals['_EMBEDDINGINPUT']._serialized_end=775
  _globals['_GETEMBEDDINGSREQUEST']._serialized_start=777
  _globals['_GETEMBEDDINGSREQUEST']._serialized_end=842
  _globals['_GETEMBEDDINGSRESPONSE']._serialized_start=844
  _globals['_GETEMBEDDINGSRESPONSE']._serialized_end=909
  _globals['_TRADEMARKMETADATA']._serialized_start=912
  _globals['_TRADEMARKMETADATA']._serialized_end=1102
  _globals['_GETALLMATCHESREQUEST']._serialized_start=1104
  _globals['_GETALLMATCHESREQUEST']._serialized_end=1140
  _globals['_MATCH']._serialized_start=1142
  _globals['_MATCH']._serialized_end=1225
  _globals['_GETALLMATCHESRESPONSE']._serialized_start=1227
  _globals['_GETALLMATCHESRESPONSE']._serialized_end=1305
  _globals['_EMBEDDINGSERVICE']._serialized_start=1308
  _globals['_EMBEDDINGSERVICE']._serialized_end=1620
# @@protoc_insertion_point(module_scope)
