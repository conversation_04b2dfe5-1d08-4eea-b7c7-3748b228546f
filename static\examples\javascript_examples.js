/**
 * Maidalv IP Check API - JavaScript Examples
 * Complete examples for integrating with the IP Check API
 */

// Configuration
const API_KEY = "{YOUR_API_KEY}";
const BASE_URL = "https://api.maidalv.com";

/**
 * Convert file to base64 string
 */
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // Remove data URL prefix (data:image/jpeg;base64,)
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
    });
}

/**
 * Submit a product for IP check
 */
async function submitCheck() {
    try {
        // Get files from input elements (browser environment)
        const mainImageFile = document.getElementById('mainImage').files[0];
        const otherImageFiles = document.getElementById('otherImages').files;
        const ipImageFiles = document.getElementById('ipImages').files;
        const referenceImageFiles = document.getElementById('referenceImages').files;
        
        // Convert to base64
        const mainImageBase64 = await fileToBase64(mainImageFile);
        
        const otherImagesBase64 = [];
        for (let file of otherImageFiles) {
            const base64 = await fileToBase64(file);
            otherImagesBase64.push(base64);
        }
        
        const ipImagesBase64 = [];
        for (let file of ipImageFiles) {
            const base64 = await fileToBase64(file);
            ipImagesBase64.push(base64);
        }
        
        const referenceImagesBase64 = [];
        for (let file of referenceImageFiles) {
            const base64 = await fileToBase64(file);
            referenceImagesBase64.push(base64);
        }
        
        // Prepare request data
        const data = {
            api_key: API_KEY,
            main_product_image: mainImageBase64,
            other_product_images: otherImagesBase64,
            ip_images: ipImagesBase64,
            description: "Wireless bluetooth headphones", // Product title recommended
            ip_keywords: ["bluetooth", "wireless", "headphones", "noise cancellation"],
            reference_text: "Similar to popular brand headphones",
            reference_images: referenceImagesBase64,
            language: "en"
        };
        
        // Submit the request
        const response = await fetch(`${BASE_URL}/check_api`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('Check submitted successfully!');
            console.log('Check ID:', result.check_id);
            console.log('Status:', result.status);
            
            if (result.estimated_completion_time) {
                console.log('Estimated completion time:', result.estimated_completion_time, 'seconds');
            }
            
            return result;
        } else {
            const errorData = await response.json();
            console.error('Error:', errorData);
            throw new Error(errorData.error?.message || 'Request failed');
        }
        
    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
}

/**
 * Poll for results with proper timing
 */
async function pollForResults(checkId, maxAttempts = 120) {
    let attempt = 0;
    
    while (attempt < maxAttempts) {
        try {
            const response = await fetch(`${BASE_URL}/check_status/${checkId}`);
            
            if (response.ok) {
                const result = await response.json();
                const status = result.status;
                
                console.log(`Attempt ${attempt + 1}: Status = ${status}`);
                
                if (status === "completed") {
                    console.log("Analysis completed!");
                    return result.result;
                    
                } else if (status === "error") {
                    console.log(`Analysis failed: ${result.message}`);
                    return null;
                    
                } else if (status === "queued" || status === "processing") {
                    // Wait based on status and estimated time
                    let waitTime;
                    if (status === "queued") {
                        // Use estimated completion time if available
                        const estimatedTime = result.estimated_completion_time || 20;
                        waitTime = Math.min(estimatedTime * 1000, 20000); // Cap at 20 seconds, convert to ms
                    } else {
                        waitTime = 3000; // Processing status - 3 seconds
                    }
                    
                    console.log(`Waiting ${waitTime/1000} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }
                
            } else {
                console.log(`HTTP Error: ${response.status}`);
                break;
            }
            
        } catch (error) {
            console.log(`Request failed: ${error}`);
            break;
        }
        
        attempt++;
    }
    
    console.log("Max attempts reached or error occurred");
    return null;
}

/**
 * Handle API request with proper error handling and retries
 */
async function handleApiRequest(url, data, maxRetries = 3) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                return await response.json();
                
            } else if (response.status === 429) {
                // Rate limit exceeded
                const errorData = await response.json();
                const errorCode = errorData.error?.error_code;
                
                if (errorCode === 'RATE_LIMIT_MINUTE_EXCEEDED') {
                    console.log('Rate limit exceeded. Waiting 60 seconds...');
                    await new Promise(resolve => setTimeout(resolve, 60000));
                    continue;
                } else if (errorCode === 'RATE_LIMIT_DAILY_EXCEEDED') {
                    console.log('Daily limit exceeded. Please try tomorrow.');
                    return null;
                }
                
            } else if (response.status === 401) {
                console.log('Authentication failed. Check your API key.');
                return null;
                
            } else {
                // Other errors
                const errorData = await response.json();
                console.log('API Error:', errorData);
                return null;
            }
            
        } catch (error) {
            console.log(`Request failed (attempt ${attempt + 1}):`, error);
            if (attempt < maxRetries - 1) {
                // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
        }
    }
    
    console.log('Max retries exceeded');
    return null;
}

/**
 * Retry function with exponential backoff and jitter
 */
async function exponentialBackoffRetry(func, maxRetries = 5, baseDelay = 1000) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            return await func();
        } catch (error) {
            if (attempt === maxRetries - 1) {
                throw error;
            }
            
            // Calculate delay with exponential backoff and jitter
            const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
            console.log(`Attempt ${attempt + 1} failed. Retrying in ${(delay/1000).toFixed(2)} seconds...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
    
    throw new Error("Max retries exceeded");
}

/**
 * Complete workflow example
 */
async function completeWorkflowExample() {
    try {
        console.log("Starting IP check workflow...");
        
        // Step 1: Submit check
        const submitResult = await submitCheck();
        if (!submitResult || !submitResult.check_id) {
            console.log("Failed to submit check");
            return;
        }
        
        // Step 2: Poll for results
        const results = await pollForResults(submitResult.check_id);
        if (results) {
            console.log("Final results received:");
            console.log(JSON.stringify(results, null, 2));
            
            // Process results
            const riskLevel = results.risk_level || "Unknown";
            console.log(`\nOverall Risk Level: ${riskLevel}`);
            
            results.results?.forEach((result, i) => {
                console.log(`\nResult ${i + 1}:`);
                console.log(`  IP Type: ${result.ip_type}`);
                console.log(`  IP Owner: ${result.ip_owner}`);
                console.log(`  Risk Level: ${result.risk_level}`);
                console.log(`  Risk Score: ${result.risk_score}`);
                
                if (result.plaintiff_name) {
                    console.log(`  Used in TRO: Yes`);
                    console.log(`  Plaintiff: ${result.plaintiff_name}`);
                    console.log(`  Cases: ${result.number_of_cases}`);
                } else {
                    console.log(`  Used in TRO: No`);
                }
            });
        } else {
            console.log("Failed to get results");
        }
        
    } catch (error) {
        console.error("Workflow failed:", error);
    }
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        fileToBase64,
        submitCheck,
        pollForResults,
        handleApiRequest,
        exponentialBackoffRetry,
        completeWorkflowExample
    };
}
