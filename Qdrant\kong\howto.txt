Uncertainties:
1. Currently nginx proxies vectorstore1.maidalv.com:6333 using SSL to Qdrant. Why not use cloudflared tunnel? Probably from the days when the GPU was in china! So I kept this feature
2. Why not use a cloundflare for vast? This would be much easier than updating the IP with a cron job.  Probably from the days when the GPU was in china! So I kept this feature



Setup the SSL cert:
1. start without ssl: KONG_PROXY_LISTEN: 0.0.0.0:8003
2. Add the cert:
curl -i -X POST http://localhost:8001/certificates \
  -F "cert=@/ssl/live/maidalv.com/fullchain.pem" \
  -F "key=@/ssl/live/maidalv.com/privkey.pem" \
  -F "snis=*.maidalv.com"

3. restart docker compose with ssl: KONG_PROXY_LISTEN: 0.0.0.0:8003, 0.0.0.0:8443 ssl http2
4. Ensure the certificate gets updated: On each renewal, certbot runs a deploy-hook (post-renew) to update Kong
   - copy the push-kong.sh to /ssl/renewal-hooks/deploy/
   - chmod +x /ssl/renewal-hooks/deploy/push-kong.sh


Explanation: here’s what happens when a client makes a request through Kong:
- Client sends request → arrives at Kong’s proxy.
- Kong checks Route definitions to find a route that matches (based on host, path, method, etc.).
- Once a Route is selected, Kong sees which Gateway Service is associated with that Route.
- The Gateway Service defines where to send the request. That could be directly to a single host/URL, or via an Upstream with multiple Targets.
- If using an Upstream, Kong load-balances among the Targets (respecting health check status, LB algorithm, etc.).
- Kong may apply plugins attached to the Route, or to the Service (or both), which can modify request, enforce policies, authentication, etc.
- The request reaches the backend (via the Service → Upstream / target), the response comes back through Kong, any response plugins run, then Kong returns the response to client.