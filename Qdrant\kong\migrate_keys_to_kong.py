#!/usr/bin/env python3
import os
import sys
import time
import json
from typing import Optional, Dict, Any, Tu<PERSON>, List

import requests
import psycopg2
import psycopg2.extras
from IP.Patents_Bulk.patent_db_grant import get_db_connection

KONG_ADMIN = os.getenv("KONG_ADMIN", "http://api2.maidalv.com:8001")

# ---- Safety knobs ------------------------------------------------------------

TIMEOUT = 10
RETRIES = 3
RETRY_BACKOFF = 0.7

# ---- HTTP helper with basic retry -------------------------------------------

def req(method: str, path: str, **kwargs) -> requests.Response:
    url = f"{KONG_ADMIN.rstrip('/')}/{path.lstrip('/')}"
    last_exc = None
    for attempt in range(1, RETRIES + 1):
        try:
            r = requests.request(method, url, timeout=TIMEOUT, **kwargs)
            return r
        except requests.RequestException as e:
            last_exc = e
            if attempt == RETRIES:
                raise
            time.sleep(RETRY_BACKOFF * attempt)
    raise last_exc  # type: ignore


# ---- Kong lookup helpers -----------------------------------------------------

def ensure_service_and_get_id(service_name: str) -> str:
    # GET /services/{name} to fetch ID
    r = req("GET", f"/services/{service_name}")
    if r.status_code == 200:
        return r.json()["id"]
    elif r.status_code == 404:
        sys.exit(f"[FATAL] Kong service '{service_name}' not found. Create it first.")
    else:
        sys.exit(f"[FATAL] Failed to query service '{service_name}': {r.status_code} {r.text}")

def get_consumer_by_custom_id(custom_id: str) -> Optional[Dict[str, Any]]:
    # List endpoint supports filtering by custom_id via query string
    # GET /consumers?custom_id=<value>
    r = req("GET", "/consumers", params={"custom_id": custom_id})
    if r.status_code != 200:
        raise RuntimeError(f"Failed to search consumer by custom_id={custom_id}: {r.status_code} {r.text}")
    data = r.json()
    items = data.get("data", [])
    return items[0] if items else None

def delete_consumer(consumer_id: str):
    # DELETE /consumers/{id}
    r = req("DELETE", f"/consumers/{consumer_id}")
    if r.status_code not in (204, 404):
        raise RuntimeError(f"Failed to delete consumer {consumer_id}: {r.status_code} {r.text}")

def ensure_consumer(client_id: int, client_name: str) -> Dict[str, Any]:
    custom_id = str(client_id)

    # Try find by custom_id first (so we don’t create dup with diff username)
    found = get_consumer_by_custom_id(custom_id)
    if found:
        delete_consumer(found["id"])

    payload = {
        "username": client_name,
        "custom_id": custom_id,
        "tags": ["migrated-legacy-keys"]
    }
    if custom_id.startswith("1"):
        payload["tags"] = ["Internal"]
    elif custom_id.startswith("2"):
        payload["tags"] = ["Seller"]
    elif custom_id.startswith("3"):
        payload["tags"] = ["IP_Center"]
    elif custom_id.startswith("4"):
        payload["tags"] = ["Partners"]
    elif custom_id.startswith("8"):
        payload["tags"] = ["Frontend"]
    r = req("POST", "/consumers", json=payload)
    if r.status_code in (200, 201):
        return r.json()
    if r.status_code == 409:
        # Conflict likely on username; fetch by username and re-create
        r2 = req("GET", f"/consumers/{client_name}")
        if r2.status_code == 200:
            consumer_id = r2.json()["id"]
            delete_consumer(consumer_id)
            r3 = req("POST", "/consumers", json=payload)
            if r3.status_code in (200, 201):
                return r3.json()
    raise RuntimeError(f"Failed to ensure consumer (client_id={client_id}): {r.status_code} {r.text}")

def ensure_keyauth_credential(username_or_id: str, api_key: str) -> Dict[str, Any]:
    # POST /consumers/{consumer}/key-auth with {"key": "..."}
    r = req("POST", f"/consumers/{username_or_id}/key-auth", json={"key": api_key})
    if r.status_code in (200, 201):
        return r.json()
    if r.status_code == 409:
        # Key already exists (keys are globally unique). Fetch existing credentials and pick the matching one.
        r2 = req("GET", f"/consumers/{username_or_id}/key-auth")
        if r2.status_code == 200:
            for cred in r2.json().get("data", []):
                if cred.get("key") == api_key:
                    return cred
        # If we can’t find it, just return an informative stub
        return {"key": api_key, "note": "credential already exists (409)"}
    raise RuntimeError(f"Failed to create key-auth credential for {username_or_id}: {r.status_code} {r.text}")

def ensure_rate_limit_plugin_for_consumer_service(consumer: Dict[str, Any], service_id: str,
                                                  minute_limit: Optional[int], day_limit: Optional[int]) -> Dict[str, Any]:
    # Prepare config: only include fields that are not None
    config: Dict[str, Any] = {
        "limit_by": "consumer",
        "policy": "redis",
        "redis": {
            "host": "redis",
            "port": 6379
        }
    }
    if minute_limit is not None:
        config["minute"] = minute_limit
    if day_limit is not None:
        config["day"] = day_limit

    # If both missing, don’t create
    if "minute" not in config and "day" not in config:
        return {"note": "no limits provided, nothing to create"}

    payload = {
        "name": "rate-limiting",
        "instance_name": f"Rate_Limit_Check_{consumer['username'].replace(' ', '_')}",
        "service": {"id": service_id},
        "consumer": {"id": consumer["id"]},
        "config": config
    }
    r = req("POST", "/plugins", json=payload)
    if r.status_code in (200, 201):
        return r.json()

    raise RuntimeError(f"Failed to create rate-limiting plugin: {r.status_code} {r.text}")


# ---- Main migration ----------------------------------------------------------

def main():
    # Validate the 'check' service exists and get its id
    check_service_id = ensure_service_and_get_id("check")

    # Connect to Postgres
    conn = get_db_connection()
    conn.autocommit = True

    rows: List[Tuple[str, int, str, int, int]] = []
    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
        cur.execute("""
            SELECT api_key, client_id, client_name, rate_limit, daily_limit
            FROM public.check_client_api_keys
            ORDER BY client_id ASC
        """)
        rows = cur.fetchall()

    migrated = 0
    skipped = 0
    with_limits = 0
    errors: List[str] = []

    for row in rows:
        api_key = row["api_key"]
        client_id = int(row["client_id"])
        client_name = row["client_name"]
        rate_limit = int(row["rate_limit"])
        daily_limit = int(row["daily_limit"])

        try:
            # 1) Ensure Consumer (custom_id = client_id)
            consumer = ensure_consumer(client_id, client_name)

            # 2) Ensure key-auth uses existing api_key
            ensure_keyauth_credential(consumer["id"], api_key)

            # 3) Attach per-consumer rate limits to the 'check' service IF not default (1 & 100)
            if not (rate_limit == 1 and daily_limit == 100):
                ensure_rate_limit_plugin_for_consumer_service(
                    consumer, check_service_id,
                    minute_limit=rate_limit,
                    day_limit=daily_limit
                )
                with_limits += 1
            else:
                skipped += 1

            migrated += 1

        except Exception as e:
            errors.append(f"client_id={client_id}: {e}")

    print(json.dumps({
        "summary": {
            "consumers_processed": len(rows),
            "consumers_migrated": migrated,
            "consumers_with_per_consumer_limits": with_limits,
            "consumers_using_service_default_limits": skipped,
            "errors": len(errors)
        },
        "errors": errors
    }, indent=2))


if __name__ == "__main__":
    main()
