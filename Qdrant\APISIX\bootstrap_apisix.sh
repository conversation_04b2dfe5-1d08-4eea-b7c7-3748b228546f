#!/usr/bin/env bash
set -euo pipefail

ADMIN="http://localhost:9180/apisix/admin"
HDR="-H X-API-KEY:${APISIX_ADMIN_KEY:-2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ}"

# Helper: wait for Admin API
until curl -fsS $HDR "$ADMIN/routes"; do
  echo "Waiting for APISIX Admin API..."
  sleep 1
done

# ---------- 0) TLS: load existing certs into an SSL object ----------
CERT=/ssl/live/maidalv.com/fullchain.pem
KEY=/ssl/live/maidalv.com/privkey.pem
if [[ ! -s "$CERT" || ! -s "$KEY" ]]; then
  echo "Cert or key missing at $CERT / $KEY"; exit 1
fi

SSL_JSON=$(jq -n --rawfile cert "$CERT" --rawfile key "$KEY" --argjson snis '["maidalv.com","*.maidalv.com"]' '{cert: $cert, key: $key, snis: $snis}')

curl -v $HDR -H 'Content-Type: application/json'   -X PUT "$ADMIN/ssls/wildcard-maidalv" -d "$SSL_JSON" >/dev/null

# ---------- 1) Global Prometheus metrics ----------
# Exposes /apisix/prometheus* and/or the 9091 exporter (we enabled in config.yaml)
curl -fsS $HDR -X PUT "$ADMIN/global_rules/prom" \
  -H 'content-type: application/json' \
  -d '{
    "id":"prom",
    "plugins": { "prometheus": {} }
  }' >/dev/null

# ---------- 2) Upstream: least_conn + health checks ----------
VAST_HOST="***********"   # update as needed
VAST_PORT="40324"

curl -fsS $HDR -X PUT "$ADMIN/upstreams/check-api" \
  -H 'content-type: application/json' \
  -d '{
    "type": "least_conn",
    "nodes": {
      "'"${VAST_HOST}"':'"${VAST_PORT}"'": 100,
      "tro_app_api:5000": 1
    },
    "checks": {
      "active": {
        "type": "http",
        "http_path": "/health",
        "timeout": 2,
        "healthy":   { "interval": 10, "successes": 2 },
        "unhealthy": { "interval": 5,  "http_failures": 1, "tcp_failures": 1, "timeouts": 1 }
      },
      "passive": {
        "type": "http",
        "healthy":   { "successes": 2 },
        "unhealthy": { "http_failures": 2, "tcp_failures": 1, "timeouts": 1 }
      }
    }
  }' >/dev/null

# ---------- 3) Services ----------
for SVC in check check_lite non_api; do
  curl -fsS $HDR \
  -X PUT "$ADMIN/services/${SVC}" \
  -H 'content-type: application/json' \
  -d '{"name":"check","upstream_id":"check-api"}' >/dev/null
done

# ---------- 4) Routes (hosts + exact paths; APISIX preserves Host by default) ----------
curl -fsS $HDR -X PUT "$ADMIN/routes/check-route" \
  -H 'content-type: application/json' \
  -d '{
    "name": "check-route",
    "hosts": ["api2.maidalv.com"],
    "uri": "/check_api",
    "service_id": "check"
  }' >/dev/null

curl -fsS $HDR -X PUT "$ADMIN/routes/check-lite-route" \
  -H 'content-type: application/json' \
  -d '{
    "name": "check-lite-route",
    "hosts": ["api2.maidalv.com"],
    "uri": "/check_lite",
    "service_id": "check_lite"
  }' >/dev/null

# Catch-all for host (prefix match)
curl -fsS $HDR -X PUT "$ADMIN/routes/catch-all" \
  -H 'content-type: application/json' \
  -d '{
    "name": "Catch-All",
    "hosts": ["api2.maidalv.com"],
    "uri": "/*",
    "service_id": "non_api"
  }' >/dev/null

# ---------- 5) Auth: key-auth at the Service level ----------
for SVC in check check_lite; do
  curl -fsS $HDR -X PATCH "$ADMIN/services/${SVC}" \
    -H 'content-type: application/json' \
    -d '{
      "plugins": {
        "key-auth": {}
      }
    }' >/dev/null
done

# ---------- 6) Rate limits per Service using Redis policy (per consumer counters) ----------
# APISIX limit-count supports redis/redis-cluster policies; we scope keys by consumer_name. 
# (Matches your Kong behavior: per-service limits applied to each authenticated consumer.)
curl -fsS $HDR -X PATCH "$ADMIN/services/check" \
  -H 'content-type: application/json' \
  -d '{
    "plugins": {
      "key-auth": {},
      "limit-count": {
        "count": 1,
        "time_window": 60,         
        "rejected_code": 429,
        "key_type": "var",
        "key": "consumer_name",
        "policy": "redis",
        "redis_host": "redis",
        "redis_port": 6379
      }
    }
  }' >/dev/null

curl -fsS $HDR -X PATCH "$ADMIN/services/check_lite" \
  -H 'content-type: application/json' \
  -d '{
    "plugins": {
      "key-auth": {},
      "limit-count": {
        "count": 10,
        "time_window": 60,             
        "rejected_code": 429,
        "key_type": "var",
        "key": "consumer_name",
        "policy": "redis",
        "redis_host": "redis",
        "redis_port": 6379
      }
    }
  }' >/dev/null

# ---------- 7) (Optional) Create Free/Paid Consumer Groups ----------
curl -fsS $HDR -X PUT "$ADMIN/consumer_groups/free" \
  -H 'content-type: application/json' \
  -d '{ "plugins": {} }' >/dev/null

curl -fsS $HDR -X PUT "$ADMIN/consumer_groups/paid" \
  -H 'content-type: application/json' \
  -d '{ "plugins": {} }' >/dev/null

# ---------- 8) (Examples) Create consumers & assign keys (+ groups) ----------
# (Edit or remove; APISIX lets each consumer have credentials & be bound to a group.)
curl -fsS $HDR -X PUT "$ADMIN/consumers/alice" \
  -H 'content-type: application/json' \
  -d '{
    "username":"alice",
    "group_id":"free"
  }' >/dev/null

curl -fsS $HDR -X PUT "$ADMIN/consumers/alice/credentials" \
  -H 'content-type: application/json' \
  -d '{
    "id": "cred-alice-key-auth",
    "plugins": { "key-auth": { "key": "alice-key" } }
  }' >/dev/null

curl -fsS $HDR -X PUT "$ADMIN/consumers/bob" \
  -H 'content-type: application/json' \
  -d '{
    "username":"bob",
    "group_id":"paid"
  }' >/dev/null

curl -fsS $HDR -X PUT "$ADMIN/consumers/bob/credentials" \
  -H 'content-type: application/json' \
  -d '{
    "id": "cred-bob-key-auth",
    "plugins": { "key-auth": { "key": "bob-key" } }
  }' >/dev/null

echo "APISIX bootstrap complete."
