#!/usr/bin/env bash
# -----------------------------------------------------------------------------
#  Sync Vast.ai public IP + HostPort ➜ Kong Upstream Targets (primary + fallback)
#  - Fetches instance details via Vast REST API
#  - Extracts .public_ipaddr and HostPort for INTERNAL_PORT (default: 5000)
#  - Updates Kong Upstream targets: adds new primary target and disables the old one
#  - No reloads: Kong applies immediately
#
#  Requirements: curl, jq
#  chmod +x /root/qdrant/vast_updater.sh
#  echo "*/2 * * * * /root/qdrant/vast_updater.sh >> /var/log/vast-sync.log 2>&1" | crontab -
#  Check the logs: tail -n 5 /var/log/vast-sync.log
# -----------------------------------------------------------------------------
set -euo pipefail

### --- user-specific settings -----------------------------------------------
VAST_API_KEY="041d67de00859f74fd6c917169213ae9782535880a323348f57de8fcdd9f0b97"
# INSTANCE_ID="${INSTANCE_ID:-23639914}"    # leave empty to auto-discover a running instance

# Kong Admin API endpoint & auth
KONG_ADMIN="http://localhost:8001"
KONG_ADMIN_TOKEN='2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ'

# State file to remember the last "primary" target we published
CACHE_DIR="/var/cache/vast-sync"
mkdir -p "$CACHE_DIR"
STATE_FILE="$CACHE_DIR/last_ip_port"

### --- helper ---------------------------------------------------------------
fatal() { echo "[vast-sync] $*" >&2; exit 1; }

api_get() {
  local url=$1
  curl -sS --fail -H "Authorization: Bearer $VAST_API_KEY" \
       -H "Accept: application/json" "$url"
}

### 2. Determine instance id if not provided ----------------------------------
if [[ -z "${INSTANCE_ID:-}" ]]; then
  INSTANCE_ID=$(api_get "https://console.vast.ai/api/v0/instances/" \
               | jq -r '.instances[] | select(.actual_status=="running") | .id' \
               | head -n1)
  [[ -n "$INSTANCE_ID" ]] || fatal "No running instance found"
fi

### 3. Fetch instance JSON ----------------------------------------------------
json_raw=$(api_get "https://console.vast.ai/api/v0/instances/${INSTANCE_ID}/")

# Vast sometimes returns trailing commas – strip them to keep jq happy
json=$(echo "$json_raw" | sed -Ee 's/,\s*([}\]])/\1/g')

### 4. Parse public IP and HostPort ------------------------------------------
PUBLIC_IP=$(echo "$json" | jq -r '.instances.public_ipaddr // .public_ipaddr')
PORT=$(echo "$json" \
       | jq -r --arg p "5000/tcp" \
             '.instances.ports[$p][0].HostPort
              // .ports[$p][0].HostPort')
PORT_GRPC=$(echo "$json" \
       | jq -r --arg p "5001/tcp" \
             '.instances.ports[$p][0].HostPort
              // .ports[$p][0].HostPort')

[[ $PUBLIC_IP =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]] || fatal "Bad IP: $PUBLIC_IP"
[[ $PORT =~ ^[0-9]+$ ]]                              || fatal "Bad port: $PORT"

CURRENT="${PUBLIC_IP}:${PORT}"
LAST=$(cat "$STATE_FILE" 2>/dev/null || echo "none")

if [[ "$CURRENT" == "$LAST" ]]; then
  echo "[vast-sync] unchanged (${CURRENT})"
  exit 0
fi

### 5. Update Kong Upstream targets (primary switch) --------------------------
# We keep this VERY simple:
#  - Add the new target as weight=PRIMARY_WEIGHT (e.g., 100)
#  - If there was a previous primary in STATE_FILE, disable it by deleting the old host:port
#
# Notes:
#  - DELETE marks the old target 'disabled' (weight=0) in Kong's target history.
#  - No reload needed; the balancer sees changes immediately.
#  - Your backup (e.g., tro_app_api:5000) stays as-is on the Upstream with a small weight.

echo "[vast-sync] updating Kong: $LAST -> $CURRENT on upstream's targets"

# 5.1 Add (or re-add) the new primary target
#     POST /upstreams/{upstream}/targets with target=<host:port>&weight=<n>
curl -sS --fail -X POST "$KONG_ADMIN/upstreams/check-api/targets" -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -d "target=${CURRENT}" -d "weight=100" >/dev/null
curl -sS --fail -X POST "$KONG_ADMIN/upstreams/check-api-inverted/targets" -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -d "target=${CURRENT}" -d "weight=1" >/dev/null

echo "[vast-sync] added/updated primary target: ${CURRENT} (weight 100 for check-api and 1 for check-api-inverted)"

# 5.2 Delete the old primary (the most recent for the old address)
TARGET_ID=$(curl -sS "$KONG_ADMIN/upstreams/check-api/targets" -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" \
  | jq -r --arg addr "$LAST" '
      .data
      | map(select(.target == $addr and .weight > 0))
      | max_by(.created_at)
      | .id // empty')

TARGET_ID_inverted=$(curl -sS "$KONG_ADMIN/upstreams/check-api-inverted/targets" -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" \
  | jq -r --arg addr "$LAST" '
      .data
      | map(select(.target == $addr and .weight > 0))
      | max_by(.created_at)
      | .id // empty')

if [[ -n "$TARGET_ID" ]]; then
  curl -sS --fail -X DELETE "$KONG_ADMIN/upstreams/check-api/targets/$TARGET_ID" -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" >/dev/null
  echo "[vast-sync] deleted previous primary by id: $TARGET_ID ($LAST)"
else
  echo "[vast-sync] warning: no matching target id found for $LAST"
fi

if [[ -n "$TARGET_ID_inverted" ]]; then
  curl -sS --fail -X DELETE "$KONG_ADMIN/upstreams/check-api-inverted/targets/$TARGET_ID_inverted" -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" >/dev/null
  echo "[vast-sync] deleted previous primary by id: $TARGET_ID_inverted ($LAST)"
else
  echo "[vast-sync] warning: no matching target id found for $LAST"
fi

# 5.3 Update the GRPC embedding route
curl -sS --fail -X PATCH "$KONG_ADMIN/services/embeddings" -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -d "protocol=grpc" -d "host=$PUBLIC_IP" -d "port=$PORT_GRPC"
echo "[vast-sync] updated embeddings service to $PUBLIC_IP:$PORT_GRPC"

# 5.4 Persist the new state
echo "$CURRENT" > "$STATE_FILE"
echo "[vast-sync] done → primary is now ${CURRENT}"
