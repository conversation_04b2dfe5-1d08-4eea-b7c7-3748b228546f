import os
import sys
sys.path.append(os.getcwd())

from DatabaseManagement.ImportExport import get_table_from_GZ
from Common.Constants import sanitize_name

def print_copyright_filenames_with_page():
    """
    This script prints the file names of all the copyrights in tb_case (i.e df) 
    that would otherwise get downloaded in collect_all_images.
    Only prints the name and counts the filename if the word "page" is part of the file name.
    """
    df = get_table_from_GZ("tb_case", force_refresh=True)
    missing_count = 0
    case_type = "trademarks"

    for index, row in df.iterrows():
        if row['images'] and isinstance(row['images'], dict) and case_type in row['images']:
            docket_sanitized = row['docket'].replace(':', '_')
            if row['images'][case_type] and isinstance(row['images'][case_type], dict):
                for image in row['images'][case_type].keys():
                    reg_no = row['images'][case_type][image].get('reg_no', '')
                    ser_no = row['images'][case_type][image].get('ser_no', '')
                    SELECT from trademarks where ser_no = ser_no
                    if notfound: 
                        missing_count += 1
                        add(row("plaintiff_id"), reg_no, ser_no)
    
    print(f"\nTotal number missing: {missing_count}")

if __name__ == "__main__":
    print_copyright_filenames_with_page()