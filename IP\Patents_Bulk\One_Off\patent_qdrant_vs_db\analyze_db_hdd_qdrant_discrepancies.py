#!/usr/bin/env python3
"""
Patent DB/HDD/Qdrant Discrepancy Analysis Tool

This script identifies discrepancies between patent records in the local database, files on HDD, and Qdrant embeddings.

Features:
- Finds patent files in DB but not on HDD, and vice versa.
- Finds patent files on HDD but not in Qdrant, and vice versa.
- Uses UUID logic for all comparisons.
- Outputs results to CSV files with detailed information.
- Configurable HDD root directory.
"""

import os
import sys
import csv
import time
import datetime
from pathlib import Path
from tqdm import tqdm
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from IP.Patents_Bulk.patent_db_grant import get_db_connection
from Common.uuid_utils import generate_uuid

# Load environment variables
load_dotenv()

QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = "IP_Assets"

OUTPUT_DIR = os.path.join(os.path.dirname(__file__), 'discrepancy_results')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# --- Configurable HDD root directory ---
DEFAULT_HDD_ROOT = os.environ.get("PATENT_HDD_ROOT", "/data/patents/")
PATENT_FILE_EXTENSIONS = ['.png', '.xml', '.pdf']  # Adjust as needed

def get_database_patent_files():
    """
    Fetches all patent fig_files from the database, generates UUIDs for each file.
    Returns:
        dict: {uuid: (reg_no, file_basename, date_published)}
    """
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            print("❌ Could not establish database connection")
            return {}
        print("🔍 Fetching patent fig_files from database...")
        db_files = {}
        with conn.cursor() as cursor:
            cursor.execute("SELECT reg_no, fig_files, date_published FROM patents WHERE fig_files IS NOT NULL AND array_length(fig_files, 1) > 0;")
            for patent in cursor:
                reg_no = patent[0]
                files = patent[1]
                date_published = patent[2]
                if files:
                    for f in files:
                        file_basename = os.path.basename(f)
                        file_id = os.path.splitext(file_basename)[0]
                        uuid = generate_uuid(file_id)
                        db_files[uuid] = (reg_no, file_basename, date_published)
        print(f"✅ Successfully fetched {len(db_files):,} patent files from database")
        return db_files
    except Exception as e:
        print(f"❌ Error fetching database patent files: {str(e)}")
        return {}
    finally:
        if conn:
            conn.close()

def scan_hdd_patent_files():
    """
    Scans the HDD for patent files using the USPTO Grants Extracted folder structure as in weekly_patent.py.
    Returns:
        dict: {uuid: (file_path, file_basename)}
    """
    from Common.Constants import local_ip_folder
    print("🔍 Scanning HDD for patent files using USPTO Grants Extracted folder structure...")
    # Path: [local_ip_folder]/Patents/USPTO_Grants/Extracted/XX/YY/US[reg_no]-*
    base_dir = Path(local_ip_folder) / "Patents" / "USPTO_Grants" / "Extracted"
    hdd_files = {}
    # Traverse XX/YY folders
    for xx_dir in base_dir.iterdir():
        if not xx_dir.is_dir():
            continue
        for yy_dir in xx_dir.iterdir():
            if not yy_dir.is_dir():
                continue
            for patent_folder in yy_dir.iterdir():
                if not patent_folder.is_dir():
                    continue
                # Extract reg_no from folder name (e.g., US12345678-...)
                folder_name = patent_folder.name
                reg_no = None
                match = None
                import re
                match = re.match(r'US([A-Z0-9]+)-', folder_name)
                if match:
                    reg_no = match.group(1)
                if not reg_no:
                    continue
                # For each file in the patent folder, collect file info
                for file_path in patent_folder.iterdir():
                    if not file_path.is_file():
                        continue
                    file_basename = file_path.name
                    file_id = os.path.splitext(file_basename)[0]
                    uuid = generate_uuid(file_id)
                    hdd_files[uuid] = (str(file_path), file_basename)
    print(f"✅ Found {len(hdd_files):,} patent files on HDD")
    return hdd_files

def get_qdrant_patent_points():
    """
    Fetches all patent points from Qdrant, mapping point.id to reg_no.
    Returns:
        dict: {uuid: reg_no}
    """
    try:
        client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
        print("🔍 Fetching patent points from Qdrant...")
        count_result = client.count(
            collection_name=COLLECTION_NAME,
            count_filter=models.Filter(
                must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Patent"))]
            ),
            exact=True
        )
        total_points = count_result.count
        print(f"   Total patent points in Qdrant: {total_points:,}")
        qdrant_points = {}
        offset = None
        while True:
            points, next_offset = client.scroll(
                collection_name=COLLECTION_NAME,
                scroll_filter=models.Filter(
                    must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Patent"))]
                ),
                limit=50000,
                offset=offset,
                with_payload=True,
                with_vectors=False
            )
            for point in points:
                reg_no = point.payload.get("reg_no")
                qdrant_points[point.id] = reg_no
            if next_offset is None:
                break
            offset = next_offset
        print(f"✅ Successfully fetched {len(qdrant_points):,} patent points from Qdrant")
        return qdrant_points
    except Exception as e:
        print(f"❌ Error fetching Qdrant patent points: {str(e)}")
        return {}

def save_to_csv(records, filename, fieldnames):
    if not records:
        print(f"ℹ️ No records to save for {filename}")
        return
    csv_path = os.path.join(OUTPUT_DIR, filename)
    try:
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(records)
        print(f"✅ Results saved to: {csv_path}")
        print(f"📊 Total records saved: {len(records):,}")
    except Exception as e:
        print(f"❌ Error saving CSV {filename}: {str(e)}")

def main():
    print("🔍 Starting Patent DB/HDD/Qdrant Discrepancy Analysis")
    print(f"📁 Output directory: {OUTPUT_DIR}")
    print("=" * 50)
    start_time = time.time()
    db_files = get_database_patent_files()
    hdd_files = scan_hdd_patent_files()
    qdrant_points = get_qdrant_patent_points()
    db_uuids = set(db_files.keys())
    hdd_uuids = set(hdd_files.keys())
    qdrant_uuids = set(qdrant_points.keys())
    all_uuids = db_uuids | hdd_uuids | qdrant_uuids
    records = []
    for uuid in all_uuids:
        db_status = "yes" if uuid in db_uuids else "no"
        hdd_status = "yes" if uuid in hdd_uuids else "no"
        qdrant_status = "yes" if uuid in qdrant_uuids else "no"
        reg_no = db_files.get(uuid, (None, None, None))[0] or qdrant_points.get(uuid, None)
        file_name = db_files.get(uuid, (None, None, None))[1] or hdd_files.get(uuid, (None, None))[1]
        file_path = hdd_files.get(uuid, (None, None))[0]
        records.append({
            'uuid': uuid,
            'reg_no': reg_no,
            'file_name': file_name,
            'file_path': file_path,
            'db_status': db_status,
            'hdd_status': hdd_status,
            'qdrant_status': qdrant_status
        })
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    save_to_csv(
        records,
        f"patent_discrepancy_status_{timestamp}.csv",
        ['uuid', 'reg_no', 'file_name', 'file_path', 'db_status', 'hdd_status', 'qdrant_status']
    )
    print("\n--- SUMMARY ---")
    print(f"Total unique patent files: {len(all_uuids):,}")
    print(f"DB files not on HDD: {sum(1 for r in records if r['db_status']=='yes' and r['hdd_status']=='no'):,}")
    print(f"HDD files not in DB: {sum(1 for r in records if r['hdd_status']=='yes' and r['db_status']=='no'):,}")
    print(f"HDD files not in Qdrant: {sum(1 for r in records if r['hdd_status']=='yes' and r['qdrant_status']=='no'):,}")
    print(f"Qdrant points not on HDD: {sum(1 for r in records if r['qdrant_status']=='yes' and r['hdd_status']=='no'):,}")
    total_time = time.time() - start_time
    print(f"\n✅ Analysis completed in {total_time:.1f} seconds")

if __name__ == "__main__":
    main()
