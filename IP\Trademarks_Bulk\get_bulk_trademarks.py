#!/usr/bin/env python3
"""
Daily Trademark Report

This script handles the downloading, processing, and database loading of USPTO trademark data.
It supports three modes of operation:
1. Historical Batch Import: One-time bulk import of historical trademark data (pre-2025)
2. Daily Catch-up: Download and process historical daily trademark update files from Jan 1st, 2025
3. Ongoing Daily Processing: Automatically download and process the latest daily trademark update file
"""

import os
import asyncio
import aiohttp
import shutil
from tqdm.asyncio import tqdm

import datetime
import tempfile
from logdata import log_message
from dotenv import load_dotenv

# Import local modules
# Import the new orchestrator function
from trademark_parser import parse_daily_trademark_xml_parallel
from trademark_db import upsert_trademarks, get_all_existing_ser_no, delete_trademarks_by_ser_no, get_latest_daily_info_source_date, get_tro_protected_ser_nos
from trademark_image import download_and_save_image, get_image_subdirectory
from trademark_file import download_file, extract_zip, merge_image_directories
from Common.Constants import local_ip_folder, nas_ip_folder
from app_embeddings import EmbeddingQueue
from IP.Trademarks.Trademark_API import get_image_from_status_zip, get_image_from_documents_zip, save_content, get_new_image_local_path, get_old_image_local_path
from IP.Trademarks.USPTO_TSDR_API import TSDRApi

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# Load environment variables
load_dotenv()

# Removed logging configuration block
# logger = logging.getLogger(__name__) # Removed logger initialization

# Constants
# PRE2025_BASE_URL for browser: "https://data.uspto.gov/ui/datasets/products/files/TRTYRAP/apc18840407-20241231-{:02d}.zip" 
# or the list is available at https://data.uspto.gov/bulkdata/datasets/trtyrap?fileDataFromDate=2025-02-24&fileDataToDate=2025-03-24
PRE2025_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/TRTYRAP/apc18840407-20241231-{:02d}.zip"
DAILY_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/TRTDXFAP/apc{}.zip"


# Directory structure
BASE_DIR = os.path.join(local_ip_folder, "Trademarks")
ZIP_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Zip")
os.makedirs(ZIP_DIR, exist_ok=True)
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")
os.makedirs(IMAGES_DIR, exist_ok=True)

# NAS paths
NAS_ZIP_DIR = f"{nas_ip_folder}/Trademarks/USPTO_Daily/Zip"
NAS_IMAGES_DIR = f"{nas_ip_folder}/Trademarks/USPTO_Daily/Images"

# Maximum concurrent tasks
MAX_CONCURRENT_IMAGES = 10      # For downloading images in parallel

DATABASE_SER_NOS = set()

# --- XML Parsing logic moved to trademark_parser.py ---
async def process_zip_file(zip_path, extract_dir, nas_dir, source, embedding_queue, tro_protected_ser_nos):
    """
    Process a single ZIP file: extract, parse XML, update database, and send to NAS.

    Args:
        zip_path (str): Path to the ZIP file
        extract_dir (str): Directory to extract to
        nas_dir (str): NAS directory to send the ZIP file to
        embedding_queue (EmbeddingQueue): The queue for image embeddings.

    Returns:
        int: Number of trademark records processed
    """
    
    global DATABASE_SER_NOS
    if not DATABASE_SER_NOS:
        DATABASE_SER_NOS = set(get_all_existing_ser_no())

    # --- Initialize API Client for Fallback Methods ---
    api_client = TSDRApi()
    await api_client.start_session()
    
    # --- Define Fallback Download Logic ---
    async def download_image_with_fallback(session, sn, reg_no, temp_images_dir, main_images_dir, semaphore, pbar):
        """
        Tries to download an image using the primary method, then falls back to other methods.
        Returns a tuple of (success, status_string, image_source).
        """
        async with semaphore:
            try:
                # --- Method 1: Primary URL Download ---
                success, status = await download_and_save_image(session, sn, temp_images_dir, main_images_dir, asyncio.Semaphore(1), None)
                if success:
                    pbar.update(1)
                    return True, status, "USPTO_URL"

                # --- Method 2: Status ZIP Fallback ---
                log_message(f"Fallback 1 for {sn}: Trying status ZIP.", level='INFO')
                formatted_reg_no = f"{reg_no:0>7}" if reg_no else None
                if formatted_reg_no:
                    with tempfile.TemporaryDirectory() as status_temp_dir:
                        # content_reg = await api_client.get_status_content(formatted_reg_no, id_key='rn', format='zip')
                        content_reg = await api_client.get_status_content(sn, id_key='sn', format='zip')
                        if content_reg:
                            status_zip_path = os.path.join(status_temp_dir, f"{sn}.zip")
                            if await save_content(content_reg, f"{sn}.zip", status_temp_dir):
                                # image_downloaded = await get_image_from_status_zip(formatted_reg_no, status_zip_path, status_temp_dir, temp_images_dir)
                                image_downloaded = await get_image_from_status_zip(formatted_reg_no, sn, status_zip_path)
                                if image_downloaded:
                                    log_message(f"Success with Fallback 1 (Status ZIP) for {sn}", level='INFO')
                                    pbar.update(1)
                                    return True, "newly_downloaded", "USPTO_STATUSZIP"

                # --- Method 3: Documents ZIP Fallback ---
                log_message(f"Fallback 2 for {sn}: Trying documents ZIP.", level='INFO')
                if formatted_reg_no:
                    with tempfile.TemporaryDirectory() as doc_temp_dir:
                        # documents_zip = await api_client.get_casedocs_bundle([formatted_reg_no], id_key='rn', format='zip')
                        documents_zip = await api_client.get_casedocs_bundle([sn], id_key='sn', format='zip')
                        if documents_zip:
                            documents_zip_path = os.path.join(doc_temp_dir, f"{sn}.zip")
                            if await save_content(documents_zip, f"{sn}.zip", doc_temp_dir):
                                # image_downloaded, _ = await get_image_from_documents_zip(formatted_reg_no, documents_zip_path, doc_temp_dir, temp_images_dir)
                                image_downloaded, _ = await get_image_from_documents_zip(formatted_reg_no, sn, documents_zip_path)
                                if image_downloaded:
                                    log_message(f"Success with Fallback 2 (Documents ZIP) for {sn}", level='INFO')
                                    pbar.update(1)
                                    return True, "newly_downloaded", "USPTO_DOCZIP"
                
                # --- Method 4: Check Image on HDD & If available then copy from old to new location---
                log_message(f"Fallback 3 for {sn}: Checking HDD for old image.", level='INFO')
                hdd_images = os.path.join(local_ip_folder, "Trademarks", "Images")
                image_path = get_new_image_local_path(ser_no=sn, reg_no=formatted_reg_no)
                if not os.path.isfile(image_path) and get_old_image_local_path(ser_no=sn, reg_no=formatted_reg_no):
                    shutil.copy2(get_old_image_local_path(ser_no=sn, reg_no=formatted_reg_no), image_path)
                
                if os.path.exists(image_path):
                    log_message(f"Success with Fallback 3 (Found Old Image on HDD) for {sn}", level='INFO')
                    if isinstance(hdd_images, dict) and "pure_img_location" in hdd_images:
                        return True, "found_on_hdd", "FOUND_HDD"
                    return True, "found_on_hdd", "FOUND_HDD"
                
                log_message(f"All download methods failed for {sn}", level='WARNING')
                pbar.update(1)
                return False, "failed", "NotFound"

            except Exception as e:
                log_message(f"Exception during image download for {sn}: {e}", level='ERROR')
                pbar.update(1)
                return False, "failed", "NotFound"

    try:
        zip_filename = os.path.basename(zip_path)
        # Extract ZIP file - there should be only one XML file per ZIP
        xml_file = extract_zip(zip_path, extract_dir)

        if not xml_file:
            log_message(f"No XML files found in ZIP: {zip_path}", level='ERROR')
            return 0

        try:
            # Call the parallel parsing orchestrator function from trademark_parser: This function handles counting, chunking, pool execution, and aggregation
            trademarks, serials_to_download, ser_nos_to_delete = parse_daily_trademark_xml_parallel(xml_file, source=source)
            
            # Create a mapping from ser_no to reg_no for the fallback methods
            ser_to_reg_map = {tm['ser_no']: tm.get('reg_no') for tm in trademarks}
            
            ### Now we will need to conduct a number of activities: 
            # 1. Delete the trademarks that are no longer active in the database
            # 2. Delete embeddings of trademarks that are no longer active
            # 3. Download the images of active trademarks (if not already local) 
            # 4. Update the database with active_trademarks
            # 5. Enqueue the logos for embedding

            ### 1. Delete the trademarks that are no longer active in the database (but not if TRO protected)
            log_message(f"Found {len(tro_protected_ser_nos)} TRO-protected trademarks that will NOT be deleted", level='INFO')

            # Filter out TRO-protected trademarks from deletion list
            ser_nos_to_delete_in_db = [sn for sn, _ in ser_nos_to_delete if sn in DATABASE_SER_NOS and sn not in tro_protected_ser_nos]

            # Log TRO-protected trademarks that would have been deleted
            tro_protected_expired = [sn for sn, _ in ser_nos_to_delete if sn in DATABASE_SER_NOS and sn in tro_protected_ser_nos]
            if tro_protected_expired:
                log_message(f"TRO PROTECTION: {len(tro_protected_expired)} expired trademarks with TRO flag were NOT deleted: {tro_protected_expired[:10]}", level='WARNING')
            log_message(f"Deleting {len(ser_nos_to_delete_in_db)} ser_no entries from DB for file {zip_filename} (excluding {len(tro_protected_expired)} TRO-protected).", level='INFO')
            if ser_nos_to_delete_in_db:
                deleted_db_count = delete_trademarks_by_ser_no(ser_nos_to_delete_in_db)
                DATABASE_SER_NOS = DATABASE_SER_NOS - set(ser_nos_to_delete_in_db)
                log_message(f"Deleted {deleted_db_count} ser_no entries from trademarks DB and DATABASE_SER_NOS", level='INFO')

            ### 2. Delete embeddings of trademarks that are no longer active ()
            ser_nos_to_delete_in_qdrant = [sn for sn, mfc in ser_nos_to_delete
                                         if mfc in [2, 3, 5]
                                         if sn not in DATABASE_SER_NOS
                                         and sn not in tro_protected_ser_nos]
            log_message(f"Deleting {len(ser_nos_to_delete_in_qdrant)} ser_no entries from Qdrant for file {zip_filename} (TRO-protected).", level='INFO')
            if ser_nos_to_delete_in_qdrant:
                qdrant_result = embedding_queue.delete_embeddings_by_ser_no(ser_nos_to_delete_in_qdrant, collection_name="IP_Assets")
                log_message(f"Qdrant delete result: {qdrant_result}", level='INFO')
            
            ### 3. Download the images of active trademarks (if not already local)
            if not trademarks:
                 log_message(f"No trademark records extracted from XML file: {xml_file}", level='WARNING')
                 return 0

            with tempfile.TemporaryDirectory(prefix="tm_images_") as temp_images_dir:
                log_message(f"Using temporary image directory: {temp_images_dir}", level='INFO')

                download_status = {}
                download_stats = {"already_present": 0, "newly_downloaded": 0, "failed": 0}
                
                # --- Asynchronous Image Downloading ---
                if serials_to_download:
                    log_message(f"Attempting to download {len(serials_to_download)} images concurrently with fallbacks...", level='INFO')
                    download_semaphore = asyncio.Semaphore(MAX_CONCURRENT_IMAGES)
                    async with aiohttp.ClientSession() as session:
                        progress_bar = tqdm(total=len(serials_to_download), desc="Downloading Images")
                        download_tasks = [
                            download_image_with_fallback(session, sn, ser_to_reg_map.get(sn), temp_images_dir, IMAGES_DIR, download_semaphore, progress_bar)
                            for sn in serials_to_download
                        ]
                        results = await asyncio.gather(*download_tasks, return_exceptions=True)

                    for sn, res in zip(serials_to_download, results):
                        if isinstance(res, Exception):
                            log_message(f"Image download task for {sn} failed with exception: {res}", level='ERROR')
                            download_status[sn] = (False, "NotFound")
                            download_stats["failed"] += 1
                        elif isinstance(res, tuple) and len(res) == 3:
                            success, status, image_source = res
                            download_status[sn] = (success, image_source)
                            download_stats[status] += 1
                        else:
                            log_message(f"Unexpected result type for {sn} download: {type(res)}", level='ERROR')
                            download_status[sn] = (False, "NotFound")
                            download_stats["failed"] += 1
                            
                    total_attempted = len(serials_to_download)
                    print(f"📊 Download Statistics for this ZIP file:")
                    print(f"   📁 Already present on HDD: {download_stats['already_present']}")
                    print(f"   ⬇️  Newly downloaded: {download_stats['newly_downloaded']}")
                    print(f"   ❌ Failed downloads: {download_stats['failed']}")
                    print(f"   📈 Total attempted: {total_attempted}")

                    success_count = sum(1 for success, _ in download_status.values() if success)
                    log_message(f"Finished image download attempts. Success count: {success_count}", level='INFO')

                    # Update trademark records with download status
                    for trademark in trademarks:
                        sn = trademark.get('ser_no')
                        # Only update if download was relevant and attempted
                        # Update image_source for all trademarks if download was attempted
                        if sn in download_status:
                            success, image_source = download_status[sn]
                            trademark['image_source'] = image_source if success else "NotFound"
                        # Ensure image_source is set even if download wasn't needed/attempted
                        elif 'image_source' not in trademark:
                            trademark['image_source'] = None
                else:
                    log_message("No images identified for download in this XML.", level='INFO')
                    # Ensure image_source is set to None if not already present
                    for trademark in trademarks:
                        if 'image_source' not in trademark:
                            trademark['image_source'] = None

                total_processed = 0

                ### 4. Update the database with active_trademarks (in a separate thread (I/O bound)) with the final trademark list
                db_task = asyncio.create_task(asyncio.to_thread(upsert_trademarks, trademarks))
                new_ser_nos = [t['ser_no'] for t in trademarks if t['ser_no']]
                DATABASE_SER_NOS.update(new_ser_nos)

                processed = await db_task
                total_processed += processed
                log_message(f"Processed {processed} trademark records from {xml_file}", level='INFO')

                if not merge_image_directories(temp_images_dir, IMAGES_DIR):
                    log_message(f"Failed to merge images from {temp_images_dir} to {IMAGES_DIR}. Check trademark_file.log for details.", level='ERROR')
                ### 5. Enqueue the logos for embedding
                items_to_enqueue = []
                for trademark in trademarks:
                    ser_no = trademark.get('ser_no')
                    mark_feature_code = trademark.get('mark_feature_code')
                    image_source = trademark.get('image_source')

                    # Compute embeddings only if trademark has mark_feature_code in [2, 3, 5] AND image downloaded correctly
                    if mark_feature_code in [2, 3, 5] and image_source not in [None, "NotFound"]:
                        image_sub_dir = get_image_subdirectory(ser_no)
                        if not image_sub_dir:
                            log_message(f"Could not determine image subdirectory for serial number: {ser_no}", level='ERROR')
                            continue
                        image_path = os.path.join(IMAGES_DIR, image_sub_dir, f"{ser_no}.webp")
                        items_to_enqueue.append((ser_no, image_path))

                if items_to_enqueue:
                    images_enqueued, images_skipped_existing = await embedding_queue.batch_enqueue_trademarks(items_to_enqueue)
                    log_message(f"Batch enqueued {images_enqueued} images for embedding processing, skipped {images_skipped_existing} existing embeddings", level='INFO')
                else:
                    log_message("No images to enqueue for embedding processing", level='INFO')

            return total_processed

        except Exception as e:
            log_message(f"Error processing XML file {xml_file}: {str(e)}", level='ERROR')
            return 0

        finally:
            # Delete XML file after processing
            xml_files = [file for file in os.listdir(extract_dir) if file.endswith('.xml')]
            for xml_file_to_del in xml_files:
                try:
                    os.remove(os.path.join(extract_dir, xml_file_to_del))
                    log_message(f"Deleted XML file: {xml_file_to_del}", level='INFO')
                except PermissionError:
                    log_message(f"Failed to delete {xml_file_to_del}", level='ERROR')

    except Exception as e:
        log_message(f"Error processing ZIP file {zip_path}: {str(e)}", level='ERROR')
        return 0
    finally:
        # --- Close API Client Session ---
        await api_client.close_session()

async def historical_batch_import(embedding_queue, tro_protected_ser_nos):
    """
    Perform a one-time bulk import of historical trademark data (pre-2025).
    There are 155000 per zip file (except maybe the last one).
    There are very few logos in the early files.
    """
    print("Starting historical batch import...")

    # Create directories if they don't exist
    os.makedirs(ZIP_DIR, exist_ok=True)

    # Setup API headers
    api_key = os.getenv("USPTO_ODP_API_KEY")
    print(api_key)
    headers = {"X-API-KEY": api_key} if api_key else None

    # Download and process each historical ZIP file
    total_processed = 0

    for i in range(1, 83):  # 82 historical ZIP files => range(1, 83)
        while embedding_queue.get_queue_status()['queue_size'] > 1000:
            await asyncio.sleep(10)
        
        zip_url = PRE2025_BASE_URL.format(i)
        zip_filename = f"apc18840407-20241231-{i:02d}.zip"
        zip_path = os.path.join(ZIP_DIR, zip_filename)

        print(f"\n\n{datetime.datetime.now().strftime('%m-%d %H-%M-%S')}: Processing historical file {i}/82: {zip_filename}")

        # Download ZIP file if it doesn't exist
        if not os.path.exists(zip_path):
            print(f"✅ Downloading {zip_filename} from {zip_url}")
            if not download_file(zip_url, zip_path, headers):
                print(f"❌ Failed to download {zip_filename}")
                continue

        # Process ZIP file
        processed = await process_zip_file(zip_path, ZIP_DIR, NAS_ZIP_DIR, source=f'bulk_{zip_filename}', embedding_queue=embedding_queue, tro_protected_ser_nos=tro_protected_ser_nos)
        total_processed += processed

        print(f"✅✅ Processed {processed} trademark records from {zip_filename}")

    print(f"\n\n\n ✅✅Historical batch import completed. Total records processed: {total_processed}\n\n\n")

async def process_date(embedding_queue, date, headers, tro_protected_ser_nos):
    """
    Process a single date: download, extract, parse, update database, and send to NAS.

    Args:
        date (datetime.date): Date to process
        headers (dict, optional): HTTP headers for API requests
        embedding_queue (EmbeddingQueue, optional): The queue for image embeddings.

    Returns:
        int: Number of trademark records processed
    """
    # Create directories if they don't exist
    os.makedirs(ZIP_DIR, exist_ok=True)

    # Construct file information - format as YYMMDD for daily files
    date_str = date.strftime("%y%m%d")  # Note: lowercase 'y' for 2-digit year
    zip_url = DAILY_BASE_URL.format(date_str)
    zip_filename = f"apc{date_str}.zip"
    zip_path = os.path.join(ZIP_DIR, zip_filename)

    log_message(f"Processing daily file for {date}: {zip_filename}", level='INFO')

    # Download ZIP file if it doesn't exist
    if not os.path.exists(zip_path):
        log_message(f"Downloading {zip_filename} from {zip_url}", level='INFO')
        if not download_file(zip_url, zip_path, headers):
            log_message(f"No file available for {date}", level='WARNING')
            return 0

    # Process ZIP file
    processed = await process_zip_file(zip_path, ZIP_DIR, NAS_ZIP_DIR, source=f'daily_{zip_filename}', embedding_queue=embedding_queue, tro_protected_ser_nos=tro_protected_ser_nos)

    log_message(f"Processed {processed} trademark records from {zip_filename}", level='INFO')
    return processed

async def daily(embedding_queue, tro_protected_ser_nos, date=None, start_date=None, end_date=None):
    """
    Download and process historical daily trademark update files from a specified start date.

    Args:
        start_date (datetime.date): Start date for catchup
        end_date (datetime.date, optional): End date for catchup. Defaults to today.
        embedding_queue (EmbeddingQueue, optional): The queue for image embeddings.
    """
    # Date logic
    if date:
        start_date = date
        end_date = date
    else:
        if start_date is None:
            last_daily_date_from_database = get_latest_daily_info_source_date()
            if last_daily_date_from_database is None:
                log_message("No previous daily_ info_source found in DB. Starting from a default date (e.g., 2025-01-01).", level='WARNING')
                # start_date = datetime.date(2025, 1, 1)
            else:
                start_date = last_daily_date_from_database + datetime.timedelta(days=1)
        if end_date is None:
            end_date = datetime.date.today() - datetime.timedelta(days=1)

    log_message(f"Starting daily catchup from {start_date} to {end_date}...", level='INFO')

    # Setup API headers
    api_key = os.getenv("USPTO_ODP_API_KEY")
    print(api_key)
    headers = {"X-API-KEY": api_key} if api_key else None

    # Iterate through each date
    current_date = start_date
    total_processed = 0
    

    while current_date <= end_date:
        while embedding_queue.get_queue_status()['queue_size'] > 1000:
            await asyncio.sleep(10)
        processed = await process_date(embedding_queue, current_date, headers, tro_protected_ser_nos)
        total_processed += processed

        # Move to next date
        current_date += datetime.timedelta(days=1)

    log_message(f"Daily catchup completed. Total records processed: {total_processed}", level='INFO')
    return total_processed


async def process_bulk_trademarks(mode, start_date=None, end_date=None, date=None):
    """
    Main function to run the appropriate mode.
    """
    # Create embedding queue with 10 concurrent workers and API rate limiting
    embedding_queue = EmbeddingQueue(max_concurrent=2)

    try:
        await embedding_queue.start()
        log_message(f"Embedding queue started with status: {embedding_queue.get_queue_status()}", level='INFO')

        tro_protected_ser_nos = get_tro_protected_ser_nos()
        log_message(f"Found {len(tro_protected_ser_nos)} TRO-protected serial numbers.", level='INFO')
        if mode == "historical":
            await historical_batch_import(embedding_queue, tro_protected_ser_nos)

        elif mode == "daily":
            await daily(embedding_queue, tro_protected_ser_nos, date=date, start_date=start_date, end_date=end_date)

        # Wait for all embedding tasks to complete
        log_message(f"Waiting for embedding queue to drain. Status: {embedding_queue.get_queue_status()}", level='INFO')
        await embedding_queue.drain()
        log_message("All embedding tasks completed", level='INFO')

    finally:
        # Always stop the workers gracefully
        await embedding_queue.stop()

if __name__ == "__main__":
    # Example usage:
    # To run historical import:
    # asyncio.run(process_bulk_trademarks(mode="historical"))
    # To run daily catchup from a specific date:
    asyncio.run(process_bulk_trademarks(mode="daily", date=datetime.date(2025, 6, 30)))