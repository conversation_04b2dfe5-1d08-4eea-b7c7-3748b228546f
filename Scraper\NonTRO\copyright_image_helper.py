import os
import sys
sys.path.append(os.getcwd())
import os
import json
import re
from dotenv import load_dotenv
import google.generativeai as genai
from AI.GC_VertexAI import vertex_genai_multi_async, parse_json
from AI.LLM_shared import get_json
from AI.GoogleSearch import search_and_save_images
from Alerts.Plaintiff import remove_acronyms
from Common.Constants import sanitize_name
from typing import List, Tuple, Dict, Any
from urllib.parse import urlparse
from PIL import Image
import base64
import subprocess

# Get environment variables
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

def clean_claimant(claimant):
    """Cleans up the copyright claimant string for better search queries."""
    claimant = re.sub(r"Transfer: By written agreement\..*", "", claimant, flags=re.IGNORECASE)
    claimant = re.sub(r"Address:.*", "", claimant, flags=re.IGNORECASE)
    claimant = claimant.replace(",", "").replace(".", "")
    claimant = remove_acronyms(claimant)
    claimant = claimant.strip()
    return claimant

def clean_title(text: str) -> str:
    """
    Cleans the bibliographic entry by removing attribution labels like '/[performed by]' or '/ill.',
    and preserving both the title and the name (e.g., performer or illustrator).
    """
    # Remove known attribution prefixes and slashes
    text = re.sub(r'/\s*(\[\s*performed by\s*\]|\s*ill\.)\s*', ' ', text, flags=re.IGNORECASE)
    
    # Normalize whitespace and return
    return re.sub(r'\s+', ' ', text).strip()

def check_image_size(image_path: str) -> Tuple[bool, int, int]:
    """
    Check if image meets minimum size requirements (100x100).
    Returns (meets_requirement, width, height)
    """
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            meets_requirement = width >= 100 and height >= 100
            return meets_requirement, width, height
    except Exception as e:
        print(f"Error checking image size for {image_path}: {e}")
        return False, 0, 0

class EnhancedApproachA:
    """Simplified direct Google query with preference scoring for specific sites."""
    
    def __init__(self):
        # Preferred sites that get priority scoring
        self.preferred_sites = [
            "deviantart.com",
            "artstation.com", 
            "behance.net",
            "pinterest.com",
            "flickr.com",
            "instagram.com",
            "dribbble.com",
            "500px.com",
            "facebook.com",
            "amazon.com",
            "artsy.net"
        ]
        
        # Site quality scoring with higher scores for preferred sites
        self.site_quality_scores = {
            # Preferred sites (highest quality)
            'deviantart.com': 5.0,
            'artstation.com': 5.0,
            'behance.net': 5.0,
            'pinterest.com': 4.5,
            'flickr.com': 4.5,
            'instagram.com': 4.0,
            'dribbble.com': 4.5,
            '500px.com': 4.5,
            'facebook.com': 3.5,
            'amazon.com': 4.0,
            'artsy.net': 4.5,
            
            # Other quality sites
            'pixiv.net': 4.0,
            'cgtrader.com': 4.0,
            'turbosquid.com': 4.0,
            'sketchfab.com': 4.0,
            'etsy.com': 3.5,
            'redbubble.com': 3.5,
            'society6.com': 3.5,
            'imgur.com': 2.0,
            'photobucket.com': 2.0,
            'tumblr.com': 2.5
        }
    
    def is_preferred_site(self, domain: str) -> bool:
        """Check if the domain is one of our preferred sites."""
        domain = domain.lower()
        return any(preferred_site in domain for preferred_site in self.preferred_sites)
    
    def generate_simplified_search_queries(self, title: str, claimant: str, basis_of_claim: str = None) -> List[Dict[str, Any]]:
        """
        Generate simplified search queries optimized for Google search.
        Assumes `title` includes both the work title and performer/illustrator.
        """
        queries = []

        # Format 1: Title + Artist + Claimant (no quotes)
        query1 = f'{title} {claimant}'
        queries.append({
            'query': query1,
            'type': 'title_artist_claimant',
            'priority': 1,
            'expected_results': 8
        })

        # Format 2: Claimant + Title + by Artist (no quotes)
        if " " in title.strip():
            # Try to split out the performer/artist (last two words)
            parts = title.strip().rsplit(' ', 2)
            if len(parts) == 3:
                raw_title = ' '.join(parts[:-1])
                artist = parts[-1]
            else:
                raw_title = title.strip()
                artist = ""
        else:
            raw_title = title.strip()
            artist = ""

        if artist:
            query2 = f'{claimant} {raw_title} by {artist}'
        else:
            query2 = f'{claimant} {title}'

        queries.append({
            'query': query2,
            'type': 'claimant_title_by_artist',
            'priority': 1,
            'expected_results': 8
        })

        # Add artwork-specific queries
        queries.append({
            'query': f'{title} {claimant} artwork',
            'type': 'artwork_specific',
            'priority': 1,
            'expected_results': 6
        })

        # Optional basis_of_claim queries
        if basis_of_claim:
            basis_lower = basis_of_claim.lower()
            if "photograph" in basis_lower:
                queries.append({
                    'query': f'{title} photographer {claimant}',
                    'type': 'photography_specific',
                    'priority': 2,
                    'expected_results': 5
                })
            elif "literary" in basis_lower:
                queries.append({
                    'query': f'{title} book {claimant} artwork',
                    'type': 'book_artwork',
                    'priority': 2,
                    'expected_results': 5
                })

        return queries
    
    async def execute_simplified_search(self, queries: List[Dict[str, Any]], 
                                       all_images_folder_path: str) -> List[Tuple[str, Dict]]:
        """Execute simplified search with preference-based scoring."""
        all_results = []
        query_performance = {}
        
        # Sort queries by priority
        sorted_queries = sorted(queries, key=lambda x: x['priority'])
        
        for query_info in sorted_queries:
            query = query_info['query']
            query_type = query_info['type']
            expected_results = query_info['expected_results']
            
            print(f"🔍 Executing {query_type} query: {query}")
            
            try:
                image_filenames_json, metadata_list_json = search_and_save_images(
                    query, all_images_folder_path, expected_results
                )
                
                image_filenames = json.loads(image_filenames_json) if image_filenames_json else []
                metadata_list = json.loads(metadata_list_json) if metadata_list_json else []
                
                results_found = len(image_filenames)
                query_performance[query] = {
                    'type': query_type,
                    'expected': expected_results,
                    'found': results_found,
                    'success_rate': results_found / expected_results if expected_results > 0 else 0
                }
                
                for filename, metadata in zip(image_filenames, metadata_list):
                    image_path = os.path.join(all_images_folder_path, filename)
                    
                    # Enhanced metadata with search context and preference scoring
                    enhanced_metadata = metadata.copy()
                    enhanced_metadata.update({
                        'source_query': query,
                        'query_type': query_type,
                        'query_priority': query_info['priority'],
                        'quality_score': self._calculate_quality_score(metadata, query_info),
                        'relevance_score': self._calculate_relevance_score(metadata, query),
                        'site_quality_score': self._get_site_quality_score(metadata.get('displayLink', '')),
                        'is_preferred_site': self.is_preferred_site(metadata.get('displayLink', '')),
                        'image_filename': filename  # Store original filename for copying
                    })
                    
                    all_results.append((image_path, enhanced_metadata))
                    
                print(f"  Found {results_found} results")
                
                # Count preferred site results
                preferred_count = sum(1 for _, metadata in zip(image_filenames, metadata_list) 
                                    if self.is_preferred_site(metadata.get('displayLink', '')))
                if preferred_count > 0:
                    print(f"   {preferred_count} results from preferred sites")
                
            except Exception as e:
                print(f"   Error: {e}")
                query_performance[query] = {
                    'type': query_type,
                    'expected': expected_results,
                    'found': 0,
                    'success_rate': 0,
                    'error': str(e)
                }
        
        # Print query performance summary
        print("\n📊 Query Performance Summary:")
        for query, perf in query_performance.items():
            print(f"  {perf['type']}: {perf['found']}/{perf['expected']} results "
                  f"({perf['success_rate']:.1%} success rate)")
        
        return all_results
    
    def _calculate_quality_score(self, metadata: Dict, query_info: Dict) -> float:
        """Calculate quality score with preference for specific sites."""
        score = 0.0
        
        # Base score from site quality
        site_score = self._get_site_quality_score(metadata.get('displayLink', ''))
        score += site_score
        
        # Preferred site bonus
        if self.is_preferred_site(metadata.get('displayLink', '')):
            score += 2.0  # Significant bonus for preferred sites
        
        # Title quality indicators
        title = metadata.get('title', '').lower()
        html_title = metadata.get('htmlTitle', '').lower()
        
        # Positive indicators
        positive_indicators = [
            'high resolution', 'hd', '4k', 'original', 'artwork', 'artist', 
            'portfolio', 'gallery', 'exhibition', 'professional', 'commission'
        ]
        
        for indicator in positive_indicators:
            if indicator in title or indicator in html_title:
                score += 0.5
        
        # Negative indicators
        negative_indicators = [
            'thumbnail', 'small', 'icon', 'avatar', 'low res', 'compressed',
            'watermark', 'preview', 'sample', 'draft'
        ]
        
        for indicator in negative_indicators:
            if indicator in title or indicator in html_title:
                score -= 1.0
        
        # Query type bonus
        query_type = query_info.get('type', '')
        if query_type in ['title_artist_claimant', 'artwork_specific']:
            score += 1.0  # Higher bonus for most effective queries
        elif query_type in ['photography_specific', 'book_artwork']:
            score += 0.5
        
        return max(0.0, score)  # Ensure non-negative score
    
    def _calculate_relevance_score(self, metadata: Dict, query: str) -> float:
        """Calculate relevance score based on query term matches."""
        score = 0.0
        
        # Extract search terms from query
        search_terms = re.findall(r'"([^"]*)"', query)
        search_terms.extend([term for term in query.replace('"', '').split() 
                           if not term.startswith('site:') and len(term) > 2])
        search_terms = [term.lower().strip() for term in search_terms if term.strip()]
        
        # Check matches in title and HTML title
        title = metadata.get('title', '').lower()
        html_title = metadata.get('htmlTitle', '').lower()
        display_link = metadata.get('displayLink', '').lower()
        
        for term in search_terms:
            if term in title:
                score += 2.0
            if term in html_title:
                score += 1.5
            if term in display_link:
                score += 1.0
        
        return score
    
    def _get_site_quality_score(self, display_link: str) -> float:
        """Get quality score for a website."""
        if not display_link:
            return 1.0
        
        domain = display_link.lower()
        
        # Check for exact matches
        for site_domain, score in self.site_quality_scores.items():
            if site_domain in domain:
                return score
        
        # Default scoring for unknown sites
        if any(tld in domain for tld in ['.edu', '.gov', '.org']):
            return 3.0
        elif any(tld in domain for tld in ['.com', '.net']):
            return 2.0
        else:
            return 1.0
    
    def remove_duplicates_and_rank(self, results: List[Tuple[str, Dict]]) -> List[Tuple[str, Dict]]:
        """Remove duplicates and rank results with preference for specific sites."""
        # Group by URL to handle duplicates
        url_groups = {}
        
        for image_path, metadata in results:
            url = metadata.get('link', '')
            if not url:
                continue
                
            if url not in url_groups:
                url_groups[url] = []
            url_groups[url].append((image_path, metadata))
        
        # Select best result for each URL and calculate final scores
        final_results = []
        
        for url, url_results in url_groups.items():
            # Select result with highest quality score
            best_result = max(url_results, key=lambda x: x[1].get('quality_score', 0))
            image_path, metadata = best_result
            
            # Calculate final comprehensive score with preferred site bonus
            base_score = (
                metadata.get('quality_score', 0) * 0.4 +
                metadata.get('relevance_score', 0) * 0.3 +
                metadata.get('site_quality_score', 0) * 0.3
            )
            
            # Extra bonus for preferred sites
            if metadata.get('is_preferred_site', False):
                base_score += 1.5  # Additional bonus for preferred sites
            
            # Frequency bonus (how many queries found this result)
            frequency_bonus = len(url_results) * 0.5
            final_score = base_score + frequency_bonus
            
            metadata['final_score'] = final_score
            metadata['query_frequency'] = len(url_results)
            metadata['all_source_queries'] = [r[1].get('source_query', '') for r in url_results]
            
            final_results.append((image_path, metadata))
        
        # Sort by final score (highest first), with preferred sites getting priority
        final_results.sort(key=lambda x: (x[1].get('is_preferred_site', False), x[1].get('final_score', 0)), reverse=True)
        
        return final_results[:15]  # Return top 15 results


async def select_best_copyright_image_from_search_results(search_query, image_data_list, claimant, title, basis_of_claim=None, nation_of_publication=None):
    
    # Optional additional context if provided
    basis_of_claim_prompt = f'*   **Basis of Claim:** "{basis_of_claim}"' if basis_of_claim else ""
    nation_of_publication_prompt = f'*   **Nation of First Publication:** "{nation_of_publication}"' if nation_of_publication else ""

    prompt_list = [("text", f"""You are a highly experienced copyright image analyst tasked with identifying the best match for a registered artwork from a set of Google Images search results. Your primary goal is to ensure that the match is accurate, preferring a "no match" or "low confidence" outcome over an incorrect positive match.

**Contextual Information:**

*   **Search Query:** "{search_query}" (This was the original search query used on Google Images, combining the Copyright Claimant and Title).
*   **Copyright Claimant:** "{claimant}"
*   **Title:** "{title}"
{basis_of_claim_prompt}
{nation_of_publication_prompt}

**Keyword Analysis:**

*   **Claimant Keywords:** (Analyze the `claimant` string and extract keywords. Consider removing common words like "Inc.", "LLC", "Ltd.", etc. Prioritize unique names or identifiers.)
*   **Title Keywords:** (Analyze the `title` string and extract keywords. Identify potentially unique or distinguishing words/phrases within the title. For example, in "GUANGDONG_ZHONGKANG_EMBROIDERY_TECHNOLOGY_COLTD_Pattern_800559-130", "ZHONGKANG" and "800559-130" are likely more important than "GUANGDONG", "EMBROIDERY", "TECHNOLOGY", "COLTD", or "Pattern".)
    *   **Title Specificity Assessment:** Assess the specificity of the title. Is it a descriptive title (e.g., "Sunset over Mountains") or a generic/identifier-based title (e.g., "POSTER 27", "Pattern 800559-130")? If the title is generic or primarily an identifier, note that this reduces the reliability of the title for matching purposes. *Specifically, consider if the title includes both descriptive elements AND identifier codes/numbers. The presence of an descriptive elements alone does not guarantee a match unless that identifier is also present in the search result's title or image content.*
*Crucially, analyze the Title, HTML Title, Link, and Display Link attached to each searched image. Compare these elements with the claimant and title of the registered artwork to identify clues and assess the probability of a match.*

**IMPORTANT ARTWORK PRIORITIZATION:**
When evaluating images of CD covers, tape covers, album covers, or similar products:
- **PRIORITIZE covers that display artwork, illustrations, or artistic designs** that match or relate to the registered title and claimant
- **DEPRIORITIZE plain or simple covers** that only show text, basic designs, or generic layouts without meaningful artwork
- Look for covers where the artwork itself appears to be the copyrighted material being searched for
- Consider that legitimate copyrighted artwork often appears on album/CD/tape covers as the primary visual element

**Size Requirements:**
Only consider images that are at least 100x100 pixels. Images smaller than this should receive significantly reduced scores or be excluded from top matches.

**Image Analysis Task:**

Verify the key textual elements (identifier code, claimant name, etc.) match exactly with the registered details; assess whether the visual style and content are congruent with the registered artwork; assign high confidence only if both textual and visual analyses achieve ≥90% match; if any primary element is missing or conflicting, reduce confidence significantly or mark as "no match." Additionally, for identifier-only titles, if the identifier does not appear in the title or image content, mark the result as low probability.
*The registered artwork should be a single, distinct, and standalone piece. It must not consist of multiple artworks combined, collaged, or presented together in a polished manner, such as in a website layout or promotional material. The AI should lower the probability of generating or identifying combined artworks as a single copyrighted work. The artwork should be unique in composition, style, and execution, with clear, individual artistic intent and authorship.

Image Categories:

1. Clear Artwork: A clean, unobstructed depiction of the artwork itself.
2. Artwork on Product: The artwork applied to a product (book cover, merchandise, CD/tape cover with meaningful artwork, etc.).
3. Artwork in Context (Non-Product): Artwork in a setting, but not for sale.
4. Video/Screenshot/Frame: A still frame from a video.
5. Other (Rare/Unclear): Doesn't fit into the above categories.

**Assumptions and Considerations:**

*   **Language and Discoverability:** If the claimant name appears to be Chinese (based on common Chinese characters or naming conventions), there's a higher likelihood that the registered artwork may be less easily discoverable or verifiable through a standard Google Images search. This is due to potential differences in online presence and indexing between Western and Chinese platforms. Therefore, exercise increased caution when evaluating matches for potentially Chinese-named claimants. *However*, if the information extracted from an image (e.g., text on the image, accompanying descriptions) shows a very high degree of overlap with the provided copyright details (claimant, title, keywords), this can override the initial assumption of lower discoverability.* Give higher weight to images with strong textual or contextual matches in such cases.
*   **Title Specificity and Match Confidence:** If the 'Title Specificity Assessment' determined the title to be generic or identifier-based, significantly reduce confidence (probability) assigned to matches; even with descriptive elements, an unmatched identifier code/number (not in the Google Images result title or image content) reduces probability; assign high confidence only if: (a) the title is highly descriptive AND the image visually matches; (b) the title has an identifier code/number AND that *exact* code/number is visible in the search result (title text or image); (c) strong corroborating evidence *beyond the title* exists (e.g., claimant's name on image, visual elements matching 'Basis of Claim').
* **Multiple Artworks by Same Claimant:** Be aware that a single claimant may register multiple artworks. A generic or identifier-based title, without further corroborating evidence, is insufficient to establish a match, as it could apply to any of the claimant's registered works.
""")]
    
    image_url = [""]

    valid_image_data_list = []
    for idx, image_data in enumerate(image_data_list, start=1):
        image_path = image_data[0]
        metadata = image_data[1]

        # Check if the image file exists and is a valid file
        if not os.path.isfile(image_path):
            print(f"[ERROR] Image file not found: {image_path}")
            continue  # Skip to the next image
        
        if os.path.getsize(image_path) == 0:
            print(f"[ERROR] Image file is empty: {image_path}")
            continue  # Skip to the next image
        
        # Check image size requirements (assuming you have this function)
        try:
            meets_size_req, width, height = check_image_size(image_path)
            if meets_size_req:
                # Add size info to metadata
                metadata['image_width'] = width
                metadata['image_height'] = height
                metadata['meets_size_requirement'] = True
                valid_image_data_list.append(image_data)
            else:
                print(f"[INFO] Image too small ({width}x{height}): {image_path}")
                # Still add to list but mark as not meeting size requirement
                metadata['image_width'] = width
                metadata['image_height'] = height
                metadata['meets_size_requirement'] = False
                valid_image_data_list.append(image_data)  # Keep for analysis but will get lower scores
        except:
            # If size check fails, still include but without size info
            metadata['meets_size_requirement'] = 'unknown'
            valid_image_data_list.append(image_data)

    for idx, image_data in enumerate(valid_image_data_list, start=1):
        image_path = image_data[0]
        metadata = image_data[1]

        size_info = ""
        if 'image_width' in metadata and 'image_height' in metadata:
            size_info = f"\nImage Size: {metadata['image_width']}x{metadata['image_height']} pixels"
            if not metadata.get('meets_size_requirement', True):
                size_info += " (Below 100x100 minimum - reduce score significantly)"

        prompt_list.append(("text", f"\n\nResult {idx}:\nTitle: {metadata.get('title', '')}\nHTML Title: {metadata.get('htmlTitle', '')}\nLink: {metadata.get('link', '')}\nDisplay Link: {metadata.get('displayLink', '')}{size_info}\n"))
        image_url.append("")
        prompt_list.append(("image_path", image_path))
        image_url.append(metadata.get('link', ''))

    prompt_list.append(("text", (
       "Please analyze these results, considering the contextual information and keyword analysis provided above. Pay special attention to prioritizing CD/tape/album covers that show meaningful artwork over plain covers with just text. Consider image size requirements in your scoring. Identify the 3 images that best match the registered artwork, *and* categorize each image according to the categories above.\n"
        'Return your answer as follow: {"Result ***": {"match": "0.xx", "category": "Y"}, "Result ***": {"match": "0.xx", "category": "Y"}, "Result ***": {"match": "0.xx", "category": "Y"}} where 0.xx is your assessment of the probability that the picture is the registered artwork (0.00 to 1.00), and Y is the category index (1-5). Higher probabilities indicate a stronger match. Images below 100x100 pixels should receive significantly reduced scores.\n'
        "Separately, also provide the category of the image you ranked as the best match, in the format: 'Best Match Category: X', where X is the category name.\n"
        "Finally, provide a concise summary (one paragraph) of your analysis and reasoning for selecting the best matches, starting with the phrase 'Explanation Summary: ', and specifically addressing: the reasoning for the best match choice, overall confidence levels (probabilities) of the top matches (e.g., high, low, or mixed), whether artwork-rich covers were prioritized over plain covers, and, if applicable, how a potentially Chinese claimant name influenced the analysis (increased caution or strong evidence to the contrary)."
    )))

    try:
        # Call the asynchronous Vertex GenAI function.
        ai_answer = await vertex_genai_multi_async(prompt_list)
        print(ai_answer)  # Print the raw AI response for debugging

        # --- Extract and process the main JSON result ---
        json_result = get_json(ai_answer)
        if isinstance(json_result, list): # !! we are expecting a dictionary tough 
            json_result.sort(key=lambda x: float(x.get("match", 0.0)), reverse=True)
            top_results = json_result[:3]
        elif isinstance(json_result, dict):
            # Sort by score
            sorted_items = sorted(json_result.items(),
                                  key=lambda item: float(item[1].get("match", 0.0)) if isinstance(item[1], dict) else 0.0,
                                  reverse=True)
            top_results = dict(sorted_items[:3])
        else:
            print("[DEBUG] Parsed JSON is not a list or a dictionary.")
            top_results = {}  # Initialize as empty dict

        # --- Extract Best Match Category (Separate from JSON) ---
        best_match_category = "Unknown"  # Default value
        lines = ai_answer.split('\n')
        for line in lines:
            if line.startswith("Best Match Category:"):
                best_match_category = line.replace("Best Match Category:", "").strip()
                break  # Stop after finding the first match

        # Extract the Explanation Summary
        explanation_summary = "Not found"  # Default value
        for line in ai_answer.split('\n'):
            if line.startswith("Explanation Summary:"):
                explanation_summary = line.replace("Explanation Summary:", "").strip()
                break  # Stop after finding the first match

        print(f"Explanation Summary: {explanation_summary}") # added print
        return top_results, best_match_category, explanation_summary # Return all three

    except Exception as e:
        print(f"Error in select_best_copyright_image_from_search_results: {str(e)}")
        return {}, "Unknown", "Not found"  # Return empty dict and "Unknown" on error
    


def display_image_and_ask_user_server(image_path: str, score: float, category: str, explanation: str, 
                                    claimant: str, title: str, image_url: str, size_info: str) -> bool:
    """
    Display image information and ask user if they want to save it to the best folder.
    Server-compatible version with multiple display options.
    
    Args:
        image_path (str): Path to the local image file
        score (float): Match confidence score
        category (str): Image category
        explanation (str): AI explanation of the match
        claimant (str): Copyright claimant name
        title (str): Artwork title
        image_url (str): Original image URL
        size_info (str): Image dimensions
    
    Returns:
        bool: True if user wants to save, False otherwise
    """
    try:
        print(f"\n{'='*80}")
        print(f" IMAGE REVIEW FOR BEST FOLDER")
        print(f"{'='*80}")
        print(f" Claimant: {claimant}")
        print(f" Title: {title}")
        print(f" Match Score: {score:.3f}")
        print(f" Category: {category}")
        print(f" Size: {size_info} pixels")
        print(f"Source URL: {image_url}")
        print(f" AI Explanation: {explanation}")
        print(f" Local Path: {image_path}")
        print(f"{'='*80}")
        
        # Try different methods to display/provide image access
        # Method 1: Generate base64 for potential web display
        try_base64_display(image_path)
        
        # Always provide file path for manual viewing
        print(f"\n Manual viewing: You can view the image at: {image_path}")
        print(f" Or view online at: {image_url}")
        
        # Ask user for decision
        return get_user_decision(claimant, title, score, category, size_info, image_url, explanation)
                
    except KeyboardInterrupt:
        print(f"\n🚪 Processing interrupted by user")
        raise
    except Exception as e:
        print(f" Error in display_image_and_ask_user_server: {e}")
        return get_fallback_user_decision(score)


def try_terminal_image_display(image_path: str) -> bool:
    """
    Try to display image in terminal using various methods.
    Returns True if successful, False otherwise.
    """
    # Method 1: Try imgcat (iTerm2)
    try:
        result = subprocess.run(['which', 'imgcat'], capture_output=True, text=True)
        if result.returncode == 0:
            subprocess.run(['imgcat', image_path], check=True)
            print("🖼️ Image displayed using imgcat")
            return True
    except:
        pass
    
    # Method 2: Try catimg
    try:
        result = subprocess.run(['which', 'catimg'], capture_output=True, text=True)
        if result.returncode == 0:
            subprocess.run(['catimg', image_path], check=True)
            print("  Image displayed using catimg")
            return True
    except:
        pass
    
    # Method 3: Try viu (rust-based image viewer)
    try:
        result = subprocess.run(['which', 'viu'], capture_output=True, text=True)
        if result.returncode == 0:
            subprocess.run(['viu', image_path], check=True)
            print("  Image displayed using viu")
            return True
    except:
        pass
    
    # Method 4: Try timg
    try:
        result = subprocess.run(['which', 'timg'], capture_output=True, text=True)
        if result.returncode == 0:
            subprocess.run(['timg', image_path], check=True)
            print("  Image displayed using timg")
            return True
    except:
        pass
    
    return False


def try_base64_display(image_path: str):
    """
    Generate base64 representation for container/web display.
    """
    try:
        with open(image_path, 'rb') as img_file:
            img_data = img_file.read()
            base64_data = base64.b64encode(img_data).decode('utf-8')
            
            # Get image format
            img_format = image_path.split('.')[-1].lower()
            if img_format == 'jpg':
                img_format = 'jpeg'
            
            print(f"\n🔗 Direct image URL: {image_url if 'image_url' in locals() else 'N/A'}")
            print(f"📱 To view image, copy this data URL to browser:")
            print(f"data:image/{img_format};base64,{base64_data}")
            
    except Exception as e:
        print(f" Could not generate base64: {e}")


def try_ascii_art_display(image_path: str):
    """
    Display ASCII art representation of the image.
    """
    try:
        from PIL import Image
        
        # Open and resize image
        img = Image.open(image_path)
        img = img.convert('L')  # Convert to grayscale
        
        # Resize to fit terminal (max 80 chars wide, maintain aspect ratio)
        width, height = img.size
        aspect_ratio = height / width
        new_width = min(80, width)
        new_height = int(new_width * aspect_ratio * 0.5)  # 0.5 to account for character height
        
        img = img.resize((new_width, new_height))
        
        # Convert to ASCII
        ascii_chars = '@%#*+=-:. '
        ascii_art = []
        
        for y in range(new_height):
            line = ''
            for x in range(new_width):
                pixel = img.getpixel((x, y))
                char_index = pixel * (len(ascii_chars) - 1) // 255
                line += ascii_chars[char_index]
            ascii_art.append(line)
        
        print(f"\ ASCII Art Preview:")
        print("┌" + "─" * new_width + "┐")
        for line in ascii_art:
            print("│" + line + "│")
        print("└" + "─" * new_width + "┘")
        
    except Exception as e:
        print(f"  Could not generate ASCII art: {e}")


def create_web_viewer(image_path: str, port: int = 8000) -> str:
    """
    Create a simple web server to view the image.
    Returns the URL to access the image.
    """
    try:
        import http.server
        import socketserver
        import threading
        import webbrowser
        from urllib.parse import quote
        
        # Get the directory containing the image
        image_dir = os.path.dirname(os.path.abspath(image_path))
        image_filename = os.path.basename(image_path)
        
        # Change to image directory
        original_dir = os.getcwd()
        os.chdir(image_dir)
        
        # Create simple HTTP server
        handler = http.server.SimpleHTTPRequestHandler
        
        # Find available port
        for port_try in range(port, port + 100):
            try:
                httpd = socketserver.TCPServer(("", port_try), handler)
                break
            except OSError:
                continue
        else:
            raise Exception("No available ports found")
        
        # Start server in background thread
        server_thread = threading.Thread(target=httpd.serve_forever, daemon=True)
        server_thread.start()
        
        url = f"http://localhost:{port_try}/{quote(image_filename)}"
        
        print(f"\n Web viewer started at: {url}")
        print(f" Open this URL in your browser to view the image")
        print(f"  Server will stop when script ends")
        
        # Restore original directory
        os.chdir(original_dir)
        
        return url
        
    except Exception as e:
        print(f"  Could not start web viewer: {e}")
        return ""


def get_user_decision(claimant: str, title: str, score: float, category: str, 
                     size_info: str, image_url: str, explanation: str) -> bool:
    """
    Get user decision optimized for container environment.
    """
    while True:
        print(f"\n Do you want to save this image to the best folder?")
        print(f"   [y/Y] = Yes, save to best folder")
        print(f"   [n/N] = No, don't save")
        print(f"   [s/S] = Show image info again")
        print(f"   [u/U] = Show source URL")
        print(f"   [q/Q] = Quit processing")
        
        user_input = input("👉 Your choice: ").strip().lower()
        
        if user_input in ['y', 'yes']:
            print(f" User confirmed: Image will be saved to best folder")
            return True
        elif user_input in ['n', 'no']:
            print(f"User declined: Image will NOT be saved to best folder")
            return False
        elif user_input in ['s', 'show']:
            # Show info again
            print(f"\n Claimant: {claimant}")
            print(f" Title: {title}")
            print(f" Match Score: {score:.3f}")
            print(f" Category: {category}")
            print(f" Size: {size_info} pixels")
            print(f" Source URL: {image_url}")
            print(f" AI Explanation: {explanation}")
            continue
        elif user_input in ['u', 'url']:
            print(f" Source URL: {image_url}")
            continue
        elif user_input in ['q', 'quit']:
            print(f" User requested to quit processing")
            raise KeyboardInterrupt("User requested to quit")
        else:
            print(f" Invalid input. Please enter 'y', 'n', 's', 'u', or 'q'")
            continue


def get_fallback_user_decision(score: float) -> bool:
    """
    Fallback decision method when image display fails.
    """
    while True:
        user_input = input(f"\n Save image (score: {score:.3f}) to best folder? [y/n]: ").strip().lower()
        if user_input in ['y', 'yes']:
            return True
        elif user_input in ['n', 'no']:
            return False
        else:
            print(" Please enter 'y' or 'n'")


# Installation instructions for terminal image viewers
def print_installation_instructions():
    """
    Print installation instructions for various terminal image viewers.
    """
    print("""
Terminal Image Viewer Installation Instructions:

imgcat (iTerm2 on macOS):
   curl -L https://iterm2.com/utilities/imgcat > /usr/local/bin/imgcat && chmod +x /usr/local/bin/imgcat

catimg (Linux/macOS):
   # Ubuntu/Debian: sudo apt-get install catimg
   # macOS: brew install catimg
   # From source: git clone https://github.com/posva/catimg.git && cd catimg && make && sudo make install

viu (Rust-based, cross-platform):
   cargo install viu

timg (Linux):
   # Ubuntu/Debian: sudo apt-get install timg
   # From source: https://github.com/hzeller/timg

Or use the web viewer option [w] when prompted!
    """)
