import os
import sys
sys.path.append(os.getcwd())
import asyncio
import pandas as pd
import fitz  # PyMuPDF

# Import necessary tools from your project
from Common.Constants import sanitize_name
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
from AI.GC_VertexAI import vertex_genai_multi_async
from Common.Constants import nas_case_folder
from FileManagement.NAS import NASConnection
from DatabaseManagement.ImportExport import get_table_from_GZ
from AI.LLM_shared import get_json, get_json_list
import Common.Constants as Constants
from Common.Constants import local_case_folder


# Define rubish keywords (case insensitive) for excluding PDFs.
rubish_keywords = [
    "heavy recruitment of chinese",
    "counterfeit and pirated goods",
    "office of strategy",
    "counterfeiting in the age of the internet",
    "accreditation",
    "department of commerce",
    "white paper",
    "chamber of commerce",
    "office of trade",
    "border protection",
    "hague convention",
    "civil procedure",
    "convention on the service",
    "seizure statistics",
    "notorious markets",
    " inta ",
    "traffic report",
    "silk road",
    "state of the",
    "briefing papers"
]

# LLM prompt to request the pages with intellectual property images.
prompt_text = (
    'I am looking at court documents for the Intellectual Property (IP) (either trademark, copyright, or patent) of the plaintiff. '
    'You will me gather all kind of information about the IP (Intellectual Property) present in the document. '
    'If you find information about the IP, you should return: {"is_ip_present": "yes", "ip_format": "full_page or table or number", "ip_type": "trademark or copyright or patent", "registration_number": ["US D1,145,748", "VA0002474847", "2,748,748"], "industry_report": "title or titles of the industry reports present in the documents"} where:\n '
    '- "full_page" means that there are images of the IP on a dedicated page of the document. The entire page is covered in IP images.\n '
    '- "table" means that there are multiple images of the IP structrued as a table on the page. \n '
    '- "number" means that you cannot find any images of the IP but you can see the IP number (patent number, trademark number, or copyright number) on the page. \n '
    '- "registration_number" is a list of all the patent numbers (always starts with US D then numbers, or US then numbers), trademark number (just numbers), or copyright number (usually has this format VA x,xxx,xxx or VA000x,xxx,xxx) that you can find in the document. This is especially important for when ip_format is "number". \n '
    'If you cannot find any evidence of the IP, you should return {"is_ip_present": "no", "possible_reason": "....", "industry_report": "title or titles of the industry reports present in the documents"} where "possible_reason" is a string explaining why you think the IP is not present (one of the possible reasons is that the document is marked as sealed, but feel free to add other reasons). '
    'In very rare cases there migh be multiple ip_format or ip_type in the document. In this case you list them all in the relevant field.'
    'Be sure to disregard any information that is part of an industry report. Some lawyers like to include industry reports in their pleadings but these do not include any information specific to the case. For industry reports you should only return the title of the industry reports as part of the industry_report field.'
    '\nHere are the court document: \n\n'
)


def find_case_no_image():
    df = df[(df["images"] == {'trademarks': {}, 'patents': {}, 'copyrights': {}}) & (df["cause"].notna())]
    print(f"There are {len(df)} cases without images.")
    return df


def explain_case_no_image(cases_df):
    # open the csv file and check the last 100 cases
    df_excel = pd.read_csv("ip_picture_results_new.csv")
    df_excel["case_id"] = pd.to_numeric(df_excel["case_id"], errors='coerce')
    df_excel = df_excel.dropna(subset=["case_id"])
    df_excel.loc[:, "case_id"] = df_excel["case_id"].astype(int)
    df = df[~df["id"].isin(df_excel["case_id"])]
    print(f"There are {len(df)} cases to process.")
    
    local_temp_root = os.path.join(local_case_folder)
    if not os.path.exists(local_temp_root):
        os.makedirs(local_temp_root, exist_ok=True)

    llm_semaphore = asyncio.Semaphore(30)  # Semaphore for LLM calls
    # process_case_semaphore = asyncio.Semaphore()  # Semaphore for process_case calls

    result_queue = asyncio.Queue() #Asycn queue for results.

    all_tasks = []

    async def process_case(i, index, row, nas, result_queue, llm_semaphore):
        try:
            date_str = row["date_filed"].strftime("%Y-%m-%d")
        except Exception:
            date_str = str(row["date_filed"])
        case_name = sanitize_name(f"{date_str} - {row['docket']}")
        remote_case_folder = nas_case_folder + "/" + case_name
        local_case_folder = os.path.join(local_temp_root, case_name)

        print(f"⚠️ {i+1}/{len(cases_df)} with index {index}: Processing case {row.get('id')}: {case_name}")
        if os.path.exists(local_case_folder):
            print(f"Local folder {local_case_folder} already exists. Skipping download.")
        else:
            found_in_NAS = nas.ssh_nas_to_local(remote_case_folder, local_case_folder)  # Still async
            if not found_in_NAS:
                print(f"Failed to download case folder for {case_name}")
                await result_queue.put({
                    "case_id": row.get("id"),
                    "case_docket": row.get("docket"),
                    "date_filed": date_str,
                    "is_ip_present": "Folder not found on NAS: ",
                    "ip_format": case_name,
                    "ip_type": "",
                    "registration_number": "",
                    "possible_reason": ""
                })
                return

        folder_1 = os.path.join(local_case_folder, "1")
        if not os.path.exists(folder_1):
            print(f"No '1' folder found in case folder: {local_case_folder}")
            await result_queue.put({
                "case_id": row.get("id"),
                "case_docket": row.get("docket"),
                "date_filed": date_str,
                "is_ip_present": "No '1' folder found in case folder: ",
                "ip_format": case_name,
                "ip_type": "",
                "registration_number": "",
                "possible_reason": ""
            })
            return

        pdf_files = [f for f in os.listdir(folder_1) if f.lower().endswith(".pdf")]
        if not pdf_files:
            print(f"No PDF files found in folder: {folder_1}")
            await result_queue.put({
                "case_id": row.get("id"),
                "case_docket": row.get("docket"),
                "date_filed": date_str,
                "is_ip_present": "No PDF files found in folder 1 of case folder:",
                "ip_format": case_name,
                "ip_type": "",
                "registration_number": "",
                "possible_reason": ""
            })
            return

        # Create LLM tasks and add them to the GLOBAL list.
        data_list = [("text", prompt_text)]
        for pdf_file in pdf_files:
            pdf_path = os.path.join(folder_1, pdf_file)

            if "schedule_a" in pdf_file.lower():
                print(f"Skipping Schedule_A PDF: {pdf_path}")
                continue

            # Exclude Rubish Report PDFs
            if "complaint" not in pdf_file.lower() and "exhibit_1" not in pdf_file.lower():
                try:
                    doc = fitz.open(pdf_path)
                except Exception as e:
                    print(f"Failed to open PDF {pdf_path}: {e}")
                    continue
                pages_to_check = list(range(min(4, doc.page_count)))
                try:
                    # Extract OCR text from the first few pages.
                    full_text, _, _, _ = OCRProcessor.process_pdf(doc, pages_to_check)
                except Exception as e:
                    print(f"Error during OCR of {pdf_path}: {e}")
                    full_text = ""
                # Exclude this PDF if any rubish keyword is found.
                if any(keyword in full_text.lower() for keyword in rubish_keywords):
                    print(f"Skipping rubish report PDF: {pdf_path}")
                    continue

            # Prepare the data list for the Vertex LLM call.
            data_list.append(("pdf_path", pdf_path))

        # Put the LLM task data onto the thread-safe queue
        await asyncio.sleep(0) # Yeild for the writer task to finish.
        all_tasks.append(asyncio.create_task(llm_task(i, index, data_list, row, date_str, result_queue, llm_semaphore)))
        print(f"🔥 LLM for case {row.get('docket')} queued!\n")
        await asyncio.sleep(0) # Yeild for the writer task to finish.


    async def llm_task(i, index, data_list, row, date_str, result_queue, llm_semaphore):
        async with llm_semaphore:  # Limit concurrent LLM calls
            try:
                ip_response = await vertex_genai_multi_async(data_list, model_name=Constants.SMART_MODEL_FREE)
            except Exception as e:
                print(f"Vertex AI call failed for {row.get('docket')}: {e}")
                return
            ip_response_json_list = get_json_list(ip_response)
            if not ip_response_json_list:
                ip_response_json_list = [get_json(ip_response)] if get_json(ip_response) else []

            print(f"✅ {i+1}/{len(cases_df)} with index {index}: LLM response for case {row.get('docket')}: {ip_response_json_list}")
            if ip_response_json_list:  # Check if ip_response_json_list is not None and handle accordingly
                try:
                    # Initialize an empty dictionary to store concatenated values
                    result_entry = {
                        "case_id": row.get("id"),
                        "case_docket": row.get("docket"),
                        "date_filed": date_str,
                    }

                    # Concatenate values from all dictionaries in the list
                    for key in ["is_ip_present", "ip_format", "ip_type", "registration_number", "possible_reason", "industry_report"]:
                        result_entry[key] = ", ".join([str(d.get(key, "")) for d in ip_response_json_list if d.get(key) is not None])
                    await asyncio.sleep(0)
                    await result_queue.put(result_entry)  # Put result on async queue
                    await asyncio.sleep(0)
                except Exception as e:
                    print(f"Error processing {row.get('docket')} result: {e}")


    async def run_all_cases():
        async def writer_task(result_queue):
            output_csv = "ip_picture_results_new.csv"
            first_write = True
            while True:
                try:
                    # Use wait_for to prevent deadlock
                    result_entry = await asyncio.wait_for(result_queue.get(), timeout=1.0)
                    if result_entry is None:
                        print("✅✅✅ All tasks are done. Exiting writer.")
                        break  # Exit if all tasks are done
                    print(f"✅Result entry: {result_entry}")
                    result_df = pd.DataFrame([result_entry])

                    if first_write:
                        result_df.to_csv(output_csv, mode='a', header=True, index=False)  # Write with header
                        first_write = False
                    else:
                        result_df.to_csv(output_csv, mode='a', header=False, index=False) # Append without header

                    print(f"Result for {result_entry['case_docket']} saved to {output_csv}")
                    result_queue.task_done()
                except asyncio.TimeoutError:
                    await asyncio.sleep(0) # Yeild for the LLM thread to finish.

        with NASConnection() as nas:
            writer = asyncio.create_task(writer_task(result_queue))

            process_case_tasks = []

            # Create process_case tasks
            for i, (index, row) in enumerate(cases_df.iterrows()):
                process_case_tasks.append(asyncio.create_task(process_case(i, index, row, nas, result_queue, llm_semaphore)))
                await asyncio.sleep(1)  # give an opportunity for the LLM task to get in the queue

            await asyncio.gather(*process_case_tasks)
            await asyncio.gather(*all_tasks)
            await result_queue.put(None)  # Sentinel value
            await writer

    asyncio.run(run_all_cases())


if __name__ == "__main__":
    df = get_table_from_GZ("tb_case", force_refresh=False)
    df_no_images = find_case_no_image(df)
    explain_case_no_image(df_no_images)
