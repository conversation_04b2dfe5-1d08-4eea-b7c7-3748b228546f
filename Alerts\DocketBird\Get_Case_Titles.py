import pandas as pd
import os
import time
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pyarrow.feather as feather
from DatabaseManagement.ImportExport import get_table_from_GZ
from Alerts.DocketBird.Login import docketbird_get_logged_in_browser
from Alerts.DocketBird.Search_Cases import build_docketbird_url
from logdata import log_message


def get_case_titles_from_docketbird():
    """
    Function to:
    1. Attempt to load partial progress from 'case_titles_in_progress.feather'.
    2. If not found, get the tb_case dataframe from the database and filter for plaintiff_id = 9.
    3. Login to DocketBird.
    4. For each case without a 'new_title':
       - Go to the DocketBird URL page
       - Capture the field <h1 id="case_name">.....</h1>
       - Add this to "new_title" field in the dataframe
       - Save the updated dataframe to 'case_titles_in_progress.feather'
    5. Save the final dataframe as an Excel file and delete the feather file.
    """
    start_time = time.time()
    log_message("Starting process to get case titles from DocketBird...")
    feather_filename = "case_titles_in_progress.feather"
    feather_path = os.path.join(os.getcwd(), feather_filename)

    # Step 1 & 2: Load data from feather file or database
    if os.path.exists(feather_path):
        log_message(f"Loading existing data from {feather_filename}...")
        filtered_df = feather.read_feather(feather_path)
        log_message(f"Resuming processing for {len(filtered_df[filtered_df['new_title'].isnull()])} remaining cases.")
    else:
        log_message("Getting data from database...")
        # df = get_table_from_GZ("tb_case", force_refresh=False) # Commented out original DB load
        # Step 2: Filter to only include cases with plaintiff_id = 9
        # filtered_df = df[df['plaintiff_id'] == 9].copy() # Commented out original filter
        # Replaced above lines with direct loading/filtering for clarity if starting fresh
        df_full = get_table_from_GZ("tb_case", force_refresh=False)
        filtered_df = df_full[df_full['plaintiff_id'] == 9].copy()
        log_message(f"Found {len(filtered_df)} cases with plaintiff_id = 9")

        if len(filtered_df) == 0:
            log_message("No cases found with plaintiff_id = 9. Exiting.")
            return

        # Initialize the new column before first save
        filtered_df['new_title'] = None
        log_message(f"Saving initial data to {feather_filename}...")
        feather.write_feather(filtered_df, feather_path)

    # Check again if dataframe is empty after potential loading
    if len(filtered_df) == 0:
        log_message("Dataframe is empty. Exiting.")
        return

    # Ensure 'new_title' column exists if loaded from feather
    if 'new_title' not in filtered_df.columns:
        filtered_df['new_title'] = None
        log_message("Added missing 'new_title' column to loaded data.")


    # Step 3: Login to DocketBird
    log_message("Logging into DocketBird...")
    driver = docketbird_get_logged_in_browser()

    # Step 4: Process each case
    success_count = 0
    failed_count = 0
    processed_count = 0 # Keep track of loops for logging

    total_to_process = len(filtered_df[filtered_df['new_title'].isnull()])
    log_message(f"Starting processing loop for {total_to_process} cases...")

    for idx, row in filtered_df.iterrows():
        processed_count += 1
        # Skip if title already exists (from previous run)
        if pd.notna(row['new_title']):
            continue

        log_message(f"Processing case {processed_count}/{len(filtered_df)} (Remaining: {total_to_process - success_count - failed_count}): {row['docket']} in {row['court']}")

        try:
            # Build and navigate to the DocketBird URL
            url = build_docketbird_url(row['court'], row['docket'])

            if not url:
                log_message(f"  Failed to build URL for case {row['docket']} in {row['court']}")
                failed_count += 1
                continue

            # Navigate to the case page
            driver.get(url)

            # Wait for the case name element to be present (timeout after 10 seconds)
            try:
                case_name_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.ID, "case_name"))
                )

                # Extract the case name
                case_title = case_name_element.text.strip()

                # Update the dataframe with the new title
                filtered_df.at[idx, 'new_title'] = case_title

                log_message(f"  Retrieved title: {case_title}")
                success_count += 1

                # Save progress to feather file
                try:
                    feather.write_feather(filtered_df, feather_path)
                    log_message(f"  Progress saved to {feather_filename}")
                except Exception as fe:
                    log_message(f"  Error saving progress to feather file: {str(fe)}")


                # Short delay to avoid overloading the server
                time.sleep(2)

            except Exception as e:
                log_message(f"  Error retrieving case title: {str(e)}")
                failed_count += 1

        except Exception as e:
            log_message(f"  Error processing case: {str(e)}")
            failed_count += 1

    # Step 5: Save the final updated dataframe to Excel
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"case_titles_{timestamp}.xlsx"
    excel_path = os.path.join(os.getcwd(), excel_filename)

    try:
        filtered_df.to_excel(excel_path, index=False)
        log_message(f"Final results saved to {excel_path}")

        # Clean up the intermediate feather file
        if os.path.exists(feather_path):
            try:
                os.remove(feather_path)
                log_message(f"Removed intermediate file: {feather_filename}")
            except OSError as oe:
                log_message(f"Error removing intermediate file {feather_filename}: {str(oe)}")

    except Exception as ex:
        log_message(f"Error saving final Excel file: {str(ex)}")
        log_message(f"Intermediate progress remains in {feather_filename}")


    # Close the browser
    driver.quit()

    # Log completion
    elapsed_time = time.time() - start_time
    log_message(f"Process completed in {elapsed_time:.2f} seconds")
    log_message(f"Successfully retrieved {success_count} new case titles this run.")
    # Calculate total successful based on non-null entries
    total_success = filtered_df['new_title'].notna().sum()
    log_message(f"Total successfully retrieved titles in dataframe: {total_success}")
    log_message(f"Failed attempts this run: {failed_count}")


    return filtered_df


if __name__ == "__main__":
    df_result = get_case_titles_from_docketbird()
    # Keep the main execution part simple, saving handled within the function
    # if df_result is not None:
    #     df_result.to_excel("case_titles_final_main.xlsx", index=False) # Example if needed outside