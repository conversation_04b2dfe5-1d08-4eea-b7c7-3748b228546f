#!/bin/bash
### This is the start file for the Docker container

# Save environment variables for SSH sessions
echo "#!/bin/bash" > /etc/profile.d/docker_env.sh
env | grep -v "PATH" | grep -v "PWD" | grep -v "HOME" | grep -v "HOSTNAME" | sed 's/^\([^=]*\)=\(.*\)/export \1="\2"/' >> /etc/profile.d/docker_env.sh
echo 'export PYTHONPATH="/app"' >> /etc/profile.d/docker_env.sh
chmod +x /etc/profile.d/docker_env.sh

# Start Redis server
redis-server --daemonize yes

# Set strict permissions for the private key for SSH sessions
chmod 600 /app/.ssh/id_rsa

# Start SSH server
/usr/sbin/sshd

start_tmux_once () {
  local session=$1; shift
  if ! tmux has-session -t "$session" 2>/dev/null; then
      echo "Launching $session …"
      tmux new-session -d -s "$session" "$@"
  else
      echo "$session already running."
  fi
}

# Start the embedding service in TMUX, you can see the logs by attaching to the session, but NOT in graphana. We use -u to make sure the output is unbuffered and we can see logs in real time.
start_tmux_once app_embeddings python -u app_embeddings.py
# Start the ARQ worker as a background process
# Create a directory for logs if it doesn't exist
mkdir -p /app/logs

echo "Launching ARQ worker in the background..."
arq Check.ARQ.worker.WorkerSettings --watch Check &
echo "Launching ARQ PDF worker in the background..."
arq Check.ARQ.pdf_worker.PdfWorkerSettings &

# Prometheus metrics directory setup
export PROMETHEUS_MULTIPROC_DIR=/app/prometheus_metrics
mkdir -p $PROMETHEUS_MULTIPROC_DIR
rm -f $PROMETHEUS_MULTIPROC_DIR/*.db


# Start the main application with Hypercorn
echo "Starting Hypercorn..."
# We keep hypercorn running in the foreground to keep the container alive.
cd /app && hypercorn app_apistudio:app --workers 4 --bind 0.0.0.0:5000