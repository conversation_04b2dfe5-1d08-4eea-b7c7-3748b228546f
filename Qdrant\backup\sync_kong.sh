#!/usr/bin/env bash
set -Eeuo pipefail

# Requirements: 
# chmod +x this script
# Put cert in /root/.ssh/id_rsa
# chmod 600 /root/.ssh/id_rsa


# Main server (where we dump from)
MAIN_SSH_USER=root
MAIN_SSH_HOST=***************
MAIN_SSH_KEY=/root/.ssh/id_rsa
KONG_CONTAINER=kong-kong-1
REMOTE_DIR=/tmp/kong-sync
LOCAL_DIR=/root/qdrant/backup

# How decK should reach its own Admin API:
ADMIN_URL=http://127.0.0.1:8001


# Admin tokens (can be the same or different)
KONG_ADMIN_TOKEN=2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ

# --- required vars sanity ---
: "${MAIN_SSH_USER:?}"; : "${MAIN_SSH_HOST:?}"; : "${MAIN_SSH_KEY:?}"
: "${ADMIN_URL:?}"; : "${KONG_ADMIN_TOKEN:?}"

# --- paths & filenames ---
TS="$(date +%Y%m%d-%H%M%S)"
STATE_FILE="kong-${TS}.yaml"
mkdir -p "$LOCAL_DIR"

echo "[1/6] Exporting config on MAIN (inside container) ..."
ssh -i "$MAIN_SSH_KEY" -o BatchMode=yes -o StrictHostKeyChecking=accept-new \
  "$MAIN_SSH_USER@$MAIN_SSH_HOST" \
  "mkdir -p '$REMOTE_DIR' && \
   docker exec '$KONG_CONTAINER' kong config db_export /tmp/kong_export.yaml && \
   docker cp '$KONG_CONTAINER':/tmp/kong_export.yaml '$REMOTE_DIR'/$STATE_FILE && \
   docker exec '$KONG_CONTAINER' rm -f /tmp/kong_export.yaml"

echo "[2/6] Copying export from MAIN to BACKUP ..."
scp -i "$MAIN_SSH_KEY" -q "$MAIN_SSH_USER@$MAIN_SSH_HOST:$REMOTE_DIR/$STATE_FILE" "$LOCAL_DIR/$STATE_FILE"

echo "[3/6] Clear BACKUP of any data ..."
# Stop container, reset the database, restart the container
TMP_ENV="/tmp/kong-pg.env"
MIG_NET="$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.NetworkID}}{{end}}' "$KONG_CONTAINER")"
KONG_IMAGE="$(docker inspect -f '{{.Config.Image}}' "$KONG_CONTAINER")"
docker exec "$KONG_CONTAINER" env | grep '^KONG_PG_' > "$TMP_ENV"
docker stop "$KONG_CONTAINER"
docker run --rm --network "$MIG_NET" --env-file "$TMP_ENV" "$KONG_IMAGE" kong migrations reset -y
docker run --rm --network "$MIG_NET" --env-file "$TMP_ENV" "$KONG_IMAGE" kong migrations bootstrap
docker start "$KONG_CONTAINER"

# after copying export to BACKUP:
echo "[4/6] Rewriting hostnames in export …"

# File path
YAMLFILE="$LOCAL_DIR/$STATE_FILE"

# Use sed-in-place to replace old → new hostnames
# (Assumes no regex metacharacters in hostnames; escapes if needed)
sed -i \
  -e 's/api\.maidalv\.com/api2.maidalv.com/g' \
  -e 's/vectorstore1\.maidalv\.com/vectorstore2.maidalv.com/g' \
  "$YAMLFILE"


echo "[5/6] Import into BACKUP's DB..."
# copy into the backup container and import
docker cp "$LOCAL_DIR/$STATE_FILE" "$KONG_CONTAINER":/tmp/kong_import.yaml
docker exec "$KONG_CONTAINER" kong config db_import /tmp/kong_import.yaml

echo "[6/6] Reload BACKUP Kong to apply changes..."
docker exec "$KONG_CONTAINER" kong reload

# ln -sfn "$LOCAL_DIR/$STATE_FILE" "$LOCAL_DIR/kong-latest.yaml"
echo "Done. State: $LOCAL_DIR/$STATE_FILE"