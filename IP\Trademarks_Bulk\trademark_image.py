# IP/Trademarks/trademark_image.py

import os
import io
import asyncio
import aiohttp
# Removed: import logging
from PIL import Image
from pathlib import Path
from logdata import log_message # Added logdata import
from tqdm.asyncio import tqdm # Added tqdm import

# Removed logging configuration block
# logger = logging.getLogger(__name__) # Removed logger initialization

# Standard User-Agent to mimic a browser
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

async def download_and_save_image(
    session: aiohttp.ClientSession,
    serial_number: str,
    save_dir: str,
    final_dir: str,
    semaphore: asyncio.Semaphore, # Added semaphore parameter
    progress_bar=None,  # Added progress bar parameter
    max_retries=5,
    timeout=90
) -> tuple[bool, str]:
    """
    Downloads a trademark image from USPTO, saves it, and returns success status,
    respecting a semaphore for concurrency control.

    Args:
        session (aiohttp.ClientSession): The shared client session.
        serial_number (str): Serial number of the trademark.
        save_dir (str): Base directory to save the image.
        final_dir (str): Final directory for images.
        semaphore (asyncio.Semaphore): Semaphore to limit concurrent downloads.
        progress_bar (tqdm, optional): Progress bar to update.
        max_retries (int): Maximum number of retry attempts.
        timeout (int): Request timeout in seconds.

    Returns:
        tuple[bool, str]: (success_status, download_status) where:
            - success_status: True if image is available (either already present or newly downloaded), False otherwise
            - download_status: "already_present", "newly_downloaded", or "failed"
    """
    if not serial_number:
        log_message("Attempted to download image with empty serial number.", level='WARNING')
        return False, "failed"

    image_sub_dir = get_image_subdirectory(serial_number)
    if not image_sub_dir:
        log_message(f"Cannot determine directory structure for serial number: {serial_number}", level='ERROR')
        return False, "failed"

    final_image_path = os.path.join(final_dir, image_sub_dir, f"{serial_number}.webp")
    os.makedirs(os.path.dirname(final_image_path), exist_ok=True) # Ensure parent directories exist
    if os.path.exists(final_image_path):
        if progress_bar:
            progress_bar.update(1)
        return True, "already_present"

    image_url = f"https://tsdr.uspto.gov/img/{serial_number}/large"
    image_data = None
    errors = []

    async with semaphore: # Acquire semaphore before proceeding
        # log_message(f"Semaphore acquired for {serial_number}. Starting download attempt.", level='DEBUG')

        for attempt in range(max_retries):
            try:
                async with session.get(image_url, headers=HEADERS, timeout=timeout) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        if attempt != 0 and image_data:
                            print(f"✅ Downloaded {image_url} on attempt {attempt + 1}/{max_retries} following e: {errors}")
                            break # Exit retry loop on success
                        elif image_data:
                            # logger.debug(f"Successfully downloaded image data for {serial_number} from {image_url}")
                            break # Exit retry loop on success
                        elif not image_data:
                            print(f"⚠️ Downloaded (code 200) {image_url} but image_data is {image_data}, no point retrying.")
                            return False, "failed"
                    elif response.status == 403:
                        print(f"⚠️ Access Forbidden (403) for URL: {image_url}. Check User-Agent or API restrictions.")
                        # No retry on 403, likely a persistent issue
                        return False, "failed"
                    elif response.status == 404:
                        print(f"⚠️ Image not found (404) at URL: {image_url}")
                        # No retry on 404
                        return False, "failed"
                    else:
                        errors.append(response.status)
                        if attempt == max_retries - 1:
                            print(f"❌ Unexpected status code {response.status} for URL: {image_url} (Attempt {attempt + 1}/{max_retries})")
                            return False, "failed" # Failed after all retries

            except asyncio.TimeoutError:
                errors.append("Timeout")
                await asyncio.sleep(5)
                if attempt == max_retries - 1:
                    print(f"❌Timeout occurred on attempt {attempt + 1}/{max_retries} for URL {image_url}")
                    return False, "failed" # Failed after all retries

            except aiohttp.ClientError as e:
                errors.append(str(e))
                print(f"Client error downloading image for {serial_number} from {image_url} (Attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return False, "failed" # Failed after all retries

            except Exception as e:
                errors.append(str(e))
                print(f"Unexpected error downloading image for {serial_number} from {image_url} (Attempt {attempt + 1}/{max_retries}): {e}", exc_info=True)
                # Wait before retrying (exponential backoff) - still inside the semaphore lock
                if attempt < max_retries - 1:
                    await asyncio.sleep(30) # This is often error 500, Error: Server Error, The server encountered an error and could not complete your request. Please try again in 30 seconds.
                else:
                    return False, "failed" # Failed after all retries


    # --- End of semaphore block ---
    if not image_data: # This should never happen, because this case is captured above
        return False, "failed"

    try:
        # Determine directory structure using the helper function
        image_sub_dir = get_image_subdirectory(serial_number)
        if not image_sub_dir:
            log_message(f"Cannot determine directory structure for serial number: {serial_number} during save.", level='ERROR')
            if progress_bar:
                progress_bar.update(1)
            return False, "failed"

        # Create directory if it doesn't exist
        full_save_path = os.path.join(save_dir, image_sub_dir)
        os.makedirs(full_save_path, exist_ok=True)

        # Save image: the original is png, we convert it to webp. This leads to 4x smaller size and same visual.
        image_path = os.path.join(full_save_path, f"{serial_number}.webp")
        image = Image.open(io.BytesIO(image_data))
        # Ensure image is in RGB mode before saving as WEBP if it's RGBA or P
        if image.mode in ('RGBA', 'P'):
             image = image.convert('RGB')
        image.save(image_path, "WEBP", quality=85) # Added quality setting
        if progress_bar:
            progress_bar.update(1)
        return True, "newly_downloaded"

    except Exception as e:
        log_message(f"Error saving image for serial number {serial_number}: {str(e)}", level='ERROR', exc_info=True)
        if progress_bar:
            progress_bar.update(1)
        return False, "failed"

def get_image_subdirectory(ser_no: str) -> str:
    """
    Generates the image subdirectory path based on the serial number.
    Images are stored in xx/yy/ser_no.webp where:
    xx = last 2 digits of ser_no
    yy = 2 digits before the last 2 of ser_no (or "00" if ser_no is too short)

    Args:
        ser_no (str): The serial number of the trademark.

    Returns:
        str: The relative subdirectory path (e.g., "76/40"). Returns empty string if ser_no is invalid.
    """
    if not ser_no:
        return ""

    ser_no_str = str(ser_no)
    if len(ser_no_str) >= 4:
        xx = ser_no_str[-2:]  # Last 2 digits
        yy = ser_no_str[-4:-2]  # 2 digits before the last 2
    elif len(ser_no_str) > 0:
        # Handle short serial numbers (e.g., "123" -> "23/00")
        xx = ser_no_str.zfill(2)[-2:]  # Pad with zeros if needed, then take last 2
        yy = "00"
    else:
        return "" # Should not happen due to initial check

    return os.path.join(xx, yy)
