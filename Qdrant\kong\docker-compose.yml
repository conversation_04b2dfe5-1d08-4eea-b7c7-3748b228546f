networks:
  qdrant_monitoring:
    external: true
    name: qdrant_monitoring

services:
  # Redis for shared rate-limit counters across nodes
  redis:
    image: redis:7
    restart: unless-stopped
    command: ["redis-server", "--appendonly", "yes"]
    networks: [qdrant_monitoring]

  # kong-migrations runs only once when the stack goes up to migrate the database (e.g. if we upgraded Kong) 
  # Reuses existing 'postgresql' container
  # docker exec -it postgresql psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "CREATE USER kong WITH PASSWORD '${KONG_PG_PASSWORD}';"
  # docker exec -it postgresql psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "CREATE DATABASE kong OWNER kong;"
  kong-migrations:
    image: kong:3.9.1
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: postgresql
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: ${KONG_PG_PASSWORD}
    command: ["kong", "migrations", "bootstrap"]
    depends_on: [redis]
    networks: [qdrant_monitoring]
    restart: on-failure

  # Kong at the edge (HTTP/HTTPS), Admin/Manager internal-only (proxied below)
  kong:
    image: kong:3.9.1
    restart: unless-stopped
    depends_on:       
      kong-migrations:
        condition: service_completed_successfully  # Start only after bootstrap has succeeded
      redis:
        condition: service_started
    environment:
      # DB
      KONG_DATABASE: postgres
      KONG_PG_HOST: postgresql
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: ${KONG_PG_PASSWORD}

      # Edge listeners
      KONG_PROXY_LISTEN: 0.0.0.0:8003, 0.0.0.0:8443 ssl http2
      KONG_NGINX_HTTP_CLIENT_MAX_BODY_SIZE: 10m

      # Admin API & Manager config (Phase 1: 0.0.0.0 for everyone can access, Phase 2: 127.0.0.1 for local only, coming from Kong after it added the KONG_ADMIN_TOKEN)
      KONG_ADMIN_LISTEN: 0.0.0.0:8001, 0.0.0.0:8444 ssl
      KONG_ADMIN_GUI_LISTEN: 0.0.0.0:8002, 0.0.0.0:8445 ssl
      KONG_ADMIN_GUI_URL: https://kong2.maidalv.com
      KONG_ADMIN_GUI_API_URL: https://kong2.maidalv.com/km-admin
      KONG_ADMIN_TOKEN: ${KONG_ADMIN_TOKEN}   # Secure the API with Token

      # Metrics & plugins
      KONG_PLUGINS: bundled,prometheus
      KONG_STATUS_LISTEN: 0.0.0.0:8100
    ports:
      - "80:8003"
      - "443:8443"
      - "127.0.0.1:8001:8001"   # Admin API (only accessible from localhost, i.e. command line, but not the tunnel because the tunnel points to 443)
      - "127.0.0.1:8002:8002"   # Kong Manager (only accessible from localhost, i.e. the tunnel)      
      - "8100:8100"   # Prometheus scrape: http://<host>:8100/metrics
    networks: [qdrant_monitoring]


