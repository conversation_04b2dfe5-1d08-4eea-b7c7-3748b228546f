// Legal Opinion Form JavaScript
// Uses Alpine.js for reactive components and htmx for async operations

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Alpine.js data
    window.legalOpinionData = {
        // Form sections state
        sections: {
            matter: { completed: false, expanded: true },
            complaint: { completed: false, expanded: false },
            patent: { completed: false, expanded: false },
            product: { completed: false, expanded: false },
            clientIP: { completed: false, expanded: false },
            priorArt: { completed: false, expanded: false },
            functionality: { completed: false, expanded: false },
            declarations: { completed: false, expanded: false }
        },
        
        // Form data
        formData: {
            // Matter & contacts
            clientLegalName: '',
            businessStructure: '',
            contactPerson: { name: '', role: '', email: '', phone: '' },
            privilegeAcknowledged: false,
            
            // Marketplace complaint
            platform: '',
            asins: [],
            listingUrls: [],
            complaintStatus: '',
            caseNoticeId: '',
            deadline: '',
            deadlineTimezone: 'EST',
            
            // Patent details
            patentNumber: '',
            patentTitle: '',
            patentOwner: '',
            
            // Product details
            productName: '',
            productSku: '',
            productAsin: '',
            variants: [],
            commercializationTimeline: [],
            
            // Client IP
            clientPatents: [],
            clientUtilityPatents: [],
            
            // Prior art
            priorArt: [],
            
            // Functionality
            functionalFeatures: [],
            articleOfManufacture: '',
            
            // Declarations
            accuracyAttestation: false,
            manufacturerContact: false,
            priorArtUse: false
        },
        
        // File uploads
        files: {
            complaint: [],
            patenteeLetters: [],
            productImages: [],
            commercializationDocs: [],
            clientPatentDocs: [],
            priorArtFiles: [],
            functionalityDocs: []
        },
        
        // Validation state
        validation: {},
        
        // UI state
        showTooltip: null,
        autosaveStatus: 'saved', // 'saving', 'saved', 'error'
        
        // Methods
        toggleSection(sectionKey) {
            this.sections[sectionKey].expanded = !this.sections[sectionKey].expanded;
        },
        
        markSectionCompleted(sectionKey) {
            this.sections[sectionKey].completed = true;
            // Auto-expand next section
            const sectionKeys = Object.keys(this.sections);
            const currentIndex = sectionKeys.indexOf(sectionKey);
            if (currentIndex < sectionKeys.length - 1) {
                const nextSection = sectionKeys[currentIndex + 1];
                this.sections[nextSection].expanded = true;
            }
        },
        
        showTooltipFor(field) {
            this.showTooltip = this.showTooltip === field ? null : field;
        },
        
        hideTooltip() {
            this.showTooltip = null;
        },
        
        addArrayItem(arrayPath, item = {}) {
            const array = this.getNestedValue(arrayPath);
            if (Array.isArray(array)) {
                array.push(item);
            }
        },
        
        removeArrayItem(arrayPath, index) {
            const array = this.getNestedValue(arrayPath);
            if (Array.isArray(array)) {
                array.splice(index, 1);
            }
        },
        
        getNestedValue(path) {
            return path.split('.').reduce((obj, key) => obj && obj[key], this);
        },
        
        setNestedValue(path, value) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            const target = keys.reduce((obj, key) => obj[key], this);
            target[lastKey] = value;
        },
        
        // Validation methods
        validateField(field, value) {
            switch (field) {
                case 'clientLegalName':
                    return this.validateClientName(value);
                case 'email':
                    return this.validateEmail(value);
                case 'phone':
                    return this.validatePhone(value);
                case 'asin':
                    return this.validateASIN(value);
                case 'patentNumber':
                    return this.validatePatentNumber(value);
                default:
                    return { valid: true, message: '' };
            }
        },
        
        validateClientName(name) {
            if (!name || name.length < 2) {
                return { valid: false, message: 'Client name must be at least 2 characters' };
            }
            if (name.length > 120) {
                return { valid: false, message: 'Client name must be less than 120 characters' };
            }
            if (/[😀-🿿]/.test(name)) {
                return { valid: false, message: 'Emoji characters are not allowed' };
            }
            return { valid: true, message: 'Valid client name' };
        },
        
        validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return { valid: false, message: 'Please enter a valid email address' };
            }
            return { valid: true, message: 'Valid email address' };
        },
        
        validatePhone(phone) {
            // E.164 format validation
            const phoneRegex = /^\+[1-9]\d{1,14}$/;
            if (!phoneRegex.test(phone)) {
                return { valid: false, message: 'Please enter phone in E.164 format (+1234567890)' };
            }
            return { valid: true, message: 'Valid phone number' };
        },
        
        validateASIN(asin) {
            const asinRegex = /^[A-Z0-9]{10}$/;
            if (!asinRegex.test(asin)) {
                return { valid: false, message: 'ASIN must be 10 characters (A-Z, 0-9)' };
            }
            return { valid: true, message: 'Valid ASIN' };
        },
        
        validatePatentNumber(patentNum) {
            const patentRegex = /^(USD)?\d{6,7}S?$/;
            if (!patentRegex.test(patentNum)) {
                return { valid: false, message: 'Enter valid patent number (e.g., USD1234567S)' };
            }
            return { valid: true, message: 'Valid patent number' };
        },
        
        // File handling
        handleFileUpload(category, files) {
            const allowedTypes = this.getAllowedFileTypes(category);
            const maxSize = this.getMaxFileSize(category);
            
            for (let file of files) {
                if (!this.validateFileType(file, allowedTypes)) {
                    alert(`File ${file.name} is not an allowed type for ${category}`);
                    continue;
                }
                
                if (!this.validateFileSize(file, maxSize)) {
                    alert(`File ${file.name} exceeds maximum size of ${maxSize / 1024 / 1024}MB`);
                    continue;
                }
                
                this.files[category].push({
                    file: file,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    id: Date.now() + Math.random()
                });
            }
            
            this.triggerAutosave();
        },
        
        removeFile(category, fileId) {
            this.files[category] = this.files[category].filter(f => f.id !== fileId);
            this.triggerAutosave();
        },
        
        getAllowedFileTypes(category) {
            const typeMap = {
                complaint: ['application/pdf', 'message/rfc822', 'text/html', 'image/png', 'image/jpeg'],
                patenteeLetters: ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/png', 'image/jpeg'],
                productImages: ['image/png', 'image/jpeg'],
                commercializationDocs: ['application/pdf', 'image/png', 'image/jpeg'],
                clientPatentDocs: ['application/pdf'],
                priorArtFiles: ['application/pdf', 'image/png', 'image/jpeg'],
                functionalityDocs: ['application/pdf']
            };
            return typeMap[category] || [];
        },
        
        getMaxFileSize(category) {
            return 25 * 1024 * 1024; // 25MB for all categories
        },
        
        validateFileType(file, allowedTypes) {
            return allowedTypes.includes(file.type);
        },
        
        validateFileSize(file, maxSize) {
            return file.size <= maxSize;
        },
        
        // Autosave functionality
        triggerAutosave() {
            if (this.autosaveTimeout) {
                clearTimeout(this.autosaveTimeout);
            }
            
            this.autosaveStatus = 'saving';
            this.autosaveTimeout = setTimeout(() => {
                this.performAutosave();
            }, 500);
        },
        
        async performAutosave() {
            try {
                const response = await fetch('/legal_opinion/autosave', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        formData: this.formData,
                        sections: this.sections
                    })
                });
                
                if (response.ok) {
                    this.autosaveStatus = 'saved';
                } else {
                    this.autosaveStatus = 'error';
                }
            } catch (error) {
                console.error('Autosave failed:', error);
                this.autosaveStatus = 'error';
            }
        },
        
        // Form submission
        async submitForm() {
            // Validate all required fields
            if (!this.validateAllFields()) {
                alert('Please complete all required fields before submitting.');
                return;
            }
            
            // Create FormData for file uploads
            const formData = new FormData();
            formData.append('data', JSON.stringify(this.formData));
            
            // Add files
            for (const [category, files] of Object.entries(this.files)) {
                files.forEach((fileObj, index) => {
                    formData.append(`${category}_${index}`, fileObj.file);
                });
            }
            
            try {
                const response = await fetch('/legal_opinion/submit', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    alert('Legal opinion request submitted successfully!');
                    // Optionally redirect or reset form
                } else {
                    const error = await response.json();
                    alert(`Submission failed: ${error.message}`);
                }
            } catch (error) {
                console.error('Submission failed:', error);
                alert('Submission failed. Please try again.');
            }
        },
        
        validateAllFields() {
            // Check required fields
            const required = [
                'formData.clientLegalName',
                'formData.businessStructure',
                'formData.contactPerson.name',
                'formData.contactPerson.email',
                'formData.privilegeAcknowledged',
                'formData.platform',
                'formData.complaintStatus',
                'formData.productName',
                'formData.articleOfManufacture',
                'formData.accuracyAttestation'
            ];
            
            return required.every(path => {
                const value = this.getNestedValue(path);
                return value !== '' && value !== false && value !== null && value !== undefined;
            });
        }
    };
    
    // Initialize Alpine.js
    if (window.Alpine) {
        window.Alpine.data('legalOpinion', () => window.legalOpinionData);
    }
    
    // Keyboard accessibility for tooltips
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && window.legalOpinionData.showTooltip) {
            window.legalOpinionData.hideTooltip();
        }
    });
    
    // Auto-expand URL fields when ASIN is entered
    document.addEventListener('input', function(e) {
        if (e.target.name === 'asin' && e.target.value.length === 10) {
            const urlField = e.target.closest('.form-group').querySelector('input[name*="url"]');
            if (urlField && !urlField.value) {
                urlField.value = `https://www.amazon.com/dp/${e.target.value}`;
                window.legalOpinionData.triggerAutosave();
            }
        }
    });

    // Enhanced file validation
    function validateImageDimensions(file) {
        return new Promise((resolve) => {
            if (!file.type.startsWith('image/')) {
                resolve({ valid: true, message: '' });
                return;
            }

            const img = new Image();
            img.onload = function() {
                const minDimension = Math.min(this.width, this.height);
                if (minDimension < 1200) {
                    resolve({
                        valid: false,
                        message: `Image ${file.name} is too small. Minimum 1200px on shortest side. Current: ${minDimension}px`
                    });
                } else {
                    resolve({ valid: true, message: 'Image dimensions are acceptable' });
                }
            };
            img.onerror = function() {
                resolve({ valid: false, message: `Could not read image ${file.name}` });
            };
            img.src = URL.createObjectURL(file);
        });
    }

    // Enhanced drag and drop
    document.addEventListener('dragover', function(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.file-upload-area');
        if (dropZone) {
            dropZone.classList.add('dragover');
        }
    });

    document.addEventListener('dragleave', function(e) {
        const dropZone = e.target.closest('.file-upload-area');
        if (dropZone && !dropZone.contains(e.relatedTarget)) {
            dropZone.classList.remove('dragover');
        }
    });

    document.addEventListener('drop', function(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.file-upload-area');
        if (dropZone) {
            dropZone.classList.remove('dragover');
        }
    });

    // Form progress tracking
    function updateFormProgress() {
        const sections = window.legalOpinionData.sections;
        const completed = Object.values(sections).filter(s => s.completed).length;
        const total = Object.keys(sections).length;
        const percentage = Math.round((completed / total) * 100);

        // Update progress in title or other UI elements
        document.title = `Legal Opinion (${percentage}% complete) - Maidalv API Studio`;

        // Save progress to localStorage
        localStorage.setItem('legalOpinionProgress', JSON.stringify({
            sections: sections,
            formData: window.legalOpinionData.formData,
            timestamp: Date.now()
        }));
    }

    // Load saved progress
    function loadSavedProgress() {
        const saved = localStorage.getItem('legalOpinionProgress');
        if (saved) {
            try {
                const data = JSON.parse(saved);
                // Only load if saved within last 24 hours
                if (Date.now() - data.timestamp < 24 * 60 * 60 * 1000) {
                    if (confirm('Found saved progress from a previous session. Would you like to restore it?')) {
                        Object.assign(window.legalOpinionData.sections, data.sections);
                        Object.assign(window.legalOpinionData.formData, data.formData);
                    }
                }
            } catch (e) {
                console.error('Error loading saved progress:', e);
            }
        }
    }

    // Initialize progress tracking
    setTimeout(() => {
        loadSavedProgress();
        updateFormProgress();

        // Watch for changes
        const observer = new MutationObserver(updateFormProgress);
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true
        });
    }, 1000);

    // Warn before leaving with unsaved changes
    window.addEventListener('beforeunload', function(e) {
        if (window.legalOpinionData.autosaveStatus === 'saving') {
            e.preventDefault();
            e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            return e.returnValue;
        }
    });

    // Enhanced error handling
    window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error);
        // Could send error reports to server here
    });

    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }, 0);
        });
    }
});
