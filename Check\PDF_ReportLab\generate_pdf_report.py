import json
import argparse
import os, sys

from reportlab.platypus import SimpleDocTemplate, PageBreak, Frame, PageTemplate, NextPageTemplate
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.lib.colors import HexColor
from reportlab.lib.utils import ImageReader

sys.path.append(os.getcwd())

from Check.PDF_ReportLab.styles import colors
from Check.PDF_ReportLab.cover_page import create_cover_page
from Check.PDF_ReportLab.results_pages import create_results_pages
from Check.PDF_ReportLab.helpers import prefetch_images

from langfuse import observe
import langfuse

def _collect_urls_for_prefetch(data):
    urls = []
    # cover page product images (first 4)
    seen = set()
    for res in data.get('results', []):
        product_urls = res.get('product_url')
        if not product_urls:
            continue
        
        # Handle both single URL (str) and multiple URLs (list)
        if isinstance(product_urls, str):
            product_urls = [product_urls]
            
        for p_url in product_urls:
            if p_url and p_url not in seen:
                urls.append(p_url)
                seen.add(p_url)
                if len(urls) >= 4:
                    break
        if len(urls) >= 4:
            break
    
    urls = urls[:4]

    # results pages IP images (caps per type)
    for res in data.get('results', []):
        ulist = res.get('ip_asset_urls') or []
        ip_type = (res.get('ip_type') or '').lower()
        cap = 9 if 'patent' in ip_type else (8 if 'copyright' in ip_type else 2)
        urls.extend(ulist[:cap])
    return urls

@observe(name="Generate PDF Report", capture_output=False)
def generate_pdf_report(data, output_path=None):
    """
    Generates a PDF report from the given JSON data.
    """

    # Create PDF
    output_filename = os.path.join('Check', 'PDF_ReportLab', 'output', f"{data['check_id']}.pdf")
    doc = SimpleDocTemplate(output_filename, pagesize=A4)
    width, height = A4
    
    # Cover Page Frame
    frame_width = 188 * mm
    frame_height = 275 * mm
    left_margin = (width - frame_width) / 2
    bottom_margin = (height - frame_height) / 2

    cover_frame = Frame(
        left_margin, bottom_margin,
        frame_width, frame_height,
        id='cover_frame',
        leftPadding=0, topPadding=0,
        rightPadding=0, bottomPadding=0
    )

    def on_cover_page(canvas, doc):
        canvas.saveState()
        # Set background color
        canvas.setFillColor(colors['light_gray'])
        canvas.rect(
            left_margin, bottom_margin,
            frame_width, frame_height,
            stroke=0, fill=1
        )
        # Set border
        canvas.setStrokeColor(colors['navy'])
        canvas.setLineWidth(1*mm)
        canvas.rect(
            left_margin, bottom_margin,
            frame_width, frame_height,
            stroke=1, fill=0
        )
        
        seal_path = 'Check/PDF_ReportLab/static/seal.png'
        target_h = 28*mm                              # fixed height
        # iw, ih = ImageReader(seal_path).getSize()     # px size
        # print(f"Seal size: {iw}x{ih}")
        iw, ih = 166, 145
        target_w = target_h * (iw/ih)                 # preserve aspect ratio

        x = width - left_margin - target_w - 10*mm  # 10mm from right edge
        y = bottom_margin + 10*mm                     # 10mm above frame bottom

        canvas.drawImage(
            seal_path, x, y,
            width=target_w, height=target_h,
            preserveAspectRatio=True, mask='auto'
        )
        canvas.restoreState()

    # Page Templates
    cover_page_template = PageTemplate(id='CoverPage', frames=[cover_frame], onPage=on_cover_page)
    cover_page_template.autoNextPageTemplate = 'LaterPages'
    
    # Create a frame for the later pages
    def on_later_page(canvas, doc):
        # thin divider above footer
        canvas.saveState()
        canvas.setStrokeColor(colors['line'])
        canvas.setLineWidth(0.5)
        canvas.line(doc.leftMargin, doc.bottomMargin - 6*mm, doc.leftMargin + doc.width, doc.bottomMargin - 6*mm)

        # centered page number
        canvas.setFont('Helvetica', 9)
        page_txt = f"Page {canvas.getPageNumber()}"
        canvas.drawCentredString(doc.leftMargin + doc.width/2, doc.bottomMargin - 10*mm, page_txt)
        canvas.restoreState()
    
    
    later_pages_frame = Frame(
        doc.leftMargin, doc.bottomMargin + 15*mm, # Move up to make space for footer
        doc.width, doc.height - 15*mm,             # Reduce height
        id='later_pages_frame'
    )
    later_pages_template = PageTemplate(id='LaterPages', frames=[later_pages_frame], onPage=on_later_page)
    
    doc.addPageTemplates([cover_page_template, later_pages_template])

    story = []

    # Cover Page
    # NEW: warm the cache; subsequent _fit_image calls become CPU-only
    img_cache = {}
    prefetch_images(_collect_urls_for_prefetch(data), img_cache)
    results_flowables, local_product_images_count, url_product_images_count = create_cover_page(frame_width, frame_height, data, img_cache)
    story.extend(results_flowables)
    story.append(PageBreak())

    # Results Pages
    results_flowables, local_IP_images_count, url_IP_images_count = create_results_pages(data, img_cache)
    story.extend(results_flowables)

    # Build the PDF
    doc.build(story)
    print(f"Successfully generated report: {output_filename}")
    
    langfuse.get_client().update_current_span(output={"report_path": output_filename, "local_product_images_count": local_product_images_count, "url_product_images_count": url_product_images_count, "local_IP_images_count": local_IP_images_count, "url_IP_images_count": url_IP_images_count})

    return output_filename

if __name__ == '__main__':
    import time
    stat_time = time.time()
    parser = argparse.ArgumentParser(description='Generate a PDF report from a JSON file.')
    parser.add_argument('json_path', type=str, nargs='?', default='PDF_service/sample.json',
                        help='The path to the input JSON file (optional).')
    args = parser.parse_args()
    with open(args.json_path, 'r') as f:
        json_data = json.load(f)
    generate_pdf_report(json_data['result'])
    print(f"PDF generation completed in {time.time() - stat_time:.1f} seconds")