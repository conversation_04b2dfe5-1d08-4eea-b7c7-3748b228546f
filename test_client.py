import grpc
from Protos import embedding_pb2
from Protos import embedding_pb2_grpc
from PIL import Image
import io

def create_fake_image_bytes():
    """Creates a fake image and returns its byte representation."""
    image = Image.new('RGB', (100, 100), color = 'red')
    byte_arr = io.BytesIO()
    image.save(byte_arr, format='PNG')
    return byte_arr.getvalue()

def run():
    channel = grpc.insecure_channel('localhost:5001')
    stub = embedding_pb2_grpc.EmbeddingServiceStub(channel)

    fake_image_bytes = create_fake_image_bytes()

    # Test GetImageEmbeddings
    print("--- Testing GetImageEmbeddings ---")
    try:
        request = embedding_pb2.GetImageEmbeddingsRequest(
            images=[embedding_pb2.Image(image_data=fake_image_bytes)]
        )
        response = stub.GetImageEmbeddings(request)
        print("GetImageEmbeddings Response Received:")
        print(response)
    except grpc.RpcError as e:
        print(f"GetImageEmbeddings failed with status {e.code()}: {e.details()}")

    # Test GetImageSplitWithEmbeddings
    print("\n--- Testing GetImageSplitWithEmbeddings ---")
    try:
        request = embedding_pb2.GetImageSplitWithEmbeddingsRequest(
            image=embedding_pb2.Image(image_data=fake_image_bytes)
        )
        response = stub.GetImageSplitWithEmbeddings(request)
        print("GetImageSplitWithEmbeddings Response Received:")
        print(response)
    except grpc.RpcError as e:
        print(f"GetImageSplitWithEmbeddings failed with status {e.code()}: {e.details()}")


if __name__ == '__main__':
    run()