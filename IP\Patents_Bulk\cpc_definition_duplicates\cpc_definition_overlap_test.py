import pandas as pd
import logging
import os
import csv
from IP.Patents_Bulk.patent_db_cpc import load_cpc_ipc_definitions_cache, get_cpc_ipc_definitions_df, get_db_connection

# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'cpc_overlap_test.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def find_overlapping_definitions(df: pd.DataFrame) -> pd.DataFrame:
    """
    Identifies classification entries that have the same section, class, subclass,
    main_group, and sub_group but different definitions.

    Args:
        df: A pandas DataFrame containing classification definitions with columns:
            'classification_type', 'publish_date', 'section', 'class', 'subclass',
            'main_group', 'sub_group', 'definition'.

    Returns:
        A DataFrame containing the overlapping entries.
    """
    if df is None or df.empty:
        logger.info("No data to process for overlapping definitions.")
        return pd.DataFrame()

    logger.info("Starting to find overlapping definitions...")

    # Group by the classification components
    grouped = df.groupby(['section', 'class', 'subclass', 'main_group', 'sub_group'])

    overlapping_groups_list = []
    found_overlapping_count = 0
    MAX_OVERLAPPING_GROUPS = 20

    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection for finding overlapping definitions.")
            return pd.DataFrame()

        for name, group in grouped:
            if found_overlapping_count >= MAX_OVERLAPPING_GROUPS:
                logger.info(f"Reached limit of {MAX_OVERLAPPING_GROUPS} overlapping groups. Stopping.")
                break

            section, class_val, subclass, main_group, sub_group = name
            
            if main_group is None or sub_group is None:
                continue
            
            # Query database for all definitions for this specific group
            sql = """
                SELECT definition, classification_type, publish_date
                FROM patents_cpc_ipc_definitions
                WHERE section = %s AND class = %s AND subclass = %s AND main_group = %s AND sub_group = %s
            """
            params = [section, class_val, subclass, main_group, sub_group]
            
            cursor = conn.cursor()
            cursor.execute(sql, params)
            definitions_data = cursor.fetchall()
            cursor.close()

            if definitions_data:
                # Convert fetched data to a DataFrame to easily check unique definitions
                temp_df = pd.DataFrame(definitions_data, columns=['definition', 'classification_type', 'publish_date'])
                
                if temp_df['definition'].nunique() > 1:
                    logger.warning(f"Found overlapping definitions for group: {name}")
                    # Add the full group data from the original DataFrame, along with fetched definitions
                    # Merge temp_df with the original group to get all columns
                    merged_group = pd.merge(group, temp_df, on=['classification_type', 'publish_date'], how='inner', suffixes=('', '_db'))
                    overlapping_groups_list.append(merged_group)
                    found_overlapping_count += 1

    except Exception as e:
        logger.error(f"Error finding overlapping definitions: {e}", exc_info=True)
    finally:
        if conn:
            conn.close()

    if not overlapping_groups_list:
        logger.info("No overlapping definitions found.")
        return pd.DataFrame()

    # Concatenate all overlapping groups into a single DataFrame
    overlapping_df = pd.concat(overlapping_groups_list).sort_values(
        by=['section', 'class', 'subclass', 'main_group', 'sub_group', 'classification_type', 'publish_date']
    )
    logger.info(f"Found {len(overlapping_df)} overlapping definition entries across {found_overlapping_count} groups.")
    return overlapping_df

def save_to_csv(df: pd.DataFrame, output_path: str):
    """
    Saves a DataFrame to a CSV file.

    Args:
        df: The DataFrame to save.
        output_path: The full path to the output CSV file.
    """
    if df.empty:
        logger.info("No overlapping definitions to save to CSV.")
        return

    try:
        df.to_csv(output_path, index=False, encoding='utf-8')
        logger.info(f"Overlapping definitions saved to {output_path}")
    except IOError as e:
        logger.error(f"Error saving CSV to {output_path}: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred while saving CSV: {e}")

def main():
    logger.info("Starting CPC/IPC definition overlap test script.")

    # Ensure the definitions cache is loaded
    if not load_cpc_ipc_definitions_cache():
        logger.error("Failed to load CPC/IPC definitions cache. Exiting.")
        return

    # Use the globally cached DataFrame
    df = get_cpc_ipc_definitions_df()

    if df is None or df.empty:
        logger.error("CPC/IPC definitions DataFrame is empty or not loaded. Cannot proceed.")
        return

    # Find overlapping definitions
    overlapping_definitions = find_overlapping_definitions(df)

    # Define output CSV path
    output_dir = os.path.dirname(__file__)
    output_csv_path = os.path.join(output_dir, 'cpc_ipc_definition_overlaps.csv')

    # Save to CSV
    save_to_csv(overlapping_definitions, output_csv_path)

    logger.info("CPC/IPC definition overlap test script finished.")

if __name__ == "__main__":
    main()