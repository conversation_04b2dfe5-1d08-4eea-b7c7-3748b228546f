from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List, Optional
from fastapi import FastAPI, Response, Form, UploadFile, File, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from jinja2 import Environment, FileSystemLoader, select_autoescape
from markdown_it import MarkdownIt
from weasyprint import HTML, CSS,  default_url_fetcher
import uuid
import hashlib, requests
from io import BytesIO
from PIL import Image
import os, sys
if sys.platform.startswith("win"):
    os.add_dll_directory(r"C:\msys64\mingw64\bin")

ROOT = Path(__file__).parent.resolve()
TEMPLATES_DIR = ROOT / "templates"
STATIC_DIR = ROOT / "static"
CACHE_DIR = ROOT / "cache"
CACHE_DIR.mkdir(exist_ok=True)

# ---------- FastAPI ----------
app = FastAPI(title="Maidalv PDF Generator", version="1.0")

# serve static for quick HTML preview in browser (optional)
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")

# ---------- Jinja ----------
env = Environment(
    loader=FileSystemLoader(str(TEMPLATES_DIR)),
    autoescape=select_autoescape(["html", "xml"]),
)
template = env.get_template("report.html")

# ---------- Markdown -> HTML ----------
md = MarkdownIt("commonmark").enable("table").enable("strikethrough")

# ---------- Pydantic models (lightweight) ----------
class ResultItem(BaseModel):
    ip_type: str
    reg_no: Optional[str] = None
    ser_no: Optional[str] = None
    int_cls_list: List[int] = []
    goods_services: Optional[str] = None
    text: Optional[str] = None
    ip_owner: Optional[str] = None
    plaintiff_id: Optional[int] = None
    plaintiff_name: Optional[str] = None
    ip_asset_urls: List[str] = []
    report: Optional[str] = None  # markdown
    risk_level: str
    risk_score: Optional[int] = None
    risk_description: Optional[str] = None
    product_url: Optional[str] = None
    last_case_docket: Optional[str] = None
    last_case_date_filed: Optional[str] = None

class Payload(BaseModel):
    status: str
    result: Dict[str, Any]

# ---------- Helpers ----------
def risk_to_badge(level: str) -> Dict[str, str]:
    l = (level or "").strip().lower()
    if l == "high risk":
        return {"class": "risk risk-high", "label": "High Risk"}
    if l == "medium risk":
        return {"class": "risk risk-med", "label": "Medium Risk"}
    return {"class": "risk risk-low", "label": level or "—"}

def normalize_results(raw_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    norm = []
    for i, r in enumerate(raw_results, start=1):
        item = ResultItem(**r)
        # markdown → html (safe for print)
        report_html = md.render(item.report or "")
        badge = risk_to_badge(item.risk_level)
        norm.append(
            {
                "index": i,
                "ip_type": item.ip_type,
                "reg_no": item.reg_no,
                "ser_no": item.ser_no,
                "int_cls_list": item.int_cls_list,
                "goods_services": item.goods_services,
                "text": item.text,
                "ip_owner": item.ip_owner,
                "plaintiff_id": item.plaintiff_id,
                "plaintiff_name": item.plaintiff_name,
                "ip_asset_urls": item.ip_asset_urls,
                "report_html": report_html,
                "risk_level": item.risk_level,
                "risk_badge_class": badge["class"],
                "risk_badge_label": badge["label"],
                "risk_score": item.risk_score,
                "risk_description": item.risk_description,
                "product_url": item.product_url,
                "last_case_docket": item.last_case_docket,
                "last_case_date_filed": item.last_case_date_filed,
            }
        )
    return norm

def default_product_images(results: List[Dict[str, Any]]) -> List[str]:
    # Collect a couple of product images for the cover (if present)
    imgs = []
    for r in results:
        if r.get("product_url"):
            imgs.append(r["product_url"])
        if len(imgs) >= 2:
            break
    # Fallback to local sample images if none provided
    if not imgs:
        for p in ["product1.webp", "product2.png"]:
            if (STATIC_DIR / p).exists():
                imgs.append(f"/static/{p}")
    return imgs[:2]

# ---------- Endpoints ----------
@app.get("/")
def health():
    return {"ok": True, "service": "Maidalv PDF Generator"}

@app.post("/render")
def render_pdf(payload: Payload):
    """
    Accepts your JSON, returns a PDF.
    """
    data = payload.result or {}
    check_id = data.get("check_id") or str(uuid.uuid4())
    client_name = data.get("client_name") or "—"
    risk_level = data.get("risk_level") or "—"
    results_raw = data.get("results") or []

    results = normalize_results(results_raw)
    cover_products = default_product_images(results)

    # Summary table rows for cover
    summary_rows = []
    for i, r in enumerate(results, start=1):
        summary_rows.append({
            "page": i + 1,
            "ip_type": r["ip_type"],
            "ip_owner": r.get("ip_owner") or (r.get("plaintiff_name") or "—"),
            "risk_level": r["risk_level"],
            "pill_class": risk_to_badge(r["risk_level"])["class"],  # precomputed
        })
        
    html = template.render(
        now=datetime.utcnow(),
        company_name="Shenzhen Maidalv Technology Co., Ltd.",
        report_title="INTELLECTUAL PROPERTY INFRINGEMENT RISK ASSESSMENT REPORT",
        check_id=check_id,
        client_name=client_name,
        global_risk=risk_level,
        global_risk_badge=risk_to_badge(risk_level),
        results=results,
        summary_rows=summary_rows,
        cover_products=cover_products,
        static_base="static",
        seal_exists=(STATIC_DIR / "seal.png").exists(),
    )

    # WeasyPrint render
    pdf_bytes = HTML(
        string=html,
        base_url=str(ROOT)
    ).write_pdf(
        stylesheets=[CSS(filename=str(STATIC_DIR / "styles.css"))],
        presentational_hints=False,
        optimize_size=("images",),   # helps a bit with inline images
        font_config=None,            # rely on core fonts
        url_fetcher=cached_url_fetcher
    )

    headers = {
        "Content-Disposition": f'inline; filename="report_{check_id}.pdf"'
    }
    return Response(content=pdf_bytes, media_type="application/pdf", headers=headers)


def cached_url_fetcher(url: str):
    # Intercept remote URLs; return local, resized bytes
    if url.startswith("http://") or url.startswith("https://"):
        h = hashlib.sha256(url.encode()).hexdigest()
        cached = CACHE_DIR / f"{h}.bin"
        mimef = CACHE_DIR / f"{h}.mime"

        if not cached.exists():
            r = requests.get(url, timeout=5)
            r.raise_for_status()
            content = r.content
            mime = r.headers.get("Content-Type", "application/octet-stream")

            if mime.startswith("image/"):
                try:
                    im = Image.open(BytesIO(content))
                    im = im.convert("RGB")
                    im.thumbnail((1200, 1200))  # cap size
                    buf = BytesIO()
                    im.save(buf, format="JPEG", quality=72, optimize=True)
                    content = buf.getvalue()
                    mime = "image/jpeg"
                except Exception:
                    pass

            cached.write_bytes(content)
            mimef.write_text(mime)

        content = cached.read_bytes()
        mime = mimef.read_text() if mimef.exists() else None
        return {"file_obj": BytesIO(content), "mime_type": mime, "encoding": None, "redirected_url": url}

    # Local files, static/, etc.
    return default_url_fetcher(url)


@app.get("/preview", response_class=HTMLResponse)
def preview_form():
    return """
<!doctype html>
<html><head>
  <meta charset="utf-8"><title>Preview JSON → HTML</title>
  <style>body{font:14px/1.4 system-ui, Segoe UI, Arial,sans-serif; margin:24px;}
  textarea{width:100%;height:300px;font-family:ui-monospace,Consolas,monospace}
  .row{display:flex;gap:12px;align-items:center;margin:8px 0}
  iframe{width:100%;height:70vh;border:1px solid #ddd;margin-top:16px}
  </style>
</head><body>
  <h2>Preview Report (POST /preview)</h2>
  <form id="f" method="post" action="/preview" enctype="multipart/form-data" target="out">
    <div class="row">
      <input type="file" name="file" accept=".json">
      <span>or paste JSON below</span>
    </div>
    <textarea name="json" placeholder="Paste JSON here..."></textarea>
    <div class="row">
      <button type="submit">Preview</button>
    </div>
  </form>
  <iframe name="out"></iframe>
</body></html>
    """

@app.post("/preview", response_class=HTMLResponse)
async def preview_html(
    request: Request,
    json: str = Form(None),
    file: UploadFile = File(None),
):
    # Accept application/json OR multipart form
    payload = None
    if request.headers.get("content-type", "").startswith("application/json"):
        payload = await request.json()
    else:
        if file and file.filename:
            payload = await file.read()
            payload = payload.decode("utf-8", errors="ignore")
        elif json:
            payload = json
        if isinstance(payload, str):
            import json as _json
            payload = _json.loads(payload)

    if not isinstance(payload, dict):
        return HTMLResponse("<h3>Invalid JSON</h3>", status_code=400)

    data = payload.get("result", {}) or {}
    check_id = data.get("check_id") or "—"
    results = normalize_results(data.get("results") or [])

    html = template.render(
        now=datetime.utcnow(),
        company_name="Shenzhen Maidalv Technology Co., Ltd.",
        report_title="INTELLECTUAL PROPERTY INFRINGEMENT RISK ASSESSMENT REPORT",
        check_id=check_id,
        client_name=data.get("client_name") or "—",
        global_risk=data.get("risk_level") or "—",
        global_risk_badge=risk_to_badge(data.get("risk_level") or "—"),
        results=results,
        summary_rows=[{
            "page": i+1,
            "ip_type": r["ip_type"],
            "ip_owner": r.get("ip_owner") or (r.get("plaintiff_name") or "—"),
            "risk_level": r["risk_level"],
            "pill_class": r["risk_badge_class"],
        } for i, r in enumerate(results)],
        cover_products=default_product_images(results),
        static_base="static",
        seal_exists=(STATIC_DIR / "seal.png").exists(),
    )
    return HTMLResponse(content=html, media_type="text/html")