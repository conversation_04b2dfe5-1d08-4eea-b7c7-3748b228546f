from html import escape
from typing import List, Tuple

from reportlab.platypus import Paragraph, ListFlowable, ListItem, XPreformatted, HRFlowable, Indenter, Spacer, Table, TableStyle
from reportlab.lib import colors as rl_colors
from reportlab.lib.units import mm

from markdown_it import MarkdownIt
from mdit_py_plugins.footnote import footnote_plugin
from mdit_py_plugins.tasklists import tasklists_plugin

# Markdown parser (tables & strikethrough via "gfm-like")
md = (MarkdownIt("gfm-like").use(footnote_plugin).use(tasklists_plugin))

# -------------------- Inline mapping --------------------
def _inline_to_rl(inline_tokens, styles) -> str:
    out = []
    for t in inline_tokens:
        if t.type == "text":
            out.append(escape(t.content))
        elif t.type == "strong_open":
            out.append("<b>")
        elif t.type == "strong_close":
            out.append("</b>")
        elif t.type == "em_open":
            out.append("<i>")
        elif t.type == "em_close":
            out.append("</i>")
        elif t.type == "link_open":
            href = dict(t.attrs or {}).get("href", "")
            out.append(f'<link href="{escape(href)}">')
        elif t.type == "link_close":
            out.append("</link>")
        elif t.type == "code_inline":
            out.append(f'<font face="Courier">{escape(t.content)}</font>')
        elif t.type in ("softbreak", "hardbreak"):
            out.append("<br/>")
        else:
            # ignore other inline token types
            pass
    return "".join(out)

# -------------------- Helpers --------------------
def _is_callout_line(text: str) -> bool:
    """Detect lines we want to render as callouts."""
    lowered = text.strip().lower()
    return lowered.startswith("overall statement:") or lowered.startswith("conclusion:")

def _normalize_heading_text(inline_tokens) -> str:
    # Extract plain text
    buf = []
    for t in inline_tokens:
        if t.type == "text":
            buf.append(t.content)
        elif t.type == "code_inline":
            buf.append(t.content)
        # ignore other formatting
    return "".join(buf).strip()

def _maybe_emit_markinfo_table(kv_pairs: List[Tuple[str, str]], out: list, styles):
    if not kv_pairs:
        return
    # Build 2-column table with MetaKey / MetaValue
    data = []
    for k, v in kv_pairs:
        data.append([Paragraph(escape(k), styles['MetaKey']), Paragraph(escape(v), styles['MetaValue'])])

    table = Table(data, colWidths=[60*mm, None])
    table.setStyle(TableStyle([
        ('ALIGN', (0,0), (-1,-1), 'LEFT'),
        ('VALIGN', (0,0), (-1,-1), 'TOP'),
        ('LEFTPADDING', (0,0), (-1,-1), 4),
        ('RIGHTPADDING', (0,0), (-1,-1), 4),
        ('TOPPADDING', (0,0), (-1,-1), 3),
        ('BOTTOMPADDING', (0,0), (-1,-1), 3),
        ('ROWBACKGROUNDS', (0,0), (-1,-1), [rl_colors.white, rl_colors.HexColor('#FAFAFA')]),
        ('INNERGRID', (0,0), (-1,-1), 0.25, rl_colors.HexColor('#E5E7EB')),
        ('BOX', (0,0), (-1,-1), 0.5, rl_colors.HexColor('#D1D5DB')),
    ]))
    out.append(table)

# -------------------- Main conversion --------------------
def markdown_to_flowables(markdown_text: str, styles) -> list:
    tokens = md.parse(markdown_text)
    out = []

    current_heading = ""  # last seen heading text (plain)
    i = 0

    # Accumulate key/value lines under "Mark Information" -> emit as table
    collecting_markinfo = False
    markinfo_pairs: List[Tuple[str, str]] = []

    def flush_markinfo_if_any():
        nonlocal markinfo_pairs, out
        _maybe_emit_markinfo_table(markinfo_pairs, out, styles)
        markinfo_pairs = []

    while i < len(tokens):
        t = tokens[i]

        # ---- Headings ----
        if t.type == "heading_open":
            level = int(t.tag[1])
            inline = tokens[i+1]  # inline
            text = _normalize_heading_text(inline.children or [])
            current_heading = text

            # Decide style by level
            if level == 1:
                out.append(Paragraph(_inline_to_rl(inline.children or [], styles), styles['H1']))
            elif level == 2:
                # flush any pending mark-info before moving on
                flush_markinfo_if_any()
                out.append(Paragraph(_inline_to_rl(inline.children or [], styles), styles['H2']))
            else:
                flush_markinfo_if_any()
                out.append(Paragraph(_inline_to_rl(inline.children or [], styles), styles['H3']))
            i += 3  # heading_open, inline, heading_close
            continue

        # ---- Paragraphs ----
        if t.type == "paragraph_open":
            inline = tokens[i+1]
            html = _inline_to_rl(inline.children or [], styles)
            raw_text = _normalize_heading_text(inline.children or [])

            # If we're collecting Mark Information and line looks like "Label: value"
            if collecting_markinfo:
                if ":" in raw_text:
                    k, v = raw_text.split(":", 1)
                    markinfo_pairs.append((k.strip(), v.strip()))
                else:
                    # if a non key:value paragraph appears, flush table then add paragraph
                    flush_markinfo_if_any()
                    collecting_markinfo = False
                    out.append(Paragraph(html, styles['Body']))
                i += 3
                continue

            # Callout detection
            if _is_callout_line(raw_text):
                out.append(Paragraph(html, styles['Callout']))
            else:
                out.append(Paragraph(html, styles['Body']))
            i += 3
            continue

        # ---- Bullet lists ----
        if t.type == "bullet_list_open":
            # If under Mark Information, capture as key:value rows
            if current_heading.lower().lstrip('0123456789. ').strip() == "mark information":
                collecting_markinfo = True
                # We will read list items and append into markinfo_pairs
                j = i + 1
                while j < len(tokens) and tokens[j].type != "bullet_list_close":
                    if tokens[j].type == "list_item_open":
                        # Expect paragraph or inline content immediately within the LI
                        # We convert the inner to raw text to split k:v
                        # The pattern is: list_item_open -> paragraph_open -> inline -> paragraph_close -> list_item_close
                        # but sometimes markdown may put inline directly.
                        # Handle both.
                        if tokens[j+1].type == "paragraph_open":
                            inline = tokens[j+2]
                            raw_text = _normalize_heading_text(inline.children or [])
                            if ":" in raw_text:
                                k, v = raw_text.split(":", 1)
                                markinfo_pairs.append((k.strip(), v.strip()))
                            else:
                                # degrade: treat as normal bullet in a subsequent paragraph
                                collecting_markinfo = False
                        else:
                            # direct inline
                            inline = tokens[j+1]
                            raw_text = _normalize_heading_text(inline.children or [])
                            if ":" in raw_text:
                                k, v = raw_text.split(":", 1)
                                markinfo_pairs.append((k.strip(), v.strip()))
                            else:
                                collecting_markinfo = False
                        # advance to list_item_close
                        while j < len(tokens) and tokens[j].type != "list_item_close":
                            j += 1
                    j += 1
                # After scanning the special list, we'll emit the table and skip the whole list
                flush_markinfo_if_any()
                collecting_markinfo = False
                # Advance i to after bullet_list_close
                while i < len(tokens) and tokens[i].type != "bullet_list_close":
                    i += 1
                i += 1
                continue
            else:
                # Generic list mapping to ListFlowable with hanging indents
                items = []
                j = i + 1
                while j < len(tokens) and tokens[j].type != "bullet_list_close":
                    if tokens[j].type == "list_item_open":
                        # Collect paragraphs inside the item
                        paras = []
                        k = j + 1
                        while k < len(tokens) and tokens[k].type != "list_item_close":
                            if tokens[k].type == "paragraph_open":
                                inline = tokens[k+1]
                                html = _inline_to_rl(inline.children or [], styles)
                                paras.append(Paragraph(html, styles['ListBullet']))
                                k += 3
                            else:
                                k += 1
                        if paras:
                            items.append(ListItem(flowables=paras))
                        # move j to after list_item_close
                        while j < len(tokens) and tokens[j].type != "list_item_close":
                            j += 1
                    j += 1
                lf = ListFlowable(items, bulletType='bullet', start='•',
                                  leftIndent=12, bulletIndent=0, spaceBefore=0, spaceAfter=2)
                out.append(lf)
                i = j + 1  # position after bullet_list_close
                continue

        # ---- Ordered lists (treat similar to bullet) ----
        if t.type == "ordered_list_open":
            items = []
            j = i + 1
            start_index = int(t.attrs.get('start', 1)) if hasattr(t, 'attrs') and t.attrs else 1
            while j < len(tokens) and tokens[j].type != "ordered_list_close":
                if tokens[j].type == "list_item_open":
                    paras = []
                    k = j + 1
                    while k < len(tokens) and tokens[k].type != "list_item_close":
                        if tokens[k].type == "paragraph_open":
                            inline = tokens[k+1]
                            html = _inline_to_rl(inline.children or [], styles)
                            paras.append(Paragraph(html, styles['ListBullet']))
                            k += 3
                        else:
                            k += 1
                    if paras:
                        items.append(ListItem(flowables=paras))
                    while j < len(tokens) and tokens[j].type != "list_item_close":
                        j += 1
                j += 1
            lf = ListFlowable(items, bulletType='1', start=start_index,
                              leftIndent=12, bulletIndent=0, spaceBefore=0, spaceAfter=2)
            out.append(lf)
            i = j + 1
            continue

        # ---- Blockquote ----
        if t.type == "blockquote_open":
            out.append(Indenter(left=10))
            i += 1
            continue
        if t.type == "blockquote_close":
            out.append(Indenter(left=-10))
            i += 1
            continue

        # ---- Horizontal rule ----
        if t.type == "hr":
            out.append(HRFlowable(width="100%", spaceBefore=6, spaceAfter=6))
            i += 1
            continue

        # ---- Code fence ----
        if t.type == "fence":
            out.append(XPreformatted(t.content.rstrip('\n'), styles['myCode']))
            i += 1
            continue

        # ---- Tables (simple) ----
        if t.type == "table_open":
            # Very simple mapping: header row, then body rows.
            # We assume structure: thead -> tr -> th, tbody -> tr -> td
            # If thead missing, treat first row as header.
            # Build rows by scanning until table_close.
            rows = []
            header_done = False
            j = i + 1
            while j < len(tokens) and tokens[j].type != "table_close":
                if tokens[j].type == "tr_open":
                    # collect cells
                    cells = []
                    j += 1
                    while j < len(tokens) and tokens[j].type != "tr_close":
                        if tokens[j].type in ("th_open", "td_open"):
                            inline = tokens[j+1]  # inline
                            html = _inline_to_rl(inline.children or [], styles)
                            style_name = 'TableHeader' if tokens[j].type == 'th_open' else 'TableCell'
                            cells.append(Paragraph(html, styles[style_name]))
                            j += 3  # skip to *_close
                        else:
                            j += 1
                    rows.append(cells)
                else:
                    j += 1

            # If no th were present, treat first row as header
            has_th = any(isinstance(cell, Paragraph) and cell.style.name == 'TableHeader'
                         for row in rows for cell in row)
            if not has_th and rows:
                # convert first row to header style
                rows[0] = [Paragraph(c.getPlainText(), styles['TableHeader']) for c in rows[0]]

            table = Table(rows, colWidths=None)
            table.setStyle(TableStyle([
                ('ALIGN', (0,0), (-1,-1), 'LEFT'),
                ('VALIGN', (0,0), (-1,-1), 'TOP'),
                ('LEFTPADDING', (0,0), (-1,-1), 4),
                ('RIGHTPADDING', (0,0), (-1,-1), 4),
                ('TOPPADDING', (0,0), (-1,-1), 3),
                ('BOTTOMPADDING', (0,0), (-1,-1), 3),
                ('INNERGRID', (0,0), (-1,-1), 0.25, rl_colors.HexColor('#E5E7EB')),
                ('BOX', (0,0), (-1,-1), 0.5, rl_colors.HexColor('#D1D5DB')),
                ('BACKGROUND', (0,0), (-1,0), rl_colors.HexColor('#F9FAFB')),
            ]))
            out.append(table)
            i = j + 1
            continue

        # ---- Spacers (treat soft lineblocks) ----
        if t.type == "softbreak":
            out.append(Spacer(0, 2))
            i += 1
            continue

        # Fallback: advance to avoid infinite loop
        i += 1

    # Flush any pending special accumulators
    if collecting_markinfo:
        _maybe_emit_markinfo_table(markinfo_pairs, out, styles)

    return out