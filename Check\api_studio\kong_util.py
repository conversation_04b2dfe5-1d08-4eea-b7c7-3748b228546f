from fastapi import Request

# The request header sent by Kong is: 
# {'via': '2 kong/3.9.1', 'host': 'tro_app_api:5089', 'connection': 'keep-alive', 'x-forwarded-for': '*************', 
# 'x-forwarded-proto': 'https', 'x-forwarded-host': 'api2.maidalv.com', 'x-forwarded-port': '8443', 'x-forwarded-path': '/check_api', 
# 'x-real-ip': '*************', 'x-kong-request-id': 'fd30de63f8632447f0936b5163d7ce79', 'content-length': '182647', 
# 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36', 
# 'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"', 'content-type': 'application/json', 
# 'apikey': '37457-48774-8887-1882', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'sec-gpc': '1', 'accept-language': 'en-US,en;q=0.9', 
# 'origin': 'https://api2.maidalv.com', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 
# 'referer': 'https://api2.maidalv.com/api_studio', 'accept-encoding': 'gzip, deflate, br, zstd', 'priority': 'u=1, i', 
# 'x-consumer-id': 'f56684ba-cb94-495e-9bdd-4f30a4386728', 'x-consumer-custom-id': '1000', 'x-consumer-username': 'TestSerge', 
# 'x-credential-identifier': 'ee4436a0-4b07-44c3-96f3-414c84662217'})

def get_consumer(request: Request):
    """
    Extracts consumer information from request headers set by Kong.

    Args:
        request: The FastAPI request object.

    Returns:
        A dictionary containing the consumer's username, id, and api_key,
        or None if the headers are not present.
    """
    username = request.headers.get("x-consumer-username")
    consumer_id = request.headers.get("x-consumer-custom-id", "0000")
    api_key = request.headers.get("apikey")

    if not all([username, consumer_id, api_key]):
        return None

    return {
        "username": username,
        "id": consumer_id,
        "api_key": api_key,
    }