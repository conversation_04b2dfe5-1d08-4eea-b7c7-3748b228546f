import json, os, time, asyncio, queue, threading, sqlite3 # Added queue back for queue.Empty and sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
from flask import jsonify, request, abort, Response
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch, save_df_to_feather
import ui_controllers.cache_manager as cache_manager
from logdata import log_message, db_path as logdata_db_path # Import log_message and db_path
from Fixes_OneOff.Plaintiffs_Related.Plaintiff_Clean_Up import improve_single_plaintiff_name as process_plaintiff_improvement
# Removed: from DatabaseManagement.Connections import get_gz_connection
from ui_controllers.queue_manager import enqueue_case_processing_job, get_progress_queue_for_case, get_queue_status # Import from new queue_manager
import traceback

# Import background task processing functions (adjust path if necessary)
# from Fixes_OneOff.Plaintiffs_Related.Plaintiff_Clean_Up import process_case_update, process_next_case_in_queue # Example if moved
# Assuming these are defined later in this file for now

# --- API Routes ---

def _apply_filters_to_dataframe(df, args, plaintiff_df=None):
    """
    Applies filters to a DataFrame based on the provided arguments.
    This function encapsulates the filtering logic.
    """
    filtered_df = df.copy() # Work on a copy to avoid modifying original DataFrame

    case_id_param = args.get('case_id', '')
    case_number = args.get('case_number', '')
    plaintiff_name = args.get('plaintiff_name', '')
    plaintiff_id = args.get('plaintiff_id', '')
    case_types = args.getlist('case_type[]')
    picture_type = args.get('picture_type', '')
    validation_status = args.get('validation_status', '')
    ip_source = args.get('ip_source', '')
    date_from = args.get('date_from', '')
    date_to = args.get('date_to', '')
    update_time_from = args.get('update_time_from', '')
    update_time_to = args.get('update_time_to', '')

    if case_id_param:
        try:
            case_id_int = int(case_id_param)
            if 'id' in filtered_df.columns:
                filtered_df['id'] = pd.to_numeric(filtered_df['id'], errors='coerce')
            filtered_df = filtered_df[filtered_df['id'] == case_id_int]
        except ValueError:
            filtered_df = filtered_df[filtered_df['id'] == -1]
            log_message(f"Invalid case_id parameter received: {case_id_param}", level="WARNING")
    else:
        if case_number:
            if 'id' in filtered_df.columns:
                filtered_df['id'] = pd.to_numeric(filtered_df['id'], errors='coerce')
            try:
                case_id_search = int(case_number)
                id_filter = (filtered_df['id'] == case_id_search) if case_id_search > 0 else pd.Series([False] * len(filtered_df), index=filtered_df.index)
            except ValueError:
                id_filter = pd.Series([False] * len(filtered_df), index=filtered_df.index)
            docket_filter = filtered_df['docket'].astype(str).str.contains(case_number, case=False, na=False)
            filtered_df = filtered_df[id_filter | docket_filter]
        if plaintiff_name:
            if "plaintiff_name" in filtered_df.columns:
                filtered_df = filtered_df[filtered_df['plaintiff_name'].str.contains(plaintiff_name, case=False, na=False)]
            elif plaintiff_df is not None:
                plaintiff_id = plaintiff_df[plaintiff_df['plaintiff_name'].str.contains(plaintiff_name, case=False, na=False)]['id'].values
                filtered_df = filtered_df[filtered_df['plaintiff_id'].isin(plaintiff_id)]
        if plaintiff_id:
            try:
                plaintiff_id_int = int(float(plaintiff_id))  
                # Convert database values to int, handling NaN values
                plaintiff_id_numeric = pd.to_numeric(filtered_df['plaintiff_id'], errors='coerce')
                filtered_df = filtered_df[plaintiff_id_numeric == plaintiff_id_int]
            except (ValueError, TypeError):
                # If conversion fails, fall back to string comparison
                filtered_df = filtered_df[filtered_df['plaintiff_id'].astype(str) == plaintiff_id]

        if validation_status:
            filtered_df = filtered_df[filtered_df['validation_status'] == validation_status]

        if case_types:
            case_type_filter = filtered_df['nos_description'].str.contains('|'.join(case_types), case=False, na=False)
            filtered_df = filtered_df[case_type_filter]

        if date_from:
            try:
                date_from_dt = datetime.strptime(date_from, '%Y-%m-%d')
                filtered_df = filtered_df[pd.to_datetime(filtered_df['date_filed'], errors='coerce') >= date_from_dt]
            except ValueError:
                pass
        if date_to:
            try:
                date_to_dt = datetime.strptime(date_to, '%Y-%m-%d')
                filtered_df = filtered_df[pd.to_datetime(filtered_df['date_filed'], errors='coerce') <= date_to_dt]
            except ValueError:
                pass

        if update_time_from:
            try:
                update_time_from_dt = datetime.strptime(update_time_from, '%Y-%m-%d')
                filtered_df = filtered_df[pd.to_datetime(filtered_df['update_time'], errors='coerce') >= update_time_from_dt]
            except ValueError:
                pass
        if update_time_to:
            try:
                update_time_to_dt = datetime.strptime(update_time_to, '%Y-%m-%d')
                update_time_to_dt += pd.Timedelta(days=1)
                filtered_df = filtered_df[pd.to_datetime(filtered_df['update_time'], errors='coerce') < update_time_to_dt]
            except ValueError:
                pass

        if ip_source:
            def has_ip_source(row):
                if pd.isna(row.get('images_status')):
                    return False
                images_status = row.get('images_status', {})
                if not isinstance(images_status, dict):
                    return False
                for ip_type in ['trademark_status', 'patent_status', 'copyright_status']:
                    status = images_status.get(ip_type, {})
                    if status and status.get(ip_source, {}).get('count', 0) > 0:
                        return True
                return False
            filtered_df = filtered_df[filtered_df.apply(has_ip_source, axis=1)]

        if picture_type:
            if picture_type == 'no_ip':
                def has_no_ip(images_data):
                    if pd.isna(images_data):
                        return True
                    try:
                        return (not images_data.get('trademarks', {}) and
                                not images_data.get('patents', {}) and
                                not images_data.get('copyrights', {}))
                    except:
                        return False
                filtered_df = filtered_df[filtered_df['images'].apply(has_no_ip)]
            else:
                def has_picture_type(images_data):
                    if pd.isna(images_data):
                        return False
                    try:
                        return bool(images_data.get(picture_type, {}))
                    except:
                        return False
                filtered_df = filtered_df[filtered_df['images'].apply(has_picture_type)]
    return filtered_df

def get_cases_with_filters():
    try: 
        
        """API endpoint to get filtered case data using cached DataFrames."""

        # Get query parameters
        case_id_param = request.args.get('case_id', '')
        sort_by = request.args.get('sort_by', 'date_filed')
        sort_order = request.args.get('sort_order', 'desc')
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))
        refresh_all = request.args.get('refresh_all', 'false').lower() == 'true'
        refresh_selection = request.args.get('refresh_selection', 'false').lower() == 'true'

        # Handle refresh logic first
        if refresh_all:
            log_message("Full data refresh requested.", level="INFO")
            if not cache_manager.refresh_cached_data(force=True):
                log_message("Failed to perform full data refresh.", level="ERROR")
                return jsonify({"error": "Failed to load data cache. Please try again later."}), 500
        elif refresh_selection:
            log_message("Selective data refresh requested.", level="INFO")
            # Apply current filters to the cached_cases_df to get the selection
            # Use a copy of request.args for the helper function to avoid modifying the original
            try:
                filtered_for_refresh_df = _apply_filters_to_dataframe(cache_manager.cached_cases_df, request.args.copy(), cache_manager.cached_plaintiff_df)
            except Exception as e:
                log_message(f"Failed to apply filters to DataFrame. Error: {e}, trackback: {traceback.format_exc()}", level="ERROR")
                return jsonify({"error": "Failed to apply filters to data. Please try again later."}), 500
            case_ids_to_refresh = filtered_for_refresh_df['id'].tolist()
            
            where_clause = None
            if len(case_ids_to_refresh) > 0:
                if len(case_ids_to_refresh) < 200:
                    # Construct WHERE clause for specific case_ids
                    case_ids_str = ','.join(map(str, case_ids_to_refresh))
                    where_clause = f"id IN ({case_ids_str})"
                    
                    # Get max create_time from the filtered selection
                    max_create_time = filtered_for_refresh_df['create_time'].max()
                    if pd.notna(max_create_time):
                        # Ensure max_create_time is a datetime object for comparison
                        if isinstance(max_create_time, pd.Timestamp):
                            max_create_time_str = max_create_time.strftime('%Y-%m-%d %H:%M:%S')
                        else: # Assume it's already a string or can be converted
                            max_create_time_str = str(max_create_time)
                        
                        # Add condition to fetch newer cases based on create_time
                        where_clause += f" OR create_time > '{max_create_time_str}'"
                    
                    log_message(f"Refreshing selection with specific case_ids and newer cases. Where clause: {where_clause}", level="INFO")
                else:
                    # If selection is too large, perform a full refresh
                    log_message("Selection too large for targeted refresh, performing full refresh.", level="INFO")
                    where_clause = None # No specific where clause for full refresh

            if not cache_manager.refresh_selected_cached_data(target_table='tb_case', where_clause=where_clause):
                log_message("Failed to perform selective data refresh.", level="ERROR")
                return jsonify({"error": "Failed to load data cache. Please try again later."}), 500
        else:
            # Default behavior: Load data without forcing a refresh
            if not cache_manager.refresh_cached_data(force=False):
                log_message("Failed to load data cache (no refresh requested).", level="ERROR")
                return jsonify({"error": "Failed to load data cache. Please try again later."}), 500

        # Check if cache loading failed (redundant check, but safe)
        if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
            log_message("Data cache is unavailable in get_cases_with_filters.", level="ERROR")
            return jsonify({"error": "Data cache is unavailable."}), 500

        # Merge data using the copies
        merged_df = pd.merge(cache_manager.cached_cases_df, cache_manager.cached_plaintiff_df,
                            how='left', left_on='plaintiff_id', right_on='id',
                            suffixes=('', '_plaintiff'))

        # Apply filters using the helper function
        merged_df = _apply_filters_to_dataframe(merged_df, request.args)

        # Determine the actual column to sort by and prepare it if necessary
        sort_column_internal = 'date_filed' # Default
        if sort_by == 'pictures':
            def count_ip_items(image_data):
                if pd.isna(image_data):
                    return 0
                current_image_dict = image_data
                if isinstance(image_data, str):
                    try:
                        current_image_dict = json.loads(image_data)
                    except json.JSONDecodeError:
                        return 0 # Or log error
                if not isinstance(current_image_dict, dict):
                    return 0
                count = 0
                count += len(current_image_dict.get('trademarks', {}))
                count += len(current_image_dict.get('patents', {}))
                count += len(current_image_dict.get('copyrights', {}))
                return count

            merged_df['_image_count'] = merged_df['images'].apply(count_ip_items)
            sort_column_internal = '_image_count'
        elif sort_by in ['date_filed', 'update_time', 'plaintiff_name', 'plaintiff_id', 'docket', 'court']:
            sort_column_internal = sort_by

        # Sort data if merged_df is not empty
        if not merged_df.empty:
            effective_sort_col = None
            if sort_column_internal in merged_df.columns:
                effective_sort_col = sort_column_internal
            elif 'date_filed' in merged_df.columns: # Fallback to 'date_filed' if primary choice isn't there
                log_message(f"Warning: Sort column '{sort_column_internal}' not found. Defaulting to 'date_filed'.", level="WARNING")
                effective_sort_col = 'date_filed'
            else:
                log_message(f"Warning: Sort column '{sort_column_internal}' and default 'date_filed' not found in non-empty DataFrame. Skipping sort.", level="WARNING")

            if effective_sort_col:
                # Handle potential NaT values in date columns before sorting
                if pd.api.types.is_datetime64_any_dtype(merged_df[effective_sort_col]):
                    fill_value = pd.Timestamp.min if sort_order.lower() == 'asc' else pd.Timestamp.max
                    merged_df[effective_sort_col] = merged_df[effective_sort_col].fillna(fill_value)
                elif merged_df[effective_sort_col].dtype == 'object': # Handle NaN in string/object columns
                    merged_df[effective_sort_col] = merged_df[effective_sort_col].fillna('')
                
                # No longer check case_id_param here, as filtering is done by _apply_filters_to_dataframe
                merged_df = merged_df.sort_values(by=effective_sort_col, ascending=(sort_order.lower() == 'asc'), na_position='last')
        else:
            log_message("DataFrame is empty after filtering, skipping sort.", level="INFO")

        # Count total results
        total_count = len(merged_df)

        # Paginate
        merged_df = merged_df.iloc[offset:offset+limit]

        # Replace NaN with None for JSON compatibility, which can occur from merges
        merged_df = merged_df.replace({np.nan: None})
        
        # Drop the temporary image count column if it was added and exists
        if '_image_count' in merged_df.columns and sort_column_internal == '_image_count':
            merged_df = merged_df.drop(columns=['_image_count'])

        # Prepare proposed plaintiff names from the reviews table
        proposed_names = {}
        if cache_manager.cached_plaintiff_reviews is not None and not cache_manager.cached_plaintiff_reviews.empty:
            # Create a dictionary of case_id -> proposed_name for cases that have a non-empty proposed name
            for _, row in cache_manager.cached_plaintiff_reviews.iterrows():
                if pd.notna(row.get('proposed_name')) and row.get('proposed_name') != '':
                    proposed_names[str(row.get('case_id'))] = row.get('proposed_name')

        # Format results
        result_data = []
        for _, row in merged_df.iterrows():
            # Format court name - remove "District Court"
            court = row.get('court', '')
            if court and isinstance(court, str):
                court = court.replace(' District Court', '')

            case_data = {
                'id': row.get('id'),
                'docket': row.get('docket'),
                'court': court,
                'plaintiff_name': row.get('plaintiff_name'),
                'plaintiff_id': row.get('plaintiff_id'),
                'nos_description': row.get('nos_description'),
                'class_code': row.get('class_code'),
                'date_filed': row.get('date_filed'),
                'plaintiff_overview': row.get('plaintiff_overview'),
                'aisummary': row.get('aisummary'),
                'validation_status': row.get('validation_status', ''),
                'images_status': row.get('images_status', {}),
                'update_time': row.get('update_time'), # Add update_time to case data
                'ln_url': row.get('ln_url') # Add LexisNexis URL
            }

            # Process images
            images = {'trademarks': [], 'patents': [], 'copyrights': []}
            if not pd.isna(row.get('images')):
                try:
                    images_data = row.get('images')  # Already decompressed

                    # Process trademarks
                    for key, value in images_data.get('trademarks', {}).items():
                        if isinstance(value, dict):
                            image_data = {
                                'image': key,
                                'trademarkText': value.get('trademark_text'),
                                'regNo': value.get('reg_no'),
                                'intClsList': value.get('int_cls_list'),
                                'fullFilename': value.get('full_filename')
                            }
                            images['trademarks'].append(image_data)

                    # Process patents - group by full_filename (certificate)
                    patent_certificates = {}  # Group patents by certificate filename
                    for key, value in images_data.get('patents', {}).items():
                        if isinstance(value, dict):
                            full_filename_list = value.get('full_filename', [])
                            if full_filename_list and len(full_filename_list) > 0:
                                certificate_filename = full_filename_list[0]  # Get the certificate filename

                                # If this certificate hasn't been processed yet, create entry
                                if certificate_filename not in patent_certificates:
                                    patent_number = value.get('patent_number')
                                    inventors = None
                                    applicant = None
                                    assignee = None

                                    if patent_number and cache_manager.cached_tro_patents_df is not None and not cache_manager.cached_tro_patents_df.empty:
                                        # Assuming 'document_id' is the column in cached_tro_patents_df corresponding to patent_number
                                        # Adjust type comparison if necessary (e.g., str(patent_number))
                                        patent_info = cache_manager.cached_tro_patents_df[cache_manager.cached_tro_patents_df['document_id'] == patent_number]
                                        if not patent_info.empty:
                                            patent_row = patent_info.iloc[0]
                                            # Fetch details from the patents DataFrame, provide defaults if columns don't exist
                                            inventors = patent_row.get('inventors')
                                            applicant = patent_row.get('applicant')
                                            assignee = patent_row.get('assignee')

                                    patent_certificates[certificate_filename] = {
                                        'image': certificate_filename,  # Use certificate filename as image
                                        'productName': value.get('product_name'),
                                        'inventors': inventors,
                                        'applicant': applicant,
                                        'assignee': assignee,
                                        'patentNumber': patent_number,
                                        'fullFilename': certificate_filename,
                                        'grouped_images': [] # Add new field to hold grouped images
                                    }
                                # Add the original image key to the list for this certificate
                                patent_certificates[certificate_filename]['grouped_images'].append(key)

                    # Add grouped patent certificates to images list
                    images['patents'] = list(patent_certificates.values())

                    # Process copyrights
                    for key, value in images_data.get('copyrights', {}).items():
                        if isinstance(value, dict):
                            image_data = {
                                'image': key,
                                'regNo': value.get('reg_no'),
                                'fullFilename': value.get('full_filename')
                            }
                            images['copyrights'].append(image_data)
                except Exception as e:
                    log_message(f"Error processing images for case {row.get('id')}: {e}", level="ERROR", exc_info=True)

                case_data.update(images)
                result_data.append(case_data)

        return jsonify({
            'total': total_count,
            'offset': offset,
            'limit': limit,
            'data': result_data,
            'last_refresh': cache_manager.last_refresh_time.isoformat() if cache_manager.last_refresh_time else None,
            'proposed_names': proposed_names  # Add proposed plaintiff names to the response
        })
    except Exception as e:
        log_message(f"Error occurred while processing request: {e}, trace: {traceback.format_exc()}", level="ERROR", exc_info=True)

def update_validation_status():
    """API endpoint to update validation status of a case"""
    # Get request data
    data = request.get_json()
    if not data or 'case_id' not in data or 'validation_status' not in data:
        return jsonify({'error': 'Missing required fields (case_id, validation_status)'}), 400

    try:
        case_id = int(data['case_id'])
        validation_status = data['validation_status']
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid case ID format or validation status'}), 400

    # Validate status values
    valid_statuses = ['validated', 'review_required', 'failed', '']
    if validation_status not in valid_statuses:
        return jsonify({'error': f'Invalid validation status. Must be one of: {valid_statuses}'}), 400

    # Ensure cases data is loaded
    if cache_manager.cached_cases_df is None:
        log_message("Cached cases data is None in update_validation_status, attempting to load.", level="WARNING")
        if not cache_manager.refresh_cached_data(force=True): # Force refresh if cache is empty
            return jsonify({'error': 'Case data not available'}), 500

    # Find the index of the case by ID
    case_indices = cache_manager.cached_cases_df.index[cache_manager.cached_cases_df['id'] == case_id].tolist()

    if not case_indices:
        log_message(f"update_validation_status: case not found for case_id: {case_id}", level="WARNING")
        return jsonify({'error': 'Case not found'}), 404

    # Assume only one case matches the ID
    idx = case_indices[0]

    # Update the validation status and update_time in the cached DataFrame
    cache_manager.cached_cases_df.loc[idx, 'validation_status'] = validation_status
    current_time = pd.Timestamp.now(tz='UTC') # Use UTC for consistency
    cache_manager.cached_cases_df.loc[idx, 'update_time'] = current_time

    try:
        # Get required fields for database update
        row_df = cache_manager.cached_cases_df.loc[[idx]]
        required_fields = ['id', 'plaintiff_id', 'docket', 'court', 'date_filed', 'title', 'validation_status'] # These field cannot be null in database. Upsert first tries insert then it fail then update. So these field must be there to try the insert.
        update_df = row_df[required_fields]

        # Update database
        insert_and_update_df_to_GZ_batch(update_df, "tb_case", "id")
        log_message(f"update_validation_status: updated case {case_id} to '{validation_status}' in database.", level="INFO")

        # Save to feather file, non blocking (for speed)
        # OLD: save_df_to_feather(cache_manager.cached_cases_df, "tb_case")
        def save_feather_background():
            save_df_to_feather(cache_manager.cached_cases_df, "tb_case")

        # Start a background thread to save the feather file
        thread = threading.Thread(target=save_feather_background)
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'case_id': case_id,
            'validation_status': validation_status,
            'update_time': current_time.strftime('%Y-%m-%d')
        })




    except Exception as e:
        log_message(f"Error updating validation status for case {case_id}: {e}", level="ERROR")
        return jsonify({
            'success': False,
            'error': f'Failed to update validation status: {str(e)}'
        }), 500


# --- Background Task Handling Routes ---

def update_existing_case():
    """API endpoint to queue an update for an existing case."""

    data = request.get_json()
    if not data or 'case_id' not in data:
        return jsonify({'error': 'Missing required fields (case_id)'}), 400

    try:
        case_id = int(data['case_id'])
    except ValueError:
        return jsonify({'error': 'Invalid case ID format'}), 400

    # file_type determines if LexisNexis is used and which files to get. Empty means use DocketBird.
    file_type = data.get('file_type', '')
    # update_steps determines if *any* step update logic (Lexis or DocketBird) should run.
    update_steps = data.get('update_steps', True) # Default to True if not provided
    force_docket_refresh = data.get('force_docket_refresh', False)

    # Construct processing_options for this type of task.
    processing_options_for_update = {
        "operation_type": "case_data_update",  # To differentiate from full reprocess
        'file_type': file_type,
        'update_steps': update_steps,
        'force_docket_refresh': force_docket_refresh
        # Add other flags as needed to guide Alerts.ReprocessCases.reprocess_cases
    }

    success, message, queue_pos = enqueue_case_processing_job(
        case_id=case_id,
        processing_options=processing_options_for_update,
        initiated_by="VisualizerAPI_Update"
    )

    if success:
        return jsonify({'success': True, 'message': "Update queued", 'case_id': case_id, 'queue_position': queue_pos})
    else:
        # Message from enqueue_case_processing_job is "Case X already in queue."
        return jsonify({'success': True, 'message': message, 'case_id': case_id, 'queued': True})




def stream_update_status(case_id):
    """Stream update status for a specific case"""
    case_id = int(case_id)

    def generate():
        # Try to get/create it via queue_manager, as it might be created by enqueue
        q = get_progress_queue_for_case(case_id)
        if not q: # Should not happen if enqueue was called
            yield 'data: No update in progress for this case\n\n'
            return
        # Initial message
        yield 'data: Connecting to update stream...\n\n'

        # For dot progression
        empty_counter = 0

        # Keep checking the queue for new messages
        while True:
            try:
                # Non-blocking get with timeout
                message = q.get(block=True, timeout=1.0)

                if message == "DONE":
                    # End of updates
                    yield 'data: Update completed\n\n'
                    break

                # Reset dot counter when new message arrives
                empty_counter = 0
                yield f'data: {message}\n\n'

            except queue.Empty:
                # Add a dot every 5 seconds instead of a new line
                empty_counter += 1
                if empty_counter == 5:
                    yield 'data: .'
                    empty_counter = 0

            except Exception as e:
                yield f'data: Error: {str(e)}\n\n'
                break

    return Response(generate(), mimetype='text/event-stream')


def add_to_reprocess_queue():
    """API endpoint to reprocess a case with new processing options structure."""

    data = request.get_json()
    if not data or 'case_id' not in data or 'processing_options' not in data:
        return jsonify({'error': 'Missing required fields (case_id, processing_options)'}), 400

    try:
        case_id = int(data['case_id'])
        processing_options = data['processing_options']
        if not isinstance(processing_options, dict):
             raise TypeError("processing_options must be a dictionary")
    except (ValueError, TypeError) as e:
        return jsonify({'error': f'Invalid case ID format or processing options: {str(e)}'}), 400

    # Ensure 'operation_type' is present if not provided, defaulting to 'full_reprocess'
    if 'operation_type' not in processing_options:
        processing_options['operation_type'] = 'full_reprocess'

    success, message, queue_pos = enqueue_case_processing_job(
        case_id=case_id,
        processing_options=processing_options,
        initiated_by="Visualizer"
    )

    if success:
        return jsonify({'success': True, 'message': "Reprocess queued", 'case_id': case_id, 'queue_position': queue_pos})
    else:
        # Message from enqueue_case_processing_job is "Case X already in queue."
        return jsonify({'success': True, 'message': message, 'case_id': case_id, 'queued': True})



def stream_reprocess_status(case_id):
    """Stream reprocess status for a specific case"""
    case_id = int(case_id)

    def generate():
        q = get_progress_queue_for_case(case_id) # Attempt to get/create
        if not q:
            yield 'data: No reprocess in progress for this case\n\n'
            return
        # Initial message
        yield 'data: Connecting to reprocess stream...\n\n'

        # For dot progression
        empty_counter = 0

        # Keep checking the queue for new messages
        while True:
            try:
                # Non-blocking get with timeout
                message = q.get(block=True, timeout=1.0)

                if message == "DONE":
                    # End of updates
                    yield 'data: Reprocess completed\n\n'
                    break

                # Reset dot counter when new message arrives
                empty_counter = 0
                yield f'data: {message}\n\n'

            except queue.Empty:
                # Add a dot every 5 seconds instead of a new line
                empty_counter += 1
                if empty_counter == 5:
                    yield 'data: .'
                    empty_counter = 0

            except Exception as e:
                yield f'data: Error: {str(e)}\n\n'
                break

    return Response(generate(), mimetype='text/event-stream')

def init_visualizer_routes(app):
    """Initialize the visualizer API routes"""
    app.add_url_rule('/api/cases', 'get_cases_with_filters', get_cases_with_filters, methods=['GET'])
    app.add_url_rule('/api/cases/validation', 'update_validation_status', update_validation_status, methods=['POST'])
    app.add_url_rule('/api/cases/update', 'update_existing_case', update_existing_case, methods=['POST'])
    app.add_url_rule('/api/cases/update/status/<case_id>', 'stream_update_status', stream_update_status, methods=['GET'])
    app.add_url_rule('/api/cases/reprocess', 'reprocess_case', add_to_reprocess_queue, methods=['POST'])
    app.add_url_rule('/api/cases/reprocess/status/<case_id>', 'stream_reprocess_status', stream_reprocess_status, methods=['GET'])
    app.add_url_rule('/api/cases/<case_id>/logs', 'get_case_logs', get_case_logs, methods=['GET']) # New route for historical logs
    app.add_url_rule('/api/queue/status', 'api_get_queue_status', api_get_queue_status, methods=['GET']) # New route for queue status
    app.add_url_rule('/api/cases/<case_id>/steps', 'get_case_steps', get_case_steps, methods=['GET'])
    app.add_url_rule('/api/cases/improve-plaintiff', 'improve_single_plaintiff_name', improve_single_plaintiff_name, methods=['POST'])

    return app


def get_case_steps(case_id):
    """API endpoint to get steps for a specific case"""
    from DatabaseManagement.Connections import get_gz_connection
    import pandas as pd

    try:
        case_id = int(case_id)

        with get_gz_connection() as gz_connection:
            # Get only the necessary columns
            df_steps = pd.read_sql_query(
                f"SELECT step_nb, step_date_filed, proceeding_text, proceeding_text_cn, files_downloaded, files_failed, update_time "
                f"FROM tb_case_steps WHERE case_id = {case_id}",
                gz_connection
            )

            # Replace NaN values with None (will become null in JSON)
            df_steps = df_steps.replace({np.nan: None})

            # Convert step_nb to numeric for proper sorting
            df_steps['step_nb_numeric'] = pd.to_numeric(df_steps['step_nb'], errors='coerce')

            # Sort by the numeric version of step_nb
            df_steps = df_steps.sort_values('step_nb_numeric').drop('step_nb_numeric', axis=1)

            # Convert to list of dictionaries for JSON response
            steps = df_steps.to_dict(orient='records')

            return jsonify({
                'success': True,
                'case_id': case_id,
                'steps': steps
            })
    except Exception as e:
        log_message(f"Error getting steps for case {case_id}: {e}", level="ERROR", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def improve_single_plaintiff_name():
    """API endpoint to improve plaintiff name for a single case"""
    data = request.get_json()
    if not data or 'case_id' not in data:
        return jsonify({'error': 'Missing case_id parameter'}), 400

    try:
        case_id_int = int(data['case_id'])
    except ValueError:
        return jsonify({'error': 'Invalid case ID format'}), 400

    try:
        # Call the imported processing function
        # Pass the main cached dataframes - assume the function modifies them or handles updates
        result = process_plaintiff_improvement(case_id_int, cache_manager.cached_cases_df,
            cache_manager.cached_plaintiff_df, cache_manager.cached_plaintiff_reviews)

        # The process_plaintiff_improvement function (which calls process_weak_cases)
        # modifies cache_manager.cached_plaintiff_reviews in place and saves to DB.
        # Now, save the in-memory cache_manager.cached_plaintiff_reviews to its feather file.
        if cache_manager.cached_plaintiff_reviews is not None:
            try:
                save_df_to_feather(cache_manager.cached_plaintiff_reviews, "tbi_case_plaintiff_name_review")
            except Exception as e_feather:
                log_message(f"Error saving plaintiff reviews cache to feather: {e_feather}", level="ERROR")

        # Fetch the latest review status from the *refreshed* cache
        updated_review = {}
        if cache_manager.cached_plaintiff_reviews is not None and not cache_manager.cached_plaintiff_reviews.empty:
             review_row = cache_manager.cached_plaintiff_reviews[cache_manager.cached_plaintiff_reviews['case_id'] == case_id_int]
             if not review_row.empty:
                 updated_review = review_row.iloc[0].to_dict()


        return jsonify({
            'success': True,
            'message': result.get('message', 'Plaintiff name improvement process initiated/completed'), # Use message from result if available
            'proposed_name': updated_review.get('proposed_name'), # Get updated value from cache
            'method_info': updated_review.get('method_info'), # Get updated value from cache
            'status': updated_review.get('status', 'unknown') # Return current status from cache
        })
    except Exception as e:
        log_message(f"Error calling improve_single_plaintiff_name API for case {case_id_int}: {e}", level="CRITICAL")
        # Attempt to refresh cache even on error
        try: cache_manager.refresh_cached_data(force=True)
        except: pass
        return jsonify({'success': False, 'error': f'An unexpected server error occurred: {str(e)}'}), 500

def api_get_queue_status():
    """API endpoint to get the current status of the processing queue."""
    try:
        status = get_queue_status()
        return jsonify(status), 200
    except Exception as e:
        log_message(f"Error getting queue status: {e}", level="ERROR", exc_info=True)
        return jsonify({"error": "Failed to retrieve queue status", "details": str(e)}), 500

def get_case_logs(case_id):
    """API endpoint to get historical logs for a specific case (where case_id is the run_id)."""
    try:
        run_id = int(case_id) # case_id from URL is the run_id for reprocessing tasks
        
        conn = sqlite3.connect(logdata_db_path) 
        conn.row_factory = sqlite3.Row # To access columns by name
        c = conn.cursor()
        
        # Fetch logs ordered by timestamp
        c.execute('''
            SELECT timestamp, level, message 
            FROM logs 
            WHERE run_id = ? 
            ORDER BY timestamp ASC
        ''', (run_id,))
        logs = [dict(row) for row in c.fetchall()]
        conn.close()
        
        return jsonify({'success': True, 'case_id': run_id, 'logs': logs})
        
    except ValueError:
        return jsonify({'success': False, 'error': 'Invalid case ID format'}), 400
    except Exception as e:
        log_message(f"Error getting logs for case {case_id}: {e}", level="ERROR", exc_info=True)
        return jsonify({'success': False, 'error': f'An unexpected server error occurred: {str(e)}'}), 500
