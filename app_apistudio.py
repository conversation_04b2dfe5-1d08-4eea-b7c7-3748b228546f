import os
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request, UploadFile
from fastapi.responses import HTMLResponse, JSONResponse
import hashlib
import time
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from arq import create_pool
import httpx
from Check.ARQ.worker import WorkerSettings

# Import the new APIRouter modules
from Check.api_studio.check_routes import check_bp as check_router
from Check.api_studio.history_routes import history_router
from Check.api_studio.admin_routes import admin_router
from Check.api_studio.embedding_routes import embedding_router

# Import LegalOpinion backend
from LegalOpinion.form_processor import LegalOpinionFormProcessor

from prometheus_fastapi_instrumentator import Instrumentator
from prometheus_client import multiprocess, CollectorRegistry
from Check.RAG.qdrant_search import init_aiohttp_session, close_aiohttp_session
from Check.Do_Check_Download import init_download_session, close_download_session

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

def validate_field(field_name: str, field_value: str) -> dict:
    """Validate individual form fields for legal opinion with enhanced security"""
    import re

    # Input sanitization
    if not field_value:
        field_value = ""

    # Length limits for security
    if len(field_value) > 1000:
        return {"valid": False, "message": "Input too long"}

    # Check for potential XSS/injection attempts
    dangerous_patterns = [
        r'<script[^>]*>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>',
        r'<object[^>]*>',
        r'<embed[^>]*>'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, field_value, re.IGNORECASE):
            return {"valid": False, "message": "Invalid characters detected"}

    if field_name == 'storeName':
        if not field_value or len(field_value) < 2:
            return {"valid": False, "message": "Store name must be at least 2 characters"}
        if len(field_value) > 120:
            return {"valid": False, "message": "Store name must be less than 120 characters"}
        if re.search(r'[😀-�]', field_value):
            return {"valid": False, "message": "Emoji characters are not allowed"}
        # Check for reasonable business name patterns
        if re.search(r'^[a-zA-Z0-9\s\.,&\-\'\"()]+$', field_value):
            return {"valid": True, "message": "Valid store name"}
        else:
            return {"valid": False, "message": "Store name contains invalid characters"}

    elif field_name == 'email':
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, field_value):
            return {"valid": False, "message": "Please enter a valid email address"}
        if len(field_value) > 254:  # RFC 5321 limit
            return {"valid": False, "message": "Email address too long"}
        return {"valid": True, "message": "Valid email address"}

    elif field_name == 'phone':
        # E.164 format validation with enhanced checks
        phone_regex = r'^\+[1-9]\d{1,14}$'
        if not re.match(phone_regex, field_value):
            return {"valid": False, "message": "Please enter phone in E.164 format (+1234567890)"}
        # Additional check for reasonable length
        if len(field_value) < 8 or len(field_value) > 16:
            return {"valid": False, "message": "Phone number length invalid"}
        return {"valid": True, "message": "Valid phone number"}

    elif field_name == 'asin':
        if not field_value:
            return {"valid": True, "message": "ASIN is optional"}
        asin_regex = r'^[A-Z0-9]{10}$'
        if not re.match(asin_regex, field_value):
            return {"valid": False, "message": "ASIN must be exactly 10 characters (A-Z, 0-9)"}
        # Check for common ASIN patterns (starts with B for most products)
        if field_value.startswith('B') or field_value.startswith('A'):
            return {"valid": True, "message": "Valid ASIN format"}
        else:
            return {"valid": True, "message": "ASIN format valid (verify it's correct)"}

    elif field_name == 'patentNumber':
        # Enhanced patent number validation
        patent_regex = r'^(USD?)?\d{6,7}S?$'
        if not re.match(patent_regex, field_value):
            return {"valid": False, "message": "Enter valid patent number (e.g., USD1234567S or 1234567)"}

        # Extract number part for additional validation
        number_part = re.sub(r'[USD]', '', field_value)
        if len(number_part) < 6 or len(number_part) > 7:
            return {"valid": False, "message": "Patent number must be 6-7 digits"}

        return {"valid": True, "message": "Valid patent number format"}

    elif field_name == 'url':
        if not field_value:
            return {"valid": True, "message": "URL is optional"}
        url_regex = r'^https?://[^\s/$.?#].[^\s]*$'
        if not re.match(url_regex, field_value):
            return {"valid": False, "message": "Please enter a valid URL starting with http:// or https://"}
        if len(field_value) > 2048:  # Common URL length limit
            return {"valid": False, "message": "URL too long"}
        return {"valid": True, "message": "Valid URL format"}

    else:
        return {"valid": True, "message": "Field validated"}

def validate_file_security(file: UploadFile) -> dict:
    """Enhanced file validation with security checks"""

    # File size check (25MB limit)
    MAX_FILE_SIZE = 25 * 1024 * 1024
    if hasattr(file, 'size') and file.size > MAX_FILE_SIZE:
        return {"valid": False, "message": f"File {file.filename} exceeds 25MB limit"}

    # Filename security check
    if not file.filename:
        return {"valid": False, "message": "File must have a name"}

    # Check for directory traversal attempts
    if '..' in file.filename or '/' in file.filename or '\\' in file.filename:
        return {"valid": False, "message": "Invalid filename"}

    # File extension whitelist
    allowed_extensions = {
        'complaint': ['.pdf', '.eml', '.msg', '.html', '.png', '.jpg', '.jpeg'],
        'patenteeLetters': ['.pdf', '.docx', '.png', '.jpg', '.jpeg'],
        'productImages': ['.png', '.jpg', '.jpeg'],
        'commercializationDocs': ['.pdf', '.png', '.jpg', '.jpeg'],
        'clientPatentDocs': ['.pdf'],
        'priorArtFiles': ['.pdf', '.png', '.jpg', '.jpeg'],
        'functionalityDocs': ['.pdf']
    }

    file_ext = '.' + file.filename.split('.')[-1].lower() if '.' in file.filename else ''

    # MIME type validation
    allowed_mimes = {
        '.pdf': 'application/pdf',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.html': 'text/html',
        '.eml': 'message/rfc822',
        '.msg': 'application/vnd.ms-outlook'
    }

    if file_ext in allowed_mimes:
        expected_mime = allowed_mimes[file_ext]
        if file.content_type and not file.content_type.startswith(expected_mime.split('/')[0]):
            return {"valid": False, "message": f"File type mismatch for {file.filename}"}

    return {"valid": True, "message": "File validation passed"}

@asynccontextmanager
async def lifespan(app: FastAPI):
   # Startup
   await init_aiohttp_session()
   await init_download_session()
   if os.getenv("DEBUG"):
       # In debug mode, run jobs inline without Redis
       arq_pool = await create_pool(job_serializer=None)
   else:
       arq_pool = await create_pool(WorkerSettings.redis_settings)
   app.state.arq_pool = arq_pool
   yield
   # Shutdown
   await close_aiohttp_session()
   await close_download_session()
   if hasattr(app.state, "arq_pool") and app.state.arq_pool:
       await app.state.arq_pool.close()

# Create FastAPI app
def create_app():
    app = FastAPI(lifespan=lifespan)
    # Configure Prometheus Instrumentator for multi-process mode
    if os.environ.get("DEBUG",0) != "1":
        registry = CollectorRegistry()
        os.environ.setdefault("PROMETHEUS_MULTIPROC_DIR", os.path.join(os.getcwd(), "prometheus_metrics"))
        print(f"Prometheus metrics directory: {os.environ['PROMETHEUS_MULTIPROC_DIR']}")
        multiprocess.MultiProcessCollector(registry)
        Instrumentator(registry=registry).instrument(app).expose(app)
        
    app.mount("/static", StaticFiles(directory="static"), name="static")
    templates = Jinja2Templates(directory="templates")
    
    templates.env.globals["url_path_for"] = app.url_path_for

    # Register Routers
    app.include_router(check_router, prefix="")
    app.include_router(history_router, prefix="/history")
    app.include_router(admin_router, prefix="/admin")
    app.include_router(embedding_router, prefix="")

    @app.get("/health")
    async def health_check():
        return {"status": "ok"}

    # UI Routes
    @app.get('/api_studio_lite', response_class=HTMLResponse)
    async def api_studio_lite_route(request: Request):
        return templates.TemplateResponse("api_studio_lite.html", {"request": request})

    @app.get('/api_studio', response_class=HTMLResponse)
    async def api_studio_route(request: Request):
        return templates.TemplateResponse("api_studio.html", {"request": request})

    @app.get('/api_studio_reverse_check', response_class=HTMLResponse)
    async def api_studio_reverse_check_route(request: Request):
        return templates.TemplateResponse("api_studio_reverse_check.html", {"request": request})

    @app.get('/documentation/{lang}', response_class=HTMLResponse)
    async def documentation_route(request: Request, lang: str):
        """
        Serves the documentation in the specified language.
        Defaults to English if the language is not supported.
        """
        supported_langs = ['en', 'zh']
        if lang not in supported_langs:
            lang = 'en'
        
        template_name = f'documentation_{lang}.html'
        return templates.TemplateResponse(template_name, {"request": request})

    @app.get('/check_history', response_class=HTMLResponse)
    async def check_history_route(request: Request):
        return templates.TemplateResponse("check_history.html", {"request": request})

    @app.get('/legal_opinion', response_class=HTMLResponse)
    async def legal_opinion_route(request: Request):
        return templates.TemplateResponse("legal_opinion.html", {"request": request})

    @app.post('/legal_opinion/validate_field')
    async def validate_legal_opinion_field(request: Request):
        """Validate individual form fields for legal opinion"""
        try:
            data = await request.json()
            field_name = data.get('field')
            field_value = data.get('value')

            # Field validation logic
            validation_result = validate_field(field_name, field_value)

            return JSONResponse(content=validation_result)
        except Exception as e:
            return JSONResponse(content={"valid": False, "message": str(e)}, status_code=400)

    @app.post('/legal_opinion/autosave')
    async def autosave_legal_opinion(request: Request):
        """Autosave legal opinion form data"""
        try:
            data = await request.json()
            # Here you would typically save to database or session
            # For now, just return success
            return JSONResponse(content={"status": "saved", "timestamp": "now"})
        except Exception as e:
            return JSONResponse(content={"status": "error", "message": str(e)}, status_code=400)

    @app.post('/legal_opinion/upload_file')
    async def upload_legal_opinion_file(request: Request):
        """Handle individual file uploads with security validation"""
        try:
            form = await request.form()
            category = form.get('category', '')
            uploaded_file = form.get('file')

            if not uploaded_file or not hasattr(uploaded_file, 'filename'):
                return JSONResponse(content={"valid": False, "message": "No file provided"}, status_code=400)

            # Validate file security
            validation_result = validate_file_security(uploaded_file)
            if not validation_result["valid"]:
                return JSONResponse(content=validation_result, status_code=400)

            # Read file content for additional validation
            file_content = await uploaded_file.read()
            await uploaded_file.seek(0)  # Reset file pointer

            # Virus scanning placeholder (implement with actual antivirus)
            if len(file_content) == 0:
                return JSONResponse(content={"valid": False, "message": "Empty file not allowed"}, status_code=400)

            # File type validation based on magic bytes
            file_signatures = {
                b'\x25\x50\x44\x46': 'pdf',  # PDF
                b'\x89\x50\x4E\x47': 'png',  # PNG
                b'\xFF\xD8\xFF': 'jpeg',     # JPEG
                b'\x50\x4B\x03\x04': 'zip',  # ZIP/DOCX
            }

            file_type_detected = None
            for signature, file_type in file_signatures.items():
                if file_content.startswith(signature):
                    file_type_detected = file_type
                    break

            # Generate secure filename
            import uuid
            import os
            file_extension = os.path.splitext(uploaded_file.filename)[1].lower()
            secure_filename = f"{uuid.uuid4()}{file_extension}"

            # Store file metadata (in production, save to secure storage)
            file_metadata = {
                "original_name": uploaded_file.filename,
                "secure_name": secure_filename,
                "size": len(file_content),
                "type": uploaded_file.content_type,
                "category": category,
                "upload_time": time.time(),
                "checksum": hashlib.sha256(file_content).hexdigest()
            }

            return JSONResponse(content={
                "valid": True,
                "message": "File uploaded successfully",
                "file_id": secure_filename,
                "metadata": file_metadata
            })

        except Exception as e:
            return JSONResponse(content={"valid": False, "message": f"Upload failed: {str(e)}"}, status_code=500)

    @app.post('/legal_opinion/submit')
    async def submit_legal_opinion(request: Request):
        """Submit complete legal opinion form using new LegalOpinion backend"""
        try:
            client_ip = request.client.host
            current_time = time.time()

            # Handle multipart form data with files
            form = await request.form()

            # Extract and validate JSON data
            form_data = form.get('data')
            if form_data:
                import json
                try:
                    form_data = json.loads(form_data)
                except json.JSONDecodeError:
                    return JSONResponse(content={"status": "error", "message": "Invalid form data"}, status_code=400)
            else:
                return JSONResponse(content={"status": "error", "message": "No form data provided"}, status_code=400)

            # Process uploaded files for the new backend
            uploaded_files = {}
            for key, value in form.items():
                if hasattr(value, 'filename') and key != 'data':  # It's a file and not the form data
                    # Validate file security using existing function
                    validation_result = validate_file_security(value)
                    if not validation_result["valid"]:
                        return JSONResponse(content={"status": "error", "message": validation_result["message"]}, status_code=400)

                    uploaded_files[key] = value

            # Initialize the LegalOpinion form processor
            processor = LegalOpinionFormProcessor()

            # Process the submission using the new backend
            result = await processor.process_submission(form_data, uploaded_files)

            if result["success"]:
                return JSONResponse(content={
                    "status": "success",
                    "message": "Legal opinion request submitted successfully",
                    "submission_id": result["submission_id"],
                    "timestamp": current_time,
                    "processing_time": result["processing_time"],
                    "workspace_path": result.get("workspace_created", False)
                })
            else:
                # Return error with details
                error_message = "Submission failed"
                if result["errors"]:
                    error_message = "; ".join(result["errors"])
                elif result["warnings"]:
                    error_message = "; ".join(result["warnings"])

                return JSONResponse(
                    content={
                        "status": "error",
                        "message": error_message,
                        "submission_id": result.get("submission_id"),
                        "details": {
                            "errors": result["errors"],
                            "warnings": result["warnings"],
                            "validation": result.get("validation", {})
                        }
                    },
                    status_code=400
                )

        except Exception as e:
            return JSONResponse(content={"status": "error", "message": f"Submission failed: {str(e)}"}, status_code=500)

    @app.get('/legal_opinion/status/{submission_id}')
    async def get_legal_opinion_status(submission_id: str):
        """Get the status of a legal opinion submission"""
        try:
            processor = LegalOpinionFormProcessor()
            status = processor.get_submission_status(submission_id)

            if status["exists"]:
                return JSONResponse(content={
                    "status": "success",
                    "submission_status": status
                })
            else:
                return JSONResponse(
                    content={"status": "error", "message": status.get("error", "Submission not found")},
                    status_code=404
                )
        except Exception as e:
            return JSONResponse(content={"status": "error", "message": f"Status check failed: {str(e)}"}, status_code=500)

    @app.post('/reverse_check_status')
    async def reverse_check_status_route(request: Request):
        """Route to relay reverse check status requests to the Qdrant API"""
        qdrant_api_url = os.getenv("QDRANT_API_URL")
        if not qdrant_api_url:
            return JSONResponse(content={"error": "QDRANT_API_URL environment variable not set"}, status_code=500)

        try:
            data = await request.json()
            # Pass through the headers from the original request.
            # We filter out host, as it's not a good practice to pass it on.
            headers = {
                name: value
                for name, value in request.headers.items()
                if name.lower() != "host"
            }
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{qdrant_api_url}/reverse_check_status",
                    json=data,
                    headers=headers,
                    timeout=30
                )
            return JSONResponse(content=response.json(), status_code=response.status_code)

        except httpx.RequestError as e:
            return JSONResponse(content={"error": f"Failed to connect to Qdrant API: {str(e)}"}, status_code=500)
        except Exception as e:
            return JSONResponse(content={"error": f"Server error: {str(e)}"}, status_code=500)
        
    return app

# Check API project
os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-a81ed7b3-cbf2-494a-a6ff-c00b21778891"

# app = create_app()

if __name__ == "__main__":
    # For debug purpose: run wsl for redis, desactivate the app=create_app() in the code + pip install uvicorn
    os.environ["DEBUG"] = "1"  # Set to "1" to enable debug mode
    app = create_app() # Create the app with the new environment variable  (deactivate the other one in the main code)
    import uvicorn, asyncio
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    uvicorn.run(app, host="0.0.0.0", port=5089)