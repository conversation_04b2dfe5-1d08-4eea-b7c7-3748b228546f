import fitz  # PyMuPDF
import os
import sys
sys.path.append(os.getcwd())
from DatabaseManagement.ImportExport import get_table_from_GZ
from Common.Constants import sanitize_name
import pandas as pd
from Common.Constants import local_case_folder, local_plaintiff_folder
import numpy as np
import cv2

# def extract_images_from_a_pdf(folder_path, filename, min_size=0):
#     pdf_name = os.path.splitext(filename)[0]
#     images_step_directory = os.path.join(folder_path, pdf_name.rstrip('. '))
#     os.makedirs(images_step_directory, exist_ok=True)
#     pdf_path = os.path.join(folder_path, filename)
#     doc = fitz.open(pdf_path)
#     for page_num in range(len(doc)):
#         page = doc[page_num]
#         images = page.get_images(full=True)
#         for img_index, img in enumerate(images):
#             xref = img[0]
#             try:
#                 base_image = doc.extract_image(xref)
#                 image_bytes = base_image["image"]
#                 image_ext = base_image["ext"]

#                  # Handle JBIG2 images
#                 if image_ext.lower() == 'jb2' or image_ext == "jpx":
#                     pix = fitz.Pixmap(doc, xref)
#                     if pix.n - pix.alpha > 3:
#                         pix = fitz.Pixmap(fitz.csRGB, pix)
#                     image_ext = 'png'
#                     image_bytes = pix.tobytes(image_ext)

#                 if len(image_bytes) >= min_size * 1024:
#                     image_name = f"{os.path.splitext(os.path.basename(pdf_path))[0]}_page{page_num+1}_{img_index}.{image_ext}"
#                     image_path = os.path.join(images_step_directory, image_name)
#                     with open(image_path, "wb") as img_file:
#                         img_file.write(image_bytes)
#             except Exception as e:
#                 print(f"Error extracting image {img_index} on page {page_num+1} in {pdf_path}: {e}")

#     if len(os.listdir(images_step_directory)) == 0:
#         os.rmdir(images_step_directory)

# @profile
def extract_images_from_a_pdf(folder_path, filename, min_size=0): # min_size is in KB
    pdf_name = os.path.splitext(filename)[0]
    images_step_directory = os.path.join(folder_path, pdf_name.rstrip('. '))
    os.makedirs(images_step_directory, exist_ok=True)
    # Delete all the content of the directory
    for file in os.listdir(images_step_directory):
        os.remove(os.path.join(images_step_directory, file))    
    pdf_path = os.path.join(folder_path, filename)
    doc = fitz.open(pdf_path)
    for page_num in range(len(doc)):
        extract_images_from_a_pdf_page(pdf_path, doc, page_num, images_step_directory, min_size)

    if len(os.listdir(images_step_directory)) == 0:
        os.rmdir(images_step_directory)

def extract_images_from_a_pdf_page(pdf_path, doc, page_num, images_step_directory, min_size) -> list:
    page = doc[page_num]
    images = page.get_images(full=True)

    extracted_image_paths = []  # Initialize list to store paths of extracted images
    # Get the page dimensions
    page_area = page.rect.width * page.rect.height

    if len(images) >= 4:
        # Extract images from page as a whole
        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2), colorspace=fitz.csRGB)  # 2x scaling for better quality, ask PyMuPDF for an RGB page to avoid the same CMYK trap (weird black and blue pictures)
        image_name = f"{os.path.splitext(os.path.basename(pdf_path))[0]}_page{page_num+1}_full.jpg"
        image_path = os.path.join(images_step_directory, image_name)
        pix.save(image_path)
        extracted_image_paths.append(image_path)
    else:
        for img_index, img in enumerate(images):
            # Get image rectangle *before* extracting
            image_rect = page.get_image_bbox(img)  # This is the key addition
            image_area = image_rect.width * image_rect.height
            if image_area < 0.20 * page_area:
                continue
            
            try:
                xref = img[0]
                # 1️⃣  build a Pixmap (decodes, applies softmask)
                pix = fitz.Pixmap(doc, xref)

                # 2️⃣  convert everything that is not already RGB
                if pix.colorspace != fitz.csRGB:
                    pix = fitz.Pixmap(fitz.csRGB, pix)

                # 3️⃣  honour original page rotation
                rotation = page.rotation or 0
                if rotation in (90, 270):
                    pix = pix.rotated(rotation)

                # 4️⃣  finally write out a colour‑correct file
                image_name = f"{os.path.splitext(os.path.basename(pdf_path))[0]}_page{page_num+1}_{img_index}.png"
                image_path = os.path.join(images_step_directory, image_name)
                pix.save(image_path)
                extracted_image_paths.append(image_path)


                # Old way that O3 said is not googd because extract_image give raw bytes
                # base_image = doc.extract_image(xref)
                # image_bytes = base_image["image"]
                # image_ext = base_image["ext"]

                # if len(image_bytes) < min_size * 1024:
                #     continue

                # # Handle JBIG2 images
                # if image_ext.lower() == 'jb2' or image_ext == "jpx":
                #     pix = fitz.Pixmap(doc, xref)
                #     if pix.colorspace is None:  # If no colorspace, create one with alpha channel
                #         pix = fitz.Pixmap(fitz.csRGB, pix.width, pix.height)
                #     else:
                #         pix = fitz.Pixmap(fitz.csRGB, pix)  # Convert to RGB
                #     image_ext = 'jpg'
                #     image_bytes = pix.tobytes(image_ext)

                # rotation = page.rotation if page.rotation else 0
                # if rotation == 90 or rotation == 270:
                #     print(f"\033[91mRotating image {img_index} on page {page_num+1} in {pdf_path} by {rotation} degrees\033[0m")
                #     # Convert image bytes to a NumPy array
                #     image_array = np.frombuffer(image_bytes, np.uint8)
                #     # Decode the image
                #     image = cv2.imdecode(image_array, cv2.IMREAD_UNCHANGED)

                #     # Rotate the image
                #     if rotation == 90:
                #         image = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
                #     elif rotation == 270:
                #         image = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)

                #     # Encode the image back to bytes
                #     _, image_bytes = cv2.imencode('.jpg', image)
                #     image_bytes = image_bytes.tobytes()

                # image_name = f"{os.path.splitext(os.path.basename(pdf_path))[0]}_page{page_num+1}_{img_index}.{image_ext}"
                # image_path = os.path.join(images_step_directory, image_name)
                # with open(image_path, "wb") as img_file:
                #     img_file.write(image_bytes)
                # extracted_image_paths.append(image_path)
            except Exception as e:
                print(f"Error extracting image {img_index} on page {page_num+1} in {pdf_path}: {e}")

    return extracted_image_paths

# def extract_images_from_a_pdf_fast(folder_path, filename, min_size=0):
#     pdf_name = os.path.splitext(filename)[0]
#     images_step_directory = os.path.join(folder_path, pdf_name.rstrip('. '))
    
#     # Only create directory if we find images that meet our criteria
#     pdf_path = os.path.join(folder_path, filename)
    
#     # Use context manager for automatic cleanup
#     with fitz.open(pdf_path) as doc:
#         # Pre-calculate basename to avoid repeated calculations
#         pdf_basename = os.path.splitext(os.path.basename(pdf_path))[0]
#         images_found = False
        
#         for page_num in range(len(doc)):
#             page = doc[page_num]
#             images = page.get_images(full=True)
            
#             if not images:
#                 continue
                
#             if len(images) >= 4:
#                 # Create directory only when first image is found
#                 if not images_found:
#                     os.makedirs(images_step_directory, exist_ok=True)
#                     images_found = True
                    
#                 # Use lower resolution for faster processing
#                 pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
#                 image_path = f"{images_step_directory}/{pdf_basename}_page{page_num+1}_full.png"
#                 pix.save(image_path)
#                 continue
            
#             for img_index, img in enumerate(images):
#                 try:
#                     base_image = doc.extract_image(img[0])
#                     image_bytes = base_image["image"]
                    
#                     # Skip small images early
#                     if len(image_bytes) < min_size * 1024:
#                         continue
                        
#                     # Create directory only when first valid image is found
#                     if not images_found:
#                         os.makedirs(images_step_directory, exist_ok=True)
#                         images_found = True
                    
#                     image_ext = base_image["ext"]
#                     if image_ext.lower() == 'jb2' or image_ext == "jpx":
#                         pix = fitz.Pixmap(doc, img[0])
#                         pix = fitz.Pixmap(fitz.csRGB, pix)
#                         image_ext = 'png'
#                         image_bytes = pix.tobytes(image_ext)
                    
#                     image_path = f"{images_step_directory}/{pdf_basename}_page{page_num+1}_{img_index}.{image_ext}"
#                     with open(image_path, "wb") as img_file:
#                         img_file.write(image_bytes)
                        
#                 except Exception as e:
#                     print(f"Error extracting image {img_index} on page {page_num+1} in {pdf_path}: {e}")
        
#         # Only attempt to remove if directory was created
#         if images_found and len(os.listdir(images_step_directory)) == 0:
#             os.rmdir(images_step_directory)



def extract_pictures_from_pdfs(df):
    for index, row in df.iterrows():
        case_directory = os.path.join(local_case_folder, sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}"))
        if os.path.isdir(case_directory):
            pdf_directory = os.path.join(case_directory, "1")
            if os.path.isdir(pdf_directory):
                for filename in os.listdir(pdf_directory):
                    if filename.lower().endswith(".pdf"):
                        extract_images_from_a_pdf(pdf_directory, filename)

def find_complaints(df):
    for index, row in df.iterrows():
        pdf_directory = os.path.join(local_case_folder, sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}"))
        if os.path.isdir(pdf_directory):
            folder_1 = os.path.join(pdf_directory, "1")
            if os.path.isdir(folder_1):
                complaint_found = any("COMPLAINT" in filename.upper() for filename in os.listdir(folder_1) if filename.lower().endswith('.pdf'))
                if complaint_found:
                    continue
            
            print(f"Complaint not found in folder: {pdf_directory}")
            for foldername in os.listdir(pdf_directory):
                folder_path = os.path.join(pdf_directory, foldername)
                if os.path.isdir(folder_path) and foldername != "1":
                    for filename in os.listdir(folder_path):
                        if filename.lower().endswith('.pdf') and "COMPLAINT" in filename.upper():
                            print(f"      Complaint found in folder: {foldername}, file: {filename}")
                            if os.path.exists(os.path.join(pdf_directory, "1")):
                                os.rename(os.path.join(pdf_directory, "1"), os.path.join(pdf_directory, "1_original"))
                            os.rename(folder_path, os.path.join(pdf_directory, "1"))
                            break



if __name__ == "__main__":
    df = get_table_from_GZ("tb_case")
    # find_complaints(df)
    extract_pictures_from_pdfs(df)
