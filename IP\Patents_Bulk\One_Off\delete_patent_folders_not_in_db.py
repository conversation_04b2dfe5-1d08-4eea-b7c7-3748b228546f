import os
import re
import sys
sys.path.append(os.getcwd())
import shutil
import time
from pathlib import Path
import psycopg2
from IP.Patents_Bulk.patent_db_grant import get_db_connection
from Common.Constants import local_ip_folder

extracted_dir = Path(local_ip_folder) / 'Patents' / 'USPTO_Grants' / 'Extracted'
deleted_dir = Path(local_ip_folder) / 'Patents' / 'USPTO_Grants' / 'Deleted'
deleted_dir.mkdir(parents=True, exist_ok=True)

def extract_reg_no(folder_name):
    match = re.match(r'US([A-Z0-9]+)-', folder_name)
    if match:
        return match.group(1)
    return None


def get_all_reg_nos_in_db():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT reg_no FROM patents')
        return set(row[0] for row in cursor.fetchall())
    except Exception as e:
        print(f"DB error: {e}")
        return set()
    finally:
        if conn:
            conn.close()


print("Loading reg_no")
all_reg_nos_in_db = get_all_reg_nos_in_db()
print("Loading done")


def reg_no_exists_in_db(reg_no):
    return reg_no in all_reg_nos_in_db

def main():
    start_time = time.time()
    for i, xx_dir in enumerate(extracted_dir.iterdir()):
        print(f"Starting folder {xx_dir.name} ({i+1}/100) at {time.time()-start_time}")
        if not xx_dir.is_dir():
            continue
        for yy_dir in xx_dir.iterdir():
            print(f"     >>Starting folder {yy_dir.name} at {time.time()-start_time}")
            if not yy_dir.is_dir():
                continue
            for folder in yy_dir.iterdir():
                if not folder.is_dir():
                    continue
                reg_no = extract_reg_no(folder.name)
                if not reg_no:
                    print(f"Could not extract reg_no from folder: {folder.name}")
                    continue
                if reg_no.startswith('D'):
                    continue  # Keep Design patents
                if not reg_no_exists_in_db(reg_no):
                    dest = deleted_dir / xx_dir.name / yy_dir.name / folder.name
                    dest.parent.mkdir(parents=True, exist_ok=True)
                    # print(f"Moving {folder} to {dest}")
                    shutil.move(str(folder), str(dest))

if __name__ == '__main__':
    main()
