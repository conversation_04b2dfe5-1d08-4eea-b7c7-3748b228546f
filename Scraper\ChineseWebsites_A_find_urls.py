#nofloqa
import re, aiohttp, os, sys
import json
sys.path.append(os.getcwd())
from markdownify import markdownify
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json_list
import Common.Constants as Constants
from langfuse import observe
import langfuse
from logdata import log_message
from Scraper.ChineseWebsites_utils import fetch_url
# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗



def format_case_number_for_site(case_number: str, site_name: str) -> str:
    """
    Generates the appropriate format for the case number based on the specific site.
    
    Args:
        case_number: e.g., "1:25-cv-00097"
        site_name: The name of the website to format for
        
    Returns:
        Formatted case number string specific to the site
    """
    match = re.match(r'\d+:(\d+)-([a-zA-Z]+)-(\d+)', case_number)
    if not match:
        log_message(f"      Warning: Could not parse case number format: {case_number}")
        return ""

    part1 = match.group(1)
    part2 = match.group(2)
    part3 = match.group(3)
    part3_short = str(int(part3))  # Remove leading zeros

    # Format specifically for each site based on working examples
    if site_name == "10100":
        return f"{part1}-{part2}-{part3}"  # e.g., 25-cv-03439
    elif site_name == "SellerDefense":
        return f"{part1}-{part2}-{part3_short}"  # e.g., 25-cv-3439
    elif site_name == "Maijiazhichi":
        return f"{part1}-{part2}-{part3}"  # e.g., 25-cv-03439
    else:
        # Default format if site not recognized
        return f"{part1}-{part2}-{part3}"

@observe(capture_input=False)
async def A_find_target_urls_using_markdown(docket: str, plaintiff_name: str, session: aiohttp.ClientSession, 
                                           site_config: dict) -> list[str]:
    """Searches case format on a website using site-specific format, converts to Markdown, and extracts target article URLs via regex."""
    target_urls = set()  # Use a set to avoid duplicates

    # Get the site name and search URL template
    site_name = site_config["name"]
    search_url_template = site_config["search_url_template"]
    exclude_words = site_config["exclude_words"]
    
    # Format case number specifically for this site
    case_format = format_case_number_for_site(docket, site_name)
    if not case_format:
        return []

    # Determine base URL for resolving relative links found in markdown
    base_domain_match = re.match(r'(https?://[^/]+)', search_url_template)
    base_url = base_domain_match.group(1) if base_domain_match else None

    # Replace xxxx in template with the site-specific case format
    search_url = search_url_template.replace("xxxx", case_format)
    log_message(f"      Searching (Markdown method): {search_url}")
    
    content = await fetch_url(session, search_url)
    if not content:
        return []

    # Convert HTML to Markdown
    try:
        # Use body_width=0 to prevent line wrapping that might break URLs
        markdown_content = markdownify(content, heading_style="ATX", bullets='*', body_width=0)
    except Exception as e:
        log_message(f"      Error converting HTML to Markdown for {search_url}: {e}")
        return []

    prompt = f'{markdown_content} \n\n' + f'This page shows search results for {docket}. I am looking for information about a legal case (case number {docket} from plaintiff "{plaintiff_name}"). What are the relevant result for getting information about the case? For each result (hopefully just one), I need its title and its URL. Return your answer as a JSON array of objects, where each object has a "title" key and a "url" key. For example: [{{"title": "Result Title", "url": "/url/to/result"}}]. If there are no relevant results, you return and empty list []'
    response = await vertex_genai_multi_async([("text", prompt)], model_name=Constants.TEXT_MODEL_FREE_LIMITED, useVertexAI=Constants.TEXT_MODEL_FREE_LIMITED_VERTEX)
    json_list_response = get_json_list(response)
    
    for json_response in json_list_response:
        # Check if the URL contains any of the exclude words
        url = json_response.get("url")
        if url is None: # Add this check to ensure url is not None
            log_message(f"      Warning: LLM response missing 'url' key in: {json_response}", level="WARNING")
            continue

        if any(word in url for word in exclude_words): # Now 'url' is guaranteed not to be None
            continue
        else:
            url = base_url + url if base_url and url.startswith('/') else url # Ensure base_url is also not None if prepending
            target_urls.add(url)

    # log_message(f"Found {len(target_urls)}")
    langfuse.get_client().update_current_span(
        input={"Docket": docket, "PlaintiffName": plaintiff_name, "SiteName": site_name, "SearchURL": search_url},
    )
    return list(target_urls)
