from openai import OpenAI
import os
from dotenv import load_dotenv

load_dotenv()

client = OpenAI(
    base_url='https://api-inference.modelscope.cn/v1',
    api_key=os.getenv("MODELSCOPE_API_KEY"), # ModelScope Token
)

response = client.chat.completions.create(
    model='deepseek-ai/DeepSeek-V3.1', # ModelScope Model-Id, required
    messages=[
        {
            'role': 'user',
            'content': 'Tell me a joke'
        }
    ],
    stream=True
)
done_reasoning = False
for chunk in response:
    reasoning_chunk = chunk.choices[0].delta.reasoning_content
    answer_chunk = chunk.choices[0].delta.content
    if reasoning_chunk != '':
        print(reasoning_chunk, end='',flush=True)
    elif answer_chunk != '':
        if not done_reasoning:
            print('\n\n === Final Answer ===\n')
            done_reasoning = True
        print(answer_chunk, end='',flush=True)