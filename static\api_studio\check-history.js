document.addEventListener('DOMContentLoaded', function() {
    initializeCheckHistoryPage();
});

function buildAuthHeaders(apiKey) {
    const headers = { 'Content-Type': 'application/json' };
    if (apiKey) {
        headers['apikey'] = apiKey;
    }
    return headers;
}
function initializeCheckHistoryPage() {
    const apiKeyInput = document.getElementById('api_key');
    const fetchHistoryBtn = document.getElementById('fetchHistoryBtn');
    const historyControls = document.getElementById('historyControls');
    const datePickerInput = document.getElementById('datePicker');
    const prevDateBtn = document.getElementById('prevDateBtn');
    const nextDateBtn = document.getElementById('nextDateBtn');
    const checkListDiv = document.getElementById('checkList');
    const outputDiv = document.getElementById('output');
    const requestInfoDiv = document.getElementById('request-info');
    const resultsDisplayDiv = document.getElementById('results-display');
    const languageSwitcher = document.getElementById('language');

    let availableDates = [];
    let currentDateIndex = -1;
    let currentCheckDetails = null; // To store the last fetched check details

    // Load API key from cookie
    const savedApiKey = getCookie('api_key');
    if (savedApiKey) {
        apiKeyInput.value = savedApiKey;
        fetchHistoryBtn.click();
    }

    fetchHistoryBtn.addEventListener('click', fetchHistory);
    prevDateBtn.addEventListener('click', () => navigateDates(1));
    nextDateBtn.addEventListener('click', () => navigateDates(-1));

    if (languageSwitcher) {
        languageSwitcher.addEventListener('change', () => {
            if (currentCheckDetails) {
                displayCheckDetails(currentCheckDetails);
            }
        });
    }

    resultsDisplayDiv.addEventListener('click', function(event) {
        const button = event.target.closest('.show-report-button');
        if (button) {
            const reportId = button.dataset.reportId;
            const reportContainer = document.getElementById(reportId);
            if (reportContainer) {
                const lang = languageSwitcher ? languageSwitcher.value : 'en';
                const isZh = lang === 'zh';
                const isHidden = reportContainer.style.display === 'none' || reportContainer.style.display === '';

                reportContainer.style.display = isHidden ? 'block' : 'none';

                if (typeof i18n !== 'undefined' && i18n.en.hide_report && i18n.zh.hide_report) {
                    const hide_report_text = isZh ? i18n.zh.hide_report : i18n.en.hide_report;
                    const show_report_text = isZh ? i18n.zh.show_report : i18n.en.show_report;
                    button.textContent = isHidden ? hide_report_text : show_report_text;
                }
            }
        }
    });

    const datePicker = flatpickr(datePickerInput, {
        onChange: function(selectedDates, dateStr, instance) {
            const newIndex = availableDates.indexOf(dateStr);
            if (newIndex !== -1) {
                currentDateIndex = newIndex;
                updateDatePicker();
                fetchChecksForDate(dateStr);
            }
        }
    });

    async function fetchHistory() {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            alert('Please enter an API Key.');
            return;
        }
        setCookie('api_key', apiKey, 30);

        try {
            const response = await fetch('/history/get_check_dates', {
                method: 'POST',
                headers: buildAuthHeaders(apiKey),
                body: JSON.stringify({})
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.error || 'Failed to fetch check dates.');
            }

            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }

            availableDates = data.dates.sort((a, b) => new Date(b) - new Date(a)); // Sort descending

            if (availableDates.length > 0) {
                historyControls.style.display = 'block';
                currentDateIndex = 0;
                datePicker.set('enable', availableDates); // Enable only available dates
                updateDatePicker();
                fetchChecksForDate(availableDates[currentDateIndex]);
            } else {
                historyControls.style.display = 'none';
                checkListDiv.innerHTML = '<p>No checks found for this API key.</p>';
                requestInfoDiv.innerHTML = '';
                resultsDisplayDiv.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching history:', error);
            alert(error.message);
        }
    }

    function navigateDates(direction) {
        // Dates are sorted descending, so "next" date is previous index, "prev" is next index
        if (direction === -1 && currentDateIndex > 0) { // "Next" button (newer date)
            currentDateIndex--;
        } else if (direction === 1 && currentDateIndex < availableDates.length - 1) { // "Previous" button (older date)
            currentDateIndex++;
        }
        updateDatePicker();
        fetchChecksForDate(availableDates[currentDateIndex]);
    }

    function updateDatePicker() {
        if (currentDateIndex !== -1) {
            datePicker.setDate(availableDates[currentDateIndex], false); // false to prevent firing onChange
        }
        // prevDateBtn should go to an older date (higher index)
        prevDateBtn.disabled = currentDateIndex >= availableDates.length - 1;
        // nextDateBtn should go to a newer date (lower index)
        nextDateBtn.disabled = currentDateIndex <= 0;
    }

    async function fetchChecksForDate(date) {
        const apiKey = apiKeyInput.value.trim();
        checkListDiv.innerHTML = '<div class="loading">Loading checks...</div>';
        requestInfoDiv.innerHTML = '';
        resultsDisplayDiv.innerHTML = '';

        try {
            const response = await fetch('/history/get_checks_for_date', {
                method: 'POST',
                headers: buildAuthHeaders(apiKey),
                body: JSON.stringify({ date: date })
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.error || 'Failed to fetch checks for the selected date.');
            }

            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }

            displayCheckList(data.checks);
        } catch (error) {
            console.error('Error fetching checks:', error);
            checkListDiv.innerHTML = `<p>${error.message}</p>`;
        }
    }

    function displayCheckList(checks) {
        checkListDiv.innerHTML = '';
        if (checks.length === 0) {
            checkListDiv.innerHTML = '<p>No checks found for this date.</p>';
            return;
        }

        // Check if we are in admin view (checks will have client_name)
        const isAdminView = checks.length > 0 && checks[0].hasOwnProperty('client_name');

        if (isAdminView) {
            const checksByUser = checks.reduce((acc, check) => {
                const user = check.client_name || 'Unknown User';
                if (!acc[user]) {
                    acc[user] = [];
                }
                acc[user].push(check);
                return acc;
            }, {});

            for (const user in checksByUser) {
                const userGroup = document.createElement('div');
                userGroup.className = 'user-group';

                const userHeader = document.createElement('div');
                userHeader.className = 'user-header';
                const checkCount = checksByUser[user].length;
                userHeader.textContent = `► User: ${user} (${checkCount})`; // Start collapsed and show count
                userHeader.addEventListener('click', () => {
                    const content = userGroup.querySelector('.user-checks');
                    const isHidden = content.style.display === 'none';
                    content.style.display = isHidden ? 'block' : 'none';
                    userHeader.textContent = `${isHidden ? '▼' : '►'} User: ${user} (${checkCount})`;
                });
                userGroup.appendChild(userHeader);

                const userChecksContainer = document.createElement('div');
                userChecksContainer.className = 'user-checks';
                userChecksContainer.style.display = 'none'; // Start collapsed
                checksByUser[user].forEach(check => {
                    const checkItem = createCheckItem(check);
                    userChecksContainer.appendChild(checkItem);
                });
                userGroup.appendChild(userChecksContainer);
                checkListDiv.appendChild(userGroup);
            }
        } else {
            checks.forEach(check => {
                const checkItem = createCheckItem(check);
                checkListDiv.appendChild(checkItem);
            });
        }

        // Auto-click the first check item if available
        const firstCheck = checkListDiv.querySelector('.check-id-item');
        if (firstCheck) {
            firstCheck.click();
        }
    }

    function createCheckItem(check) {
        const checkItem = document.createElement('div');
        checkItem.className = 'check-id-item';
        checkItem.textContent = `Check ID: ${check.check_id} (${new Date(check.timestamp).toLocaleTimeString()})`;
        checkItem.dataset.checkId = check.check_id;
        checkItem.addEventListener('click', (e) => {
            document.querySelectorAll('.check-id-item').forEach(item => item.classList.remove('active'));
            e.currentTarget.classList.add('active');
            fetchCheckDetails(check.check_id);
        });
        return checkItem;
    }

    async function fetchCheckDetails(checkId) {
        const apiKey = apiKeyInput.value.trim();
        requestInfoDiv.innerHTML = '<div class="loading">Loading details...</div>';
        resultsDisplayDiv.innerHTML = '';
        currentCheckDetails = null; // Reset on new fetch

        try {
            const response = await fetch('/history/get_check_details', {
                method: 'POST',
                headers: buildAuthHeaders(apiKey),
                body: JSON.stringify({ check_id: checkId })
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.error || 'Failed to fetch check details.');
            }

            const data = await response.json();
            console.log("Received check details from backend:", data);
            if (data.error) {
                throw new Error(data.error);
            }

            currentCheckDetails = data; // Store details
            displayCheckDetails(data);
        } catch (error) {
            console.error('Error fetching check details:', error);
            requestInfoDiv.innerHTML = `<p>${error.message}</p>`;
        }
    }

    function displayCheckDetails(data) {
        requestInfoDiv.innerHTML = '';
        resultsDisplayDiv.innerHTML = '';

        const requestTitle = document.createElement('h3');
        requestTitle.textContent = 'Request Information';
        requestInfoDiv.appendChild(requestTitle);

        const requestData = data.request;

        const createDetailItem = (label, value) => {
            // Only create the item if the value is not null, undefined, an empty string, or an empty array
            if (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
                return null;
            }

            const itemDiv = document.createElement('div');
            itemDiv.className = 'detail-item';
            const labelStrong = document.createElement('strong');
            labelStrong.textContent = `${label.replace(/_/g, ' ')}: `;
            itemDiv.appendChild(labelStrong);

            let parsedValue = value;
            if (typeof value === 'string' && (value.startsWith('[') || value.startsWith('{'))) {
                try {
                    parsedValue = JSON.parse(value);
                } catch (e) { /* ignore */ }
            }

            const renderValue = (val) => {
                const container = document.createElement('span');
                
                if (Array.isArray(val)) {
                    const arrayContainer = document.createElement('div');
                    arrayContainer.className = 'value-array-container';
                    val.forEach(item => arrayContainer.appendChild(renderValue(item)));
                    return arrayContainer;
                } else if (typeof val === 'string' && (val.startsWith('http') || val.startsWith('data:image'))) {
                    const img = document.createElement('img');
                    img.src = val;
                    img.className = 'history-image';
                    container.appendChild(img);
                } else {
                    const textSpan = document.createElement('span');
                    textSpan.textContent = val;
                    container.appendChild(textSpan);
                }
                return container;
            };
            
            itemDiv.appendChild(renderValue(parsedValue));
            return itemDiv;
        };

        const displayOrder = [
            'product_category', 'description', 'ip_keywords', 'reference_text',
            'main_product_image', 'other_product_images', 'ip_images', 'reference_images'
        ];
        
        displayOrder.forEach(key => {
            if (requestData.hasOwnProperty(key)) {
                const item = createDetailItem(key, requestData[key]);
                if (item) { // Add to div only if an element was created
                    requestInfoDiv.appendChild(item);
                }
            }
        });

        if (data.result) {
            // Now displayResults takes a container, so we pass resultsDisplayDiv directly
            displayResults(data.result, resultsDisplayDiv);
        } else {
            resultsDisplayDiv.innerHTML = '<p>No results found for this check.</p>';
        }
    }
}

