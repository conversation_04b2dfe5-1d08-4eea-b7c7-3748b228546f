from reportlab.platypus import Image, Table, TableStyle
from reportlab.lib.units import mm
from reportlab.lib.utils import ImageReader
from Check.PDF_ReportLab.styles import myStyles, colors
import os


from Check.PDF_ReportLab.helpers import _para, _sp, _hr,  _images_grid, RiskBadge

def create_cover_page(width, height, data, img_cache):
    """Creates the cover page and returns it as a list of flowables."""
    story = []

    # Brand Logo and Meta
    path = 'Check/PDF_ReportLab/static/logo.png'
    # iw, ih = ImageReader(path).getSize()       # pixel size
    # print(f"Logo size: {iw}x{ih}")
    iw, ih = 4200, 2000  # Save speed give the size never changes)
    target_h = 24*mm
    logo = Image(path, width=target_h * (iw/ih), height=target_h)
    logo.hAlign = 'CENTER'
    story.append(logo)
    story.append(_sp(5))
    story.append(_para("Shenzhen Maidalv Technology Co., Ltd.", myStyles['BrandMeta']))
    story.append(_sp(8))

    # Title
    title_text = "INTELLECTUAL PROPERTY INFRINGEMENT<br/>RISK ASSESSMENT REPORT"
    title_table = Table([[_para(title_text, myStyles['Title'])]], colWidths=[width])
    title_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), colors['navy']),
        ('TOPPADDING', (0, 0), (-1, -1), 5*mm),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 5*mm),
    ]))
    story.append(title_table)
    story.append(_sp(8))

    # Meta Grid
    risk_level = data['risk_level']
    risk_badge = RiskBadge(risk_level.upper(), risk_level)
    
    meta_data = [
        [_para('Check ID', myStyles['MetaGridLabelCentered']), _para('Client', myStyles['MetaGridLabelCentered']), _para('Overall Risk', myStyles['MetaGridLabelCentered'])],
        [_para(data['check_id'], myStyles['MetaGridValueCentered']), _para(data['client_name'], myStyles['MetaGridValueCentered']), risk_badge]
    ]
    row_heights = [20, None]  # fixed height for first row, auto for second
    meta_table = Table(meta_data, colWidths=[width/3]*3, rowHeights=row_heights)
    meta_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
        
    
    story.append(meta_table)
    story.append(_sp(6))
    story.append(_hr())
    story.append(_sp(4))

    # Products
    story.append(_para('Product Being Assessed', myStyles['SectionTitle']))
    story.append(_sp(2))
    # Prioritize local paths for product images
    product_images = []
    local_images_count = 0
    url_images_count = 0
    
    results = data.get('results', [])
    processed_urls = set()

    for res in results:
        # Prioritize local paths
        local_path = res.get('product_local_path')
        
        if local_path and os.path.exists(local_path) and local_path in processed_urls:
            continue
            
        if local_path and os.path.exists(local_path) and local_path not in processed_urls:
            product_images.append(local_path)
            processed_urls.add(local_path)
            local_images_count += 1
            continue

        # Fallback to URL
        # Fallback to URL, handling both string and list
        urls = res.get('product_url')
        if urls:
            if isinstance(urls, str):
                urls = [urls] # Normalize to list
            
            for url in urls:
                if url and url not in processed_urls:
                    product_images.append(url)
                    processed_urls.add(url)
                    url_images_count += 1

    if product_images:
        story.append(_images_grid(product_images[:4], img_cache, thumb_w_mm=42, thumb_h_mm=42))
    story.append(_sp(6))
    story.append(_hr())
    story.append(_sp(4))

    # Summary Table
    story.append(_para('Overview of Assessment', myStyles['SectionTitle']))
    story.append(_sp(4))
    summary_data = [['#', 'Type', 'IP Owner', 'Risk', 'Page']]
    for i, res in enumerate(data.get('results', [])):
        summary_data.append([
            str(i + 1),
            res.get('ip_type', ''),
            res.get('ip_owner', ''),
            res.get('risk_level', ''),
            'N/A'  # Page numbers will be added later
        ])
    
    summary_table = Table(summary_data, colWidths=[10*mm, 30*mm, 60*mm, 30*mm, 20*mm])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors['accent_bg']),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors['black']),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('INNERGRID', (0, 0), (-1, -1), 0.25, colors['black']),
        ('BOX', (0, 0), (-1, -1), 0.25, colors['black'])
    ]))
    story.append(summary_table)

    return story, local_images_count, url_images_count