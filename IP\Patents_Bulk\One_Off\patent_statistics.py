#!/usr/bin/env python3
"""
Unified Patent Discrepancy Analysis

Extracts all records from DB, HDD, and Qdrant, then performs analysis for:
1. DB vs Qdrant
2. Qdrant vs DB
3. DB vs HDD
4. HDD vs DB
5. HDD vs Qdrant
6. Qdrant vs HDD

For each case, outputs:
- Total difference
- Missing records with design drawing
- Missing records without design drawing
- Progress bars for DB fetch and each analysis
- Separate CSV output for each case
"""

import os
import sys
import csv
from tqdm import tqdm
from dotenv import load_dotenv
from pathlib import Path
import json
import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from IP.Patents_Bulk.patent_db_grant import get_db_connection
from Common.uuid_utils import generate_uuid

# Load environment variables
load_dotenv()

QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = "IP_Assets"
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), 'discrepancy_results')
os.makedirs(OUTPUT_DIR, exist_ok=True)

BATCH_SIZE = 50000

def is_design_drawing(file_name):
    parts = file_name.split('-')
    return len(parts) > 2 and parts[-1].startswith('D') and file_name.endswith('.png')

def get_database_patent_files():
    conn = get_db_connection()
    db_files = {}
    with conn.cursor() as cursor:
        cursor.execute("SELECT COUNT(*) FROM patents WHERE fig_files IS NOT NULL AND array_length(fig_files, 1) > 0;")
        total = cursor.fetchone()[0]
        cursor.execute("SELECT reg_no, fig_files FROM patents WHERE fig_files IS NOT NULL AND array_length(fig_files, 1) > 0;")
        fetched = 0
        pbar = tqdm(total=total, desc="Fetching DB records", unit="patent")
        while True:
            rows = cursor.fetchmany(BATCH_SIZE)
            if not rows:
                break
            for patent in rows:
                reg_no = patent[0]
                files = patent[1]
                if files:
                    for f in files:
                        file_basename = os.path.basename(f)
                        file_id = os.path.splitext(file_basename)[0]
                        uuid = generate_uuid(file_id)
                        db_files[uuid] = (reg_no, file_basename)
                fetched += 1
                pbar.update(1)
        pbar.close()
    conn.close()
    return db_files

def get_qdrant_patent_points():
    from qdrant_client import QdrantClient, models
    client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
    qdrant_points = {}
    offset = None
    total = client.count(
        collection_name=COLLECTION_NAME,
        count_filter=models.Filter(
            must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Patent"))]
        ),
        exact=True
    ).count
    pbar = tqdm(total=total, desc="Fetching Qdrant records", unit="point")
    while True:
        points, next_offset = client.scroll(
            collection_name=COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Patent"))]
            ),
            limit=BATCH_SIZE,
            offset=offset,
            with_payload=True,
            with_vectors=False
        )
        for point in points:
            reg_no = point.payload.get("reg_no")
            qdrant_points[point.id] = reg_no
            pbar.update(1)
        if next_offset is None:
            break
        offset = next_offset
    pbar.close()
    return qdrant_points

def scan_hdd_patent_files():
    from Common.Constants import local_ip_folder
    base_dir = Path(local_ip_folder) / "Patents" / "USPTO_Grants" / "Extracted"
    hdd_files = {}
    folders = list(base_dir.glob("*/*/*"))
    pbar = tqdm(folders, desc="Scanning HDD folders", unit="folder")
    for patent_folder in pbar:
        if not patent_folder.is_dir():
            continue
        for file_path in patent_folder.iterdir():
            if not file_path.is_file():
                continue
            file_basename = file_path.name
            file_id = os.path.splitext(file_basename)[0]
            uuid = generate_uuid(file_id)
            hdd_files[uuid] = (str(file_path), file_basename)
    pbar.close()
    return hdd_files

def analyze_case(case_name, source_dict, target_dict, other_dict, source_label, target_label, other_label, csv_fields):
    print(f"\n[{case_name}] Starting analysis...")
    source_uuids = set(source_dict.keys())
    target_uuids = set(target_dict.keys())
    other_uuids = set(other_dict.keys())
    missing_uuids = source_uuids - target_uuids
    records = []
    design_missing = 0
    non_design_missing = 0
    design_records = []
    for uuid in tqdm(missing_uuids, desc=f"Analyzing {case_name}", unit="record"):
        src = source_dict.get(uuid)
        # Try to get file_name, fallback to reg_no, else 'UNKNOWN'
        file_name = None
        if src is not None:
            if isinstance(src, tuple) and len(src) > 1 and src[1]:
                file_name = src[1]
            elif isinstance(src, tuple) and len(src) > 0 and src[0]:
                file_name = src[0]
            elif isinstance(src, str):
                file_name = src
        if not file_name:
            file_name = "UNKNOWN"
        design = is_design_drawing(file_name)
        record = {
            'uuid': uuid,
            'file_name': file_name,
            f'{source_label}_status': 'yes',
            f'{target_label}_status': 'no',
            f'{other_label}_status': 'yes' if uuid in other_uuids else 'no'
        }
        if design:
            design_missing += 1
            design_records.append(record)
        else:
            non_design_missing += 1
        records.append(record)
    print(f"Total difference: {len(missing_uuids):,}")
    print(f"Missing records with design drawing: {design_missing:,}")
    print(f"Missing records without design drawing: {non_design_missing:,}")
    out_csv = os.path.join(OUTPUT_DIR, f"{case_name.replace(' ', '_').lower()}_missing.csv")
    with open(out_csv, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=csv_fields)
        writer.writeheader()
        writer.writerows(design_records)
    print(f"Results saved to: {out_csv}\n")
    return design_missing

def push_stat_to_db(metric_name, metric_value, details_preview):
    conn = get_db_connection()
    now = datetime.datetime.now()
    report_time = now.strftime('%Y-%m-%d %H:%M:%S')
    details_json = json.dumps(details_preview)
    with conn.cursor() as cur:
        cur.execute("""
            INSERT INTO statistics_log (report_timestamp, metric_name, metric_value, details_preview, created_at)
            VALUES (%s, %s, %s, %s, %s)
        """, (report_time, metric_name, metric_value, details_json, now))
    conn.commit()
    conn.close()

def get_patent_statistics():
    print("\nUnified Patent Discrepancy Analysis Starting...")
    db_files = get_database_patent_files()
    hdd_files = scan_hdd_patent_files()
    qdrant_points = get_qdrant_patent_points()
    print("\n--- SUMMARY ---")
    print(f"Total DB records: {len(db_files):,}")
    print(f"Total HDD records: {len(hdd_files):,}")
    print(f"Total Qdrant records: {len(qdrant_points):,}")
    print("------------------\n")
    # 1. DB vs Qdrant
    db_not_qdrant_design = analyze_case(
        "DB vs Qdrant",
        db_files, qdrant_points, hdd_files,
        "db", "qdrant", "hdd",
        ['uuid', 'file_name', 'db_status', 'qdrant_status', 'hdd_status']
    )
    # 2. Qdrant vs DB
    qdrant_not_db_design = analyze_case(
        "Qdrant vs DB",
        qdrant_points, db_files, hdd_files,
        "qdrant", "db", "hdd",
        ['uuid', 'file_name', 'qdrant_status', 'db_status', 'hdd_status']
    )
    # 3. DB vs HDD
    db_not_hdd_design = analyze_case(
        "DB vs HDD",
        db_files, hdd_files, qdrant_points,
        "db", "hdd", "qdrant",
        ['uuid', 'file_name', 'db_status', 'hdd_status', 'qdrant_status']
    )
    # 4. HDD vs DB
    hdd_not_db_design = analyze_case(
        "HDD vs DB",
        hdd_files, db_files, qdrant_points,
        "hdd", "db", "qdrant",
        ['uuid', 'file_name', 'hdd_status', 'db_status', 'qdrant_status']
    )
    # 5. HDD vs Qdrant
    hdd_not_qdrant_design = analyze_case(
        "HDD vs Qdrant",
        hdd_files, qdrant_points, db_files,
        "hdd", "qdrant", "db",
        ['uuid', 'file_name', 'hdd_status', 'qdrant_status', 'db_status']
    )

    # Push stats to DB
    push_stat_to_db("patents_in_db_not_qdrant_with_design_drawing", db_not_qdrant_design, {"description": "Patents in DB not Qdrant with design drawing"})
    push_stat_to_db("patents_in_qdrant_not_db_with_design_drawing", qdrant_not_db_design, {"description": "Patents in Qdrant not DB with design drawing"})
    push_stat_to_db("patents_in_db_not_hdd_with_design_drawing", db_not_hdd_design, {"description": "Patents in DB not HDD with design drawing"})
    push_stat_to_db("patents_on_hdd_not_db_with_design_drawing", hdd_not_db_design, {"description": "Patents on HDD not DB with design drawing"})
    push_stat_to_db("patents_on_hdd_not_qdrant_with_design_drawing", hdd_not_qdrant_design, {"description": "Patents on HDD not Qdrant with design drawing"})

    print("\nUnified analysis completed.")

if __name__ == "__main__":
    get_patent_statistics()
