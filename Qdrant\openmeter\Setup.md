**Clickhouse:**

docker exec clickhouse clickhouse-client -u clickhouse --password '919cfa8b561fc3' --multiquery <<'SQL'
CREATE DATABASE IF NOT EXISTS openmeter;
CREATE USER IF NOT EXISTS om IDENTIFIED BY '2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ';
GRANT ALL ON openmeter.* TO om;
SQL

=> After being on the same network, the connection string will be: clickhouse://om:2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ@clickhouse:9000/openmeter?dial_timeout=10s


**PostgreSQL**:

docker exec -it postgresql psql -U maidalv -d maidalv_db -c "CREATE ROLE openmeter LOGIN PASSWORD '2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ';"

docker exec -it postgresql psql -U maidalv -d maidalv_db -c "CREATE DATABASE openmeter OWNER openmeter;"

=> After being on the same network, the connection string will be: *********************************************************************/openmeter?sslmode=disable
