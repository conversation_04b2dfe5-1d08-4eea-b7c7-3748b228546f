import os
import sys
import shutil
import pandas as pd
from tqdm import tqdm

sys.path.append(os.getcwd())

from DatabaseManagement.ImportExport import get_table_from_GZ
from Check.RAG.Collect_Images import get_docket_from_filename
from Common.Constants import local_ip_tro_folder

def migrate_copyright_filenames():
    """
    Renames files in the copyright production folder by prepending the plaintiff_id.
    """
    copyright_production_folder = os.path.join(local_ip_tro_folder, "copyrights", "Production")
    
    if not os.path.exists(copyright_production_folder):
        print(f"Error: Production folder not found at {copyright_production_folder}")
        return

    print("Loading case data...")
    cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    # Create a mapping from docket to plaintiff_id for faster lookup
    docket_to_plaintiff_map = cases_df.set_index('docket')['plaintiff_id'].to_dict()

    print(f"Scanning files in {copyright_production_folder}...")
    filenames = os.listdir(copyright_production_folder)
    
    for filename in tqdm(filenames, desc="Migrating filenames"):
        original_path = os.path.join(copyright_production_folder, filename)
        if not os.path.isfile(original_path):
            continue

        docket = get_docket_from_filename(filename)
        if not docket:
            print(f"Could not extract docket from {filename}. Skipping.")
            continue

        plaintiff_id = docket_to_plaintiff_map.get(docket)
        if plaintiff_id is None or pd.isna(plaintiff_id):
            print(f"Warning: Could not find plaintiff_id for docket {docket} (from file {filename}). Skipping.")
            continue

        # Check if the filename already starts with the plaintiff_id
        if filename.startswith(f"{int(plaintiff_id)}_"):
            continue

        new_filename = f"{int(plaintiff_id)}_{filename}"
        new_path = os.path.join(copyright_production_folder, new_filename)

        print(f"Renaming: '{filename}' to '{new_filename}'")
        shutil.move(original_path, new_path)

    print("Filename migration complete.")

if __name__ == "__main__":
    migrate_copyright_filenames()