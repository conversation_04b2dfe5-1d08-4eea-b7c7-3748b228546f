// =============================================
// General Utility Functions
// =============================================

/**
 * Activates a menu item by adding the 'active' class.
 * @param {string} menuId The ID of the menu item element.
 */
function activateMenuItem(menuId) {
    document.addEventListener('DOMContentLoaded', () => {
        const menuElement = document.getElementById(menuId);
        if (menuElement) {
            // Remove 'active' from other main menu items first
            document.querySelectorAll('.main-menu .menu-item.active').forEach(item => {
                item.classList.remove('active');
            });
            // Add 'active' to the target item
            menuElement.classList.add('active');
        }
    });
}

/**
 * Escapes HTML special characters in a string.
 * @param {string} unsafe The string to escape.
 * @returns {string} The escaped string.
 */
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return unsafe;
    return unsafe
         .replace(/&/g, "&") // Must be first
         .replace(/</g, "<")
         .replace(/>/g, ">")
         .replace(/"/g, "\"") // Escape the double quote
         .replace(/'/g, "&#039;");
}

/**
 * Shows or hides a loading indicator within a specified container.
 * Assumes a standard '.loading' element exists within the container.
 * @param {string|HTMLElement} containerOrSelector The container element or its selector.
 * @param {boolean} show True to show loading, false to hide.
 * @param {string} [message='Loading...'] Optional message to display.
 */
function showLoadingState(containerOrSelector, show, message = 'Loading...') {
    const container = typeof containerOrSelector === 'string'
        ? document.querySelector(containerOrSelector)
        : containerOrSelector;

    if (!container) {
        console.warn('showLoadingState: Container not found:', containerOrSelector);
        return;
    }

    let loadingElement = container.querySelector('.loading');
    if (!loadingElement) {
        // Create if it doesn't exist
        loadingElement = document.createElement('div');
        loadingElement.className = 'loading';
        // Try to insert it appropriately, e.g., at the beginning
        container.insertBefore(loadingElement, container.firstChild);
    }

    if (show) {
        loadingElement.textContent = message;
        loadingElement.style.display = 'block'; // Or 'flex', etc., depending on CSS
    } else {
        loadingElement.style.display = 'none';
    }
}

/**
 * Shows an error message within a specified container.
 * Assumes a standard '.error-message' element exists or can be created.
 * @param {string|HTMLElement} containerOrSelector The container element or its selector.
 * * @param {string} message The error message to display.
 */
function showErrorState(containerOrSelector, message) {
     const container = typeof containerOrSelector === 'string'
        ? document.querySelector(containerOrSelector)
        : containerOrSelector;

    if (!container) {
        console.warn('showErrorState: Container not found:', containerOrSelector);
        return;
    }

    // Clear previous content or hide loading/no-results
    container.innerHTML = ''; // Simple clearing, adjust if needed

    const errorElement = document.createElement('div');
    errorElement.className = 'error-message'; // Use common CSS class
    errorElement.innerHTML = `<strong>Error:</strong><p>${escapeHtml(message)}</p>`; // Basic structure
    container.appendChild(errorElement);
    container.style.display = 'block'; // Ensure container is visible
}

/**
 * Shows a "no results" message within a specified container.
 * Assumes a standard '.no-results' element exists or can be created.
 * @param {string|HTMLElement} containerOrSelector The container element or its selector.
 * @param {string} [message='No results found.'] Optional message.
 */
function showNoResultsState(containerOrSelector, message = 'No results found.') {
    const container = typeof containerOrSelector === 'string'
        ? document.querySelector(containerOrSelector)
        : containerOrSelector;

    if (!container) {
        console.warn('showNoResultsState: Container not found:', containerOrSelector);
        return;
    }

     // Clear previous content or hide loading/error
    container.innerHTML = ''; // Simple clearing, adjust if needed

    const noResultsElement = document.createElement('div');
    noResultsElement.className = 'no-results'; // Use common CSS class
    noResultsElement.textContent = message;
    container.appendChild(noResultsElement);
    container.style.display = 'block'; // Ensure container is visible
}


/**
 * Creates a simple full-screen image preview overlay.
 * @param {string} imageUrl The URL of the image to preview.
 */
function previewImage(imageUrl) {
    // Remove existing overlay if any
    const existingOverlay = document.getElementById('image-preview-overlay'); // Use ID from common.css
    if (existingOverlay) {
        document.body.removeChild(existingOverlay);
    }

    // Create overlay element using CSS class
    const overlay = document.createElement('div');
    overlay.id = 'image-preview-overlay'; // Use ID from common.css
    // Classes will be added later to show it

    // Create content container
    const content = document.createElement('div');
    content.id = 'image-preview-content'; // Use ID from common.css

    // Create image element
    const img = document.createElement('img');
    img.id = 'image-preview-img'; // Use ID from common.css
    img.src = imageUrl;
    img.alt = 'Image Preview';

    // Create close button
    const closeBtn = document.createElement('button');
    closeBtn.id = 'image-preview-close'; // Use ID from common.css
    closeBtn.innerHTML = '&times;'; // Simple 'x'

    // Assemble elements
    content.appendChild(img);
    content.appendChild(closeBtn);
    overlay.appendChild(content);

    // Function to close the overlay
    const closeOverlay = () => {
        overlay.classList.remove('show');
        // Optional: Delay removal to allow for fade-out animation if added in CSS
        setTimeout(() => {
             if (document.body.contains(overlay)) {
                 document.body.removeChild(overlay);
             }
        }, 200); // Adjust timing based on potential CSS transition
    };

    // Event listeners
    overlay.addEventListener('click', (event) => {
        // Close only if clicking the background overlay itself, not the content/image
        if (event.target === overlay) {
            closeOverlay();
        }
    });
    closeBtn.addEventListener('click', closeOverlay);

    // Append to body and show
    document.body.appendChild(overlay);
    // Use setTimeout to allow the element to be added to the DOM before adding the class for transition
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
}



// =============================================
// UI Helper Functions (Added during debugging)
// =============================================

/**
 * Adds a labeled information item to a container.
 * @param {HTMLElement} container The parent element to append to.
 * @param {string} label The label text for the info item.
 * @param {string} value The value text for the info item.
 */
function addInfoItem(container, label, value) {
    const infoItem = document.createElement('div');
    infoItem.className = 'info-item';

    const labelEl = document.createElement('span');
    labelEl.className = 'info-label';
    // Remove the colon from the label if it already has one
    const labelText = label.endsWith(':') ? label : label + ':';
    labelEl.textContent = labelText;

    const valueElement = document.createElement('span');
    valueElement.className = 'info-value';

    // Check if this is plaintiff and log for debugging
    if (label === 'Plaintiff') {
        console.log(`addInfoItem for Plaintiff with value ${value}, container:`, container);
    }

    // Skip the special plaintiff handling since we now handle it in the title
    valueElement.textContent = value || 'N/A'; // Ensure a fallback

    infoItem.appendChild(labelEl);
    infoItem.appendChild(valueElement);
    container.appendChild(infoItem);
    return infoItem; // Ensure the created item is returned
}

/**
 * Adds a labeled information item specifically for IP details.
 * @param {HTMLElement} container The parent element to append to.
 * @param {string} label The label text for the info item.
 * @param {string} value The value text for the info item.
 */
function addIpInfoItem(container, label, value) {
    const infoItem = document.createElement('div');
    // Use a specific class for IP items if needed, otherwise use 'info-item'
    infoItem.className = 'ip-info-item info-item'; // Add both for potential styling

    const labelEl = document.createElement('span');
    labelEl.className = 'info-label';
    const labelText = label.endsWith(':') ? label : label + ':';
    labelEl.textContent = labelText;

    const valueElement = document.createElement('span');
    valueElement.className = 'info-value'; // Or 'ip-info-value' if specific styling exists
    valueElement.textContent = value || 'N/A'; // Ensure a fallback

    infoItem.appendChild(labelEl);
    infoItem.appendChild(valueElement);
    container.appendChild(infoItem);
    return infoItem; // Ensure the created item is returned
}

/**
 * Constructs the URL for an IP image.
 * @param {number|string} plaintiffId The ID of the plaintiff.
 * @param {string} imageName The filename of the image.
 * @param {string} [ipType=null] The type of IP ('trademarks', 'patents', 'copyrights'). Used to determine image quality.
 * @returns {string} The full URL to the image.
 */
function getImagePath(plaintiffId, imageName, ipType = null) {
    // Use high quality for patents (certificates), low quality for others
    // Note: This logic might need refinement if 'full_filename' images (certificates)
    // should always be 'high' regardless of IP type. The makeInfoItemValueLinkable
    // function below assumes 'high' for linked files.
    const quality = (ipType === 'patents') ? 'high' : 'low';
    // Ensure imageName is not null or undefined before using it
    const validImageName = imageName || '';
    return `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${plaintiffId}/${quality}/${validImageName}`;
}

/**
 * Constructs the URL for a certificate/full file image, which is typically high quality.
 * @param {number|string} plaintiffId The ID of the plaintiff.
 * @param {string} filename The filename of the certificate/full file.
 * @returns {string} The full URL to the certificate image.
 */
function getCertificateImageUrl(plaintiffId, filename) {
     // Ensure filename is not null or undefined before using it
    const validFilename = filename || '';
    return `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${plaintiffId}/high/${validFilename}`;
}

/**
 * Makes the value(s) within an IP info item linkable to corresponding full filenames.
 * Handles cases where values and filenames are arrays.
 * @param {HTMLElement} infoItemElement The div element created by addIpInfoItem.
 * @param {number|string} plaintiffId The ID of the plaintiff.
 * @param {string} label The label text (e.g., 'Reg #:', 'Patent #:'). Used in alerts.
 * @param {string|string[]} values The value(s) to display and link (e.g., reg number(s), patent number(s)).
 * @param {string[]} fullFilenames The array of corresponding full filenames.
 */
function makeInfoItemValueLinkable(infoItemElement, plaintiffId, label, values, fullFilenames) {
    if (!infoItemElement) {
        console.warn(`makeInfoItemValueLinkable: infoItemElement is missing for label "${label}".`);
        return;
    }

    const valueSpan = infoItemElement.querySelector('span:last-child');
    if (!valueSpan) {
        console.warn(`makeInfoItemValueLinkable: Could not find value span in infoItemElement for label "${label}".`);
        return;
    }

    // Ensure values is an array
    const valuesArray = Array.isArray(values) ? values : (values !== null && values !== undefined && values !== 'N/A' ? [values] : []);
    // Ensure fullFilenames is an array
    const filenamesArray = Array.isArray(fullFilenames) ? fullFilenames : [];

    // Check for valid data and matching lengths
    if (valuesArray.length === 0 || filenamesArray.length === 0 || valuesArray.length !== filenamesArray.length) {
        // If data is missing or lengths don't match, just display the original value(s) as text
        valueSpan.textContent = Array.isArray(values) ? values.join(', ') || 'N/A' : values || 'N/A';
        if (valuesArray.length > 0 && filenamesArray.length > 0 && valuesArray.length !== filenamesArray.length) {
            console.warn(`makeInfoItemValueLinkable: Mismatch between values count (${valuesArray.length}) and filenames count (${filenamesArray.length}) for label "${label}". Cannot create links.`);
        } else if (valuesArray.length > 0 && filenamesArray.length === 0) {
            console.warn(`makeInfoItemValueLinkable: No full filenames provided for label "${label}" with values: ${valuesArray.join(', ')}. Cannot create links.`);
        }
        return;
    }

    // Clear existing content to replace with links
    valueSpan.innerHTML = '';

    valuesArray.forEach((value, index) => {
        if (index > 0) {
            valueSpan.appendChild(document.createTextNode(', ')); // Add separator
        }

        const link = document.createElement('a');
        link.href = 'javascript:void(0);'; // Use void(0) to prevent page navigation
        link.textContent = value || 'N/A'; // If value is empty, display 'N/A' as link text
        link.style.cursor = 'pointer';
        link.style.textDecoration = 'underline';

        const filename = filenamesArray[index];

        link.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default link behavior
            if (filename) {
                const certUrl = getCertificateImageUrl(plaintiffId, filename);
                previewImage(certUrl); // Call the common preview function
            } else {
                alert(`Image preview not available for ${label.replace(':', '').trim()} "${value}". Full filename is missing.`);
            }
        });

        valueSpan.appendChild(link);
    });
}