"""
Maidalv IP Check API - Python Examples
Complete examples for integrating with the IP Check API
"""

import requests
import base64
import json
import time

# Configuration
API_KEY = "{YOUR_API_KEY}"
BASE_URL = "https://api.maidalv.com"

def encode_image_to_base64(image_path):
    """Convert image file to base64 string"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def submit_check_example():
    """Example: Submit a product for IP check"""
    
    # Prepare the request data
    data = {
        "api_key": API_KEY,
        "main_product_image": encode_image_to_base64("product.jpg"),
        "other_product_images": [
            encode_image_to_base64("product2.jpg"),
            encode_image_to_base64("product3.jpg")
        ],
        "ip_images": [
            encode_image_to_base64("ip_reference.jpg")
        ],
        "description": "Wireless bluetooth headphones",  # Product title recommended
        "ip_keywords": ["bluetooth", "wireless", "headphones", "noise cancellation"],
        "reference_text": "Similar to popular brand headphones",
        "reference_images": [
            encode_image_to_base64("reference.jpg")
        ],
        "language": "en"
    }

    # Submit the check request
    try:
        response = requests.post(
            f"{BASE_URL}/check_api",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            check_id = result.get("check_id")
            status = result.get("status")
            
            print(f"Check submitted successfully!")
            print(f"Check ID: {check_id}")
            print(f"Status: {status}")
            
            if "estimated_completion_time" in result:
                print(f"Estimated completion time: {result['estimated_completion_time']} seconds")
                
            return check_id
            
        else:
            error_data = response.json()
            print(f"Error: {error_data}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def poll_for_results(check_id, max_attempts=120):
    """Poll for results with proper timing based on estimated completion time"""
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/check_status/{check_id}")
            
            if response.status_code == 200:
                result = response.json()
                status = result.get("status")
                
                print(f"Attempt {attempt + 1}: Status = {status}")
                
                if status == "completed":
                    print("Analysis completed!")
                    return result.get("result")
                    
                elif status == "error":
                    print(f"Analysis failed: {result.get('message')}")
                    return None
                    
                elif status in ["queued", "processing"]:
                    # Wait based on status and estimated time
                    if status == "queued":
                        # Use estimated completion time if available
                        estimated_time = result.get("estimated_completion_time", 20)
                        wait_time = min(estimated_time, 20)  # Cap at 20 seconds
                    else:
                        wait_time = 3  # Processing status
                    
                    print(f"Waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                    
            else:
                print(f"HTTP Error: {response.status_code}")
                break
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            break
            
        attempt += 1
    
    print("Max attempts reached or error occurred")
    return None

def handle_api_request_with_retry(url, data, max_retries=3):
    """Handle API request with proper error handling and retries"""
    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                return response.json()
                
            elif response.status_code == 429:
                # Rate limit exceeded
                error_data = response.json()
                error_code = error_data.get('error', {}).get('error_code')
                
                if error_code == 'RATE_LIMIT_MINUTE_EXCEEDED':
                    print("Rate limit exceeded. Waiting 60 seconds...")
                    time.sleep(60)
                    continue
                elif error_code == 'RATE_LIMIT_DAILY_EXCEEDED':
                    print("Daily limit exceeded. Please try tomorrow.")
                    return None
                    
            elif response.status_code == 401:
                print("Authentication failed. Check your API key.")
                return None
                
            else:
                # Other errors
                error_data = response.json()
                print(f"API Error: {error_data}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"Request timeout (attempt {attempt + 1})")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None
    
    print("Max retries exceeded")
    return None

def exponential_backoff_retry(func, max_retries=5, base_delay=1):
    """Retry function with exponential backoff and jitter"""
    import random
    
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            
            # Calculate delay with exponential backoff and jitter
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"Attempt {attempt + 1} failed. Retrying in {delay:.2f} seconds...")
            time.sleep(delay)
    
    raise Exception("Max retries exceeded")

# Complete workflow example
def complete_workflow_example():
    """Complete example workflow"""
    print("Starting IP check workflow...")
    
    # Step 1: Submit check
    check_id = submit_check_example()
    if not check_id:
        print("Failed to submit check")
        return
    
    # Step 2: Poll for results
    results = poll_for_results(check_id)
    if results:
        print("Final results received:")
        print(json.dumps(results, indent=2))
        
        # Process results
        risk_level = results.get("risk_level", "Unknown")
        print(f"\nOverall Risk Level: {risk_level}")
        
        for i, result in enumerate(results.get("results", []), 1):
            print(f"\nResult {i}:")
            print(f"  IP Type: {result.get('ip_type')}")
            print(f"  IP Owner: {result.get('ip_owner')}")
            print(f"  Risk Level: {result.get('risk_level')}")
            print(f"  Risk Score: {result.get('risk_score')}")
            
            if result.get('plaintiff_name'):
                print(f"  Used in TRO: Yes")
                print(f"  Plaintiff: {result.get('plaintiff_name')}")
                print(f"  Cases: {result.get('number_of_cases')}")
            else:
                print(f"  Used in TRO: No")
    else:
        print("Failed to get results")

if __name__ == "__main__":
    complete_workflow_example()
