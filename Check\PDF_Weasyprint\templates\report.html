<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>{{ report_title }} – {{ check_id }}</title>
  <link rel="stylesheet" href="{{ static_base }}/styles.css">
  <link rel="stylesheet" href="{{ static_base }}/cover.css">
</head>
<body>
  <div class="pdf-header">
    <div class="hdr-left">
      <img src="{{ static_base }}/logo.png" class="hdr-logo" alt="logo">
      <div class="hdr-company">{{ company_name }}</div>
    </div>
    <div class="hdr-right"><span class="confidential">CONFIDENTIAL</span></div>
  </div>

  <div class="pdf-footer">
    <div>{{ report_title }}</div>
    <div>Page <span class="page-number"></span> of <span class="page-count"></span></div>
  </div>

<!-- ===== Cover Page ===== -->
<section class="cover">
    <div class="brand">
      <img src="{{ static_base }}/logo.png" class="brand-logo" alt="logo">
      <div class="brand-meta">
        {{ company_name }}
      </div>
    </div>
    <div class="title">INTELLECTUAL PROPERTY INFRINGEMENT<br>RISK ASSESSMENT REPORT</div>

    <div class="meta-grid">
      <div>
        <div class="label">Check ID</div>
        <div class="value mono">{{ check_id }}</div>
      </div>
      <div>
        <div class="label">Client</div>
        <div class="value">{{ client_name }}</div>
      </div>
      <div>
        <div class="label">Overall Risk</div>
        <div class="value">
          <span class="risk-badge {{ global_risk_badge.class }}">{{ global_risk_badge.label }}</span>
        </div>
      </div>
    </div>
    <div class="cover-products">
      <hr>
      <div class="section-title">Product Being Assessed</div>
      <div class="product-grid">
        {% for p in cover_products %}
          <div class="product-card" style="background-image: url('{{ p }}');">
            &nbsp;
          </div>
        {% endfor %}
      </div>
    </div>    
    
    <div class="cover-overview">
      <hr>
      <div class="section-title">Overview of Assessment</div>
      <table class="summary">
        <thead>
          <tr>
            <th>#</th><th>Type</th><th>IP Owner</th><th>Risk</th><th>Page</th>
          </tr>
        </thead>
        <tbody>
        {% for row in summary_rows %}
          <tr>
            <td>{{ loop.index }}</td>
            <td>{{ row.ip_type }}</td>
            <td>{{ row.ip_owner }}</td>
            <td><span class="risk-pill {{ row.pill_class }}">{{ row.risk_level }}</span></td>
            <td>{{ row.page }}</td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </div>



    {% if seal_exists %}
    <div class="seal">
      <img src="{{ static_base }}/seal.png" alt="seal">
    </div>
    {% endif %}
  </div>
</section>

<!-- ===== Per-Result Pages ===== -->
{% for r in results %}
<section class="result-page">
  <div class="result-header">
    <div class="left">
      <div class="chip">{{ r.ip_type }}</div>
      <div class="risk-badge {{ r.risk_badge_class }}">{{ r.risk_badge_label }}</div>
    </div>
    <div class="right">
      {% if r.ip_owner %}
      <div class="row"><span class="key">IP Owner:</span><span class="val">{{ r.ip_owner }}</span></div>
      {% endif %}
      {% if r.plaintiff_name %}
      <div class="row"><span class="key">Plaintiff:</span><span class="val">{{ r.plaintiff_name }}</span></div>
      {% endif %}
      {% if r.text %}
      <div class="row"><span class="key">Text / Title:</span><span class="val">{{ r.text }}</span></div>
      {% endif %}
      {% if r.reg_no %}
      <div class="row"><span class="key">Registration #:</span><span class="val mono">{{ r.reg_no }}</span></div>
      {% endif %}
      {% if r.ser_no %}
      <div class="row"><span class="key">Serial #:</span><span class="val mono">{{ r.ser_no }}</span></div>
      {% endif %}
      {% if r.last_case_docket %}
      <div class="row"><span class="key">Last Docket:</span><span class="val mono">{{ r.last_case_docket }}</span></div>
      {% endif %}
      {% if r.last_case_date_filed %}
      <div class="row"><span class="key">Filed:</span><span class="val">{{ r.last_case_date_filed }}</span></div>
      {% endif %}
      {% if r.risk_description %}
      <div class="row"><span class="key">Risk:</span><span class="val">{{ r.risk_description }}</span></div>
      {% endif %}
      {% if r.int_cls_list %}
      <div class="row"><span class="key">Intl. Classes:</span><span class="val">{{ r.int_cls_list | join(", ") }}</span></div>
      {% endif %}
    </div>
  </div>

  <div class="ip-and-product">
    <div class="ip-images">
      <div class="section-title">IP Images</div>
      {% set cols = 3 if r.ip_type != 'Patent' else 3 %}
      <div class="ip-grid ip-cols-{{ cols }}">
        {% for u in r.ip_asset_urls %}
          <div class="ip-thumb"><img src="{{ u }}" alt="ip image {{ loop.index }}"></div>
        {% endfor %}
      </div>
    </div>
    {% if r.product_url %}
    <div class="product-single">
      <div class="section-title">Product Image</div>
      <div class="product-shot">
        <img src="{{ r.product_url }}" alt="product image">
      </div>
    </div>
    {% endif %}
  </div>

  <div class="report-block">
    <div class="section-title">Analysis & Opinion</div>
    <div class="md">{{ r.report_html | safe }}</div>
  </div>
</section>
{% endfor %}

<!-- ===== Disclaimer Page ===== -->
<section class="page disclaimer">
  <h2>Disclaimer</h2>
  <p>
    This report is generated automatically by Maidalv’s AI-assisted analysis pipeline based on
    the inputs provided. It is intended solely for the recipient client and is confidential and
    privileged. It does not constitute legal advice. Final conclusions should be reviewed by qualified counsel.
  </p>
  <p>
    © {{ now.year }} Shenzhen Maidalv Technology Co., Ltd. All rights reserved.
  </p>
</section>

</body>
</html>
