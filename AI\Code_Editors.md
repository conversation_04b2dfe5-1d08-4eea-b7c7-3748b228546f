| Name               | Tokens                         | Indexing | CodeComplete              | Free Tier                                                        | Requirements                         | Best use                                                                                           |
| ------------------ | ------------------------------ | -------- | ------------------------- | ---------------------------------------------------------------- | ------------------------------------ | -------------------------------------------------------------------------------------------------- |
| Gemini Code Assist | Provided,<br />Gemini 2.5 pro  | Yes      | Short<br />Fast<br />Good | Unlimited                                                        | Google Account                       | Complex Questions<br />Build a plan<br />Small narrow edits on small files                         |
| Augment Code       | Provided,<br />unknown         | Yes      | Complicated               | 50 user message<br />per month                                   | Augment<br />Account                 | Large agentic work (like huge<br />refactoring or new build))                                      |
| Roo Code / Cline   | API key,                       | No       | X                         | Free, Open source<br />API: 500 api calls <br />per day on Flash | Google Gemini<br />API key for free | All, the only limitation is that<br />Gemini 2.5 flash is not the <br />best model => easier tasks |
| Github Copilot     | Provided,<br />Claude Sonnet 4 | ???      | ???                       | 50 ??? per month                                                | Github Account                       | ???_request_with_retry                                                                             |
| Windsurf           | Provided,<br />Claude Sonnet 4 | ???      | ???                       | 50 ??? per month                                                | Windsurf Account                    | ???                                                                                                |
| Claude Code        | Beta, not tested               |          |                           | ???                                                              |                                      |                                                                                                    |
| Blackbox AI        |                                |          |                           | 3 months free                                                    | Credit card                          |                                                                                                    |
| Kilo Code          | Provided,<br />all models      | No       | ???                       | 20 USD                                                           | Credit card                          |                                                                                                    |
|                    |                                |          |                           |                                                                  |                                      |                                                                                                    |
| Trae               | Provided,<br />Deepseek        |          |                           | Unlimited                                                        | Account                              | Nothing, Deepseek is not<br />good enough                                                          |
|                    |                                |          |                           |                                                                  |                                      |                                                                                                    |



**API to Google AI Studio:**


Setup: 

* git clone https://github.com/CJackHwang/AIstudioProxyAPI
* cd AIstudioProxyAPI
* python -m venv venv
* venv\Scripts\activate
* pip install -U camoufox[geoip] -r requirements.txt
* camoufox fetch
* playwright install-deps firefox

Run:

* python launch_camoufox.py --debug --server-port 2048
* Login, then in terminal press Y, then Y then nothing
* In Roo: OpenAI Compatible, http://127.0.0.1:2048/v1, and select the model / write the model name (the Gemini model))
