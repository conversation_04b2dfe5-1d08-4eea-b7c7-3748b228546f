import os
import json
import hashlib
from Common.Constants import local_ip_tro_folder

def get_cache_dir(case_type):
    """Constructs the path to the cache directory for a given case type."""
    cache_dir = os.path.join(local_ip_tro_folder, case_type, "llm_cache")
    os.makedirs(cache_dir, exist_ok=True)
    return cache_dir

def get_cache_filepath(case_type, cache_filename):
    """Constructs the full path to a specific cache file."""
    return os.path.join(get_cache_dir(case_type), cache_filename)

def load_cache(cache_filepath):
    """Loads a JSON cache file if it exists, otherwise returns an empty dictionary."""
    if os.path.exists(cache_filepath):
        with open(cache_filepath, 'r') as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return {}
    return {}

def save_cache(cache_filepath, cache_data):
    """Saves a dictionary to a JSON cache file."""
    with open(cache_filepath, 'w') as f:
        json.dump(cache_data, f, indent=4)

def get_image_hash(image_path):
    """Computes the MD5 hash of an image file."""
    if not os.path.exists(image_path):
        return None
    with open(image_path, "rb") as f:
        return hashlib.md5(f.read()).hexdigest()

def get_cache_key_for_one_image(image_path):
    """Generates a cache key for a single image."""
    return get_image_hash(image_path)

def get_cache_key_for_two_images(image_path1, image_path2):
    """Generates a sorted, combined cache key for two images."""
    hash1 = get_image_hash(image_path1)
    hash2 = get_image_hash(image_path2)
    if not hash1 or not hash2:
        return None
    # Sort hashes to ensure consistency
    return "_".join(sorted([hash1, hash2]))

def vertex_genai_multi_with_cache(case_type, cache_filename, prompt_list, image_path1, image_path2=None):
    """
    A wrapper for vertex_genai_multi that adds a caching layer.
    The cache key is generated based on the hash of the image(s).
    Returns a string that can be processed by get_json, similar to vertex_genai_multi.
    """
    # Determine cache key
    if image_path2:
        cache_key = get_cache_key_for_two_images(image_path1, image_path2)
    else:
        cache_key = get_cache_key_for_one_image(image_path1)

    if not cache_key:
        # If hashing fails, fall back to calling the LLM directly without caching
        from AI.GC_VertexAI import vertex_genai_multi
        return vertex_genai_multi(prompt_list)

    # Load cache
    cache_filepath = get_cache_filepath(case_type, cache_filename)
    cache = load_cache(cache_filepath)

    # Check cache
    if cache_key in cache:
        # Return the cached JSON as a string so get_json can process it
        return json.dumps(cache[cache_key])

    # If not in cache, call LLM
    from AI.GC_VertexAI import vertex_genai_multi
    from AI.LLM_shared import get_json
    
    ai_answer_str = vertex_genai_multi(prompt_list)
    json_answer = get_json(ai_answer_str)

    # Save to cache and return
    if json_answer: # Only cache if we got a valid json
        cache[cache_key] = json_answer
        save_cache(cache_filepath, cache)
    
    return ai_answer_str