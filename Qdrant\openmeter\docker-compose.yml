services:
  # Single-broker Kafka via Redpanda (simple + fast)
  redpanda:
    image: docker.redpanda.com/redpandadata/redpanda:v24.2.5
    command:
      - redpanda
      - start
      - --overprovisioned
      - --smp=1
      - --memory=1G
      - --reserve-memory=0M
      - --check=false
      - --kafka-addr internal://0.0.0.0:9092,external://0.0.0.0:19092
      - --advertise-kafka-addr internal://redpanda:9092,external://localhost:19092
    ports:
      - "19092:19092"
    healthcheck:
      test: ["CMD", "rpk", "cluster", "info", "-X", "brokers=redpanda:9092"]
      interval: 5s
      timeout: 3s
      retries: 30

  # OpenMeter OSS API
  openmeter:
    image: ghcr.io/openmeterio/openmeter:1.0.0-beta.223
    depends_on:
      redpanda:
        condition: service_healthy
    # Attach to external networks where your Postgres/ClickHouse live
    networks:
      - default
      - langfuse_default   # external: where ClickHouse is
      - qdrant_monitoring         # external: where Postgres is
    environment:
      # Point to your existing infra (examples; adjust host/user/pass/db)
      OPENMETER_CLICKHOUSE_DSN: "clickhouse://om:2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ@clickhouse-host:9000/openmeter?dial_timeout=10s"
      OPENMETER_KAFKA_BROKERS: "redpanda:9092"
      OPENMETER_DATABASE_URL: "************************************************************************/openmeter?sslmode=disable"
      # Optional: serve with auth key if you want
      # OPENMETER_API_KEY: "replace-me"
    volumes:
      - ./openmeter.config.yaml:/etc/openmeter/config.yaml:ro
    command: ["serve", "--config", "/etc/openmeter/config.yaml"]
    ports:
      - "8888:8888"

  # Optional: Redis + Svix for webhook notifications
  redis:
    image: redis:7-alpine
    profiles: ["webhook"]
  svix:
    image: svix/svix-server:latest
    environment:
      SVIX_DB_DSN: "***************************************/svix"
      SVIX_REDIS_DSN: "redis://redis:6379"
    profiles: ["webhook"]
    depends_on: [redis]
    ports: ["8071:8071"]

networks:
  langfuse_default:
    external: true
  qdrant_monitoring:
    external: true
