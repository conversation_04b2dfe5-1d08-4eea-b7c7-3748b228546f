"""
Patent Processor

Main processor for handling patent-related operations in legal opinion processing.
Combines database lookups, asset downloads, figure management, and evidence tracking.
"""

import os
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from logdata import log_message

from LegalOpinion.database_manager import DatabaseManager
from LegalOpinion.figure_manager import FigureManager
from LegalOpinion.evidence_manager import EvidenceManager
from LegalOpinion.prior_art_processor import PriorArtProcessor
from LegalOpinion.Curver_check import analyze_curver_scope
from LegalOpinion.patent_utils import normalize_patent_number, is_design_patent, get_patent_country, compare_patent_dates


class PatentProcessor:
    """
    Main processor for patent-related operations in legal opinion processing.
    
    Handles:
    - Asserted patent processing (normalization, DB lookup, asset download)
    - Client patent processing (US and non-US)
    - Figure file management
    - Evidence tracking and manifest generation
    """
    
    def __init__(self, submission_id: str, workspace_paths: Dict[str, str]):
        """
        Initialize the patent processor.
        
        Args:
            submission_id: Unique submission identifier
            workspace_paths: Dictionary of workspace directory paths
        """
        self.submission_id = submission_id
        self.workspace_paths = workspace_paths
        
        # Initialize managers
        self.db_manager = DatabaseManager()
        self.figure_manager = FigureManager()
        self.evidence_manager = EvidenceManager(
            submission_id=submission_id,
            manifests_dir=workspace_paths["manifests"]
        )
        self.prior_art_processor = PriorArtProcessor(
            submission_id=submission_id,
            prior_art_dir=workspace_paths["prior_art"],
            evidence_manager=self.evidence_manager
        )
    
    async def process_asserted_patent(self, patent_number: str) -> Dict[str, Any]:
        """
        Process the asserted patent with full workflow.
        
        Args:
            patent_number: Raw patent number from form
            
        Returns:
            Dictionary with processing results
        """
        log_message(f"Processing asserted patent: {patent_number}", level='INFO')
        
        result = {
            "success": False,
            "patent_number": patent_number,
            "normalized_reg_no": "",
            "db_status": "missing",
            "patent_data": None,
            "downloaded_files": {},
            "figure_files": {},
            "evidence_entries": 0,
            "error": None
        }
        
        try:
            # Step 1: Process patent through database manager
            asserted_dir = self.workspace_paths["asserted_patent"]
            db_result = await self.db_manager.process_asserted_patent(patent_number, asserted_dir)
            
            # Update result with database processing info
            result.update({
                "normalized_reg_no": db_result.get("reg_no", ""),
                "db_status": db_result.get("db_status", "missing"),
                "patent_data": db_result.get("patent_data"),
                "downloaded_files": db_result.get("downloaded_files", {})
            })
            
            # Step 2: Process figures if patent data is available
            if db_result.get("patent_data") and db_result.get("db_status") == "found":
                figure_result = self.figure_manager.process_patent_figures(
                    patent_data=db_result["patent_data"],
                    destination_dir=asserted_dir
                )
                result["figure_files"] = figure_result
            
            # Step 3: Add files to evidence manifest
            evidence_count = 0
            
            # Add downloaded patent files
            for file_type, file_path in result["downloaded_files"].items():
                if file_path and os.path.exists(file_path):
                    if self.evidence_manager.add_file_to_manifest(
                        file_path=file_path,
                        category="asserted_patent",
                        provenance="USPTO_API",
                        metadata={
                            "file_type": file_type,
                            "patent_reg_no": result["normalized_reg_no"],
                            "document_id": result["patent_data"].get("document_id") if result["patent_data"] else None
                        }
                    ):
                        evidence_count += 1
            
            # Add figure files
            if result["figure_files"].get("copied_files"):
                for figure_path in result["figure_files"]["copied_files"]:
                    if self.evidence_manager.add_file_to_manifest(
                        file_path=figure_path,
                        category="asserted_patent",
                        provenance="USPTO_Grants",
                        metadata={
                            "file_type": "design_figure",
                            "patent_reg_no": result["normalized_reg_no"]
                        }
                    ):
                        evidence_count += 1
            
            # Add metadata file
            if db_result.get("metadata_path") and os.path.exists(db_result["metadata_path"]):
                if self.evidence_manager.add_file_to_manifest(
                    file_path=db_result["metadata_path"],
                    category="asserted_patent",
                    provenance="database_metadata",
                    metadata={
                        "file_type": "metadata",
                        "patent_reg_no": result["normalized_reg_no"]
                    }
                ):
                    evidence_count += 1
            
            result["evidence_entries"] = evidence_count
            result["success"] = db_result.get("success", False)
            
            log_message(f"Completed asserted patent processing: {patent_number} -> {result['normalized_reg_no']}", level='INFO')
            
        except Exception as e:
            log_message(f"Error processing asserted patent {patent_number}: {e}", level='ERROR')
            result["error"] = str(e)
            result["success"] = False
        
        return result
    
    async def process_client_patents(self, client_patents: List[Dict[str, Any]],
                                   uploaded_files: Optional[Dict[str, Any]] = None,
                                   asserted_patent_data: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Process client-owned patents (both US and non-US).

        Args:
            client_patents: List of client patent info from form
            uploaded_files: Dictionary of uploaded patent files
            asserted_patent_data: Asserted patent data for date comparison

        Returns:
            List of processing results for each patent
        """
        log_message(f"Processing {len(client_patents)} client patents", level='INFO')
        
        results = []
        client_patents_dir = self.workspace_paths["client_patents"]
        
        try:
            # Process US patents through database
            us_results = await self.db_manager.process_client_patents(client_patents, client_patents_dir)
            
            # Process each result and add figures/evidence
            for i, db_result in enumerate(us_results):
                result = {
                    "index": i,
                    "success": False,
                    "patent_info": db_result.get("original_patent_info", {}),
                    "country": db_result.get("country", ""),
                    "normalized_reg_no": db_result.get("reg_no", ""),
                    "db_status": db_result.get("db_status", "missing"),
                    "patent_data": db_result.get("patent_data"),
                    "downloaded_files": db_result.get("downloaded_files", {}),
                    "figure_files": {},
                    "uploaded_file": None,
                    "evidence_entries": 0,
                    "error": None
                }
                
                try:
                    # Handle US patents with database data
                    if result["country"] in ["US", "USA"] and result["db_status"] == "found":
                        # Process figures
                        if result["patent_data"]:
                            patent_dir = os.path.join(client_patents_dir, result["normalized_reg_no"])
                            figure_result = self.figure_manager.process_patent_figures(
                                patent_data=result["patent_data"],
                                destination_dir=patent_dir
                            )
                            result["figure_files"] = figure_result
                    
                    # Handle uploaded files for this patent
                    patent_file_key = f"clientPatentDocs_{i}"
                    if uploaded_files and patent_file_key in uploaded_files:
                        uploaded_file = uploaded_files[patent_file_key]
                        
                        # Determine patent directory
                        if result["normalized_reg_no"]:
                            patent_dir = os.path.join(client_patents_dir, result["normalized_reg_no"])
                        else:
                            # Use original patent number for non-US or unknown patents
                            original_number = result["patent_info"].get("patentNumber", f"patent_{i}")
                            patent_dir = os.path.join(client_patents_dir, original_number)
                        
                        os.makedirs(patent_dir, exist_ok=True)
                        
                        # Save uploaded file
                        file_extension = os.path.splitext(uploaded_file.filename)[1]
                        saved_file_path = os.path.join(patent_dir, f"patent_document{file_extension}")
                        
                        with open(saved_file_path, 'wb') as f:
                            content = await uploaded_file.read()
                            f.write(content)
                        
                        result["uploaded_file"] = saved_file_path

                    # Compare dates with asserted patent if available
                    if asserted_patent_data and result["patent_info"].get("date"):
                        asserted_date = asserted_patent_data.get("issue_date") or asserted_patent_data.get("publication_date")
                        if asserted_date:
                            date_comparison = compare_patent_dates(
                                client_patent_date=result["patent_info"]["date"],
                                asserted_patent_date=asserted_date
                            )
                            result["date_comparison"] = date_comparison

                            if date_comparison.get("is_prior_to_asserted"):
                                log_message(f"Client patent {result['normalized_reg_no']} is prior art to asserted patent", level='INFO')

                    # Add files to evidence manifest
                    evidence_count = 0
                    category = "client_patents"
                    
                    # Add downloaded files (US patents)
                    for file_type, file_path in result["downloaded_files"].items():
                        if file_path and os.path.exists(file_path):
                            if self.evidence_manager.add_file_to_manifest(
                                file_path=file_path,
                                category=category,
                                provenance="USPTO_API",
                                metadata={
                                    "file_type": file_type,
                                    "patent_reg_no": result["normalized_reg_no"],
                                    "country": result["country"],
                                    "client_patent_index": i
                                }
                            ):
                                evidence_count += 1
                    
                    # Add figure files (US patents)
                    if result["figure_files"].get("copied_files"):
                        for figure_path in result["figure_files"]["copied_files"]:
                            if self.evidence_manager.add_file_to_manifest(
                                file_path=figure_path,
                                category=category,
                                provenance="USPTO_Grants",
                                metadata={
                                    "file_type": "design_figure",
                                    "patent_reg_no": result["normalized_reg_no"],
                                    "country": result["country"],
                                    "client_patent_index": i
                                }
                            ):
                                evidence_count += 1
                    
                    # Add uploaded file
                    if result["uploaded_file"] and os.path.exists(result["uploaded_file"]):
                        if self.evidence_manager.add_file_to_manifest(
                            file_path=result["uploaded_file"],
                            category=category,
                            provenance="client_upload",
                            metadata={
                                "file_type": "patent_document",
                                "patent_reg_no": result["normalized_reg_no"] or result["patent_info"].get("patentNumber", ""),
                                "country": result["country"],
                                "client_patent_index": i
                            }
                        ):
                            evidence_count += 1
                    
                    # Add metadata file
                    if db_result.get("metadata_path") and os.path.exists(db_result["metadata_path"]):
                        if self.evidence_manager.add_file_to_manifest(
                            file_path=db_result["metadata_path"],
                            category=category,
                            provenance="database_metadata",
                            metadata={
                                "file_type": "metadata",
                                "patent_reg_no": result["normalized_reg_no"],
                                "country": result["country"],
                                "client_patent_index": i
                            }
                        ):
                            evidence_count += 1
                    
                    result["evidence_entries"] = evidence_count
                    result["success"] = True
                    
                except Exception as e:
                    log_message(f"Error processing client patent {i}: {e}", level='ERROR')
                    result["error"] = str(e)
                    result["success"] = False
                
                results.append(result)
            
            log_message(f"Completed processing {len(results)} client patents", level='INFO')
            
        except Exception as e:
            log_message(f"Error processing client patents: {e}", level='ERROR')
        
        return results
    
    async def process_product_images(self, uploaded_files: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process uploaded product images.
        
        Args:
            uploaded_files: Dictionary of uploaded files
            
        Returns:
            Processing results
        """
        result = {
            "success": False,
            "saved_files": [],
            "evidence_entries": 0,
            "error": None
        }
        
        try:
            product_images_dir = self.workspace_paths["product_images"]
            os.makedirs(product_images_dir, exist_ok=True)
            
            # Process product images
            for key, uploaded_file in uploaded_files.items():
                if key.startswith("productImages_"):
                    # Save the file
                    file_extension = os.path.splitext(uploaded_file.filename)[1]
                    saved_file_path = os.path.join(product_images_dir, uploaded_file.filename)
                    
                    with open(saved_file_path, 'wb') as f:
                        content = await uploaded_file.read()
                        f.write(content)
                    
                    result["saved_files"].append(saved_file_path)
                    
                    # Add to evidence manifest
                    if self.evidence_manager.add_file_to_manifest(
                        file_path=saved_file_path,
                        category="product_images",
                        provenance="client_upload",
                        metadata={
                            "file_type": "product_image",
                            "original_filename": uploaded_file.filename
                        }
                    ):
                        result["evidence_entries"] += 1
            
            result["success"] = True
            log_message(f"Processed {len(result['saved_files'])} product images", level='INFO')
            
        except Exception as e:
            log_message(f"Error processing product images: {e}", level='ERROR')
            result["error"] = str(e)
        
        return result

    async def process_prior_art(self, form_data: Dict[str, Any],
                              uploaded_files: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process prior art from form data and uploaded files.

        Args:
            form_data: Form data containing prior art information
            uploaded_files: Dictionary of uploaded files (optional)

        Returns:
            Processing results
        """
        result = {
            "success": False,
            "uploaded_files": {},
            "form_entries": {},
            "evidence_entries": 0,
            "error": None
        }

        try:
            # Process uploaded prior art files
            if uploaded_files:
                uploaded_result = await self.prior_art_processor.process_uploaded_prior_art(uploaded_files)
                result["uploaded_files"] = uploaded_result
                result["evidence_entries"] += uploaded_result.get("evidence_entries", 0)

            # Process prior art entries from form
            prior_art_data = form_data.get("priorArt", [])
            if prior_art_data:
                form_result = self.prior_art_processor.process_form_prior_art(prior_art_data)
                result["form_entries"] = form_result
                result["evidence_entries"] += form_result.get("evidence_entries", 0)

            result["success"] = True
            log_message(f"Processed prior art for submission: {self.submission_id}", level='INFO')

        except Exception as e:
            log_message(f"Error processing prior art: {e}", level='ERROR')
            result["error"] = str(e)

        return result

    async def perform_curver_analysis(self, asserted_patent_data: Dict[str, Any],
                                    product_images_dir: str,
                                    product_description: str = "") -> Dict[str, Any]:
        """
        Perform Curver article-of-manufacture analysis for scope checking.

        Args:
            asserted_patent_data: Asserted patent data from database
            product_images_dir: Directory containing product images
            product_description: Product description from form

        Returns:
            Curver analysis results
        """
        result = {
            "success": False,
            "analysis": {},
            "error": None
        }

        try:
            # Collect product image files
            product_images = []
            if os.path.exists(product_images_dir):
                for filename in os.listdir(product_images_dir):
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff')):
                        product_images.append(os.path.join(product_images_dir, filename))

            if not product_images:
                log_message("No product images found for Curver analysis", level='WARNING')

            # Perform Curver analysis
            analysis = await analyze_curver_scope(
                patent_data=asserted_patent_data,
                product_images=product_images,
                product_description=product_description
            )

            result["analysis"] = analysis
            result["success"] = analysis.get("success", False)

            if result["success"]:
                log_message(f"Curver analysis completed: {analysis.get('patent_article', '')} vs {analysis.get('product_article', '')}, mismatch: {analysis.get('scope_mismatch', False)}", level='INFO')
            else:
                result["error"] = analysis.get("error", "Unknown error in Curver analysis")

        except Exception as e:
            log_message(f"Error performing Curver analysis: {e}", level='ERROR')
            result["error"] = str(e)

        return result

    async def finalize_processing(self, form_data: Dict[str, Any],
                                processing_results: Dict[str, Any]) -> bool:
        """
        Finalize processing by saving manifests and creating collected case JSON.
        
        Args:
            form_data: Original form data
            processing_results: All processing results
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Save evidence manifest
            if not self.evidence_manager.save_manifest():
                return False
            
            # Create collected case JSON
            if not self.evidence_manager.create_collected_case_json(form_data, processing_results):
                return False
            
            log_message(f"Finalized processing for submission: {self.submission_id}", level='INFO')
            return True
            
        except Exception as e:
            log_message(f"Error finalizing processing: {e}", level='ERROR')
            return False
    
    async def cleanup(self):
        """Clean up resources."""
        try:
            await self.db_manager.close_patent_api()
        except Exception as e:
            log_message(f"Error during cleanup: {e}", level='WARNING')
