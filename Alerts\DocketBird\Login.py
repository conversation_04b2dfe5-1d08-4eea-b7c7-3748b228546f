from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from Alerts.Chrome_Driver import send_keyboard_input
from Common.Constants import chrome_user_data_dir
import os
from Alerts.Chrome_Driver import get_driver, move_mouse_to, random_delay
from logdata import log_message
from selenium.common.exceptions import NoSuchElementException


def docketbird_get_logged_in_browser():
    # Determine the appropriate user data directory and location of the driver
    driver = get_driver()
    random_delay()
    driver.get('https://www.docketbird.com/members_home')

    # Wait for the login form to appear for up to 40 seconds
    WebDriverWait(driver, 40).until(EC.presence_of_element_located((By.ID, 'login-form')))

    # Check if need to login into the platform
    try:
        if driver.find_element(By.ID, 'login-form'):
            login_into_platform_fast(driver)
        log_message(f'User has been logged in successfully in DocketBird.')
        WebDriverWait(driver, 40).until(EC.presence_of_element_located((By.ID, 'add-case-button')))
    except NoSuchElementException:
        log_message(f'User already logged in in DocketBird.')

    return driver


def login_into_platform_fast(driver):
    try:
        driver.find_element(By.ID, 'email_address').send_keys(os.environ["DBusername"])
        driver.find_element(By.ID, 'password').send_keys(os.environ["DBpassword"])
        driver.find_element(By.ID, 'submit-button').click()
    except Exception as e:
        print("Login form not found or error during login:", e)


def login_into_platform_slow(driver):
    # Check if the login form is present
    try:
        actions = ActionChains(driver)
        
        # Move to the username input, click, and enter the username one by one
        username_input = driver.find_element(By.ID, 'email_address')
        send_keyboard_input(driver, username_input, os.environ["DBusername"])
 
        password_input = driver.find_element(By.ID, 'password')
        send_keyboard_input(driver, password_input, os.environ["DBpassword"])

        # Move to the "Sign In" button and click it
        sign_in_button = driver.find_element(By.ID, 'submit-button')
        actions.move_to_element(sign_in_button).click().perform()

    except Exception as e:
        print("Login form not found or error during login:", e)


if __name__ == "__main__":
    docketbird_get_logged_in_browser()
