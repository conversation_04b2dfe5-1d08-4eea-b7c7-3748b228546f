import os, json, cv2, time, asyncio, grpc
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor, as_completed
import numpy as np
import logging
from pathlib import Path
from psycopg2.pool import <PERSON>Con<PERSON><PERSON><PERSON>ool
from psycopg2.extras import execute_values
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor
from Protos import embedding_pb2
from Protos import embedding_pb2_grpc
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance, PointIdsList
from qdrant_client.http.models import UpdateStatus
from logdata import log_message
from Common.uuid_utils import generate_uuid, generate_obfuscated_key
from FileManagement.Tencent_COS import get_cos_client, upload_image_buffer

import cv2, numpy as np
from PIL import Image
import io
import pyvips
cv2.setNumThreads(1)


# For 3000 images

# All the same: 
# 7 workers, 14 threads, 16 connections = 835 sec
# 3 workers, 30 threads, 16 connections = 838 sec
# 3 workers , 15 thread, no connection pool, cos client in the function = 838 sec
# 3 workers , 6 thread, no connection pool, cos client in the function  = 949 sec
# 3 workers , 6 thread,  no connection pool, cos client in init = 837 sec
# 2 workers, 4 threads, 16 connection = 832 sec
# 2 workers, 4threads, 2 connections (no connection queue) = 824 sec

# 25% longer: 
# 2 workers , 2 thread = 1001 sec
# 1 worker, 1 thread = nothing is happening
# 1 worker, 5 thread = 1066 sec


RESIZE_IMAGE_SIZE = 512
PATENT_BATCH_SIZE = 10  # Number of patents to process per embedding request
TRADEMARK_BATCH_SIZE = 50  # Image-based batching for trademarks

# Database connection pool
db_pool = SimpleConnectionPool(1, 10, user=os.getenv("POSTGRES_USER"), password=os.getenv("POSTGRES_PASSWORD"), host=os.getenv("POSTGRES_HOST"), port=os.getenv("POSTGRES_PORT"), database=os.getenv("POSTGRES_DB"))
class EmbeddingQueue:
    def __init__(self, ip_type="trademark", max_concurrent=5):
        """
        Unified embedding queue for both patents and trademarks
        
        Args:
            ip_type (str): Either "patent" or "trademark" to determine processing behavior
            max_concurrent (int): Maximum concurrent processing tasks
        """
        self.ip_type = ip_type.lower()
        self.queue = asyncio.Queue()
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.client = QdrantClient(url=os.environ.get("QDRANT_URL"), api_key=os.environ.get("QDRANT_API_KEY"), timeout=30, prefer_grpc=True)
        self.collection_name = "IP_Assets"
        self.max_concurrent = max_concurrent
        self.workers = []
        self.shutdown_event = asyncio.Event()
        
        # Silence verbose loggers
        logging.getLogger("qcloud_cos").setLevel(logging.ERROR)
        logging.getLogger("urllib3.connectionpool").setLevel(logging.ERROR)
        logging.getLogger('pyvips').setLevel(logging.WARNING)

        # Shared thread pool for blocking IO-bound tasks
        self.thread_executor = ThreadPoolExecutor(max_workers=10) # For network/DB calls
        
        # Process pool for CPU-bound tasks like image resizing
        self.process_executor = ProcessPoolExecutor(max_workers=10) # Use available cores

        # Reusable gRPC channel with authentication
        token = os.getenv("API_BEARER_TOKEN")
        if not token:
            log_message("API_BEARER_TOKEN environment variable not set for EmbeddingQueue.", level='ERROR')
            raise ValueError("API_BEARER_TOKEN environment variable not set for EmbeddingQueue.")
        
        class AuthInterceptor(grpc.UnaryUnaryClientInterceptor):
            def intercept_unary_unary(self, continuation, client_call_details, request):
                metadata = []
                if client_call_details.metadata is not None:
                    metadata = list(client_call_details.metadata)
                metadata.append(('authorization', f'Bearer {token}'))
                client_call_details = client_call_details._replace(metadata=metadata)
                return continuation(client_call_details, request)

        # self.grpc_channel = grpc.insecure_channel('', options=[ # '93.82.137.6:40381'  62.34.68.0:49644
        #     ('grpc.max_send_message_length', 100 * 1024 * 1024),
        #     ('grpc.max_receive_message_length', 100 * 1024 * 1024)
        # ])
        
        creds = grpc.ssl_channel_credentials()  # uses system CAs
        self.grpc_channel = grpc.secure_channel('embeddings.maidalv.com:443', creds, options=[
            ('grpc.max_send_message_length', 100 * 1024 * 1024),
            ('grpc.max_receive_message_length', 100 * 1024 * 1024)
        ])
        
        intercepted_channel = grpc.intercept_channel(self.grpc_channel, AuthInterceptor())
        self.grpc_stub = embedding_pb2_grpc.EmbeddingServiceStub(intercepted_channel)
        
        self._ensure_collection()

        # Initialize COS client once
        self.cos_client, self.cos_bucket = get_cos_client(secret_id_env="COS_MDLV_SECRET_ID",secret_key_env="COS_MDLV_SECRET_KEY",bucket="tro-1330776830")

    async def drain(self):
        """Wait until queue is empty and all tasks are processed"""
        await self.queue.join()
        log_message("All embedding queue tasks completed", level='INFO')
    
    def _ensure_collection(self):
        """Create collection if it doesn't exist"""
        collections = self.client.get_collections().collections
        if not any(c.name == self.collection_name for c in collections):
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
            )
            log_message(f"Created Qdrant collection: {self.collection_name}", level='INFO')


    async def process_batch(self, batch):
        """Process batch of images - handles both patent splitting and trademark embedding workflows"""
        if not batch:
            return

        if self.ip_type == "patent":
            await self._process_patent_batch(batch)
        elif self.ip_type == "trademark":
            await self._process_trademark_batch(batch)
        else:
            log_message(f"Unknown ip_type: {self.ip_type}", level='ERROR')

    async def _process_patent_batch(self, batch):
        """Process batch of patent image groups with splitting workflow using gRPC."""
        image_infos = []
        flattened_items = []
        processed_groups = 0

        for group in batch:
            if not isinstance(group, (list, tuple)):
                log_message(f"Unexpected patent batch item type: {type(group)}", level='WARNING')
                continue

            group_valid_items = []
            for item in group:
                ip_type, reg_no, image_path = item
                if not os.path.exists(image_path) or os.path.isdir(image_path):
                    log_message(f"Image missing or is a directory: {image_path}", level='WARNING')
                    continue

                try:
                    with open(image_path, "rb") as img_file:
                        image_bytes = img_file.read()
                except Exception as read_error:
                    log_message(f"Failed to read image {image_path}: {read_error}", level='ERROR')
                    continue

                image_infos.append(embedding_pb2.ImageInfo(
                    image_data=image_bytes,
                    original_filename=os.path.basename(image_path)
                ))
                group_valid_items.append(item)

            if group_valid_items:
                flattened_items.extend(group_valid_items)
                processed_groups += 1

        if not flattened_items:
            return

        request = embedding_pb2.GetImageSplitWithEmbeddingsRequest(images=image_infos)

        while True:
            try:
                start_time = time.time()
                response = self.grpc_stub.GetImageSplitWithEmbeddings(request)
                api_end_time = time.time()
                await self._process_patent_splitting_result(response, flattened_items, start_time, api_end_time)
                # log_message(f"Processed {processed_groups} patent groups containing {len(flattened_items)} images in a single embedding request.",level='INFO')
                break  # Success, exit loop
            except grpc.RpcError as e:
                if e.code() in [grpc.StatusCode.UNAVAILABLE, grpc.StatusCode.INTERNAL, grpc.StatusCode.DEADLINE_EXCEEDED]:
                    log_message(f"gRPC service transient error ({e.code().name}), retrying in 60 seconds: {e.details()}", level='WARNING')
                    await asyncio.sleep(60)
                else:
                    log_message(f"Unhandled gRPC error processing patent batch: {e.code()} - {e.details()}", level='ERROR')
                    break
            except Exception as e:
                log_message(f"Error processing patent batch: {str(e)}", level='ERROR')
                break

    async def _process_patent_splitting_result(self, response, valid_items, start_time=None, api_end_time=None):
        """Process patent splitting results from gRPC response."""
        processing_start_time = time.time()
        
        all_points_to_upsert = []
        db_update_map = {}  # Aggregate updates per reg_no so each patent row updates once
        all_images_to_process = [] # Will hold tuples of (image_bytes, filename_stem, destination_path)

        # Create a map of original_filename to item details
        items_map = {os.path.basename(item[2]): item for item in valid_items}

        # One result is one image of one patent
        for result in response.results:
            original_filename = result.original_filename
            item = items_map.get(original_filename)
            if not item:
                log_message(f"Could not find matching item for {original_filename}", level='WARNING')
                continue
            
            ip_type, reg_no, original_image_path = item
            original_path = Path(original_image_path)
            destination_folder = original_path.parent

            new_filenames = []
            points_to_upsert = []
            images_to_process = [] # (image_bytes, filename_stem, destination_path)
            rectangles_map = {}

            # Reconstruct rectangles from the map
            for filename, rect_list in result.rects_map.items():
                reconstructed_rects = []
                for rect in rect_list.rects:
                    # Points are flattened, reshape back to (N, 2). Assuming 4 points per rect.
                    points = np.array(rect.points).reshape(-1, 2).tolist()
                    reconstructed_rects.append(points)
                rectangles_map[filename] = reconstructed_rects

            # One image of one patent has been split into multiple images.
            # If the image was not split, there is only one image (the original, or original without the "fig."" text)
            for i, split in enumerate(result.image_splits):
                try:
                    new_filename = split.filename
                    image_bytes = split.image_data
                    new_stem = Path(new_filename).stem
                    
                    new_file_path = destination_folder / new_filename
                    
                    # Pass bytes and metadata to the next step for processing
                    new_filenames.append(new_filename)
                    images_to_process.append((image_bytes, new_stem, new_file_path))

                    point_id = generate_uuid(new_stem)
                    embedding = np.array(split.embedding, dtype=np.float32)

                    point = PointStruct(
                        id=point_id,
                        vector={"siglip_vector": embedding.tolist()},
                        payload={"ip_type": ip_type, "reg_no": reg_no}
                    )
                    points_to_upsert.append(point)

                except Exception as e:
                    log_message(f"Error processing split image {split.filename} for {original_filename}: {str(e)}", level='ERROR')

            if new_filenames:
                reg_entry = db_update_map.setdefault(reg_no, {
                    "reg_no": reg_no,
                    "new_filenames": [],
                    "rectangles": {} # Changed to dict
                })
                reg_entry["new_filenames"].extend(new_filenames)
                reg_entry["rectangles"].update(rectangles_map)

            all_points_to_upsert.extend(points_to_upsert)
            all_images_to_process.extend(images_to_process)

        db_update_tasks = list(db_update_map.values())

        # Perform batch operations in parallel using a thread pool for blocking tasks
        loop = asyncio.get_running_loop()
        
        # Run blocking IO/CPU-bound operations in the shared executor
        qdrant_task = loop.run_in_executor(self.thread_executor, self.upsert_to_qdrant, all_points_to_upsert)
        cos_task = loop.run_in_executor(self.thread_executor, self._save_convert_and_upload_patent_images, all_images_to_process)
        db_task = loop.run_in_executor(self.thread_executor, self.batch_update_patent_fig_files, db_update_tasks)
        
        await asyncio.gather(qdrant_task, cos_task, db_task)

        queue_size = self.queue.qsize()
        processing_end_time = time.time()
        api_duration = api_end_time - start_time if start_time and api_end_time else "N/A"
        processing_duration = processing_end_time - processing_start_time
        
        last_image_name = os.path.splitext(os.path.basename(all_images_to_process[-1][2]))[0] if all_images_to_process else "N/A"
        log_message(f"Processed {len(valid_items)} patent images (incl. {last_image_name}). API: {api_duration:.2f}s, Processing: {processing_duration:.2f}s. {queue_size} items remaining.", level='INFO')

    def _upload_image_task(self, buffer, cos_key):
        """(Sync, I/O-bound) Uploads a single image buffer to COS."""
        if not buffer or not cos_key:
            return False
        try:
            result, _ = upload_image_buffer(self.cos_client, self.cos_bucket, buffer, cos_key)
            return result
        except Exception as e:
            log_message(f"Error uploading to COS key {cos_key}: {e}", level="ERROR")
            return False

    def _save_convert_and_upload_patent_images(self, images_to_process):
        """
        (Sync) Pipelines image saving to TIFF (I/O-bound), resizing for COS (CPU-bound),
        and uploading (I/O-bound) using appropriate executors.
        """
        if not images_to_process:
            return

        successful_uploads = 0
        failed_uploads = 0

        # 1) Save TIFF files and schedule CPU-bound resizing jobs
        cpu_futs = {}
        for image_bytes, stem, dest_path in images_to_process:
            try:
                # I/O-bound: Save the original TIFF in the current thread
                with open(dest_path, 'wb') as f:
                    f.write(image_bytes)
                
                # CPU-bound: Schedule resizing job
                fut = self.process_executor.submit(_process_and_resize_image_task, image_bytes, stem)
                cpu_futs[fut] = stem
            except Exception as e:
                log_message(f"Failed to save or schedule resizing for {stem}: {e}", level="ERROR")
                failed_uploads += 1

        # 2) As each CPU job finishes, schedule its I/O-bound upload job
        upload_futs = {}
        for cpu_fut in as_completed(cpu_futs):
            original_stem = cpu_futs[cpu_fut]
            try:
                result = cpu_fut.result()
                if not result:
                    failed_uploads += 1
                    continue
                
                buffer, cos_key = result
                if not buffer or not cos_key:
                    failed_uploads += 1
                    continue
                
                upload_fut = self.thread_executor.submit(self._upload_image_task, buffer, cos_key)
                upload_futs[upload_fut] = cos_key
            except Exception as e:
                log_message(f"Resizing failed for {original_stem}: {e}", level="ERROR")
                failed_uploads += 1

        # 3) Wait for uploads to complete
        for upload_fut in as_completed(upload_futs):
            cos_key = upload_futs[upload_fut]
            try:
                success = upload_fut.result()
                if success:
                    successful_uploads += 1
                else:
                    failed_uploads += 1
            except Exception as e:
                log_message(f"Upload failed for {cos_key}: {e}", level="ERROR")
                failed_uploads += 1
        
        if failed_uploads > 0:
            log_message(f"Patent image upload complete. Succeeded: {successful_uploads}, Failed: {failed_uploads}", level="WARNING")

    async def _process_trademark_batch(self, batch):
        """Process batch of trademark images with embedding workflow using gRPC."""
        point_ids = [item[0] for item in batch]
        try:
            existing_points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=point_ids,
                with_vectors=False
            )
            existing_ids = {point.id for point in existing_points}
        except Exception as e:
            log_message(f"Qdrant batch check failed: {str(e)}", level='ERROR')
            existing_ids = set()

        to_process = [item for item in batch if item[0] not in existing_ids]

        if not to_process:
            return

        image_bytes_list = []
        valid_items = []
        for point_id, image_path, ser_no in to_process:
            if os.path.exists(image_path):
                try:
                    with open(image_path, "rb") as img_file:
                        image_bytes_list.append(img_file.read())
                    valid_items.append((point_id, ser_no))
                except Exception as e:
                    log_message(f"Error reading {image_path}: {str(e)}", level='ERROR')
            else:
                log_message(f"Image missing: {image_path}", level='WARNING')

        if not valid_items:
            return

        while True:
            try:
                images_proto = [embedding_pb2.Image(image_data=img_bytes) for img_bytes in image_bytes_list]
                request = embedding_pb2.GetImageEmbeddingsRequest(images=images_proto)
                
                response = self.grpc_stub.GetImageEmbeddings(request)
                
                embeddings = [list(emb.embedding.embedding) for emb in response.embeddings]
                
                await self._process_embeddings_result(embeddings, valid_items)
                break # Success, exit loop

            except grpc.RpcError as e:
                if e.code() in [grpc.StatusCode.UNAVAILABLE, grpc.StatusCode.INTERNAL, grpc.StatusCode.DEADLINE_EXCEEDED]:
                    log_message(f"gRPC service transient error ({e.code().name}), retrying in 60 seconds: {e.details()}", level='WARNING')
                    await asyncio.sleep(60)
                else:
                    log_message(f"gRPC error processing trademark batch: {e.code()} - {e.details()}", level='ERROR')
                    break
            except Exception as e:
                log_message(f"Trademark embedding processing failed: {str(e)}", level='ERROR')
                break

    async def _process_embeddings_result(self, embeddings, valid_items):
        """
        Process the embeddings result and store in Qdrant (for trademarks)

        Args:
            embeddings (list): The list of embeddings
            valid_items (list): List of (point_id, ser_no) tuples
        """
        try:
            if len(embeddings) != len(valid_items):
                log_message(f"Embedding count mismatch: expected {len(valid_items)}, got {len(embeddings)}", level='ERROR')
                return

            # Create points for Qdrant
            points = [
                PointStruct(
                    id=item[0],
                    vector={"siglip_vector": embedding},
                    payload={"ip_type": "Trademark"}
                )
                for item, embedding in zip(valid_items, embeddings)
            ]

            # Upload to Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=points,
                wait=True
            )

            queue_size = self.queue.qsize()
            log_message(f"Uploaded {len(points)} trademark embeddings to Qdrant, {queue_size} items remaining in queue", level='INFO')

        except Exception as e:
            log_message(f"Error processing embeddings result: {str(e)}", level='ERROR')

    def upsert_to_qdrant(self, points):
        """(Sync) Upsert points to Qdrant."""
        if not points:
            return
        try:
            self.client.upsert(collection_name=self.collection_name, points=points, wait=True)
        except Exception as e:
            log_message(f"Error uploading embeddings: {str(e)}", level='ERROR')

    def batch_update_patent_fig_files(self, update_data):
        """Batch update fig_files and fig_rectangles using execute_values."""
        if not update_data:
            return
        conn = None
        try:
            conn = db_pool.getconn()
            if not conn:
                log_message("Failed to get database connection for batch fig_files update", level='ERROR')
                return

            with conn.cursor() as cursor:
                # Prepare data for execute_values: list of tuples
                update_tuples = [
                    (item['new_filenames'], json.dumps(item['rectangles']), item['reg_no'])
                    for item in update_data
                ]
                
                # The SQL query with a temporary table for updating
                # This is a common and efficient pattern for bulk updates in PostgreSQL
                sql = """
                    UPDATE patents p SET
                        fig_files = v.fig_files,
                        fig_rectangles = v.fig_rectangles::jsonb
                    FROM (VALUES %s) AS v(fig_files, fig_rectangles, reg_no)
                    WHERE p.reg_no = v.reg_no;
                """
                
                execute_values(cursor, sql, update_tuples)
                conn.commit()

        except Exception as e:
            log_message(f"Error during batch patent fig update with execute_values: {str(e)}", level='ERROR')
            if conn:
                conn.rollback()
        finally:
            if conn:
                db_pool.putconn(conn)


    async def batch_enqueue_patents(self, items):
        """Batch enqueue for patents (list of lists,one list is one patent, each inner list contains tuples of (ip_type, reg_no, image_path))"""
        enqueued_groups = 0
        total_images = 0

        for group in items:
            await self.queue.put(group)
            enqueued_groups += 1
            total_images += len(group)

        log_message(f"✅ Patent batch enqueue completed: 📈 {enqueued_groups} groups enqueued containing {total_images} images",level='INFO')
        return enqueued_groups, 0

    async def batch_enqueue_trademarks(self, items, batch_size=1000):
        """Batch enqueue for trademarks with existence checking"""
        # Generate point IDs for all items
        items_with_ids = [(generate_uuid(ser_no), ser_no, image_path) for ser_no, image_path in items if ser_no]

        if not items_with_ids:
            log_message("No valid trademark items to enqueue", level='WARNING')
            return 0, 0

        all_point_ids = [item[0] for item in items_with_ids]
        existing_ids = set()

        # Batch check for existing embeddings
        log_message(f"Checking existence of {len(all_point_ids)} trademark embeddings in batches of {batch_size}", level='INFO')

        for i in range(0, len(all_point_ids), batch_size):
            batch_ids = all_point_ids[i:i + batch_size]
            try:
                existing_points = self.client.retrieve(
                    collection_name=self.collection_name,
                    ids=batch_ids,
                    with_vectors=False
                )
                existing_ids.update(point.id for point in existing_points)
            except Exception as e:
                log_message(f"Qdrant batch check failed for batch {i//batch_size + 1}: {str(e)}", level='WARNING')
                # Continue without adding to existing_ids if check fails

        # Enqueue only items that don't already exist
        enqueued_count = 0
        skipped_count = 0

        for point_id, ser_no, image_path in items_with_ids:
            if point_id not in existing_ids:
                await self.queue.put((point_id, image_path, ser_no))
                enqueued_count += 1
            else:
                skipped_count += 1

        log_message(f"✅ Trademark batch enqueue completed: 📈 {enqueued_count} enqueued, 📊 {skipped_count} skipped (already exist)", level='INFO')
        return enqueued_count, skipped_count

    def get_queue_status(self):
        """Get current queue status for debugging"""
        return {
            'queue_size': self.queue.qsize(),
            'active_workers': len(self.workers),
            'shutdown_requested': self.shutdown_event.is_set(),
            'ip_type': self.ip_type
        }

    async def worker(self, worker_id):
        """Process queue items in batches"""
        log_message(f"Embedding worker {worker_id} started for {self.ip_type}", level='INFO')

        while not self.shutdown_event.is_set():
            batch = []
            batch_items = []
            target_batch_size = PATENT_BATCH_SIZE if self.ip_type == "patent" else TRADEMARK_BATCH_SIZE

            # Collect up to the configured batch size (patents -> groups, trademarks -> images)
            try:
                # Wait for at least one item, but with timeout
                item = await asyncio.wait_for(self.queue.get(), timeout=5.0)
                batch.append(item)
                batch_items.append(item)

                # Try to get more items quickly to fill the batch
                while len(batch) < target_batch_size:
                    try:
                        item = await asyncio.wait_for(self.queue.get(), timeout=0.1)
                        batch.append(item)
                        batch_items.append(item)
                    except asyncio.TimeoutError:
                        break

            except asyncio.TimeoutError:
                # No items available, check if we should continue
                if self.shutdown_event.is_set():
                    break
                continue

            if batch:
                try:
                    async with self.semaphore:
                        await self.process_batch(batch)
                except Exception as e:
                    log_message(f"Worker {worker_id} batch processing failed: {str(e)}", level='ERROR')
                finally:
                    # Mark all items in this batch as done
                    for _ in batch_items:
                        self.queue.task_done()

        log_message(f"Embedding worker {worker_id} stopped", level='INFO')

    async def start(self):
        """Start concurrent processing workers"""
        if self.workers:
            log_message("Workers already started", level='WARNING')
            return

        # Create and start concurrent workers based on max_concurrent
        for i in range(self.max_concurrent):
            worker_task = asyncio.create_task(self.worker(i))
            self.workers.append(worker_task)

        log_message(f"Started {self.max_concurrent} embedding queue workers for {self.ip_type}", level='INFO')

    async def stop(self):
        """Stop all workers and clean up resources gracefully"""
        if not self.workers:
            return

        log_message("Stopping embedding queue workers...", level='INFO')

        # Signal shutdown
        self.shutdown_event.set()

        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()

        # Clean up resources
        self.thread_executor.shutdown(wait=True)
        self.process_executor.shutdown(wait=True)
        if self.grpc_channel:
            self.grpc_channel.close()
            
        log_message("All embedding queue workers stopped and resources cleaned up", level='INFO')

    # --- Trademark-specific deletion functionality ---
    def delete_embeddings_by_ser_no(self, ser_nos, collection_name="IP_Assets"):
        """
        Delete points from Qdrant by ser_no (using generated UUIDs) - trademark-specific
        Logs which points were deleted and which did not exist.
        """
        if self.ip_type != "trademark":
            log_message("delete_embeddings_by_ser_no is only available for trademark queues", level='ERROR')
            return {"deleted_ser_nos": [], "not_found_ser_nos": ser_nos}

        if not ser_nos:
            log_message("No serial numbers provided for deletion.", level='INFO')
            return {"deleted_ser_nos": [], "not_found_ser_nos": []}

        point_id_map = {generate_uuid(ser_no): ser_no for ser_no in ser_nos}
        point_ids_to_delete = list(point_id_map.keys())

        not_found_ser_nos = []

        try:
            # Check which points exist before attempting deletion
            existing_points = self.client.retrieve(
                collection_name=collection_name,
                ids=point_ids_to_delete,
                with_vectors=False,
                with_payload=False
            )
            existing_point_ids = {point.id for point in existing_points}

            # Separate existing from non-existing
            for point_id, ser_no in point_id_map.items():
                if point_id not in existing_point_ids:
                    not_found_ser_nos.append(ser_no)
                # Points that exist will be attempted for deletion

            if existing_point_ids:
                # Perform the delete operation only for existing points
                delete_result = self.client.delete(collection_name=collection_name, points_selector=PointIdsList(points=list(existing_point_ids)))
                if delete_result.status == UpdateStatus.COMPLETED:
                    log_message(f"Successfully deleted {len(existing_point_ids)} embeddings from Qdrant. Operation ID: {delete_result.operation_id}", level='INFO')
                else:
                    log_message(f"Qdrant delete operation failed: {delete_result}", level='WARNING')
                    # If deletion status is not 'completed', assume none were deleted for logging purposes
                    not_found_ser_nos.extend([point_id_map[pid] for pid in existing_point_ids])

            if not_found_ser_nos:
                log_message(f"The following {len(not_found_ser_nos)} embeddings were not found in Qdrant: {', '.join(not_found_ser_nos)}", level='INFO')

            # Calculate successfully deleted ser_nos
            deleted_ser_nos = [ser_no for ser_no in ser_nos if ser_no not in not_found_ser_nos]
            return {"deleted_ser_nos": deleted_ser_nos, "not_found_ser_nos": not_found_ser_nos}

        except Exception as e:
            log_message(f"Error during Qdrant deletion for serial numbers {ser_nos}: {str(e)}", level='ERROR', exc_info=True)
            # If an error occurs, consider all as not found or failed to delete
            return {"deleted_ser_nos": [], "not_found_ser_nos": ser_nos}

# Top level to be pickable by a process

def _process_and_resize_image_task(image_bytes, tiff_stem):
    """
    (Sync, CPU-bound, Top-level) Resizes a TIFF from bytes and encodes a PNG for upload.
    """
    try:
        # Load the image from buffer. VIPS can handle 1-bit TIFFs efficiently.
        image = pyvips.Image.new_from_buffer(image_bytes, "")
        
        # Calculate new dimensions
        h, w = image.height, image.width
        if h > w:
            scale = RESIZE_IMAGE_SIZE / h
        else:
            scale = RESIZE_IMAGE_SIZE / w
        
        resized_image = image.resize(scale)

        # Encode to PNG buffer
        buffer = resized_image.pngsave_buffer(compression=6)

        obfuscated_name = generate_obfuscated_key(tiff_stem)
        cos_key = f"ip_assets/Patents/{obfuscated_name}.png"
        return buffer, cos_key
        
    except pyvips.Error as e:
        log_message(f"Pyvips error processing image {tiff_stem}: {e.message}", level="ERROR")
        return None, None
    except Exception as e:
        log_message(f"Error processing/resizing image for {tiff_stem}: {e}", level="ERROR")
        return None, None
