trodatadb_high = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-ashburn-1.oraclecloud.com))(connect_data=(service_name=gc16d5f2ff5232a_trodatadb_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

trodatadb_low = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-ashburn-1.oraclecloud.com))(connect_data=(service_name=gc16d5f2ff5232a_trodatadb_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

trodatadb_medium = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-ashburn-1.oraclecloud.com))(connect_data=(service_name=gc16d5f2ff5232a_trodatadb_medium.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

trodatadb_tp = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-ashburn-1.oraclecloud.com))(connect_data=(service_name=gc16d5f2ff5232a_trodatadb_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

trodatadb_tpurgent = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-ashburn-1.oraclecloud.com))(connect_data=(service_name=gc16d5f2ff5232a_trodatadb_tpurgent.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))



