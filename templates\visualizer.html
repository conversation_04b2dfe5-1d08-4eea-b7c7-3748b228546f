{% extends 'layout_ui.html' %}

{% block title %}Case Data Visualizer{% endblock %}

{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='visualizer/visualizer.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@6.0/dist/fancybox/fancybox.css" />
    <style>
        .fancybox__image {
            max-width: 95vw;
            max-height: 95vh;
            object-fit: contain;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="app-container">
        <div class="filters-container">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="validation_status">Validation:</label>
                    <select id="validation_status">
                        <option value="">All</option>
                        <option value="validated">Validated</option>
                        <option value="review_required">Review Required</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="date_from">Filed From:</label>
                    <input type="date" id="date_from">
                </div>
                <div class="filter-group">
                    <label for="date_to">Filed To:</label>
                    <input type="date" id="date_to">
                </div>
                <div class="filter-group">
                    <label for="case_number">Case #:</label>
                    <input type="text" id="case_number" placeholder="Enter case number">
                </div>
                <div class="filter-group">
                    <label for="plaintiff_name">Plaintiff:</label>
                    <input type="text" id="plaintiff_name" placeholder="Enter plaintiff name">
                </div>
                <div class="filter-group">
                    <label for="plaintiff_id">Plaintiff ID:</label>
                    <input type="text" id="plaintiff_id" placeholder="Enter plaintiff ID">
                </div>
                <div class="filter-group">
                    <label for="picture_type">IP Type:</label>
                    <select id="picture_type">
                        <option value="">All IP Types</option>
                        <option value="trademarks">Trademark</option>
                        <option value="patents">Patent</option>
                        <option value="copyrights">Copyright</option>
                        <option value="no_ip">No IP</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="ip_source">IP Source:</label>
                    <select id="ip_source">
                        <option value="">All Sources</option>
                        <option value="exhibit">Exhibit</option>
                        <option value="byregno">By Reg#</option>
                        <option value="cn_website">CN Website</option>
                        <option value="byname">By Name</option>
                        <option value="bygoogle">By Google</option>
                        <option value="manual">Manual</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Case Type:</label>
                    <div class="dropdown-checkbox">
                        <div class="dropdown-checkbox-header" id="case-type-dropdown-header">
                            <span>Select types</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="dropdown-checkbox-content" id="case-type-dropdown-content">
                            <div class="dropdown-checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="case_type" value="Patent"> Patent
                                </label>
                            </div>
                            <div class="dropdown-checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="case_type" value="Trademark"> Trademark
                                </label>
                            </div>
                            <div class="dropdown-checkbox-item">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="case_type" value="Copyrights"> Copyrights
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="filter-group">
                    <label for="sort_by">Sort By:</label>
                    <select id="sort_by">
                        <option value="date_filed">Filing Date</option>
                        <option value="update_time">Update Time</option>
                        <option value="plaintiff_name">Plaintiff</option>
                        <option value="plaintiff_id">Plaintiff ID</option>
                        <option value="docket">Case #</option>
                        <option value="court">Court</option>
                        <option value="pictures">Images Count</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sort_order">Order:</label>
                    <select id="sort_order">
                        <option value="desc">Descending</option>
                        <option value="asc">Ascending</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="limit">Results:</label>
                    <select id="limit">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                        <option value="300">300</option>
                        <option value="500">500</option>
                    </select>
                </div>
            </div>

            <div class="actions-row">
                <div class="results-info">
                    <span id="total-results">0 results</span>
                    (<span id="last-refresh">Last refresh: Never</span>)
                </div>
                <div class="action-buttons">
                    <button id="apply-filters" class="btn primary">Apply</button>
                    <button id="refresh-all-data" class="btn secondary">Refresh all</button>
                    <button id="refresh-selection-data" class="btn secondary">Refresh selection</button>
                </div>
            </div>
        </div>

        <div class="cases-container" id="cases-container">
            <!-- Cases will be populated here dynamically -->
            <div class="loading">Loading cases...</div>
        </div>

        <div class="pagination-controls">
            <button id="prev-page" class="btn secondary" disabled>Previous</button>
            <span id="page-info">Page 1 of 1</span>
            <button id="next-page" class="btn secondary" disabled>Next</button>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@6.0/dist/fancybox/fancybox.umd.js"></script>
    <script type="module" src="{{ url_for('static', filename='visualizer/visualizer.js') }}"></script>
    <script>
        // Add active class to menu item - might need a more robust solution later
        const menuItem = document.getElementById('menu-visualizer');
        if (menuItem) {
            menuItem.classList.add('active');
        }
    </script>
{% endblock %}