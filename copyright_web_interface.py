from flask import Flask, render_template, request, redirect, url_for, send_from_directory
import numpy as np
import os

app = Flask(__name__)

def load_matches():
    path = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "MultiModelBestMatches.npy")
    return np.load(path, allow_pickle=True)

@app.route('/document/<path:filename>')
def document_file(filename):
    data_dir = os.path.join(app.root_path, 'Documents')  # Or wherever your data folder is
    return send_from_directory(data_dir, filename)

@app.route('/data/<path:filename>')
def data_file(filename):
    data_dir = os.path.join(app.root_path, 'data')  # Or wherever your data folder is
    return send_from_directory(data_dir, filename)

@app.route('/product', methods=['GET', 'POST'])
def product_view():
    matches = load_matches()
    
    if request.method == 'POST':
        # Get ORIGINAL product ID from form before navigation
        original_id = int(request.form.get('id', 0))
        
        # Save quality assessments for ORIGINAL product
        for model_idx in range(len(matches[original_id]['models'])):
            for match_idx in range(3):
                key = f"qa_{model_idx}_{match_idx}"
                if key in request.form and request.form[key].strip():
                    matches[original_id]['models'][model_idx]['top_matches'][match_idx]['qualityAssessment'] = float(request.form[key])
        
        # Save updated data
        np.save(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "MultiModelBestMatches.npy"), matches)
        
        # Calculate NEW product ID after saving
        new_id = original_id  # Start with original ID
        if 'next' in request.form:
            new_id = min(original_id + 1, len(matches)-1)
        elif 'prev' in request.form:
            new_id = max(original_id - 1, 0)
        
        # Redirect to GET with NEW ID
        return redirect(url_for('product_view', id=new_id))
    
    # GET handling - show requested product
    product_id = int(request.args.get('id', 0))
    return render_template('product_view.html', 
                         product=matches[product_id],
                         product_id=product_id,
                         total_products=len(matches))

def calculate_statistics(matches):
    # Replace NaN values with 0 in all quality assessments
    for product in matches:
        for model in product['models']:
            for i, match in enumerate(model['top_matches']):
                # Handle numpy structured array access
                try:
                    qa = match['qualityAssessment']
                except (KeyError, ValueError):
                    qa = 0.0
                
                # Convert numpy types to Python floats and handle NaN
                if isinstance(qa, np.generic):
                    qa = qa.item()
                match['qualityAssessment'] = qa if not np.isnan(qa) else 0.0
    
    # Initialize statistics dictionaries
    model_count = len(matches[0]['models'])
    overall_scores = {i: 0.0 for i in range(model_count)}
    top1_scores = {i: 0.0 for i in range(model_count)}
    all_scores = [[] for _ in range(model_count)]  # For correlation matrix
    
    # Calculate scores
    for product in matches:
        for model_idx, model in enumerate(product['models']):
            # Get scores for this model-product combination
            scores = [match['qualityAssessment'] for match in model['top_matches'][:3]]
            
            # Calculate weighted scores (Top1 + Top2/2 + Top3/3)
            weighted_sum = sum(score/(i+1) for i, score in enumerate(scores))
            overall_scores[model_idx] += weighted_sum
            
            # Top1 score
            top1_scores[model_idx] += scores[0] if len(scores) > 0 else 0
            
            # Collect scores for correlation
            all_scores[model_idx].extend(scores)
    
    # Calculate correlations
    correlation_matrix = np.corrcoef([np.array(scores) for scores in all_scores])
    
    return {
        'overall_scores': overall_scores,
        'top1_scores': top1_scores,
        'correlation_matrix': correlation_matrix
    }

@app.route('/statistics')
def show_statistics():
    matches = load_matches()
    stats = calculate_statistics(matches)
    return render_template('statistics.html', 
                         overall_scores=stats['overall_scores'],
                         top1_scores=stats['top1_scores'],
                         correlation_matrix=stats['correlation_matrix'])

if __name__ == '__main__':
    app.run(debug=True) 