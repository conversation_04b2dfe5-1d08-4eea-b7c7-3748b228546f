<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maidalv IP Check API Documentation</title>
    <link rel="shortcut icon" href="{{ url_path_for('static', path='images/favicon.ico') }}" type="image/x-icon">
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/inter-fonts.css') }}">
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/prism.min.css') }}">
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/prism-tomorrow.min.css') }}" id="prism-dark-theme" disabled>
    <link rel="stylesheet" href="{{ url_path_for('static', path='css/documentation.css') }}">
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">Maidalv API</div>
                <button class="theme-toggle" onclick="toggleTheme()" title="Toggle theme">
                    🌙
                </button>
            </div>
            <nav class="nav-menu">
                <a href="#overview" class="nav-item active">Overview</a>
                <a href="#authentication" class="nav-item">Authentication</a>
                <a href="#submit-check" class="nav-item">Submit Check</a>
                <a href="#retrieve-results" class="nav-item">Retrieve Results</a>
                <a href="#errors" class="nav-item">Error Handling</a>
                <a href="#rate-limiting" class="nav-item">Rate Limiting</a>
                <a href="#faq" class="nav-item">FAQ</a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1>IP Check API Documentation</h1>
                <p>Comprehensive guide to integrating with the Maidalv Intellectual Property Check API</p>
            </div>

            <!-- Overview Section -->
            <section id="overview" class="section">
                <h2>Overview</h2>
                <p>This API service helps ecommerce sellers quickly check whether their products may infringe on the intellectual property (IP) of another party — covering copyrights, trademarks, and patents.</p>

                <h3>What You Can Submit</h3>
                <ul>
                    <li><strong>Product images</strong> - Images of the product you want to check</li>
                    <li><strong>IP images</strong> - Any image of intellectual property used in making the product</li>
                    <li><strong>Reference images</strong> - Similar products that inspired the design</li>
                    <li><strong>Product description</strong> - Text description of your product</li>
                    <li><strong>IP keywords</strong> - Relevant keywords related to intellectual property</li>
                </ul>

                <h3>What You Get Back</h3>
                <ul>
                    <li><strong>Infringement analysis</strong> - The most likely infringements identified</li>
                    <li><strong>IP evidence</strong> - Images of the infringed intellectual property</li>
                    <li><strong>Court case history</strong> - Information on whether that IP has been used in court cases</li>
                    <li><strong>Legal opinion report</strong> - Assessment of the severity of potential infringement</li>
                    <li><strong>Risk assessment</strong> - Overall risk level (High, Medium, Low)</li>
                </ul>

                <p>This enables sellers to make informed decisions before listing or selling their products.</p>

                <div class="alert info">
                    <strong>Base URL:</strong> <code>https://api.maidalv.com</code>
                </div>
            </section>

            <!-- Authentication Section -->
            <section id="authentication" class="section">
                <h2>Authentication</h2>
                <p>All API requests require authentication using an API key. Include your API key in the request header as the <code>apikey</code> field.</p>

                <div class="alert warning">
                    <strong>Security Note:</strong> Keep your API key secure and never expose it in client-side code. Always make API calls from your server.
                </div>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests

# Your API key
API_KEY = "{YOUR_API_KEY}"

# Include in request headers
headers = {
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# Example request
response = requests.post(url, headers=headers, json=data)</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Your API key
const API_KEY = "{YOUR_API_KEY}";

// Include in request headers
const headers = {
    "apikey": API_KEY,
    "Content-Type": "application/json"
};

// Example request
fetch(url, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(data)
});</code></pre>
                    </div>
                </div>
            </section>

            <!-- Submit Check Section -->
            <section id="submit-check" class="section">
                <h2>Submit a Product for IP Check</h2>
                <p><span class="badge post">POST</span> <code>/check_api</code></p>

                <p>Submit a product for intellectual property analysis. This endpoint accepts product images, descriptions, and related information to perform comprehensive IP checking.</p>

                <h3>Request Parameters</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>api_key</code></td>
                                <td>string</td>
                                <td><span class="badge required">Required</span></td>
                                <td>Your API authentication key</td>
                            </tr>
                            <tr>
                                <td><code>main_product_image</code></td>
                                <td>string</td>
                                <td><span class="badge required">Required</span></td>
                                <td>Base64 encoded image with data URI header (data:image/jpeg;base64,{base64_data}) or image URL</td>
                            </tr>
                            <tr>
                                <td><code>other_product_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of additional product images (base64 with data URI header or URLs). Maximum 5 images.</td>
                            </tr>
                            <tr>
                                <td><code>ip_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of IP-related images (base64 with data URI header or URLs). Maximum 3 images.</td>
                            </tr>
                            <tr>
                                <td><code>reference_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of reference images from similar products (base64 with data URI header or URLs). Maximum 3 images.</td>
                            </tr>
                            <tr>
                                <td><code>description</code></td>
                                <td>string</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Product description. For best results, use only the product title.</td>
                            </tr>
                            <tr>
                                <td><code>ip_keywords</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of IP-related keywords. Maximum 20 keywords.</td>
                            </tr>
                            <tr>
                                <td><code>reference_text</code></td>
                                <td>string</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Text (e.g. product title) from similar products</td>
                            </tr>
                            <tr>
                                <td><code>language</code></td>
                                <td>string</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Response language ('en' or 'zh', default: 'zh'). Only affects the language of the legal opinion report.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="alert warning">
                    <strong>⚠️ Important:</strong> When sending base64 encoded images, you MUST include the data URI header format: <code>data:image/jpeg;base64,{base64_data}</code> or <code>data:image/png;base64,{base64_data}</code>. The backend expects this format and will reject plain base64 strings without the header.
                </div>

                <h3>Example Request</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import base64
import json

# Your API configuration
API_KEY = "{YOUR_API_KEY}"
BASE_URL = "https://api.maidalv.com"

# Function to encode image to base64 with data URI header
def encode_image_to_base64(image_path):
    # Determine MIME type based on file extension
    if image_path.lower().endswith(('.jpg', '.jpeg')):
        mime_type = 'image/jpeg'
    elif image_path.lower().endswith('.png'):
        mime_type = 'image/png'
    elif image_path.lower().endswith('.webp'):
        mime_type = 'image/webp'
    else:
        mime_type = 'image/jpeg'  # default

    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        # Return with data URI header as required by the API
        return f"data:{mime_type};base64,{encoded_string}"

# Prepare the request data
data = {
    "api_key": API_KEY,
    "main_product_image": encode_image_to_base64("product.jpg"),  # Returns data:image/jpeg;base64,{base64_data}
    "other_product_images": [
        encode_image_to_base64("product2.jpg"),
        encode_image_to_base64("product3.jpg")
    ],
    "ip_images": [
        encode_image_to_base64("ip_reference.jpg")
    ],
    "description": "Wireless bluetooth headphones",  # Product title recommended
    "ip_keywords": ["bluetooth", "wireless", "headphones", "noise cancellation"],
    "reference_text": "Similar to popular brand headphones",
    "language": "en"
}

# Submit the check request
try:
    response = requests.post(
        f"{BASE_URL}/check_api",
        json=data,
        headers={"apikey": API_KEY, "Content-Type": "application/json"},
        timeout=30
    )

    if response.status_code == 200:
        result = response.json()
        check_id = result.get("check_id")
        status = result.get("status")

        print(f"Check submitted successfully!")
        print(f"Check ID: {check_id}")
        print(f"Status: {status}")

        if "estimated_completion_time" in result:
            print(f"Estimated completion time: {result['estimated_completion_time']} seconds")

    else:
        error_data = response.json()
        print(f"Error: {error_data}")

except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Your API configuration
const API_KEY = "{YOUR_API_KEY}";
const BASE_URL = "https://api.maidalv.com";

// Function to convert file to base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = error => reject(error);
    });
}

// Example with file inputs (browser environment)
async function submitCheck() {
    try {
        // Get files from input elements
        const mainImageFile = document.getElementById('mainImage').files[0];
        const otherImageFiles = document.getElementById('otherImages').files;

        // Convert to base64
        const mainImageBase64 = await fileToBase64(mainImageFile);
        const otherImagesBase64 = [];

        for (let file of otherImageFiles) {
            const base64 = await fileToBase64(file);
            otherImagesBase64.push(base64);
        }

        // Prepare request data
        const data = {
            api_key: API_KEY,
            main_product_image: mainImageBase64,
            other_product_images: otherImagesBase64,
            description: "Wireless bluetooth headphones",  // Product title recommended
            ip_keywords: ["bluetooth", "wireless", "headphones", "noise cancellation"],
            reference_text: "Similar to popular brand headphones",
            language: "en"
        };

        // Submit the request
        const response = await fetch(`${BASE_URL}/check_api`, {
            method: 'POST',
            headers: {
                'apikey': API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Check submitted successfully!');
            console.log('Check ID:', result.check_id);
            console.log('Status:', result.status);

            if (result.estimated_completion_time) {
                console.log('Estimated completion time:', result.estimated_completion_time, 'seconds');
            }

            return result;
        } else {
            const errorData = await response.json();
            console.error('Error:', errorData);
            throw new Error(errorData.error?.message || 'Request failed');
        }

    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
}

// Call the function
submitCheck()
    .then(result => {
        // Handle successful submission
        console.log('Submission result:', result);
    })
    .catch(error => {
        // Handle errors
        console.error('Submission failed:', error);
    });</code></pre>
                    </div>
                </div>

                <div class="disclosure">
                    <div class="disclosure-header" onclick="toggleDisclosure(this)">
                        <span>Show cURL example</span>
                        <span class="disclosure-arrow">▶</span>
                    </div>
                    <div class="disclosure-content">
                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-tabs">
                                    <button class="code-tab active" data-lang="bash">cURL</button>
                                </div>
                                <button class="copy-button" onclick="copyCode(this)">Copy</button>
                            </div>
                            <div class="code-block active" data-lang="bash">
                                <pre><code class="language-bash">curl -X POST "https://api.maidalv.com/check_api" \
  -H "Content-Type: application/json" \
  -H "apikey: {YOUR_API_KEY}" \
  -d '{
    "main_product_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "other_product_images": [
      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
    ],
    "description": "Wireless bluetooth headphones",
    "ip_keywords": ["bluetooth", "wireless", "headphones"],
    "language": "en"
  }'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>Response</h3>
                <p>The API returns a JSON response with a unique <code>check_id</code> that you'll use to retrieve results.</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">Response</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "check_id": "1234567890123456",
  "status": "queued",
  "message": "Analysis has been queued. Use the check_status endpoint to poll for results.",
  "estimated_completion_time": 45
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- Retrieve Results Section -->
            <section id="retrieve-results" class="section">
                <h2>Retrieve Results</h2>
                <p><span class="badge get">GET</span> <code>/check_status/{check_id}</code></p>

                <p>Retrieve the analysis results for a submitted check. Since analysis can take time, you'll need to poll this endpoint until the status becomes "completed". Use the <code>estimated_completion_time</code> (in seconds) from the submit response to determine appropriate polling intervals.</p>

                <h3>Request Parameters</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>check_id</code></td>
                                <td>string</td>
                                <td><span class="badge required">Required</span></td>
                                <td>The check ID returned from the submit endpoint</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Response Status Values</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Description</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>queued</code></td>
                                <td>Request is waiting in the processing queue</td>
                                <td>Poll again in 20 seconds</td>
                            </tr>
                            <tr>
                                <td><code>processing</code></td>
                                <td>Analysis is currently running</td>
                                <td>Poll again in 3 seconds</td>
                            </tr>
                            <tr>
                                <td><code>completed</code></td>
                                <td>Analysis is complete, results available</td>
                                <td>Process the results</td>
                            </tr>
                            <tr>
                                <td><code>error</code></td>
                                <td>Analysis failed due to an error</td>
                                <td>Check error details and retry if needed</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Example Request</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import time

def poll_for_results(check_id, base_url, max_attempts=120):
    """
    Poll for results with exponential backoff
    """
    attempt = 0

    while attempt < max_attempts:
        try:
            response = requests.get(f"{base_url}/check_status/{check_id}")

            if response.status_code == 200:
                result = response.json()
                status = result.get("status")

                print(f"Attempt {attempt + 1}: Status = {status}")

                if status == "completed":
                    print("Analysis completed!")
                    return result.get("result")

                elif status == "error":
                    print(f"Analysis failed: {result.get('message')}")
                    return None

                elif status in ["queued", "processing"]:
                    # Wait based on status
                    wait_time = 20 if status == "queued" else 3
                    print(f"Waiting {wait_time} seconds...")
                    time.sleep(wait_time)

            else:
                print(f"HTTP Error: {response.status_code}")
                break

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            break

        attempt += 1

    print("Max attempts reached or error occurred")
    return None

# Usage example
BASE_URL = "https://api.maidalv.com"
check_id = "1234567890123456"  # From submit response

results = poll_for_results(check_id, BASE_URL)
if results:
    print("Final results:", results)
else:
    print("Failed to get results")</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">const BASE_URL = "https://api.maidalv.com";

async function pollForResults(checkId, maxAttempts = 120) {
    let attempt = 0;

    while (attempt < maxAttempts) {
        try {
            const response = await fetch(`${BASE_URL}/check_status/${checkId}`);

            if (response.ok) {
                const result = await response.json();
                const status = result.status;

                console.log(`Attempt ${attempt + 1}: Status = ${status}`);

                if (status === "completed") {
                    console.log("Analysis completed!");
                    return result.result;

                } else if (status === "error") {
                    console.log(`Analysis failed: ${result.message}`);
                    return null;

                } else if (status === "queued" || status === "processing") {
                    // Wait based on status
                    const waitTime = status === "queued" ? 20000 : 3000;
                    console.log(`Waiting ${waitTime/1000} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }

            } else {
                console.log(`HTTP Error: ${response.status}`);
                break;
            }

        } catch (error) {
            console.log(`Request failed: ${error}`);
            break;
        }

        attempt++;
    }

    console.log("Max attempts reached or error occurred");
    return null;
}

// Usage example
const checkId = "1234567890123456"; // From submit response

pollForResults(checkId)
    .then(results => {
        if (results) {
            console.log("Final results:", results);
        } else {
            console.log("Failed to get results");
        }
    })
    .catch(error => {
        console.error("Polling failed:", error);
    });</code></pre>
                    </div>
                </div>

                <h3>Successful Response Example</h3>
                <p>When the analysis is complete, you'll receive a comprehensive response with infringement details:</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">Response</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "status": "completed",
  "result": {
    "check_id": "1234567890123456",
    "status": "success",
    "risk_level": "High Risk",
    "results": [
      {
        "ip_type": "Trademark",
        "ip_owner": "Apple Inc.",
        "risk_level": "High Risk",
        "risk_score": 8.5,
        "text": "AirPods Pro",
        "reg_no": "US87654321",
        "ip_asset_urls": ["http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/12345/high/trademark.jpg"],
        "plaintiff_id": 12345,
        "plaintiff_name": "Apple Inc.",
        "number_of_cases": 15,
        "last_case_docket": "1:23-cv-00123",
        "last_case_date_filed": "2023-08-15",
        "risk_description": "High similarity to IP of a known TRO plaintiff. There is a high risk of litigation.",
        "report": "**Trademark Risk Assessment Report**\n\n**1. Mark Information:**\nThe analyzed product shows high similarity to registered trademark 'AirPods Pro'..."
      },
      {
        "ip_type": "Patent",
        "ip_owner": "Sony Corporation",
        "risk_level": "Medium Risk",
        "risk_score": 6.2,
        "text": "Noise cancellation technology for wireless headphones",
        "reg_no": "US10123456",
        "ip_asset_urls": ["http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/67890/high/patent_page1.jpg"],
        "plaintiff_id": 67890,
        "plaintiff_name": "Sony Corporation",
        "number_of_cases": 8,
        "last_case_docket": "1:22-cv-00456",
        "last_case_date_filed": "2022-12-03",
        "risk_description": "Moderate similarity to IP of a known TRO plaintiff. There is a medium risk of litigation.",
        "report": "**Patent Risk Assessment Report**\n\n**1. Patent Information:**\nThe analyzed product may infringe on patent US10123456..."
      },
      {
        "ip_type": "Copyright",
        "ip_owner": "Design Studio LLC",
        "risk_level": "Low Risk",
        "risk_score": 3.1,
        "text": "Wireless Headphone Design",
        "reg_no": "VA0002345678",
        "ip_asset_urls": ["https://api.maidalv.com/ip/copyright/VA0002345678.webp"]
      }
    ]
  }
}</code></pre>
                    </div>
                </div>

                <h3>Response Fields</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>check_id</code></td>
                                <td>string</td>
                                <td>Unique identifier for this check</td>
                            </tr>
                            <tr>
                                <td><code>status</code></td>
                                <td>string</td>
                                <td>Overall status of the analysis</td>
                            </tr>
                            <tr>
                                <td><code>risk_level</code></td>
                                <td>string</td>
                                <td>Overall risk assessment (High Risk, Medium Risk, Low Risk)</td>
                            </tr>
                            <tr>
                                <td><code>results</code></td>
                                <td>array</td>
                                <td>Array of potential IP infringements found</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_type</code></td>
                                <td>string</td>
                                <td>Type of IP (trademark, patent, copyright)</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_owner</code></td>
                                <td>string</td>
                                <td>Owner of the intellectual property</td>
                            </tr>
                            <tr>
                                <td><code>results[].risk_score</code></td>
                                <td>number</td>
                                <td>Numerical risk score (0-10)</td>
                            </tr>
                            <tr>
                                <td><code>results[].risk_description</code></td>
                                <td>string</td>
                                <td>Textual description of the risk level</td>
                            </tr>
                            <tr>
                                <td><code>results[].text</code></td>
                                <td>string</td>
                                <td>IP text content (trademark text, patent title, or copyright title)</td>
                            </tr>
                            <tr>
                                <td><code>results[].reg_no</code></td>
                                <td>string</td>
                                <td>Registration number (trademark serial number, patent number, or copyright registration number)</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_asset_urls</code></td>
                                <td>array</td>
                                <td>URLs to view the IP evidence images</td>
                            </tr>
                            <tr>
                                <td><code>results[].report</code></td>
                                <td>string</td>
                                <td>AI-generated legal assessment report (optional)</td>
                            </tr>
                            <tr>
                                <td><code>results[].plaintiff_id</code></td>
                                <td>number</td>
                                <td>ID of the plaintiff (only present if IP has been used in TRO cases)</td>
                            </tr>
                            <tr>
                                <td><code>results[].plaintiff_name</code></td>
                                <td>string</td>
                                <td>Name of the plaintiff (only present if IP has been used in TRO cases)</td>
                            </tr>
                            <tr>
                                <td><code>results[].number_of_cases</code></td>
                                <td>number</td>
                                <td>Number of court cases filed by this plaintiff (only present if IP has been used in TRO cases)</td>
                            </tr>
                            <tr>
                                <td><code>results[].last_case_docket</code></td>
                                <td>string</td>
                                <td>Court docket number of the most recent case (only present if IP has been used in TRO cases)</td>
                            </tr>
                            <tr>
                                <td><code>results[].last_case_date_filed</code></td>
                                <td>string</td>
                                <td>Date of the most recent case filing (only present if IP has been used in TRO cases)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert info">
                    <strong>Note about TRO Cases:</strong> If the IP has not been used in a TRO (Temporary Restraining Order) case in court, then the case-specific information (plaintiff_name, number_of_cases, last_case_docket, last_case_date_filed) will not be present in the response. The plaintiff_name may differ from the ip_owner due to different entities in corporate structures or individual vs. corporate ownership.
                </div>
            </section>

            <!-- Error Handling Section -->
            <section id="errors" class="section">
                <h2>Error Handling</h2>
                <p>The API uses standard HTTP status codes and returns detailed error information in a consistent format.</p>

                <h3>Error Response Format</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">Error Response</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "error": {
    "error_code": "INVALID_API_KEY",
    "message": "The provided API Key is invalid or does not exist.",
    "details": "Please check your API key and ensure it's correctly included in the request."
  }
}</code></pre>
                    </div>
                </div>

                <h3>Common Error Codes</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>HTTP Status</th>
                                <th>Error Code</th>
                                <th>Description</th>
                                <th>Solution</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>401</td>
                                <td><code>INVALID_API_KEY</code></td>
                                <td>Invalid or missing API key</td>
                                <td>Check your API key is correct and included in the request</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td><code>MISSING_MAIN_IMAGE</code></td>
                                <td>Main product image is required</td>
                                <td>Include a main_product_image in your request</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td><code>MISSING_REQUIRED_FIELD</code></td>
                                <td>A required field is missing</td>
                                <td>Check the error details for the specific missing field</td>
                            </tr>
                            <tr>
                                <td>429</td>
                                <td><code>RATE_LIMIT_MINUTE_EXCEEDED</code></td>
                                <td>Per-minute rate limit exceeded</td>
                                <td>Wait 60 seconds before making another request</td>
                            </tr>
                            <tr>
                                <td>429</td>
                                <td><code>RATE_LIMIT_DAILY_EXCEEDED</code></td>
                                <td>Daily rate limit exceeded</td>
                                <td>Wait until the next day or upgrade your plan</td>
                            </tr>
                            <tr>
                                <td>404</td>
                                <td><code>RESULTS_NOT_FOUND</code></td>
                                <td>Check ID not found</td>
                                <td>Verify the check_id is correct</td>
                            </tr>
                            <tr>
                                <td>500</td>
                                <td><code>SERVER_ERROR</code></td>
                                <td>Internal server error</td>
                                <td>Retry the request or contact support</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Error Handling Example</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import time

def handle_api_request(url, data, max_retries=3):
    """
    Handle API request with proper error handling and retries
    """
    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, timeout=30)

            if response.status_code == 200:
                return response.json()

            elif response.status_code == 429:
                # Rate limit exceeded
                error_data = response.json()
                error_code = error_data.get('error', {}).get('error_code')

                if error_code == 'RATE_LIMIT_MINUTE_EXCEEDED':
                    print("Rate limit exceeded. Waiting 60 seconds...")
                    time.sleep(60)
                    continue
                elif error_code == 'RATE_LIMIT_DAILY_EXCEEDED':
                    print("Daily limit exceeded. Please try tomorrow.")
                    return None

            elif response.status_code == 401:
                print("Authentication failed. Check your API key.")
                return None

            else:
                # Other errors
                error_data = response.json()
                print(f"API Error: {error_data}")
                return None

        except requests.exceptions.Timeout:
            print(f"Request timeout (attempt {attempt + 1})")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None

    print("Max retries exceeded")
    return None</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">async function handleApiRequest(url, data, maxRetries = 3) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                return await response.json();

            } else if (response.status === 429) {
                // Rate limit exceeded
                const errorData = await response.json();
                const errorCode = errorData.error?.error_code;

                if (errorCode === 'RATE_LIMIT_MINUTE_EXCEEDED') {
                    console.log('Rate limit exceeded. Waiting 60 seconds...');
                    await new Promise(resolve => setTimeout(resolve, 60000));
                    continue;
                } else if (errorCode === 'RATE_LIMIT_DAILY_EXCEEDED') {
                    console.log('Daily limit exceeded. Please try tomorrow.');
                    return null;
                }

            } else if (response.status === 401) {
                console.log('Authentication failed. Check your API key.');
                return null;

            } else {
                // Other errors
                const errorData = await response.json();
                console.log('API Error:', errorData);
                return null;
            }

        } catch (error) {
            console.log(`Request failed (attempt ${attempt + 1}):`, error);
            if (attempt < maxRetries - 1) {
                // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
        }
    }

    console.log('Max retries exceeded');
    return null;
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- Rate Limiting Section -->
            <section id="rate-limiting" class="section">
                <h2>Rate Limiting & Retries</h2>
                <p>The API implements rate limiting to ensure fair usage and system stability.</p>

                <h3>Rate Limits</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Limit Type</th>
                                <th>Default Limit</th>
                                <th>Reset Period</th>
                                <th>Error Code</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Per-minute requests</td>
                                <td>As negotiated</td>
                                <td>60 seconds</td>
                                <td><code>RATE_LIMIT_MINUTE_EXCEEDED</code></td>
                            </tr>
                            <tr>
                                <td>Daily requests</td>
                                <td>As negotiated</td>
                                <td>24 hours</td>
                                <td><code>RATE_LIMIT_DAILY_EXCEEDED</code></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert info">
                    <strong>Note:</strong> Rate limits are negotiated and communicated by your client relationship manager/sales contact at Maidalv.
                </div>

                <h3>Best Practices</h3>
                <ul>
                    <li><strong>Implement exponential backoff</strong> - Wait progressively longer between retries</li>
                    <li><strong>Handle rate limit responses</strong> - Wait for the specified time before retrying</li>
                    <li><strong>Cache results</strong> - Store completed analysis results to avoid duplicate requests</li>
                    <li><strong>Batch processing</strong> - Process multiple products sequentially rather than concurrently</li>
                    <li><strong>Monitor usage</strong> - Track your API usage to stay within limits</li>
                </ul>

                <h3>Retry Strategy Example</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import time
import random

def exponential_backoff_retry(func, max_retries=5, base_delay=1):
    """
    Retry function with exponential backoff and jitter
    """
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e

            # Calculate delay with exponential backoff and jitter
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"Attempt {attempt + 1} failed. Retrying in {delay:.2f} seconds...")
            time.sleep(delay)

    raise Exception("Max retries exceeded")

# Usage
def submit_check():
    # Your API call here
    response = requests.post(url, json=data)
    if response.status_code != 200:
        raise Exception(f"API call failed: {response.status_code}")
    return response.json()

try:
    result = exponential_backoff_retry(submit_check)
    print("Success:", result)
except Exception as e:
    print("Failed after retries:", e)</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">async function exponentialBackoffRetry(func, maxRetries = 5, baseDelay = 1000) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            return await func();
        } catch (error) {
            if (attempt === maxRetries - 1) {
                throw error;
            }

            // Calculate delay with exponential backoff and jitter
            const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
            console.log(`Attempt ${attempt + 1} failed. Retrying in ${delay/1000:.2f} seconds...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw new Error("Max retries exceeded");
}

// Usage
async function submitCheck() {
    const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
    }

    return await response.json();
}

try {
    const result = await exponentialBackoffRetry(submitCheck);
    console.log("Success:", result);
} catch (error) {
    console.log("Failed after retries:", error);
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- FAQ Section -->
            <section id="faq" class="section">
                <h2>Frequently Asked Questions</h2>

                <h3>General Questions</h3>

                <h4>How long does analysis take?</h4>
                <p>Analysis typically takes 30-90 seconds, depending on the complexity of your product and current queue length. The API provides estimated completion times when you submit a request.</p>

                <h4>What image formats are supported?</h4>
                <p>We support JPEG, PNG, WebP, and GIF formats. Images should be base64 encoded or provided as accessible URLs. Maximum file size is 10MB per image.</p>

                <h4>How accurate is the IP analysis?</h4>
                <p>Our AI-powered analysis combines visual similarity detection with comprehensive IP databases. While highly accurate, results should be reviewed by legal professionals for final decisions.</p>

                <h4>Can I check multiple products at once?</h4>
                <p>Each API call analyzes one product at a time. For multiple products, submit separate requests and process them sequentially to respect rate limits.</p>

                <h4>Where are your servers located?</h4>
                <p>The API check endpoint (https://api.maidalv.com) is located outside China.</p>

                <h4>Where are the IP images located?</h4>
                <p>The IP evidence images (results[].ip_asset_urls) are hosted inside China for faster access within the region.</p>

                <h4>Why is the plaintiff name different from the IP owner name?</h4>
                <p>This occurs due to different entities within corporate structures, or differences between corporate and individual ownership (e.g., for copyright, the artist may be individual while the plaintiff is a corporation).</p>

                <h3>Technical Questions</h3>

                <h4>What should I do if my request times out?</h4>
                <p>Implement proper timeout handling and retry logic. If analysis takes longer than expected, continue polling the status endpoint rather than resubmitting the request.</p>

                <h4>How do I handle base64 encoding?</h4>
                <p>Most programming languages have built-in base64 encoding functions. Ensure you encode the raw image bytes, not the data URL prefix.</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python"># Correct base64 encoding
import base64

with open("image.jpg", "rb") as f:
    image_data = base64.b64encode(f.read()).decode('utf-8')

# Use image_data in your API request</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Correct base64 encoding (browser)
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // Remove data URL prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
    });
}</code></pre>
                    </div>
                </div>

                <h4>What's the difference between IP images and reference images?</h4>
                <ul>
                    <li><strong>IP images:</strong> Images of existing intellectual property that you believe might be relevant to your product</li>
                    <li><strong>Reference images:</strong> Images of similar products or designs that inspired your product</li>
                    <li><strong>Product images:</strong> Images of your actual product that you want to check</li>
                </ul>

                <h3>Troubleshooting</h3>

                <h4>Common Issues and Solutions</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Issue</th>
                                <th>Cause</th>
                                <th>Solution</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Invalid base64 error</td>
                                <td>Incorrect encoding or data URL prefix included</td>
                                <td>Ensure proper base64 encoding without data URL prefix</td>
                            </tr>
                            <tr>
                                <td>Request timeout</td>
                                <td>Large images or network issues</td>
                                <td>Reduce image size or increase timeout values</td>
                            </tr>
                            <tr>
                                <td>Empty results</td>
                                <td>No similar IP found in database</td>
                                <td>This is normal - it means no significant matches were found</td>
                            </tr>
                            <tr>
                                <td>Rate limit errors</td>
                                <td>Too many requests in short time</td>
                                <td>Implement proper delays and retry logic</td>
                            </tr>
                            <tr>
                                <td>Image URL not accessible</td>
                                <td>The provided image URL cannot be accessed by our servers</td>
                                <td>Ensure the URL is publicly accessible or use base64 encoding instead</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert success">
                    <strong>Need Help?</strong> If you encounter issues not covered here, please contact our support team with your check ID and error details.
                </div>
            </section>
        </main>
    </div>

    <!-- Prism.js for syntax highlighting -->
    <script src="{{ url_path_for('static', path='js/prism-core.min.js') }}" defer></script>
    <script src="{{ url_path_for('static', path='js/prism-autoloader.min.js') }}" defer></script>

    <!-- Documentation JavaScript -->
    <script src="{{ url_path_for('static', path='js/documentation.js') }}" defer></script>
</body>
</html>