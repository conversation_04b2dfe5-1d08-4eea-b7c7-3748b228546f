#!/usr/bin/env python3
"""
Migration script to update patent records in the database based on registration numbers found in case data.
 
This script:
1. Processes all cases with patent images in the dataframe.
2. Finds patent records using `reg_no` in the patent database.
3. Updates database records (`tro=TRUE`, `plaintiff_id`).
4. Updates dataframe with new filenames and `reg_no` information.
"""

import os
import sys
import re
import asyncio
import json
import logging
from typing import Dict, Optional, Tuple
import traceback
from AI.GC_VertexAI import vertex_genai_multi_async
import Common.Constants as Constants
from IP.Patent import get_patent_data_by_reg_no

# --- Setup Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

USER_ANSWERS_CACHE_FILE = os.path.join(os.path.dirname(__file__), "user_answers_cache_patent.json")

PROCESSED_PATENTS = set()
PROBLEMATIC_PATENTS = set()

sys.path.append(os.getcwd())

import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from IP.Patents_Bulk.patent_db_grant import get_db_connection
import Common.Constants as Constants

def clean_and_validate_reg_no(reg_no: str) -> Optional[str]:
    """Cleans and validates a patent registration number."""
    if not isinstance(reg_no, str):
        return None
    cleaned_reg_no = re.sub(r'[,\s]', '', reg_no).strip()
    match = re.search(r'^(D?\d+)$', cleaned_reg_no)
    return match.group(1) if match else None

class PatentFilenameMigrator:
    def __init__(self):
        self.db_conn = None
        self.user_answers_cache = {}
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
        self.tro_updated_reg_nos = set()

        # Summary counters from the original script
        self.summary_processed_patents = 0
        self.summary_patents_found_in_patents = 0
        self.summary_patents_not_found_in_patents = 0
        self.summary_patents_found_in_patents_all = 0
        self.summary_patents_with_date_lt_2011 = 0
        self.summary_other_not_found_patents = 0
        self.summary_top_10_other_not_found_patents = []
        self.summary_patents_all_found_by_exhibit = 0
        self.summary_patents_all_found_by_regno = 0
        self.summary_patents_all_found_by_name = 0
        self.summary_patents_all_found_unknown_source = 0
        self.examples_patents_all_by_exhibit = []
        self.examples_patents_all_by_regno = []
        self.examples_patents_all_by_name = []
        self.summary_found_on_uspto = 0
        self.summary_uspto_date_lt_2011 = 0
        self.summary_found_by_gemini = 0
        self.summary_gemini_not_found = 0
        self.summary_gemini_search_found_in_patents = 0
        self.summary_gemini_search_found_in_patents_all = 0
        self.summary_gemini_search_found_on_uspto = 0
        
    async def __aenter__(self):
        self.db_conn = get_db_connection()
        self._load_user_answers_cache()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self._save_user_answers_cache()
        if self.db_conn:
            self.db_conn.close()

    def _load_user_answers_cache(self):
        try:
            if os.path.exists(USER_ANSWERS_CACHE_FILE):
                with open(USER_ANSWERS_CACHE_FILE, 'r') as f:
                    self.user_answers_cache = json.load(f)
                logger.info(f"Loaded {len(self.user_answers_cache)} cached user answers.")
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"Error loading user answers cache: {e}")
            self.user_answers_cache = {}

    def _save_user_answers_cache(self):
        try:
            with open(USER_ANSWERS_CACHE_FILE, 'w') as f:
                json.dump(self.user_answers_cache, f, indent=2)
        except IOError as e:
            logger.error(f"Error saving user answers cache: {e}")

    def _prompt_user_for_approval(self, original_reg_no: str, new_reg_no: str) -> bool:
        cache_key = f"{original_reg_no}_{new_reg_no}"
        if cache_key in self.user_answers_cache:
            logger.info(f"Using cached decision for {original_reg_no} -> {new_reg_no}: {'Approved' if self.user_answers_cache[cache_key] else 'Rejected'}")
            return self.user_answers_cache[cache_key]

        while True:
            prompt = f"AI suggests changing patent '{original_reg_no}' to '{new_reg_no}'. Approve? [Y/n]: "
            answer = input(prompt).lower().strip()
            if answer in ['y', 'yes', '']:
                self.user_answers_cache[cache_key] = True
                self._save_user_answers_cache()
                return True
            elif answer in ['n', 'no']:
                self.user_answers_cache[cache_key] = False
                self._save_user_answers_cache()
                return False
            else:
                logger.warning("Invalid input. Please enter 'y' or 'n'.")

    def reset_patent_tro_plaintiff_ids(self) -> bool:
        user_confirmation = input("⚠️ DANGEROUS: This will reset 'tro' and 'plaintiff_id' for ALL entries in the 'patents' table. Are you sure you want to continue? (yes/no): ")
        if user_confirmation.lower() != 'yes':
            logger.info("❌ Operation cancelled by user.")
            return False
        try:
            with self.db_conn.cursor() as cursor:
                cursor.execute("UPDATE patents SET tro = NULL, plaintiff_id = NULL;")
                self.db_conn.commit()
                logger.info("✅ Successfully set 'tro' and 'plaintiff_id' to NULL in the 'patents' table.")
                return True
        except Exception as e:
            logger.error(f"❌ Error resetting 'tro' and 'plaintiff_id' in 'patents' table: {e}", exc_info=True)
            self.db_conn.rollback()
            return False

    def find_patent_in_db(self, reg_no: str) -> Optional[Tuple[str, Optional[str], str]]:
        if not reg_no:
            return None
        try:
            with self.db_conn.cursor() as cursor:
                cursor.execute("SELECT reg_no, date_published FROM patents WHERE reg_no = %s", (reg_no,))
                result = cursor.fetchone()
                if result:
                    return result[0], result[1], "patents"
                
                cursor.execute("SELECT reg_no, date_published FROM patents_all WHERE reg_no = %s", (reg_no,))
                result = cursor.fetchone()
                if result:
                    return result[0], result[1], "patents_all"
        except Exception as e:
            logger.error(f"Database error looking up reg_no {reg_no}: {e}", exc_info=True)
        return None

    def update_patent_record(self, reg_no: str, plaintiff_id: int) -> bool:
        try:
            with self.db_conn.cursor() as cursor:
                cursor.execute(
                    "UPDATE patents SET tro = 'TRUE', plaintiff_id = %s WHERE reg_no = %s;",
                    (plaintiff_id, reg_no)
                )
                self.db_conn.commit()
                return True
        except Exception as e:
            logger.error(f"Database update error for reg_no {reg_no}: {e}", exc_info=True)
            self.db_conn.rollback()
            return False

    async def process_patent_entry(self, index: int, df: pd.DataFrame, patent_data: Dict, plaintiff_id: int, case_id: str, old_filename: str, case_images_directory: str) -> bool:
        original_doc_no = patent_data.get('patent_number', "")
        original_reg_no = clean_and_validate_reg_no(original_doc_no)

        if not original_reg_no:
            logger.warning(f"⚠️ No valid reg_no found for patent data: {patent_data}. Skipping.")
            return False

        if (case_id, original_reg_no, plaintiff_id) in PROCESSED_PATENTS:
            return True

        PROCESSED_PATENTS.add((case_id, original_reg_no, plaintiff_id))
        self.summary_processed_patents += 1

        db_result = self.find_patent_in_db(original_reg_no)
        if db_result and db_result[2] == "patents":
            self.summary_patents_found_in_patents += 1
            found_reg_no = db_result[0]
            if found_reg_no not in self.tro_updated_reg_nos:
                if self.update_patent_record(found_reg_no, plaintiff_id):
                    self.tro_updated_reg_nos.add(found_reg_no)
                else:
                    PROBLEMATIC_PATENTS.add((case_id, original_reg_no, plaintiff_id, 'update_failed'))
                    return False
            return True
        
        if db_result and db_result[2] == "patents_all":
            self.summary_patents_found_in_patents_all += 1
            return True

        self.summary_patents_not_found_in_patents += 1
        
        # Try Gemini OCR if not found in local DB
        local_image_path = os.path.join(case_images_directory, old_filename)
        if not os.path.exists(local_image_path):
            logger.error(f"❌ Image not found at {local_image_path}. Cannot perform OCR.")
            PROBLEMATIC_PATENTS.add((case_id, original_reg_no, plaintiff_id, 'no_image'))
            self.summary_other_not_found_patents += 1
            return False

        try:
            prompt_list = [("text", "What is the patent number? It is usually at the top right and starts with 'US'"), ("image_path", local_image_path)]
            ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE, useVertexAI=Constants.IMAGE_MODEL_FREE_VERTEX)
            new_reg_no = clean_and_validate_reg_no(ai_answer)

            if new_reg_no and new_reg_no != original_reg_no:
                self.summary_found_by_gemini += 1
                
                # Verify new_reg_no
                new_db_result = self.find_patent_in_db(new_reg_no)
                uspto_patents = None
                if not new_db_result:
                    uspto_patents = await get_patent_data_by_reg_no([new_reg_no])

                # If found anywhere, prompt for approval
                if new_db_result or uspto_patents:
                    if not self._prompt_user_for_approval(original_reg_no, new_reg_no):
                        logger.info(f"User rejected AI suggestion: {original_reg_no} -> {new_reg_no}")
                        PROBLEMATIC_PATENTS.add((case_id, original_reg_no, plaintiff_id, 'user_rejected'))
                        return False
                    
                    # --- Approved: Update everything ---
                    if new_db_result and new_db_result[2] == "patents":
                        self.summary_gemini_search_found_in_patents += 1
                        self.update_patent_record(new_reg_no, plaintiff_id)
                    elif new_db_result and new_db_result[2] == "patents_all":
                        self.summary_gemini_search_found_in_patents_all += 1
                    elif uspto_patents:
                        self.summary_gemini_search_found_on_uspto += 1
                    
                    # Update DataFrame and tb_case
                    try:
                        patent_entry_data = df.at[index, 'images']['patents'].pop(old_filename)
                        patent_entry_data['patent_number'] = new_reg_no
                        df.at[index, 'images']['patents'][f"{new_reg_no}.png"] = patent_entry_data
                        insert_and_update_df_to_GZ_batch(df.loc[[index]], "tb_case", key_column="id")
                        logger.info(f"Successfully updated tb_case for case {case_id} with new patent {new_reg_no}")
                        self.tro_updated_reg_nos.add(new_reg_no)
                        return True
                    except Exception as e:
                        logger.error(f"Failed to update DataFrame or tb_case for {case_id}: {e}", exc_info=True)
                        PROBLEMATIC_PATENTS.add((case_id, new_reg_no, plaintiff_id, 'df_update_failed'))
                        return False
                else:
                    logger.warning(f"❌ New reg_no {new_reg_no} from Gemini not found anywhere.")
                    self.summary_gemini_not_found += 1
                    PROBLEMATIC_PATENTS.add((case_id, new_reg_no, plaintiff_id, 'gemini_not_found'))
                    return False
            else:
                logger.warning(f"❌ Gemini could not find a valid new reg_no. AI answer: {ai_answer}")
                self.summary_gemini_not_found += 1
                PROBLEMATIC_PATENTS.add((case_id, original_reg_no, plaintiff_id, 'gemini_failed'))
                return False
        except Exception as e:
            logger.error(f"Error during Gemini OCR for {original_reg_no}: {e}", exc_info=True)
            PROBLEMATIC_PATENTS.add((case_id, original_reg_no, plaintiff_id, 'gemini_exception'))
            return False
        
        return False

    async def migrate_case_patents(self, index: int, case_row: pd.Series, df: pd.DataFrame) -> bool:
        case_id = case_row['id']
        plaintiff_id = int(float(case_row['plaintiff_id']))
        images_data = case_row.get('images', {})
        patents_data = images_data.get('patents', {})

        if not patents_data:
            return True

        date_filed = case_row['date_filed']
        docket = case_row['docket']
        case_directory = os.path.join(Constants.local_case_folder, str(date_filed.year), docket)
        case_images_directory = os.path.join(case_directory, "images")

        for old_filename, patent_data in list(patents_data.items()):
            success = await self.process_patent_entry(index, df, patent_data, plaintiff_id, case_id, old_filename, case_images_directory)
            if not success:
                self.error_count += 1
            else:
                self.processed_count += 1
        return True

    async def migrate_all_cases(self, df: pd.DataFrame) -> pd.DataFrame:
        logger.info(f"Starting migration of {len(df)} cases")
        
        for i, (index, case_row) in enumerate(df.iterrows()):
            case_id = case_row.get('id', 'unknown')
            try:
                await self.migrate_case_patents(index, case_row, df)
                if (i + 1) % 10 == 0:
                    logger.info(f"⛏ Processed {i + 1}/{len(df)} cases")
            except Exception as e:
                logger.error(f"Error processing case {case_id}: {e}", exc_info=True)
                self.error_count += 1
 
        logger.info(f"✅ Migration completed. Processed: {self.processed_count}, Skipped: {self.skipped_count}, Errors: {self.error_count}")
        return df

def main():
    """Main migration function"""
    logger.info("Starting Patents Filename Migration")
    logger.info("=" * 50)

    try:
        logger.info("Loading cases from database...")
        df = get_table_from_GZ("tb_case")
    except Exception as e:
        logger.critical(f"Failed to load tb_case from database: {e}", exc_info=True)
        return

    df['has_patents'] = df['images'].apply(lambda x: isinstance(x, dict) and 'patents' in x and bool(x['patents']))
    df_to_migrate = df[df['has_patents']].copy()
    
    if df_to_migrate.empty:
        logger.info("No cases with patent images found.")
        return

    logger.info(f"Found {len(df_to_migrate)} cases with patent images")
    df_to_migrate = df_to_migrate.sort_values(by='date_filed', ascending=True)

    async def run_migration():
        async with PatentFilenameMigrator() as migrator:
            if not migrator.reset_patent_tro_plaintiff_ids():
                logger.error("Failed to reset 'tro' and 'plaintiff_id' columns. Aborting migration.")
                return None
            
            await migrator.migrate_all_cases(df_to_migrate)
            return migrator

    migrator_instance = asyncio.run(run_migration())

    if migrator_instance:
        logger.info("\n" + "=" * 50)
        logger.info("Migration Summary:")
        logger.info(f"Total patents processed: {migrator_instance.summary_processed_patents}")
        logger.info(f"Patents found in patents: {migrator_instance.summary_patents_found_in_patents}")
        logger.info(f"Patents not found patents: {migrator_instance.summary_patents_not_found_in_patents}")
        logger.info(f"-- Patents found in patents_all: {migrator_instance.summary_patents_found_in_patents_all}")
        logger.info(f"---- Found by exhibit: {migrator_instance.summary_patents_all_found_by_exhibit}")
        if migrator_instance.examples_patents_all_by_exhibit:
            logger.info(f"------ Examples: {', '.join(migrator_instance.examples_patents_all_by_exhibit)}")
        logger.info(f"---- Found by reg no: {migrator_instance.summary_patents_all_found_by_regno}")
        if migrator_instance.examples_patents_all_by_regno:
            logger.info(f"------ Examples: {', '.join(migrator_instance.examples_patents_all_by_regno)}")
        logger.info(f"---- Found by name: {migrator_instance.summary_patents_all_found_by_name}")
        if migrator_instance.examples_patents_all_by_name:
            logger.info(f"------ Examples: {', '.join(migrator_instance.examples_patents_all_by_name)}")
        logger.info(f"---- Unknown source: {migrator_instance.summary_patents_all_found_unknown_source}")
        logger.info(f"-- Patents with date < 2011: {migrator_instance.summary_patents_with_date_lt_2011}")
        logger.info(f"-- Found on USPTO: {migrator_instance.summary_found_on_uspto}")
        logger.info(f"---- USPTO patents with date < 2011: {migrator_instance.summary_uspto_date_lt_2011}")
        logger.info(f"-- Found by Gemini: {migrator_instance.summary_found_by_gemini}")
        logger.info(f"---- Gemini search found in patents: {migrator_instance.summary_gemini_search_found_in_patents}")
        logger.info(f"---- Gemini search found in patents_all: {migrator_instance.summary_gemini_search_found_in_patents_all}")
        logger.info(f"---- Gemini search found on USPTO: {migrator_instance.summary_gemini_search_found_on_uspto}")
        logger.info(f"---- Gemini not found: {migrator_instance.summary_gemini_not_found}")
        logger.info(f"-- Other not found patents: {migrator_instance.summary_other_not_found_patents}")
        if migrator_instance.summary_top_10_other_not_found_patents:
            logger.info("---- Top 10 examples of other not found patents:")
            for reg_no in migrator_instance.summary_top_10_other_not_found_patents:
                logger.info(f"---- reg no: {reg_no}")
        logger.info("=" * 50)


if __name__ == "__main__":
    main()
