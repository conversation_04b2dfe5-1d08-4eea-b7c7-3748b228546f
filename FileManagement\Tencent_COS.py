## This way optimized by claude is 2x faster than the old way! No idea what it does :-P

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import sys
# Removed: import logging
import os
sys.path.append(os.getcwd())
from logdata import log_message # Added logdata import
from Common.Constants import sanitize_name
import json
import time
from Common.Constants import local_case_folder
import asyncio
from concurrent.futures import ThreadPoolExecutor
from Common.uuid_utils import generate_obfuscated_key
from langfuse import observe
import langfuse
import threading
import tempfile
from queue import Queue

semaphore = None

# Global client manager instance
_client_manager_lock = threading.Lock()
_client_managers = {}

def get_cos_client_manager(secret_id_env="COS_MDLV_SECRET_ID", secret_key_env="COS_MDLV_SECRET_KEY", bucket="tro-1330776830", pool_size=16):
    """
    Factory function to get a singleton COSClientManager for a given configuration.
    """
    config_key = (secret_id_env, secret_key_env, bucket)
    with _client_manager_lock:
        if config_key not in _client_managers:
            _client_managers[config_key] = COSClientManager(
                secret_id_env=secret_id_env,
                secret_key_env=secret_key_env,
                bucket=bucket,
                pool_size=pool_size
            )
        return _client_managers[config_key]

class COSClientManager:
    """Manages a pool of COS S3 clients for concurrent uploads. Thread-safe."""
    def __init__(self, secret_id_env, secret_key_env, bucket, pool_size=16):
        self.bucket = bucket
        self._pool = Queue(maxsize=pool_size)
        for _ in range(pool_size):
            client, _ = get_cos_client(secret_id_env, secret_key_env, bucket)
            self._pool.put_nowait(client)

    def get_client(self):
        """Acquire a client from the pool. Blocks until a client is available."""
        return self._pool.get()

    def release_client(self, client):
        """Release a client back to the pool."""
        self._pool.put(client)

def process_images_data(images_data):
    """Process images data from different formats"""
    if isinstance(images_data, str):
        return json.loads(images_data)
    return images_data if isinstance(images_data, dict) else {}


def get_case_dir(row):
    """Get case directory path"""
    return os.path.join(
        local_case_folder,
        sanitize_name(f"{row['date_filed'].strftime('%Y-%m-%d')} - {row['docket']}")
    )

def get_image_paths(images, case_dir, plaintiff_id):
    """Generate all image paths and keys"""
    # Reminder of the structure of the field: df["images"]["trademark"][filename]= {regno = "8765468468", full_filename = ["us_indef_pag5"]}
    for ip in images.keys():
        for image in images[ip].keys():
            paths = []
            # Actual images
            for res in ["high", "low"]:
                file_path = os.path.join(case_dir, "images", res, image)
                key = f'plaintiff_images/{int(plaintiff_id)}/{res}/{image}'
                paths.append((key, file_path))

            # Certificates images
            for full_image in images[ip][image]["full_filename"]:
                file_path = os.path.join(case_dir, "images", "high", full_image)
                key = f'plaintiff_images/{int(plaintiff_id)}/high/{full_image}'
                paths.append((key, file_path))

            yield image, paths


def get_cos_client(secret_id_env="COS_SECRET_ID", secret_key_env="COS_SECRET_KEY", bucket=None):
    config = CosConfig(
        Region='ap-guangzhou',
        SecretId=os.getenv(secret_id_env),
        SecretKey=os.getenv(secret_key_env),
        Token=None,
        Scheme='https'
    )
    if bucket is None:
        bucket = 'troimages-1329604052'
    return CosS3Client(config), bucket


### Used by Check
# What it is: A coroutine is a special type of function that can be paused during its execution and resumed later from where it left off. They are defined using the async def syntax in Python.
# Key features: Yielding control: When a coroutine encounters an await, it yields control back to the event loop, allowing the loop to run other coroutines.
async def async_upload_file_with_retry(client, bucket, key, file_path, max_retries=5):
    """Helper function for asynchronous file upload with retry logic"""
    # Removed logging manipulation block

    for attempt in range(max_retries):
        try:
            # Use a semaphore to limit concurrent connections
            # async with semaphore:
                loop = asyncio.get_event_loop()
                # The Tencent COS SDK (client.upload_file()) is synchronous/blocking code. To use it in an async context without blocking the event loop, we need to run it in a separate thread/process.
                response = await loop.run_in_executor(
                    None,  # Use default executor
                    lambda: client.upload_file(
                        Bucket=bucket,
                        LocalFilePath=file_path,
                        Key=key,
                        EnableMD5=False,
                        MAXThread=10
                    )
                )
                # log_message(f"Uploaded 1 file to COS: {key}", level='INFO') # Added key for context
                return response
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            log_message(f"Failed to upload {key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
            await asyncio.sleep(2 ** attempt)  # Exponential backoff

    # Restore original logging configuration
    # Removed logging restoration block

### Used by Check
async def async_copy_file_with_retry(client, bucket, to_key, from_key, max_retries=3):
    """Helper function for asynchronous file upload with retry logic"""
    # Removed logging manipulation block

    for attempt in range(max_retries):
        try:
            # Use a semaphore to limit concurrent connections
            # async with semaphore:
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None,  # Use default executor
                    lambda: client.copy_object(
                        Bucket=bucket,
                        Key=to_key,
                        CopySource={
                            'Bucket': bucket,
                            'Key': from_key,
                            'Region': 'ap-guangzhou'
                        }
                    )
                )
                log_message(f"Copied {from_key} to {to_key} in COS", level='INFO') # Added keys for context
                return response
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            log_message(f"Failed to copy {from_key} to {to_key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
            await asyncio.sleep(2 ** attempt)  # Exponential backoff

### End of Check functions

# Used by images sync (trademarks, patent, and copyright), and send_pictures_to_cos (using dataframe from Alerts module)
def upload_image(client, bucket, file_path, cos_key, max_retries=5):
    """Upload single image to COS with retry logic"""
    for attempt in range(max_retries):
        try:
            client.upload_file(
                Bucket=bucket,
                LocalFilePath=file_path,
                Key=cos_key,
                EnableMD5=False
            )
            # log_message(f"Uploaded 1 file to COS: {cos_key}", level='INFO')
            return True, file_path
        except Exception as e:
            if attempt == max_retries - 1:
                log_message(f"Failed to upload {file_path} to {cos_key}: {str(e)}", level='ERROR')
                return False, file_path
            else:
                log_message(f"Failed to upload {file_path} to {cos_key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
                time.sleep(2 ** attempt) # Exponential backoff

# Used by Alerts module
async def async_upload_image(client, bucket, file_path, cos_key, max_retries=5):
    """Asynchronously upload a single image to COS with retry logic."""
    loop = asyncio.get_event_loop()
    for attempt in range(max_retries):
        try:
            await loop.run_in_executor(
                None,  # Use default executor
                lambda: client.upload_file(
                    Bucket=bucket,
                    LocalFilePath=file_path,
                    Key=cos_key,
                    EnableMD5=False
                )
            )
            return True, file_path
        except Exception as e:
            if attempt == max_retries - 1:
                log_message(f"Failed to upload {file_path} to {cos_key}: {str(e)}", level='ERROR')
                return False, file_path
            else:
                log_message(f"Failed to upload {file_path} to {cos_key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
                await asyncio.sleep(2 ** attempt)  # Asynchronous sleep

def upload_image_buffer(client, bucket, buffer, cos_key, max_retries=5):
    """Synchronously upload a single image buffer to COS with retry logic."""
    for attempt in range(max_retries):
        try:
            client.put_object(
                Bucket=bucket,
                Body=buffer,
                Key=cos_key,
                EnableMD5=False
            )
            return True, cos_key
        except Exception as e:
            if attempt == max_retries - 1:
                log_message(f"Failed to upload buffer to {cos_key}: {str(e)}", level='ERROR')
                return False, cos_key
            else:
                log_message(f"Failed to upload buffer to {cos_key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
                time.sleep(2 ** attempt)  # Synchronous sleep

def upload_image_buffer2(client, bucket, buffer, cos_key, max_retries=5):
    """
    Uploads an image buffer to COS by first saving it to a temporary file.
    This is for speed comparison with upload_image_buffer.
    """
    # Create a temporary file
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        temp_file.write(buffer)
        temp_file_path = temp_file.name

    try:
        for attempt in range(max_retries):
            try:
                client.upload_file(
                    Bucket=bucket,
                    LocalFilePath=temp_file_path,
                    Key=cos_key,
                    EnableMD5=False
                )
                return True, cos_key
            except Exception as e:
                if attempt == max_retries - 1:
                    log_message(f"Failed to upload {temp_file_path} to {cos_key}: {str(e)}", level='ERROR')
                    return False, cos_key
                else:
                    log_message(f"Failed to upload {temp_file_path} to {cos_key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
                    time.sleep(2 ** attempt)  # Synchronous sleep
    finally:
        # Clean up the temporary file
        os.remove(temp_file_path)

@observe(capture_input=False, capture_output=False)
async def send_pictures_to_cos(df):
    """
    Asynchronous version that uploads pictures to COS using asyncio.gather
    to process multiple files concurrently.
    """
    if len(df) == 0:
        return True

    client, bucket = get_cos_client()
    upload_tasks_paths = []

    # Collect all file paths and keys from the dataframe
    for index, row in df.iterrows():
        images = process_images_data(row["images"])
        case_dir = get_case_dir(row)
        for image, paths in get_image_paths(images, case_dir, row["plaintiff_id"]):
            upload_tasks_paths.extend(paths)

    if not upload_tasks_paths:
        return True  # Early return if no tasks to process

    log_message(f"⬆️  ☁️ Starting upload of {len(upload_tasks_paths)} files to COS for case {row['docket']}", level='INFO')

    # Create asyncio tasks for all uploads
    tasks = [
        async_upload_image(client, bucket, file_path, key)
        for key, file_path in upload_tasks_paths
    ]

    # Run all tasks concurrently
    results = await asyncio.gather(*tasks)

    # Process results
    successful = sum(1 for r in results if r[0])
    failed = [r[1] for r in results if not r[0]]

    # Report results
    if failed:
        langfuse.get_client().update_current_span(level="ERROR", status_message=f"Failed to upload {len(failed)} files to COS", output={"FailedFiles": failed, "SuccessfulUploads": successful})
        log_message(f"❌  ⬆️  ☁️ COS: {successful} files uploaded successfully, {len(failed)} failed, for case {row['docket']}", level='WARNING')
        return False
    
    langfuse.get_client().update_current_span(output=f"All {successful} files uploaded successfully for case {row['docket']}")
    log_message(f"✅  ⬆️  ☁️ COS: All {successful} files uploaded successfully for case {row['docket']}", level='INFO')
    return True






def delete_cos_files(client, bucket, file_infos):
    # Construct the batch delete request.
    delete_list = []
    for file in file_infos:
        delete_list.append({"Key": file['Key']})

    response = client.delete_objects(Bucket=bucket, Delete={"Object": delete_list})
    log_message(f"COS Delete response: {response}", level='INFO') # Log response

def download_from_cos(client, bucket, cos_key, local_path):
    """Download a file from COS to a local path"""
    try:
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        response = client.download_file(
            Bucket=bucket,
            Key=cos_key,
            DestFilePath=local_path
        )
        return True
    except Exception as e:
        log_message(f"download_from_cos: Failed to download {cos_key}: {str(e)}", level='ERROR')
        return False



def list_all_files_in_COS(prefix=None, client=None, bucket=None):
    """
    Lists all files (keys) in the specified COS bucket that start with the given prefix.
    Handles pagination to retrieve more than 1000 files.
    """
    if client is None or bucket is None:
        client, bucket = get_cos_client()
    
    all_files = []
    marker = ""
    while True:
        if prefix:
            response = client.list_objects(Bucket=bucket, Prefix=prefix, Marker=marker)
        else:
            response = client.list_objects(Bucket=bucket, Marker=marker)
        if 'Contents' in response:
            for content in response['Contents']:
                all_files.append(content['Key'])
        if response['IsTruncated'] == 'false':
            break
        marker = response["NextMarker"]
    return all_files



# Used by Patent bulk, Trademark bulk
def images_cos_sync(base_path, cos_prefix, extension):
    if not os.path.exists(base_path):
        log_message(f"Directory not found: {base_path}")
        return

    # Get COS client with custom credentials
    client, bucket = get_cos_client(secret_id_env="COS_MDLV_SECRET_ID",secret_key_env="COS_MDLV_SECRET_KEY",bucket="tro-1330776830")

    # Collect all image paths
    image_paths = []
    for root, _, files in os.walk(base_path):
        for file in files:
            if file.endswith(f".{extension}"):
                full_path = os.path.join(root, file)
                image_paths.append(full_path)

    log_message(f"Found {len(image_paths)} images")
    

    # Get existing files in COS
    start_time = time.time()
    log_message("Listing existing files in COS, it might take 15 minutes per million files...")
    existing_cos_files = set(list_all_files_in_COS(prefix=cos_prefix, client=client, bucket=bucket))
    log_message(f"Found {len(existing_cos_files)} existing files in COS, in {time.time() - start_time:.2f} seconds")

    # Process uploads in parallel
    uploaded_count = 0
    skipped_count = 0
    failed_count = 0
    expected_keys_set = set()
    
    # Remove trailing slash from cos_prefix
    if cos_prefix[-1] == '/':
        cos_prefix = cos_prefix[:-1]
        
    
    to_upload_paths = [p for p in image_paths 
                      if f"{cos_prefix}/{generate_obfuscated_key(os.path.splitext(os.path.basename(p))[0])}.{extension}" not in existing_cos_files]
    total_images = len(to_upload_paths)
    log_message(f"Found {len(to_upload_paths)} images to upload")
    
    # Process the uploads
    start_time = time.time()
    start_time_1k = start_time
    with ThreadPoolExecutor(max_workers=50) as executor:
        futures = []
        for path in to_upload_paths:
            filename = os.path.splitext(os.path.basename(path))[0]
            obfuscated = generate_obfuscated_key(filename)
            cos_key = f"{cos_prefix}/{obfuscated}.{extension}"
            expected_keys_set.add(cos_key) # Add to expected keys set
            
            if cos_key in existing_cos_files:
                log_message(f"Skipping {path}: already exists in COS as {cos_key}")
                skipped_count += 1
            else:
                futures.append(executor.submit(upload_image, client, bucket, path, cos_key))
        
        # Track results
        completed_count = skipped_count # Initialize with skipped images
        progress_lock = threading.Lock() # Thread-safe counter lock

        for future in futures:
            result, path = future.result()
            with progress_lock: # Ensure thread-safe increment
                if result:
                    uploaded_count += 1
                else:
                    failed_count += 1
                    log_message(f"Failed: {path}")
                completed_count += 1
                if completed_count % 10000 == 0 or completed_count == total_images:
                    log_message(f"\033[91mProcessed {completed_count} of {total_images} images in {format(time.time() - start_time),'.1f'} sec. Last 10k in {format(time.time() - start_time_1k),'.1f'} sec\033[0m", level='INFO')
                    start_time_1k = time.time()

    log_message(f"Upload complete. Uploaded: {uploaded_count}, Skipped: {skipped_count}, Failed: {failed_count}")

    # Compute COS files to delete
    expected_keys_set  = set([f"{cos_prefix}/{generate_obfuscated_key(os.path.splitext(os.path.basename(p))[0])}.{extension}" for p in image_paths])
    to_delete = existing_cos_files - expected_keys_set
    log_message(f"Files to delete: {len(to_delete)}")
    # deleted_count = 0
    # if to_delete:
    #     log_message(f"Found {len(to_delete)} COS files to delete.")
    #     # COS allows max 1000 objects per delete request
    #     batch_size = 1000
    #     to_delete_list = list(to_delete)
    #     for i in range(0, len(to_delete_list), batch_size):
    #         batch = to_delete_list[i:i + batch_size]
    #         file_infos = [{"Key": key} for key in batch]
    #         try:
    #             delete_cos_files(client, bucket, file_infos)
    #             deleted_count += len(batch)
    #             log_message(f"Deleted {len(batch)} COS files in batch {i//batch_size + 1}.")
    #         except Exception as e:
    #             log_message(f"Error deleting COS files batch starting with {batch[0]}: {e}")
    # else:
    #     log_message("No COS files to delete.")

    # log_message(f"Deletion complete. Deleted: {deleted_count}")
    # log_message(f"Total time: {time.time()-start_time:.2f} seconds")



if __name__ == "__main__":
    # list_all_files_in_COS(prefix="/plaintiff_images/665/")
    # Sync ip_assets
    from Common.Constants import local_ip_folder
    images_cos_sync(os.path.join(local_ip_folder, "Trademarks", "USPTO_Daily", "Images"), "ip_assets/Trademarks/", "webp")
    
    
    # client, bucket = get_cos_client(secret_id_env="COS_MDLV_SECRET_ID",secret_key_env="COS_MDLV_SECRET_KEY",bucket="tro-1330776830")
    # Delete any file with key starting with ip_assets/Patents/ and ending with .webp
    # to_delete = list_all_files_in_COS(prefix="ip_assets/Patents/", client=client, bucket=bucket)
    # print(f"deleting {len(to_delete)} webp images")
    
    # chunk_size = 1000
    # for i in range(0, len(to_delete), chunk_size):
    #     chunk = to_delete[i:i + chunk_size]
    #     client.delete_objects(Bucket=bucket, Delete={"Object": [{"Key": key} for key in chunk if key.endswith(".webp")]})
    #     print(f"Deleted {len(chunk)} webp images")
    
    # !!!! For 8 million patents it will take 20 minutes to get the list from the SSD and 1 hours to get the list from the COS
    images_cos_sync(os.path.join(local_ip_folder, "Patents", "USPTO_Grants", "Extracted"), "ip_assets/Patents/", "png")
    
    
    # images_cos_sync(os.path.join(local_ip_folder, "Copyrights"), "ip_assets/Copyrights/")
    
    
    
    # import pandas as pd
    # df = get_table_from_GZ("tb_case")
    # df = df[df['date_filed'] == pd.to_datetime('2024-01-15').date()]
    # # df = df[df['date_filed'] <= '2024-01-16']
    # start_time = time.time()
    # send_pictures_to_cos(df)
    # end_time = time.time()
    # print(f"Total time taken: {end_time - start_time:.1f} seconds")
