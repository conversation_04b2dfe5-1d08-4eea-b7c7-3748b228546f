from arq.jobs import Job, JobStatus
from fastapi import APIRouter, Request, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
import copy
from datetime import datetime
import langfuse
from Check.api_studio.kong_util import get_consumer
from Qdrant.api.utils.db import get_db_connection as get_pg_connection
from Check.api_studio.admin_routes import create_error_response
import psycopg2.extras
import os
import redis
from prometheus_client import Counter

# Prometheus Counter
REQUESTS_TOTAL = Counter(
   "requests_total",
   "Total number of requests",
   ["client_name", "route", "status", "server"],
)


# Determine server identifier for Langfuse tracing
server_location = os.environ.get('SERVER_LOCATION')
current_path = os.getcwd()
if 'workspace' in current_path:
    server_type = "Main"
elif 'app' in current_path:
    server_type = "Backup"
else:
    import socket
    server_type = socket.gethostname()

server_identifier = f"{server_type}{server_location[-1] if server_location else ''}"

# Create APIRouter
check_bp = APIRouter()




async def get_estimated_completion_time(request: Request, job_id: str, queue_name='arq:queue', workers: int = 4):
    """
    Calculates the estimated completion time based on queue length.
    Formula: 30s base + (position / 4 workers) * 30s per job.
    """
    pool  = request.app.state.arq_pool
    s     = await pool.zscore(queue_name, job_id)       # job's scheduled time (ms since epoch)
    if s is None:
        return None  # not in queue (already running, done, or missing)
    ahead = await pool.zcount(queue_name, -float("inf"), int(s))  # jobs scheduled to run no later than this job
    base_s, per_job = 30, 30
    return base_s + int((max(ahead - 1, 0) / max(workers, 1)) * per_job)

def _create_success_response(check_id, result, trace_id):
    """Creates a standardized success response for a completed task."""
    print(f"✅✅✅ Results are sent to client for check_id: {check_id}")
    try:
        with langfuse.get_client().start_as_current_span(name="Results Fetched and Found", trace_context={"trace_id": trace_id}):
            langfuse.get_client().update_current_span(output={"✅✅✅ Results are sent to client at": datetime.now().isoformat()})
    except Exception as trace_error:
        print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}, error: {str(trace_error)}")
    return JSONResponse(content={'status': 'completed', 'result': result})

def _create_failure_response(check_id, error_info):
    """Creates a standardized failure response from ARQ task info."""
    error_code = 'JOB_FAILED'
    message = "The analysis job failed unexpectedly."
    details = str(error_info)
    error_dict = None
    if isinstance(error_info, dict):
        error_dict = error_info
    elif isinstance(error_info, Exception) and len(error_info.args) > 0 and isinstance(error_info.args[0], dict):
        error_dict = error_info.args[0]
    if error_dict:
        error_code = error_dict.get('error_code', 'JOB_FAILED')
        message = error_dict.get('exc_message', "The analysis job failed unexpectedly.")
        details = error_dict.get('details', str(error_info))
    print(f"🔥 Task failed for check_id: {check_id}. Reason: {details}")
    return JSONResponse(content={'status': 'error', 'error_code': error_code, 'message': message, 'details': details})

@check_bp.get('/check_status/{check_id}')
async def check_status(check_id: str, request: Request):
    trace_id = langfuse.get_client().create_trace_id(seed=check_id)
    arq_pool = request.app.state.arq_pool
    job = Job(check_id, arq_pool)

    # Prefer non-blocking result lookup first
    job_result = await job.result_info()
    if job_result is not None:
        if job_result.success:
            result = job_result.result
            client_name = result['client_name']
            REQUESTS_TOTAL.labels(client_name=client_name, route="/check_status", status="completed", server=server_identifier).inc()
            return _create_success_response(check_id, result, trace_id)
        else:
            return _create_failure_response(check_id, job_result.result)

    # No result yet — ask ARQ for status
    status = await job.status()
    if status in (JobStatus.queued, JobStatus.deferred, JobStatus.in_progress):
        estimated_time = await get_estimated_completion_time(request, check_id)
        payload = {'status': 'processing', 'job_status': status.value}
        if estimated_time is not None:
            payload['estimated_completion_time'] = estimated_time
        return JSONResponse(content=payload)

    if status == JobStatus.complete:
        try:
            result = await job.result(timeout=0)
            client_name = result['client_name']
            REQUESTS_TOTAL.labels(client_name=client_name, route="/check_status", status="completed", server=server_identifier).inc()
            return _create_success_response(check_id, result, trace_id)
        except Exception as e:
            return _create_failure_response(check_id, e)

    # Not found in ARQ — your DB fallback
    if not check_id.isdigit():
        return create_error_response('RESULTS_NOT_FOUND', "No results found for the given Check ID.", status_code=404)

    pg_conn = None
    try:
        pg_conn = get_pg_connection()
        with pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute("SELECT result FROM check_api_results WHERE check_id = %s", (int(check_id),))
            row = cur.fetchone()
        
        if row and row['result']:
            result = row['result']
            client_name = result['client_name']
            REQUESTS_TOTAL.labels(client_name=client_name, route="/check_status", status="completed_from_db", server=server_identifier).inc()
            return _create_success_response(check_id, result, trace_id)
        
        REQUESTS_TOTAL.labels(client_name=client_name, route="/check_status", status="not_found", server=server_identifier).inc()
        return create_error_response('RESULTS_NOT_FOUND', "No results found for the given Check ID.", status_code=404)
    except Exception as e:
        return create_error_response('DATABASE_CONNECTION_ERROR', "Could not connect to the database.", details=str(e), status_code=500)
    finally:
        if pg_conn:
            pg_conn.close()

@check_bp.post('/check_api')
async def check_api_route(request: Request):
    now = datetime.now()
    data = await request.json()
    
    consumer_data = get_consumer(request)
    if not consumer_data:
        return create_error_response(
            'UNAUTHORIZED',
            "Missing or invalid API key.",
            status_code=401
        )

    client_name = consumer_data["username"]
    client_id = consumer_data["id"]

    if client_name in ["MiniApp", "H5"]:
        check_id = data.get('check_id')
    else:
        check_id = f"{client_id}{now.strftime('%Y%m%d%H%M%S')}"
    
    print(f"Generated check_id: {check_id}")
    trace_id = langfuse.get_client().create_trace_id(seed=check_id)
    print(f"Generated Langfuse trace_id: {trace_id}")
    return await check_api(check_id, trace_id, request, langfuse_trace_id=trace_id)

@langfuse.observe(name="/check_api")
async def check_api(check_id: str, trace_id: str, request: Request):
    data = await request.json()
    trace_id = langfuse.get_client().get_current_trace_id()
    
    # Create a copy of data for logging to avoid modifying the original
    log_data = copy.deepcopy(data) if data else {}
    
    # Identify and truncate likely base64 image strings for logging
    for key, value in log_data.items() if isinstance(log_data, dict) else []:
        if key in ['main_product_image', 'other_product_images', 'ip_images', 'reference_images']:
            # Handle array of images or direct image strings
            if isinstance(value, list):
                for i in range(len(value)):
                    if isinstance(value[i], str) and len(value[i]) > 500:
                        log_data[key][i] = f"[base64 image: {len(value[i])} chars]"
            elif isinstance(value, str) and len(value) > 500:
                log_data[key] = f"[base64 image: {len(value)} chars]"

    print(f"\n\n📥📥📥 API request received at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Parsed JSON data: {log_data}")
    
    try:
        # API key is already validated in check_api_route
        consumer_data = get_consumer(request)
        client_name = consumer_data["username"]
        client_id = consumer_data["id"]
        api_key = consumer_data["api_key"]
        
        langfuse.get_client().update_current_trace(name=f"[{server_identifier}] API Check", user_id=client_name, session_id=check_id, metadata={"check_id": check_id, "client": client_name, "api_key": api_key, "server": server_identifier})
        langfuse.get_client().update_current_span(input=log_data)
        print(f"📊 Langfuse trace_id for check_id {check_id}: {langfuse.get_client().get_current_trace_id()}")
        print(f"📊 Updated Langfuse trace with check_id: {check_id} for client: {client_name}")
    
        # Retrieve and validate other required fields.
        product_category = data.get('product_category', '') or ""
        main_product_image = data.get('main_product_image')
        if not main_product_image:
            print(f"⛔ API validation error: Missing main_product_image.")
            return create_error_response('MISSING_MAIN_IMAGE', "A main product image is required for the analysis.", status_code=400)
            
        other_product_images = data.get('other_product_images', []) or []
        client_ip_images = data.get('ip_images', []) or []
        ip_keywords = data.get('ip_keywords', []) or []
        description = data.get('description', '')

        # Apply limits to arrays - use first N items if more than limit provided
        if len(other_product_images) > 5:
            other_product_images = other_product_images[:5]
            print(f"⚠️ Limited other_product_images to first 5 items (received {len(data.get('other_product_images', []))})")

        if len(client_ip_images) > 3:
            client_ip_images = client_ip_images[:3]
            print(f"⚠️ Limited ip_images to first 3 items (received {len(data.get('ip_images', []))})")

        if len(ip_keywords) > 20:
            ip_keywords = ip_keywords[:20]
            print(f"⚠️ Limited ip_keywords to first 20 items (received {len(data.get('ip_keywords', []))})")
            
        reference_text = data.get('reference_text', '') or ""
        reference_images = data.get('reference_images', []) or []
        language = data.get('language', 'zh')

        # Apply limit to reference_images
        if len(reference_images) > 3:
            reference_images = reference_images[:3]
            print(f"⚠️ Limited reference_images to first 3 items (received {len(data.get('reference_images', []))})")
        
    except KeyError as e:
        field_name = str(e).strip("'")
        error_message = f"Missing required field: {field_name}"
        print(f"⛔ API validation error: {error_message}")
        return create_error_response('MISSING_REQUIRED_FIELD', error_message, details=f"The field '{field_name}' is required.", status_code=400)
    except Exception as e:
        error_message = f"Internal server error: {str(e)}"
        print(f"🔥 API server error: {error_message}")
        return create_error_response('SERVER_ERROR', "An unexpected internal server error occurred.", details=str(e), status_code=500)


    try:
        # Submit the job to the ARQ queue.
        # --- Start Data Integrity Logging ---
        if main_product_image:
            sender_debug_info = {
                "sender_image_len": len(main_product_image),
                "sender_image_hash": hash(main_product_image)
            }
            print(f"DEBUG: [SENDER] check_id={check_id}, {sender_debug_info}")
            langfuse.get_client().update_current_span(metadata=sender_debug_info)
        # --- End Data Integrity Logging ---

        # --- ARQ Task ---
        arq_pool = request.app.state.arq_pool
        await arq_pool.enqueue_job(
            'process_check_task',
            check_id,
            client_id,
            client_name,
            trace_id,
            product_category,
            api_key,
            language,
            main_product_image=main_product_image,
            other_product_images=other_product_images,
            client_ip_images=client_ip_images,
            ip_keywords=ip_keywords,
            description=description,
            reference_text=reference_text,
            reference_images=reference_images,
            _job_id=check_id
        )

        # Return an immediate response with an estimated completion time.
        estimated_time = await get_estimated_completion_time(request, check_id)
        response_data = {
            'check_id': check_id,
            'status': 'queued',
            'message': 'Analysis has been queued. Use the check_status endpoint to poll for results.'
        }
        if estimated_time is not None:
            response_data['estimated_completion_time'] = estimated_time
        
        REQUESTS_TOTAL.labels(client_name=client_name, route="/check_api", status="200_queued", server=server_identifier).inc()
        return JSONResponse(content=response_data)
    except Exception as e:
        error_message = f"Error submitting task to ARQ: {str(e)}"
        print(f"🔥 API task submission error: {error_message}")
        REQUESTS_TOTAL.labels(client_name=client_name, route="/check_api", status="200_submission_error", server=server_identifier).inc()
        return create_error_response('TASK_SUBMISSION_ERROR', "There was an error submitting your request for processing.", details=str(e), status_code=500)


@check_bp.post('/check_lite')
@langfuse.observe(name="/check_lite")
async def check_lite_route(request: Request):
    now = datetime.now()
    data = await request.json()
    
    consumer_data = get_consumer(request)
    if not consumer_data:
        return create_error_response(
            'UNAUTHORIZED',
            "Missing or invalid API key.",
            status_code=401
        )

    client_name = consumer_data["username"]
    client_id = consumer_data["id"]
    api_key = consumer_data["api_key"]

    # Generate check_id for lite version
    check_id = f"{client_id}{now.strftime('%Y%m%d%H%M%S')}"

    print(f"Generated check_id for lite check: {check_id}")
    # Create a copy of data for logging to avoid modifying the original
    log_data = copy.deepcopy(data) if data else {}

    # Identify and truncate likely base64 image strings for logging
    for key, value in log_data.items() if isinstance(log_data, dict) else []:
        if key in ['main_product_image']:
            if isinstance(value, str) and len(value) > 500:
                log_data[key] = f"[base64 image: {len(value)} chars]"

    print(f"\n\n📥📥📥 LITE API request received at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Parsed JSON data: {log_data}")

    try:
        # Create Langfuse trace
        # trace_id = langfuse.get_client().create_trace_id(seed=check_id)
        # trace_id = langfuse.get_client().get_current_trace_id()
        # print(f"Generated Langfuse trace_id: {trace_id}")

        langfuse.get_client().update_current_trace(name=f"[{server_identifier}] API Check Lite", user_id=client_name, session_id=check_id, metadata={"check_id": check_id, "client": client_name, "api_key": api_key, "server": server_identifier})
        langfuse.get_client().update_current_span(input=log_data)
        # print(f"📊 Langfuse trace_id for lite check_id {check_id}: {langfuse.get_client().get_current_trace_id()}")

        # Retrieve and validate required fields
        main_product_image = data.get('main_product_image')
        if not main_product_image:
            print(f"⛔ API validation error: Missing main_product_image.")
            return create_error_response('MISSING_MAIN_IMAGE', "A main product image is required for the analysis.", status_code=400)

        ip_keywords = data.get('ip_keywords', []) or []
        description = data.get('description', '')
        product_category = data.get('product_category', '') or ""
        language = data.get('language', 'zh')

        # Apply limits to ip_keywords
        if len(ip_keywords) > 20:
            ip_keywords = ip_keywords[:20]
            print(f"⚠️ Limited ip_keywords to first 20 items (received {len(data.get('ip_keywords', []))})")

    except KeyError as e:
        field_name = str(e).strip("'")
        error_message = f"Missing required field: {field_name}"
        print(f"⛔ API validation error: {error_message}")
        return create_error_response('MISSING_REQUIRED_FIELD', error_message, details=f"The field '{field_name}' is required.", status_code=400)
    except Exception as e:
        error_message = f"Internal server error: {str(e)}"
        print(f"🔥 API server error: {error_message}")
        return create_error_response('SERVER_ERROR', "An unexpected internal server error occurred.", details=str(e), status_code=500)

    try:
        # Data Integrity Logging
        if main_product_image:
            sender_debug_info = {
                "sender_image_len": len(main_product_image),
                "sender_image_hash": hash(main_product_image)
            }
            print(f"DEBUG: [SENDER] check_id={check_id}, {sender_debug_info}")
            langfuse.get_client().update_current_span(metadata=sender_debug_info)

        # Synchronous-like execution for lite check with ARQ
        print("LITE: Enqueuing task with ARQ and waiting for result.")
        try:
            trace_id = langfuse.get_client().get_current_trace_id()
            arq_pool = request.app.state.arq_pool
            job = await arq_pool.enqueue_job(
                'process_check_lite_task',
                check_id,
                client_id,
                client_name,
                api_key,
                product_category,
                main_product_image,
                ip_keywords,
                description,
                trace_id,
                language,
                _job_id=check_id
            )
            try:
                result = await job.result(timeout=20)  # Wait up to 20 seconds for result
                return JSONResponse(content={
                    'check_id': check_id,
                    'status': 'completed',
                    'result': result
                })
            except TimeoutError:
                REQUESTS_TOTAL.labels(client_name=client_name, route="/check_lite", status="timeout", server=server_identifier).inc()
                return JSONResponse(content={
                    'check_id': check_id,
                    'status': 'timeout',
                    'message': 'Your request timedout. Please retry.'
                })
        except Exception as e:
            error_message = f"Error during synchronous lite task execution: {str(e)}"
            print(f"🔥 {error_message}")
            REQUESTS_TOTAL.labels(client_name=client_name, route="/check_lite", status="sync_task_error", server=server_identifier).inc()
            return create_error_response('SYNC_TASK_ERROR', "An error occurred during synchronous processing.", details=str(e), status_code=500)
    except Exception as e:
        error_message = f"Error executing lite task: {str(e)}"
        print(f"🔥 API lite task execution error: {error_message}")
        REQUESTS_TOTAL.labels(client_name=client_name, route="/check_lite", status="task_execution_error", server=server_identifier).inc()
        return create_error_response('TASK_EXECUTION_ERROR', "There was an error executing your request.", details=str(e), status_code=500)

@check_bp.post('/get_check_pdf')
async def get_check_pdf(request: Request, background_tasks: BackgroundTasks):
    data = await request.json()
    check_id = data.get('check_id')
    if not check_id:
        return create_error_response('MISSING_CHECK_ID', "A check_id is required.", status_code=400)

    pdf_path = os.path.join('Check', 'PDF_ReportLab', 'output', f"{check_id}.pdf")

    def cleanup_pdf(path):
        if os.path.exists(path):
            os.remove(path)

    if os.path.exists(pdf_path):
        background_tasks.add_task(cleanup_pdf, pdf_path)
        return FileResponse(pdf_path, media_type='application/pdf', filename=f"{check_id}.pdf")
    
    arq_pool = request.app.state.arq_pool
    job = await arq_pool.enqueue_job('generate_pdf_task', data, _queue_name='pdf_queue')
    result = await job.result(timeout=20)

    if result['status'] == 'completed':
        # FastAPI's BackgroundTasks are designed to be registered before the response is sent, but they are executed after the response has been delivered to the client.
        background_tasks.add_task(cleanup_pdf, result['path'])
        return FileResponse(result['path'], media_type='application/pdf', filename=f"{check_id}.pdf")
    else:
        return create_error_response('PDF_GENERATION_FAILED', "Failed to generate the PDF report.", details=result['error'], status_code=500)
