#!/usr/bin/env python3
"""
Analysis: DB vs HDD (Design Drawings Only)

Finds records present in DB but missing on HDD, only for files ending with '-D*.png'.
Outputs CSV with db_status, hdd_status, qdrant_status for each record.
"""
import os
import sys
import csv
from tqdm import tqdm
from dotenv import load_dotenv
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from IP.Patents_Bulk.patent_db_grant import get_db_connection
from Common.uuid_utils import generate_uuid

# Load environment variables
load_dotenv()

QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = "IP_Assets"
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), 'discrepancy_results')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def get_database_patent_files():
    conn = get_db_connection()
    db_files = {}
    with conn.cursor() as cursor:
        cursor.execute("SELECT reg_no, fig_files FROM patents WHERE fig_files IS NOT NULL AND array_length(fig_files, 1) > 0;")
        for patent in cursor:
            reg_no = patent[0]
            files = patent[1]
            if files:
                for f in files:
                    file_basename = os.path.basename(f)
                    file_id = os.path.splitext(file_basename)[0]
                    uuid = generate_uuid(file_id)
                    db_files[uuid] = (reg_no, file_basename)
    conn.close()
    return db_files

def get_qdrant_patent_points():
    from qdrant_client import QdrantClient, models
    client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
    qdrant_points = {}
    offset = None
    while True:
        points, next_offset = client.scroll(
            collection_name=COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Patent"))]
            ),
            limit=50000,
            offset=offset,
            with_payload=True,
            with_vectors=False
        )
        for point in points:
            reg_no = point.payload.get("reg_no")
            qdrant_points[point.id] = reg_no
        if next_offset is None:
            break
        offset = next_offset
    return qdrant_points

def scan_hdd_patent_files():
    from Common.Constants import local_ip_folder
    base_dir = Path(local_ip_folder) / "Patents" / "USPTO_Grants" / "Extracted"
    hdd_files = {}
    for xx_dir in base_dir.iterdir():
        if not xx_dir.is_dir():
            continue
        for yy_dir in xx_dir.iterdir():
            if not yy_dir.is_dir():
                continue
            for patent_folder in yy_dir.iterdir():
                if not patent_folder.is_dir():
                    continue
                for file_path in patent_folder.iterdir():
                    if not file_path.is_file():
                        continue
                    file_basename = file_path.name
                    file_id = os.path.splitext(file_basename)[0]
                    uuid = generate_uuid(file_id)
                    hdd_files[uuid] = (str(file_path), file_basename)
    return hdd_files

def main():
    print("\n[DB vs HDD] Starting analysis for Design Drawing files...")
    db_files = get_database_patent_files()
    hdd_files = scan_hdd_patent_files()
    qdrant_points = get_qdrant_patent_points()
    db_uuids = set(db_files.keys())
    hdd_uuids = set(hdd_files.keys())
    qdrant_uuids = set(qdrant_points.keys())
    print("\n[DB vs HDD] Fetching DB patent files...")
    db_files = get_database_patent_files()
    print(f"Fetched {len(db_files):,} patent files from DB.")
    print("[DB vs HDD] Scanning HDD patent files...")
    hdd_files = scan_hdd_patent_files()
    print(f"Scanned {len(hdd_files):,} patent files on HDD.")
    print("[DB vs HDD] Fetching Qdrant patent points...")
    qdrant_points = get_qdrant_patent_points()
    print(f"Fetched {len(qdrant_points):,} patent points from Qdrant.")
    db_uuids = set(db_files.keys())
    hdd_uuids = set(hdd_files.keys())
    qdrant_uuids = set(qdrant_points.keys())
    records = []
    non_d_missing = 0
    for uuid in tqdm(db_uuids, desc="Processing DB patent files"):
        file_name = db_files[uuid][1]
        db_status = "yes"
        hdd_status = "yes" if uuid in hdd_uuids else "no"
        qdrant_status = "yes" if uuid in qdrant_uuids else "no"
        if hdd_status == "no":
            records.append({
                'uuid': uuid,
                'reg_no': db_files[uuid][0],
                'file_name': file_name,
                'db_status': db_status,
                'hdd_status': hdd_status,
                'qdrant_status': qdrant_status
            })
            # Analytics for non-D files
            parts = file_name.split('-')
            if not (len(parts) > 2 and parts[-1].startswith('D') and file_name.endswith('.png')):
                non_d_missing += 1
    print(f"\nFound {len(records):,} patent files in DB missing on HDD.")
    print("\n--- ANALYTICS ---")
    total_db = len(db_uuids)
    total_hdd = len(hdd_uuids)
    total_qdrant = len(qdrant_uuids)
    print(f"Total DB patent files: {total_db:,}")
    print(f"Total HDD patent files: {total_hdd:,}")
    print(f"Total Qdrant patent points: {total_qdrant:,}")
    print(f"Missing on HDD: {len(records):,}")
    print(f"Of which, non-D files missing: {non_d_missing:,}")
    print("------------------\n")
    out_csv = os.path.join(OUTPUT_DIR, "db_vs_hdd_missing.csv")
    with open(out_csv, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['uuid', 'reg_no', 'file_name', 'db_status', 'hdd_status', 'qdrant_status'])
        writer.writeheader()
        writer.writerows(records)
    print(f"Results saved to: {out_csv}\n")

if __name__ == "__main__":
    main()
