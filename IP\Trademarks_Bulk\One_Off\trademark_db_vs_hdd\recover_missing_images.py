#!/usr/bin/env python3
"""
Recover Missing Image Sources

This script recovers missing image sources for trademarks that have mark_feature_code in [2, 3, 5]
but image_source is None. It follows a complete pipeline to download, store, and process images
for embedding computation.

Pipeline:
1. Get records from database with missing image_source
2. Try to download images using multiple methods
3. Update database with image_source
4. Store images in proper directory structure
5. Compute embeddings and push to Qdrant
"""

import os
import asyncio
# Removed aiohttp - not needed since we're using Trademark_API.py functions directly
from tqdm.asyncio import tqdm
import tempfile
from logdata import log_message
from dotenv import load_dotenv
import sys

# Import local modules
# Path: /app/IP/Trademarks_Bulk/One_Off/ -> /app/IP/Trademarks_Bulk/
sys.path.append(os.path.join(os.path.dirname(__file__), '..','..'))
from trademark_db import get_db_connection, update_image_source
from trademark_image import download_and_save_image, get_image_subdirectory
from app_embeddings import EmbeddingQueue

# Import Common constants
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from Common.Constants import local_ip_folder, nas_ip_folder

# Import the existing functions directly from Trademark_API.py (eliminates code duplication)
# Path: /app/IP/Trademarks_Bulk/One_Off/ -> /app/IP/Trademarks/
import sys
import importlib.util
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'Trademarks'))
try:
    from IP.Trademarks.Trademark_API import get_image_from_url, save_content, get_image_from_status_zip, get_image_from_documents_zip
except ImportError:
    # Fallback import if the above doesn't work
    spec = importlib.util.spec_from_file_location("Trademark_API",
                                                  os.path.join(os.path.dirname(__file__), '..', '..', 'Trademarks', 'Trademark_API.py'))
    trademark_api = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(trademark_api)
    get_image_from_url = trademark_api.get_image_from_url
    save_content = trademark_api.save_content
    get_image_from_status_zip = trademark_api.get_image_from_status_zip
    get_image_from_documents_zip = trademark_api.get_image_from_documents_zip

# Import USPTO API
# Path: /app/IP/Trademarks_Bulk/One_Off/ -> /app/IP/Trademarks/
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'Trademarks'))
from IP.Trademarks.USPTO_TSDR_API import TSDRApi

# Load environment variables
load_dotenv()

# Configuration - Following the same pattern as get_bulk_trademarks.py
BASE_DIR = os.path.join(local_ip_folder, "Trademarks")
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")  # Same as get_bulk_trademarks.py

# Removed HEADERS - not needed since we're using Trademark_API.py functions directly
# The TSDRApi.download_from_uspto() method has proper headers built-in

# Create permanent directories (following get_bulk_trademarks.py pattern)
os.makedirs(IMAGES_DIR, exist_ok=True)


def get_missing_image_records(limit=10):
    """
    Get trademark records that have mark_feature_code in [2, 3, 5] but image_source is None.
    
    Args:
        limit (int): Number of records to fetch
        
    Returns:
        list: List of trademark records
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        sql = """
        SELECT ser_no, reg_no, mark_feature_code, applicant_name, mark_text
        FROM trademarks 
        WHERE mark_feature_code IN (2, 3, 5) 
        AND (image_source IS NULL OR image_source = '' OR image_source = 'NotFound')
        LIMIT %s
        """
        
        cursor.execute(sql, (limit,))
        records = cursor.fetchall()
        
        # Convert to list of dictionaries
        columns = ['ser_no', 'reg_no', 'mark_feature_code', 'applicant_name', 'mark_text']
        result = []
        for record in records:
            result.append(dict(zip(columns, record)))
            
        log_message(f"Found {len(result)} records with missing image_source")
        return result
        
    except Exception as e:
        log_message(f"Error fetching missing image records: {str(e)}", level='ERROR')
        return []
    finally:
        if conn:
            conn.close()


async def download_image_with_detailed_status_reporting(api_client, ser_no, reg_no,
                                                      status_folder, document_folder, image_folder, certificate_folder):
    """
    Download image using existing Trademark_API.py functions directly (lines 246-265 pattern).
    This eliminates ALL code duplication and uses the exact same functions as the main pipeline.

    Returns:
        dict: Detailed status information
    """
    result = {
        'success': False,
        'method': None,
        'http_status': None,
        'content_length': None,
        'failure_reason': None,
        'image_source': None,
        'detailed_log': []
    }

    try:
        # Format registration number for API calls
        formatted_reg_no = f"{reg_no:0>7}" if reg_no else None
        result['detailed_log'].append(f"📋 Processing ser_no: {ser_no}, reg_no: {formatted_reg_no}")

        # Use existing Trademark_API.py functions directly (following lines 246-265 pattern)
        # No manual HTTP requests needed - the Trademark_API.py functions handle everything
        result['detailed_log'].append("🔄 STEP 1: Using existing Trademark_API.py functions (no code duplication)")
        print(f"🔄 STEP 1: Using Trademark_API.py functions for {ser_no}")

        if formatted_reg_no:
            image_source = None

            # Step 2a: Try to get image using URL (using get_image_from_url from Trademark_API.py)
            result['detailed_log'].append("🔄 STEP 2a: Trying get_image_from_url")
            print(f"🔄 STEP 2a: Trying get_image_from_url for {ser_no}")

            try:
                image_downloaded, image_source = await get_image_from_url(api_client, formatted_reg_no, ser_no, image_folder)
                if image_downloaded:
                    # Copy image to final directory structure with proper ser_no naming
                    copy_success = await copy_image_to_final_structure(ser_no, formatted_reg_no, image_folder, IMAGES_DIR)
                    if copy_success:
                        result['success'] = True
                        result['image_source'] = image_source
                        result['method'] = image_source
                        result['detailed_log'].append(f"✅ Image downloaded using get_image_from_url: {image_source}")
                        print(f"✅ SUCCESS: Image downloaded using get_image_from_url for {ser_no}")
                        return result
                else:
                    result['detailed_log'].append("❌ get_image_from_url returned False - no image found")
                    print(f"⚠️ STEP 2a FAILED: get_image_from_url returned False for {ser_no}")
            except Exception as url_exception:
                # Parse the error message to extract HTTP status and reason
                error_msg = str(url_exception)
                if "500" in error_msg:
                    failure_reason = "http_500_server_error"
                    result['detailed_log'].append("❌ HTTP 500 - Internal Server Error from USPTO")
                    print(f"❌ STEP 2a FAILED: HTTP 500 Internal Server Error for {ser_no}")
                elif "404" in error_msg:
                    failure_reason = "http_404_not_found"
                    result['detailed_log'].append("❌ HTTP 404 - Image not found")
                    print(f"❌ STEP 2a FAILED: HTTP 404 Not Found for {ser_no}")
                elif "403" in error_msg:
                    failure_reason = "http_403_forbidden"
                    result['detailed_log'].append("❌ HTTP 403 - Access Forbidden")
                    print(f"❌ STEP 2a FAILED: HTTP 403 Forbidden for {ser_no}")
                elif "timeout" in error_msg.lower():
                    failure_reason = "timeout_error"
                    result['detailed_log'].append("❌ Timeout error during image download")
                    print(f"❌ STEP 2a FAILED: Timeout error for {ser_no}")
                else:
                    failure_reason = "unknown_error"
                    result['detailed_log'].append(f"❌ Unknown error: {error_msg}")
                    print(f"❌ STEP 2a FAILED: Unknown error for {ser_no}: {error_msg}")

                result['detailed_log'].append(f"🔄 STEP 2a failed ({failure_reason}) - proceeding to Step 2b")
                print(f"🔄 STEP 2a failed ({failure_reason}) - proceeding to Step 2b for {ser_no}")

            # Step 2b: If image couldn't be retrieved via URL, try status zip (exact copy of lines 247-265)
            result['detailed_log'].append("🔄 STEP 2b: Trying status zip extraction")
            print(f"🔄 STEP 2b: Trying status zip for {ser_no}")

            # Try to get image from status zip
            content_reg = await api_client.get_status_content(formatted_reg_no, id_key='rn', format='zip')

            if content_reg and len(content_reg) > 0:
                result['detailed_log'].append(f"📦 Status zip content: {len(content_reg)} bytes")
                print(f"📦 Status zip downloaded: {len(content_reg)} bytes for {ser_no}")

                status_zip_path = os.path.join(status_folder, f"{formatted_reg_no}.zip")
                if await save_content(content_reg, f"{formatted_reg_no}.zip", status_folder):
                    result['detailed_log'].append("💾 Status zip saved successfully")

                    image_downloaded = await get_image_from_status_zip(formatted_reg_no, status_zip_path, status_folder, image_folder)
                    if image_downloaded:
                        # Copy image to final directory structure with proper ser_no naming
                        copy_success = await copy_image_to_final_structure(ser_no, formatted_reg_no, image_folder, IMAGES_DIR)
                        if copy_success:
                            result['success'] = True
                            result['image_source'] = "USPTO_STATUSZIP"
                            result['method'] = "USPTO_STATUSZIP"
                            result['detailed_log'].append("✅ Image extracted from status zip")
                            print(f"✅ SUCCESS: Image extracted from status zip for {ser_no}")
                            return result
                        else:
                            result['detailed_log'].append("❌ Failed to copy image to final location")
                            print(f"❌ STEP 2b FAILED: Could not copy image to final location for {ser_no}")
                    else:
                        result['detailed_log'].append("❌ No image found in status zip")
                        print(f"❌ STEP 2b FAILED: No image found in status zip for {ser_no}")
                else:
                    result['detailed_log'].append("❌ Failed to save status zip")
                    print(f"❌ STEP 2b FAILED: Could not save status zip for {ser_no}")
            else:
                result['detailed_log'].append("⚠️ Empty or no content from status zip")
                print(f"⚠️ STEP 2b FAILED: Empty or no content from status zip for {ser_no}")

            result['detailed_log'].append("🔄 STEP 2b failed - proceeding to Step 2c")
            print(f"🔄 STEP 2b failed - proceeding to Step 2c for {ser_no}")

            # Step 2c: Try documents zip
            result['detailed_log'].append("🔄 STEP 2c: Trying documents zip extraction")
            print(f"🔄 STEP 2c: Trying documents zip for {ser_no}")

            documents_zip = await api_client.get_casedocs_bundle([formatted_reg_no], id_key='rn', format='zip')

            if documents_zip and len(documents_zip) > 0:
                result['detailed_log'].append(f"📦 Documents zip content: {len(documents_zip)} bytes")
                print(f"📦 Documents zip downloaded: {len(documents_zip)} bytes for {ser_no}")

                documents_zip_path = os.path.join(document_folder, f"{formatted_reg_no}.zip")
                if await save_content(documents_zip, f"{formatted_reg_no}.zip", document_folder):
                    result['detailed_log'].append("💾 Documents zip saved successfully")

                    image_downloaded, certificate_found = await get_image_from_documents_zip(formatted_reg_no, documents_zip_path, document_folder, image_folder, certificate_folder, ser_no)
                    if image_downloaded:
                        # Copy image to final directory structure with proper ser_no naming
                        copy_success = await copy_image_to_final_structure(ser_no, formatted_reg_no, image_folder, IMAGES_DIR)
                        if copy_success:
                            result['success'] = True
                            result['image_source'] = "USPTO_DOCZIP"
                            result['method'] = "USPTO_DOCZIP"
                            result['detailed_log'].append("✅ Image extracted from documents zip")
                            if certificate_found:
                                result['detailed_log'].append("📜 Certificate also found and saved")
                            print(f"✅ SUCCESS: Image extracted from documents zip for {ser_no}")
                            return result
                        else:
                            result['detailed_log'].append("❌ Failed to copy image to final location")
                            print(f"❌ STEP 2c FAILED: Could not copy image to final location for {ser_no}")
                    else:
                        result['detailed_log'].append("❌ No image found in documents zip")
                        print(f"❌ STEP 2c FAILED: No image found in documents zip for {ser_no}")
                else:
                    result['detailed_log'].append("❌ Failed to save documents zip")
                    print(f"❌ STEP 2c FAILED: Could not save documents zip for {ser_no}")
            else:
                result['detailed_log'].append("⚠️ Empty or no content from documents zip")
                print(f"⚠️ STEP 2c FAILED: Empty or no content from documents zip for {ser_no}")

            # All methods failed
            result['failure_reason'] = "all_methods_failed"
            result['detailed_log'].append("❌ All Trademark_API.py methods failed")
            print(f"❌ FAILED: All Trademark_API.py methods failed for {ser_no}")
        else:
            result['failure_reason'] = "no_reg_no"
            result['detailed_log'].append("❌ No registration number available")
            print(f"❌ ERROR: No reg_no available for {ser_no}")

        return result

    except Exception as e:
        result['failure_reason'] = "exception"
        result['detailed_log'].append(f"❌ Exception: {str(e)}")
        print(f"❌ ERROR: Exception during download for {ser_no}: {str(e)}")
        log_message(f"Error downloading image for {ser_no}: {str(e)}", level='ERROR')
        return result


# Removed try_fallback_methods_with_detailed_logging function
# Now using existing download_image_with_fallback from get_bulk_trademarks.py


async def copy_image_to_final_structure(ser_no, formatted_reg_no, temp_image_folder, final_images_dir):
    """
    Copy image from temp folder to final directory structure with proper ser_no naming.
    The Trademark_API.py functions save images with formatted_reg_no, but we need ser_no naming.

    Args:
        ser_no (str): Serial number for final filename
        formatted_reg_no (str): Registration number used in temp filename
        temp_image_folder (str): Temporary image folder
        final_images_dir (str): Final images directory
    """
    try:
        # Source image path (saved by Trademark_API.py functions)
        temp_image_path = os.path.join(temp_image_folder, f"{formatted_reg_no}.webp")

        if os.path.exists(temp_image_path):
            # Determine final path using ser_no
            image_sub_dir = get_image_subdirectory(ser_no)
            if image_sub_dir:
                final_image_path = os.path.join(final_images_dir, image_sub_dir, f"{ser_no}.webp")
                os.makedirs(os.path.dirname(final_image_path), exist_ok=True)

                # Copy the image with proper naming
                import shutil
                shutil.copy2(temp_image_path, final_image_path)
                print(f"📁 Image copied to final location: {final_image_path}")
                return True
            else:
                print(f"⚠️ Could not determine final directory for {ser_no}")
                return False
        else:
            print(f"⚠️ No temp image found at {temp_image_path}")
            return False

    except Exception as e:
        print(f"❌ Error copying image for {ser_no}: {str(e)}")
        log_message(f"Error copying image for {ser_no}: {str(e)}", level='ERROR')
        return False


async def process_missing_images(limit=10):
    """
    Main function to process missing images using Trademark_API.py functions directly.
    No manual HTTP requests or session management needed.

    Args:
        limit (int): Number of records to process
    """
    # Step 1: Get records from database
    print(f"🔍 Fetching {limit} records with missing image_source...")
    records = get_missing_image_records(limit)

    if not records:
        print("No records found with missing image_source")
        return

    # Initialize components (no semaphore needed - Trademark_API.py functions handle concurrency)
    api_client = TSDRApi()

    # Start API client session (following USPTO_TSDR_API.py pattern)
    await api_client.start_session()

    # Initialize embedding queue (following get_bulk_trademarks.py pattern)
    embedding_queue = EmbeddingQueue(max_concurrent=2)
    await embedding_queue.start()

    successful_downloads = 0
    failed_downloads = 0
    successful_embeddings = 0
    failed_embeddings = 0
    skipped_embeddings = 0

    # Create temporary directories following Trademark_API.py pattern
    base_folder = os.path.join(local_ip_folder, "Trademarks")
    status_folder = os.path.join(base_folder, "Status")
    document_folder = os.path.join(base_folder, "Documents")
    image_folder = os.path.join(base_folder, "Images")
    certificate_folder = os.path.join(base_folder, "Certificates")

    # Create directories (following Trademark_API.py pattern)
    for folder in [base_folder, status_folder, document_folder, image_folder, certificate_folder]:
        os.makedirs(folder, exist_ok=True)

    try:
        # No session needed - Trademark_API.py functions use TSDRApi client internally
        print(f"\n🚀 Starting image recovery for {len(records)} records...")

        # Process each record with detailed logging
        for i, record in enumerate(records, 1):
                ser_no = record['ser_no']
                reg_no = record['reg_no']
                mark_feature_code = record.get('mark_feature_code')

                print(f"\n{'='*80}")
                print(f"🔄 PROCESSING RECORD {i}/{len(records)}: {ser_no}")
                print(f"📋 Registration Number: {reg_no}")
                print(f"🏷️  Mark Feature Code: {mark_feature_code}")
                print(f"{'='*80}")

                # Step 2: Try to download image with detailed status (using Trademark_API.py functions only)
                result = await download_image_with_detailed_status_reporting(
                    api_client, ser_no, reg_no,
                    status_folder, document_folder, image_folder, certificate_folder
                )

                # Print detailed log
                print(f"\n📋 DETAILED PROCESSING LOG for {ser_no}:")
                for log_entry in result['detailed_log']:
                    print(f"   {log_entry}")

                # Print HTTP response details
                if result['http_status']:
                    print(f"\n📡 HTTP RESPONSE ANALYSIS:")
                    print(f"   Status Code: {result['http_status']}")
                    print(f"   Content Length: {result['content_length']} bytes")
                    if result['http_status'] == 200 and result['content_length'] == 0:
                        print(f"   ⚠️  SEGREGATION: HTTP 200 with empty content (b'')")
                    elif result['http_status'] == 404:
                        print(f"   ❌ SEGREGATION: HTTP 404 - Image not found")
                    elif result['http_status'] == 200 and result['content_length'] > 0:
                        print(f"   ✅ SEGREGATION: HTTP 200 with valid content")

                if result['success'] and result['image_source']:
                    # Step 3: Update database
                    print(f"\n📝 STEP 3: Updating database for {ser_no}")
                    print(f"   Image Source: {result['image_source']}")
                    print(f"   Method Used: {result['method']}")

                    update_result = update_image_source(ser_no, result['image_source'])

                    if update_result:
                        print(f"   ✅ Database updated successfully")
                        successful_downloads += 1

                        # Step 4: Process embedding immediately for this record
                        print(f"\n🧠 STEP 4: Processing embedding for this record")
                        if mark_feature_code in [2, 3, 5]:
                            image_sub_dir = get_image_subdirectory(ser_no)
                            if image_sub_dir:
                                image_path = os.path.join(IMAGES_DIR, image_sub_dir, f"{ser_no}.webp")
                                print(f"   📁 Image path: {image_path}")

                                # Process embedding immediately for this single record
                                try:
                                    print(f"   🔄 Enqueuing image for embedding processing...")
                                    images_enqueued, images_skipped = await embedding_queue.batch_enqueue_trademarks([(ser_no, image_path)])

                                    if images_enqueued > 0:
                                        print(f"   📥 Successfully enqueued: {images_enqueued}")
                                        print(f"   ⏳ Waiting for embedding processing to complete...")

                                        # Wait for this specific embedding to complete
                                        await embedding_queue.drain()

                                        print(f"   ✅ Embedding processing completed!")
                                        print(f"   📤 Embedding computed and pushed to Qdrant")
                                        successful_embeddings += 1
                                    elif images_skipped > 0:
                                        print(f"   ⏭️  Embedding skipped (already exists): {images_skipped}")
                                        skipped_embeddings += 1
                                    else:
                                        print(f"   ⚠️  No embeddings processed")
                                        failed_embeddings += 1

                                except Exception as e:
                                    print(f"   ❌ Error during embedding processing: {str(e)}")
                                    log_message(f"Error processing embedding for {ser_no}: {str(e)}", level='ERROR')
                                    failed_embeddings += 1
                            else:
                                print(f"   ⚠️  Could not determine image subdirectory")
                                failed_embeddings += 1
                        else:
                            print(f"   ℹ️  Skipping embedding (mark_feature_code: {mark_feature_code})")
                            skipped_embeddings += 1
                    else:
                        print(f"   ❌ Failed to update database")
                        failed_downloads += 1
                else:
                    failed_downloads += 1
                    print(f"\n❌ FINAL RESULT: Failed to recover image for {ser_no}")
                    print(f"   Failure Reason: {result['failure_reason']}")
                    print(f"   HTTP Status: {result['http_status']}")
                    print(f"   Content Length: {result['content_length']}")

                print(f"\n📊 RECORD {i} SUMMARY:")
                print(f"   Image Download: {'✅ YES' if result['success'] else '❌ NO'}")
                print(f"   Method: {result['method'] or 'None'}")
                print(f"   Database Updated: {'✅ YES' if result['success'] and update_result else '❌ NO'}")
                if result['success'] and mark_feature_code in [2, 3, 5]:
                    print(f"   Embedding Processed: ✅ YES")
                elif mark_feature_code not in [2, 3, 5]:
                    print(f"   Embedding Processed: ℹ️  SKIPPED (mark_feature_code: {mark_feature_code})")
                else:
                    print(f"   Embedding Processed: ❌ NO (image download failed)")

                # Add a delay between records to reduce API pressure and allow processing time
                # print(f"   ⏳ Waiting 2 seconds before next record...")
                # await asyncio.sleep(2.0)

        # All embedding processing is now done individually per record
        print(f"\n{'='*80}")
        print(f"🧠 EMBEDDING PROCESSING SUMMARY")
        print(f"{'='*80}")
        print(f"   ✅ Successful embeddings: {successful_embeddings}")
        print(f"   ❌ Failed embeddings: {failed_embeddings}")
        print(f"   ⏭️  Skipped embeddings: {skipped_embeddings}")
        print(f"   📊 Total embedding operations: {successful_embeddings + failed_embeddings + skipped_embeddings}")

    finally:
        # Cleanup (following USPTO_TSDR_API.py pattern)
        if api_client:
            await api_client.close_session()
        if embedding_queue:
            await embedding_queue.stop()

        # Print comprehensive summary
        print(f"\n{'='*80}")
        print(f"🎉 FINAL RECOVERY SUMMARY")
        print(f"{'='*80}")
        print(f"📊 PROCESSING STATISTICS:")
        print(f"   Total records processed: {len(records)}")
        print(f"   ✅ Successful recoveries: {successful_downloads}")
        print(f"   ❌ Failed recoveries: {failed_downloads}")
        print(f"   📈 Success rate: {(successful_downloads/len(records)*100):.1f}%")

        print(f"\n🔍 RECOVERY METHOD BREAKDOWN:")
        # Count methods used (this would need to be tracked during processing)
        print(f"   📡 USPTO_URL: Direct download from image URL")
        print(f"   📦 USPTO_STATUSZIP: Extracted from status ZIP files")
        print(f"   📄 USPTO_DOCZIP: Extracted from document ZIP files")

        print(f"\n🧠 EMBEDDING PROCESSING:")
        print(f"   ✅ Successful embeddings: {successful_embeddings}")
        print(f"   ❌ Failed embeddings: {failed_embeddings}")
        print(f"   ⏭️  Skipped embeddings: {skipped_embeddings}")
        if successful_embeddings > 0:
            print(f"   🎯 All successful embeddings computed and stored in Qdrant")
        else:
            print(f"   ℹ️  No embeddings were successfully processed")

        print(f"\n📋 NEXT STEPS:")
        if failed_downloads > 0:
            print(f"   🔄 Consider running the script again for failed records")
            print(f"   🔍 Check logs for specific failure reasons")
        if failed_embeddings > 0:
            print(f"   🧠 Consider re-running for failed embedding processing")
        print(f"   📊 Run statistics to see updated image_source distribution")
        if successful_embeddings > 0:
            print(f"   🧪 Test embedding search functionality with recovered images")
        print(f"   ⏱️  Sequential processing reduces API rate limiting issues")


def get_statistics():
    """
    Get statistics about missing images in the database.
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Total trademarks with mark_feature_code in [2, 3, 5]
        cursor.execute("""
            SELECT COUNT(*) FROM trademarks
            WHERE mark_feature_code IN (2, 3, 5)
        """)
        total_eligible = cursor.fetchone()[0]

        # Missing image_source
        cursor.execute("""
            SELECT COUNT(*) FROM trademarks
            WHERE mark_feature_code IN (2, 3, 5)
            AND (image_source IS NULL OR image_source = '' OR image_source = 'NotFound')
        """)
        missing_images = cursor.fetchone()[0]

        # By image_source type
        cursor.execute("""
            SELECT image_source, COUNT(*) FROM trademarks
            WHERE mark_feature_code IN (2, 3, 5)
            AND image_source IS NOT NULL AND image_source != ''
            GROUP BY image_source
        """)
        by_source = cursor.fetchall()

        print(f"\n📊 DATABASE STATISTICS:")
        print(f"   Total eligible trademarks (mark_feature_code 2,3,5): {total_eligible:,}")
        print(f"   Missing image_source: {missing_images:,}")
        print(f"   Recovery potential: {(missing_images/total_eligible*100):.1f}%")
        print(f"\n   Current image sources:")
        for source, count in by_source:
            print(f"     {source}: {count:,}")

        # Calculate recovery percentage
        recovery_percentage = (missing_images/total_eligible*100) if total_eligible > 0 else 0.0

        return {
            'total_eligible': total_eligible,
            'missing_images': missing_images,
            'recovery_percentage': recovery_percentage,
            'by_source': dict(by_source)
        }

    except Exception as e:
        log_message(f"Error getting statistics: {str(e)}", level='ERROR')
        return None
    finally:
        if conn:
            conn.close()


async def run_recovery_batch(batch_size=10):
    """
    Run recovery process in batches using Trademark_API.py functions directly.

    Args:
        batch_size (int): Number of records to process per batch
    """
    print("🚀 Starting Missing Image Recovery Process")
    print("=" * 50)

    # Show statistics first
    stats = get_statistics()
    if not stats or stats['missing_images'] == 0:
        print("No missing images found!")
        return

    total_to_process = min(batch_size, stats['missing_images'])
    print(f"\nProcessing {total_to_process} records...")

    await process_missing_images(limit=total_to_process)

    # Show updated statistics
    print("\n" + "=" * 50)
    print("📊 UPDATED STATISTICS:")
    get_statistics()


if __name__ == "__main__":
    # Configuration parameters (instead of argparser)
    BATCH_SIZE = 22          # Number of records to process
    STATS_ONLY = True       # Set to True to show statistics only
    # Removed MAX_CONCURRENT - not needed since Trademark_API.py functions handle concurrency

    print(f"🚀 Starting Missing Image Recovery Process")
    print(f"📁 Current working directory: {os.getcwd()}")
    print(f"📁 Script location: {os.path.dirname(__file__)}")
    print("=" * 50)

    if STATS_ONLY:
        # Show database statistics without processing any records
        get_statistics()
    else:
        # Run the complete recovery process using Trademark_API.py functions
        asyncio.run(run_recovery_batch(batch_size=BATCH_SIZE))
