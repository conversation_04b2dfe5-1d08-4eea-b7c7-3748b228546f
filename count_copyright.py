
import os
from qdrant_client import QdrantClient, models
from collections import Counter

# It seems you're using environment variables for Qdrant configuration.
# Make sure QDRANT_URL and QDRANT_API_KEY are set in your environment.
qdrant_client = QdrantClient(
    url=os.environ.get("QDRANT_URL"), 
    api_key=os.environ.get("QDRANT_API_KEY")
)

collection_name = "IP_Assets"

# Define the filter for 'ip_type' = 'Copyright'
copyright_filter = models.Filter(
    must=[
        models.FieldCondition(
            key="ip_type",
            match=models.MatchValue(value="Copyright"),
        )
    ]
)

count = qdrant_client.count(
    collection_name="IP_Assets",
    count_filter=copyright_filter
).count

print(f"Total records according to count(): {count}")

reg_nos = []
next_page_offset = None
total_fetched = 0
non_copyright_records = 0

print("\nFetching records...")
while True:
    records, next_page_offset = qdrant_client.scroll(
        collection_name=collection_name,
        scroll_filter=copyright_filter,
        limit=5000,
        offset=next_page_offset,
        with_payload=True,  # Fetch payloads
        with_vectors=False,
    )
    
    batch_size = len(records)
    total_fetched += batch_size
    print(f"Fetched batch of {batch_size} records. Total fetched so far: {total_fetched}")
    
    for record in records:
        # Validate that each record is actually a Copyright
            
        reg_no = record.payload.get("reg_no")
        if reg_no:
            reg_nos.append(reg_no)
            
    if not records or next_page_offset is None:
        break

print("\n--- Analysis ---")
print(f"Initial count from count(): {count}")
print(f"Total records fetched: {total_fetched}")
print(f"Non-Copyright records found: {non_copyright_records}")
print(f"Copyright records with reg_no: {len(reg_nos)}")

# Count unique reg_no
unique_reg_nos = set(reg_nos)
print(f"Total unique reg_no: {len(unique_reg_nos)}")

# Find duplicates
reg_no_counts = Counter(reg_nos)
duplicates = {reg_no: count for reg_no, count in reg_no_counts.items() if count > 1}

if duplicates:
    print(f"\nFound {len(duplicates)} duplicate reg_nos.")
    print("Examples of reg_nos appearing multiple times:")
    
    count = 0
    for reg_no, num in duplicates.items():
        print(f"  - '{reg_no}' appears {num} times")
        count += 1
        if count >= 5:
            break
else:
    print("\nNo duplicate reg_nos found.")
