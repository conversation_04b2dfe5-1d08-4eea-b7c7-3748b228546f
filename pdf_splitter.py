import os
from pypdf import <PERSON>d<PERSON><PERSON><PERSON><PERSON>, PdfWriter

def split_pdf(input_pdf_path, chunk_size=50):
    """
    Splits a PDF file into multiple smaller PDF files, each containing a specified number of pages.

    Args:
        input_pdf_path (str): The path to the input PDF file.
        chunk_size (int): The number of pages per output chunk (default is 50).

    Returns:
        list: A list of paths to the created PDF chunks.
        None: If the input file doesn't exist or is not a PDF.
    """
    if not os.path.exists(input_pdf_path) or not input_pdf_path.lower().endswith('.pdf'):
        print(f"Error: Input file not found or is not a PDF: {input_pdf_path}")
        return None

    output_files = []
    try:
        reader = PdfReader(input_pdf_path)
        total_pages = len(reader.pages)
        base_name = os.path.splitext(os.path.basename(input_pdf_path))[0]
        output_dir = os.path.dirname(input_pdf_path) or '.' # Use input dir or current dir

        for i in range(0, total_pages, chunk_size):
            writer = PdfWriter()
            start_page = i
            end_page = min(i + chunk_size, total_pages)
            
            for page_num in range(start_page, end_page):
                writer.add_page(reader.pages[page_num])

            chunk_index = (i // chunk_size) + 1
            output_filename = f"{base_name}_{chunk_index}.pdf"
            output_path = os.path.join(output_dir, output_filename)
            
            with open(output_path, "wb") as output_pdf:
                writer.write(output_pdf)
            output_files.append(output_path)
            print(f"Created chunk: {output_path}")

        return output_files

    except Exception as e:
        print(f"An error occurred: {e}")
        return None

# Example Usage (you can uncomment and modify this part to test)
# if __name__ == "__main__":
#     input_file = "path/to/your/large_document.pdf" # <-- Replace with your PDF file path
#     if os.path.exists(input_file):
#         created_files = split_pdf(input_file, 50)
#         if created_files:
#             print("\nPDF splitting completed successfully.")
#             print("Output files:", created_files)
#         else:
#             print("\nPDF splitting failed.")
#     else:
#         print(f"Error: Example input file not found at {input_file}")
#         print("Please replace 'path/to/your/large_document.pdf' with the actual path to your PDF.")