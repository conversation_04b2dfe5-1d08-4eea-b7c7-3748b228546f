// Statistics Page JavaScript

class StatisticsManager {
    constructor() {
        this.currentData = {};
        this.charts = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadStatistics();
    }

    setupEventListeners() {
        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadStatistics();
        });

        // Collect button
        document.getElementById('collect-btn').addEventListener('click', () => {
            this.collectStatistics();
        });

        // Days filter
        document.getElementById('days-filter').addEventListener('change', () => {
            this.loadStatistics();
        });
    }

    async loadStatistics() {
        try {
            this.showLoading(true);
            
            // Get latest statistics
            const response = await fetch('/api/statistics/latest');
            const result = await response.json();
            
            if (result.success) {
                this.currentData = result.data;
                this.renderStatistics();
                this.loadTrendCharts();
            } else {
                this.showError('Failed to load statistics: ' + result.error);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
            this.showError('Failed to load statistics: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async collectStatistics() {
        try {
            const collectBtn = document.getElementById('collect-btn');
            const originalText = collectBtn.textContent;
            collectBtn.textContent = 'Collecting...';
            collectBtn.disabled = true;

            const response = await fetch('/api/statistics/collect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(result.message);
                // Reload statistics after collection
                setTimeout(() => this.loadStatistics(), 1000);
            } else {
                this.showError('Failed to collect statistics: ' + result.error);
            }
        } catch (error) {
            console.error('Error collecting statistics:', error);
            this.showError('Failed to collect statistics: ' + error.message);
        } finally {
            const collectBtn = document.getElementById('collect-btn');
            collectBtn.textContent = 'Collect New Statistics';
            collectBtn.disabled = false;
        }
    }

    renderStatistics() {
        const grid = document.getElementById('statistics-grid');
        grid.innerHTML = '';

        // Define metric display names and descriptions
        const metricConfig = {
            'cases_without_cause': {
                title: 'Cases Without Cause',
                description: 'Cases that have not been scraped yet'
            },
            'cases_without_images': {
                title: 'Cases Without Images',
                description: 'Cases with no IP images found'
            },
            'cases_with_corrupted_image_data': {
                title: 'Cases with Corrupted Image Data',
                description: 'Cases where the images field (JSON blob) is corrupted'
            },
            'duplicate_cases': {
                title: 'Duplicate Cases',
                description: 'Cases with same docket and date filed'
            },
            'cases_ai_summary_no_translation': {
                title: 'Cases AI Summary No Translation',
                description: 'Cases without Chinese AI summary translation'
            },
            'cases_without_steps': {
                title: 'Cases Without Steps',
                description: 'Cases without any procedural steps'
            },
            'cases_court_not_in_mapping': {
                title: 'Cases Court Not in Mapping',
                description: 'Cases with courts not in mapping'
            },
            'duplicate_plaintiffs': {
                title: 'Duplicate Plaintiffs',
                description: 'Plaintiffs with duplicate names'
            },
            'plaintiffs_without_cases': {
                title: 'Plaintiffs Without Cases',
                description: 'Plaintiffs not associated with any cases'
            },
            'plaintiffs_without_overview': {
                title: 'Plaintiffs Without Overview',
                description: 'Plaintiffs without overview information'
            },
            'plaintiffs_without_chinese_overview': {
                title: 'Plaintiffs Without Chinese Overview',
                description: 'Plaintiffs without Chinese overview translation'
            },
            'steps_without_case': {
                title: 'Steps Without Case',
                description: 'Steps not associated with any case'
            },
            'duplicate_steps': {
                title: 'Duplicate Steps',
                description: 'Steps with same case ID and step number'
            },
            'steps_without_translation': {
                title: 'Steps Without Translation',
                description: 'Steps without Chinese translation'
            },
            'steps_with_null_translation': {
                title: 'Steps with NULL Translation',
                description: 'Steps with [NULL] translation'
            },
            'pictures_in_cos_not_in_dataframe': {
                title: 'Pictures in COS Not in DataFrame',
                description: 'Pictures in COS that are not associated with any case'
            },
            'cases_with_pictures_missing_from_cos': {
                title: 'Cases with Pictures Missing from COS',
                description: 'Cases with pictures missing from COS storage'
            }
        };

        // Sort metrics by value (highest first) for better visibility
        const sortedMetrics = Object.entries(this.currentData)
            .sort(([,a], [,b]) => b.value - a.value);

        sortedMetrics.forEach(([metricName, data]) => {
            const config = metricConfig[metricName] || {
                title: metricName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                description: 'No description available'
            };

            const card = this.createStatisticCard(metricName, data, config);
            grid.appendChild(card);
        });
    }

    createStatisticCard(metricName, data, config) {
        const card = document.createElement('div');
        card.className = `statistic-card metric-${metricName}`;
        
        // Determine value styling based on metric value
        let valueClass = '';
        if (data.value > 100) valueClass = 'error';
        else if (data.value > 10) valueClass = 'warning';

        card.innerHTML = `
            <div class="statistic-header">
                <h3 class="statistic-title">${config.title}</h3>
                <div class="statistic-value ${valueClass}">${data.value.toLocaleString()}</div>
            </div>
            <div class="statistic-description">${config.description}</div>
            <div class="statistic-chart" id="chart-${metricName}">
                <div class="chart-loading">Loading trend...</div>
            </div>
            <div class="statistic-footer">
                <div class="statistic-timestamp">
                    ${data.timestamp ? new Date(data.timestamp).toLocaleString() : 'No data'}
                </div>
                <button class="details-btn" onclick="statisticsManager.showDetails('${metricName}')">
                    View Details
                </button>
            </div>
        `;

        return card;
    }

    async loadTrendCharts() {
        const daysBack = document.getElementById('days-filter').value;
        
        for (const metricName of Object.keys(this.currentData)) {
            try {
                const response = await fetch(`/api/statistics/trend/${metricName}?days_back=${daysBack}`);
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    this.renderChart(metricName, result.data);
                } else {
                    this.renderEmptyChart(metricName);
                }
            } catch (error) {
                console.error(`Error loading trend for ${metricName}:`, error);
                this.renderEmptyChart(metricName);
            }
        }
    }

    renderChart(metricName, data) {
        const container = document.getElementById(`chart-${metricName}`);
        if (!container) return;

        container.innerHTML = '';

        const margin = { top: 10, right: 10, bottom: 20, left: 30 };
        const width = container.clientWidth - margin.left - margin.right;
        const height = 120 - margin.top - margin.bottom;

        const svg = d3.select(container)
            .append('svg')
            .attr('width', width + margin.left + margin.right)
            .attr('height', height + margin.top + margin.bottom);

        const g = svg.append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);

        // Parse dates and prepare data
        const parseTime = d3.timeParse('%Y-%m-%dT%H:%M:%S.%fZ');
        const parsedData = data.map(d => ({
            date: new Date(d.timestamp),
            value: d.value
        })).sort((a, b) => a.date - b.date);

        // Scales
        const xScale = d3.scaleTime()
            .domain(d3.extent(parsedData, d => d.date))
            .range([0, width]);

        const yScale = d3.scaleLinear()
            .domain([0, d3.max(parsedData, d => d.value)])
            .range([height, 0]);

        // Line generator
        const line = d3.line()
            .x(d => xScale(d.date))
            .y(d => yScale(d.value))
            .curve(d3.curveMonotoneX);

        // Area generator
        const area = d3.area()
            .x(d => xScale(d.date))
            .y0(height)
            .y1(d => yScale(d.value))
            .curve(d3.curveMonotoneX);

        // Add area
        g.append('path')
            .datum(parsedData)
            .attr('class', 'chart-area')
            .attr('d', area);

        // Add line
        g.append('path')
            .datum(parsedData)
            .attr('class', 'chart-line')
            .attr('d', line);

        // Add dots
        g.selectAll('.chart-dot')
            .data(parsedData)
            .enter().append('circle')
            .attr('class', 'chart-dot')
            .attr('cx', d => xScale(d.date))
            .attr('cy', d => yScale(d.value))
            .append('title')
            .text(d => `${d.date.toLocaleDateString()}: ${d.value}`);

        // Add axes
        g.append('g')
            .attr('class', 'chart-axis')
            .attr('transform', `translate(0,${height})`)
            .call(d3.axisBottom(xScale).ticks(3).tickFormat(d3.timeFormat('%m/%d')));

        g.append('g')
            .attr('class', 'chart-axis')
            .call(d3.axisLeft(yScale).ticks(3));
    }

    renderEmptyChart(metricName) {
        const container = document.getElementById(`chart-${metricName}`);
        if (!container) return;
        
        container.innerHTML = '<div class="chart-loading">No trend data available</div>';
    }

    async showDetails(metricName) {
        try {
            document.getElementById('details-loading').style.display = 'block';
            document.getElementById('details-content').innerHTML = '';
            document.getElementById('details-title').textContent = 
                metricName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + ' Details';
            document.getElementById('details-overlay').classList.add('show');

            const response = await fetch(`/api/statistics/details/${metricName}`);
            const result = await response.json();

            if (result.success) {
                this.renderDetails(result.data);
            } else {
                this.renderDetailsError(result.error);
            }
        } catch (error) {
            console.error('Error loading details:', error);
            this.renderDetailsError(error.message);
        } finally {
            document.getElementById('details-loading').style.display = 'none';
        }
    }

    renderDetails(data) {
        const content = document.getElementById('details-content');
        
        if (data.items && data.items.length > 0) {
            let html = `
                <div class="details-summary">
                    <h4>Total Items: ${data.total}</h4>
                    <p>Showing ${data.items.length} items</p>
                </div>
                <table class="details-table">
                    <thead>
                        <tr>
            `;

            // Generate table headers based on first item
            const firstItem = data.items[0];
            Object.keys(firstItem).forEach(key => {
                if (key !== 'link') {
                    html += `<th>${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</th>`;
                }
            });
            html += '<th>Action</th></tr></thead><tbody>';

            // Generate table rows
            data.items.forEach(item => {
                html += '<tr>';
                Object.entries(item).forEach(([key, value]) => {
                    if (key !== 'link') {
                        html += `<td>${value || 'N/A'}</td>`;
                    }
                });
                html += `<td><a href="${item.link}" target="_blank">View</a></td>`;
                html += '</tr>';
            });

            html += '</tbody></table>';
            content.innerHTML = html;
        } else {
            content.innerHTML = `
                <div class="empty-state">
                    <h3>No Details Available</h3>
                    <p>${data.message || 'No items found for this metric.'}</p>
                </div>
            `;
        }
    }

    renderDetailsError(error) {
        const content = document.getElementById('details-content');
        content.innerHTML = `
            <div class="error-message">
                <strong>Error loading details:</strong> ${error}
            </div>
        `;
    }

    showLoading(show) {
        document.getElementById('loading').style.display = show ? 'block' : 'none';
    }

    showError(message) {
        // You can implement a toast notification system here
        console.error(message);
        alert('Error: ' + message);
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        console.log(message);
        alert('Success: ' + message);
    }
}

// Global functions
function closeDetailsModal() {
    document.getElementById('details-overlay').classList.remove('show');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.statisticsManager = new StatisticsManager();
});

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    if (e.target.id === 'details-overlay') {
        closeDetailsModal();
    }
});
