import os
import sys
import logging
from datetime import datetime
from tqdm import tqdm

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from IP.Trademarks_Bulk.trademark_db import get_db_connection, get_table_from_db
from DatabaseManagement.ImportExport import get_table_from_GZ

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), '..', 'logs', 'fix_copyrights_plaintiff_id.log')),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def fix_copyrights_plaintiff_id():
    """Fix missing plaintiff_id in copyrights table by linking through cn_websites_files and cn_websites"""

    logger.info("Starting fix for missing plaintiff_id in copyrights table")

    # Get database connection
    conn = get_db_connection()
    if conn is None:
        logger.error("Could not establish database connection")
        return

    try:
        # Load case data
        case_df = get_table_from_GZ("tb_case", force_refresh=True)
        logger.info(f"Loaded {len(case_df)} cases from tb_case")

        # Get all copyright records with null plaintiff_id
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, registration_number
                FROM copyrights
                WHERE plaintiff_id IS NULL AND registration_number IS NOT NULL
            """)
            copyright_records = cur.fetchall()

        logger.info(f"Found {len(copyright_records)} copyright records with missing plaintiff_id")

        updated_count = 0
        for copyright_id, reg_number in tqdm(copyright_records, desc="Processing copyrights", unit="record"):
            try:
                # Look for matching file in cn_websites_files
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT cn_websites_id
                        FROM cn_websites_files
                        WHERE reg_no = %s AND type = 'single'
                        LIMIT 1
                    """, (reg_number,))

                    file_record = cur.fetchone()
                    if not file_record:
                        logger.debug(f"No matching file found for registration {reg_number}")
                        continue

                    cn_websites_id = file_record[0]

                    # Get case_id from cn_websites
                    cur.execute("""
                        SELECT case_id
                        FROM cn_websites
                        WHERE id = %s AND case_id IS NOT NULL
                    """, (cn_websites_id,))

                    website_record = cur.fetchone()
                    if not website_record:
                        logger.debug(f"No case_id found in cn_websites for id {cn_websites_id}")
                        continue

                    case_id = website_record[0]

                    # Get plaintiff_id from case_df
                    case_row = case_df[case_df['id'] == case_id]
                    if case_row.empty:
                        logger.debug(f"No matching case found for case_id {case_id}")
                        continue

                    plaintiff_id = case_row.iloc[0].get('plaintiff_id')
                    if plaintiff_id is None:
                        logger.debug(f"No plaintiff_id found for case_id {case_id}")
                        continue

                    # Update the copyright record
                    cur.execute("""
                        UPDATE copyrights
                        SET plaintiff_id = %s
                        WHERE id = %s
                    """, (int(float(plaintiff_id)), copyright_id))

                    updated_count += 1
                    logger.info(f"Updated copyright {reg_number} (ID: {copyright_id}) with plaintiff_id {plaintiff_id}")

                    # Commit after each update to avoid large transactions
                    conn.commit()

            except Exception as e:
                logger.error(f"Error processing copyright {reg_number}: {e}")
                # Rollback to clear any aborted transaction
                conn.rollback()
                continue

        # Print final summary
        with conn.cursor() as cur:
            cur.execute("SELECT COUNT(*) FROM copyrights WHERE plaintiff_id IS NOT NULL")
            total_with_plaintiff_id = cur.fetchone()[0]

            cur.execute("SELECT COUNT(*) FROM copyrights WHERE plaintiff_id IS NULL")
            remaining_null_count = cur.fetchone()[0]

        logger.info("=" * 50)
        logger.info("FINAL SUMMARY")
        logger.info("=" * 50)
        logger.info(f"Total copyright records with plaintiff_id: {total_with_plaintiff_id}")
        logger.info(f"Remaining copyright records with NULL plaintiff_id: {remaining_null_count}")
        logger.info(f"Copyright records updated in this run: {updated_count}")
        logger.info(f"Success rate: {(updated_count / len(copyright_records) * 100) if copyright_records else 0:.1f}%")
        logger.info("=" * 50)

    except Exception as e:
        logger.error(f"Error during fix operation: {e}")
        conn.rollback()

    finally:
        conn.close()
        logger.info("Database connection closed")

if __name__ == "__main__":
    logger.info("Starting one-off script to fix missing plaintiff_id in copyrights table")
    fix_copyrights_plaintiff_id()
    logger.info("Script completed")