import os
from FileManagement.Tencent_COS import async_upload_file_with_retry
from langfuse import observe
import asyncio
import time
import shutil
from Common import Constants
from CheckLite.RAG.qdrant_search import find_similar_assets_qdrant
from Check.Create_Report import create_product_url
import langfuse

@observe(capture_input=False, capture_output=False)
async def check_copyrights(client, bucket, temp_dir, client_id, check_id, local_product_images, cases_df, plaintiff_df, precomputed_embeddings_map=None, language='zh'):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "check_id": check_id,
        "local_product_images": local_product_images,
        "language": language
    })
    start_time = time.time()
    # Use Qdrant for vector search
    query_image_paths=local_product_images
    top_n = 3
    sim_results = await find_similar_assets_qdrant(
        query_image_paths=query_image_paths,
        check_id=check_id,
        client_id=client_id,
        ip_type="Copyright",
        temp_dir=temp_dir,
        cases_df=cases_df,
        plaintiff_df=plaintiff_df,
        plaintiff_id=None,
        top_n=top_n, # 3 per pictures => could be 50!
        similarity_threshold=0.7,
        precomputed_embeddings_map=precomputed_embeddings_map
    )

    print(f"\033[33mCopyright: Copyright RAG done for {len(query_image_paths)} pictures in {time.time() - start_time:.1f} seconds\033[0m")

    if sim_results:
        print(f"\033[33m ✅ Copyright: RAG returned {len(sim_results)} potential copyright matches in {time.time() - start_time:.1f} seconds\033[0m")
        langfuse.get_client().update_current_span(output={"results": sim_results})
        return sim_results
    else:
        print(f"\03d[33m ✅ Copyright: RAG returned no potential copyright matches in {time.time() - start_time:.1f} seconds\033[0m")
        langfuse.get_client().update_current_span(output={"results": []})
        return []


if __name__ == '__main__':
    # This block allows the script to be run directly for testing purposes.
    import tempfile
    import sys

    # Ensure the project root is in the Python path to resolve imports correctly.
    sys.path.append(os.getcwd())

    from DatabaseManagement.ImportExport import get_table_from_GZ
    from Check.gRPC_Client import get_siglip_embeddings_grpc

    async def main():
        """Main function to run a standalone copyright check on a single local image."""
        print("--- Running Standalone Copyright Check ---")
        
        # HARDCODED: Provide a path to a local image for testing.
        test_image_path = r"D:\Win10User\Downloads\ChuanshenGuida\product_1_15949c7b-3705-4eec-90d7-9a8f54818c3e_part_0.jpg"
        test_image_path = r"D:\Win10User\Downloads\Screenshot 2025-07-20 101411.png"
        test_image_path = r"D:\Win10User\Downloads\Image_20250810203836_65.jpg"
        
        local_product_images = [test_image_path]
        
        # Create a persistent directory for output, named after the test file.
        output_dir = os.path.join(os.path.dirname(test_image_path), os.path.splitext(os.path.basename(test_image_path))[0] + "_output")
        os.makedirs(output_dir, exist_ok=True)
        print(f"Using output directory: {output_dir}")

        if not os.path.exists(test_image_path):
            print(f"Test image not found at {test_image_path}. Creating a dummy file.")
            os.makedirs(os.path.dirname(test_image_path), exist_ok=True)
            with open(test_image_path, 'w') as f:
                f.write("dummy") # Create a dummy file

        cases_df = get_table_from_GZ("tb_case", force_refresh=False)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)

        embedding = await get_siglip_embeddings_grpc(local_product_images)
        precomputed_embeddings_map = {path: emb for path, emb in zip(local_product_images, embedding)}

        print(f"Checking file: {local_product_images[0]}")
        results = await check_copyrights(
            client=None,
            bucket=None,
            temp_dir=output_dir, # Pass the persistent output directory
            client_id="local_test",
            check_id="local_test",
            local_product_images=local_product_images,
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            precomputed_embeddings_map=precomputed_embeddings_map
        )
        print("\n--- Copyright Check Results ---")
        print(results)
        print("--- End of Report ---")

    asyncio.run(main())