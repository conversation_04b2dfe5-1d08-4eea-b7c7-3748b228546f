import os
import asyncio
import aiohttp
import zipfile
from lxml import etree as ET
import tempfile
from tqdm import tqdm
import datetime

# Constants from get_bulk_trademarks.py
PRE2025_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/TRTYRAP/apc18840407-20241231-{:02d}.zip"
DAILY_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/TRTDXFAP/apc{}.zip"
BASE_DIR = os.path.join(".", "IP", "Trademarks_Bulk") # Adjusted for script location
ZIP_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Zip")
os.makedirs(ZIP_DIR, exist_ok=True)

SERIAL_TO_FIND = "90168978"

def download_file_sync(url, dest_path, headers=None):
    """Synchronous file download."""
    import requests
    try:
        response = requests.get(url, headers=headers, stream=True)
        response.raise_for_status()
        with open(dest_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        return True
    except requests.exceptions.RequestException as e:
        print(f"Error downloading {url}: {e}")
        return False

def find_serial_in_xml(xml_path, serial_number):
    """
    Search for a serial number in a given XML file.
    """
    try:
        context = ET.iterparse(xml_path, events=('end',), tag='case-file', recover=True)
        for _, elem in context:
            found_serial = elem.findtext('.//serial-number')
            if found_serial and found_serial.strip() == serial_number:
                elem.clear()
                while elem.getprevious() is not None: del elem.getparent()[0]
                return True
            elem.clear()
            while elem.getprevious() is not None: del elem.getparent()[0]
    except ET.XMLSyntaxError as e:
        print(f"XML Syntax Error in {xml_path}: {e}")
    return False

def process_zip_file(zip_path, zip_filename, serial_to_find):
    """
    Extracts a zip file and searches for the serial number in the XML.
    Returns True if found, False otherwise.
    """
    try:
        with tempfile.TemporaryDirectory() as extract_dir:
            print(f"Extracting {zip_filename} to temporary directory...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                xml_files = [f for f in zip_ref.namelist() if f.endswith('.xml')]
                if not xml_files:
                    print(f"No XML file found in {zip_filename}.")
                    return False
                
                # Assuming one XML per zip
                zip_ref.extract(xml_files[0], extract_dir)
                xml_file_path = os.path.join(extract_dir, xml_files[0])

                print(f"Searching for serial number in {xml_files[0]}...")
                if find_serial_in_xml(xml_file_path, serial_to_find):
                    print(f"SUCCESS: Found serial number {serial_to_find} in {zip_filename}")
                    return True
                else:
                    print(f"Serial number not found in {zip_filename}.")
                    return False

    except zipfile.BadZipFile:
        print(f"Error: {zip_filename} is not a valid ZIP file. It might be a download error page.")
        return False
    except Exception as e:
        print(f"An unexpected error occurred while processing {zip_filename}: {e}")
        return False

async def main():
    """
    Main function to download and search through historical and daily trademark ZIP files.
    """
    print(f"Starting search for serial number: {SERIAL_TO_FIND}")
    
    found_in_files = []

    api_key = os.getenv("USPTO_ODP_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # --- 1. Process Historical Files ---
    print("\n--- Processing 82 Historical Files ---")
    for i in range(1, 83):
        zip_filename = f"apc18840407-20241231-{i:02d}.zip"
        zip_path = os.path.join(ZIP_DIR, zip_filename)
        zip_url = PRE2025_BASE_URL.format(i)

        print(f"\nProcessing file {i}/82: {zip_filename}")

        if not os.path.exists(zip_path):
            print(f"Downloading {zip_filename}...")
            if not download_file_sync(zip_url, zip_path, headers):
                print(f"Failed to download {zip_filename}. Skipping.")
                continue
        else:
            print(f"{zip_filename} already exists locally.")
        
        if await asyncio.to_thread(process_zip_file, zip_path, zip_filename, SERIAL_TO_FIND):
            found_in_files.append(zip_filename)

    # --- 2. Process Daily Files ---
    print("\n--- Processing Daily Files (Jan 1, 2025 to Today) ---")
    start_date = datetime.date(2025, 1, 1)
    end_date = datetime.date.today()
    current_date = start_date

    while current_date <= end_date:
        date_str = current_date.strftime("%y%m%d")
        zip_filename = f"apc{date_str}.zip"
        zip_path = os.path.join(ZIP_DIR, zip_filename)
        zip_url = DAILY_BASE_URL.format(date_str)

        print(f"\nProcessing daily file for {current_date}: {zip_filename}")

        # For daily files, we always try to download as we might not have it.
        # The download function will handle errors if the file doesn't exist on the server.
        print(f"Downloading {zip_filename}...")
        if not download_file_sync(zip_url, zip_path, headers):
            # This is expected for dates with no data pack (weekends, holidays)
            print(f"No file available for {current_date}. Skipping.")
            current_date += datetime.timedelta(days=1)
            continue
        
        if await asyncio.to_thread(process_zip_file, zip_path, zip_filename, SERIAL_TO_FIND):
            found_in_files.append(zip_filename)
            
        current_date += datetime.timedelta(days=1)


    print("\n--- Search Complete ---")
    if found_in_files:
        print(f"Serial number {SERIAL_TO_FIND} was found in the following files:")
        for f in found_in_files:
            print(f"- {f}")
    else:
        print(f"Serial number {SERIAL_TO_FIND} was not found in any of the searched files.")

if __name__ == "__main__":
    # To run, make sure you have USPTO_ODP_API_KEY in your .env file or environment variables
    # pip install requests lxml tqdm
    asyncio.run(main())