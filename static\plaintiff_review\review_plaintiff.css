/* Specific button groupings if needed, otherwise rely on .actions-row and .button-group */
.left-buttons {
    display: flex; /* Keep if specific grouping is needed beyond .button-group */
    gap: var(--spacing-md); /* Use common variable */
}
.right-buttons {
    display: flex; /* Keep if specific grouping is needed beyond .button-group */
    align-items: center;
    gap: var(--spacing-md); /* Use common variable */
}

/* Overlay styles are handled by common.css .overlay */

/* Specific overlay content styling */
.overlay-list { /* Keep if specific list styling is needed */
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: var(--spacing-md); /* Use common variable */
    /* Add any other specific list styles here */
}

.plaintiff-names-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.plaintiff-names-cell:hover {
    overflow: visible;
    white-space: normal;
    background-color: var(--card-background);
    position: relative;
    z-index: 20;
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    padding: 5px;
}

.action-cell {
    display: flex;
    gap: var(--spacing-sm); /* Use smaller gap for buttons */
    align-items: center;
    justify-content: center;
}

.action-cell .btn {
    padding: 6px 12px; /* Smaller buttons */
    font-size: 13px;
    min-width: 70px;
}

/* Case selection modal specific styles */
.case-selection-overlay .overlay-content {
    width: 90%;
    max-width: 900px;
}

.case-selection-overlay .common-table {
    margin-bottom: 0;
}

.case-selection-overlay .common-table th,
.case-selection-overlay .common-table td {
    padding: 8px;
    font-size: 13px;
}

.case-selection-overlay .truncate {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.case-selection-overlay .action-link {
    font-size: 12px;
    padding: 4px 8px;
}

/* Checkbox styling */
.case-selection-overlay input[type="checkbox"] {
    transform: scale(1.1);
    margin: 0;
}

/* Disabled link styling */
.disabled {
    color: var(--text-muted);
    font-style: italic;
}
