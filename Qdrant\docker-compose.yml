services:
  # --- Application Services ---
  qdrant:
    image: qdrant/qdrant
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "6333:6333" # Standard REST/gRPC port
      - "6334:6334" # Internal gRPC port (often not needed externally)
    environment:
      # Set the API key for security
      - QDRANT__SERVICE__API_KEY=${QDRANT_API_KEY}
            # --- Enable TLS and point to wildcard certs in /ssl ---
      - QDRANT__SERVICE__ENABLE_TLS=true
      # Paths will be under the base domain name used for the cert request
      - QDRANT__TLS__CERT=/ssl/live/maidalv.com/fullchain.pem
      - QDRANT__TLS__KEY=/ssl/live/maidalv.com/privkey.pem
    volumes:
      # Mount persistent storage for Qdrant data
      - /docker/qdrant_storage:/qdrant/storage
      - /mnt/4tb/qdrant/snapshots:/qdrant/snapshots
      - /mnt/4tb/qdrant/temp:/qdrant/temp
      - /ssl:/ssl:ro
    networks:
      monitoring:
          aliases:
            - qdrantinternal.maidalv.com   # matches your *.maidalv.com cert

  postgresql:
    image: postgres # Use official postgres image
    container_name: postgresql
    restart: unless-stopped
    ports:
      - "5432:5432" # Standard PostgreSQL port
    environment:
      # Set user and password for the database
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      # Optionally specify a database name, otherwise defaults to POSTGRES_USER
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      # Mount persistent storage for PostgreSQL data
      - /docker/postgresql:/var/lib/postgresql/data
    networks:
      - monitoring
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: qdrant_check_api
    restart: unless-stopped
    ports:
      - "8000:8000" # FastAPI port
    env_file:
      - ./api/.env
    volumes:
      - '/mnt/4tb/maidalv_data/IP_Data:/app/Documents/IP'
    depends_on:
      - qdrant
      - postgresql
    networks:
      - monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 10s
      timeout: 5s
      retries: 3

# --- SSL Certificate Management ---
  certbot:
    build:
      context: .
      dockerfile: Dockerfile.certbot
    container_name: certbot
    restart: unless-stopped
    volumes:
      # Use a named volume to store Let's Encrypt data persistently
      - /ssl:/etc/letsencrypt
    environment:
      # Cloudflare API Token (ensure this is set in your .env file or environment)
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      # Email for Let's Encrypt notifications
      - CERTBOT_EMAIL=<EMAIL>
      # Domains to certify (base domain first for wildcard)
      - CERTBOT_DOMAINS=maidalv.com,*.maidalv.com
      # Optional: Key type (default is ecdsa, rsa might have wider compatibility)
      # - CERTBOT_KEY_TYPE=rsa
      # Optional: Set renewal interval if needed (default is 12 hours)
      # - RENEWAL_INTERVAL=43200
    networks:
      - monitoring

  # --- Monitoring Stack ---
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090" # Prometheus UI/API
    volumes:
      # Mount the configuration file
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      # Mount persistent storage for Prometheus time-series data
      - /docker/prometheus:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle' # Allows reloading config via HTTP POST
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000" # Grafana UI
    environment:
      # Set the initial admin password (change this!)
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD}
      # Optionally configure anonymous access, SMTP, etc.
      # - GF_AUTH_ANONYMOUS_ENABLED=true
      # - GF_AUTH_ANONYMOUS_ORG_ROLE=Viewer
    volumes:
      # Mount persistent storage for Grafana dashboards, datasources, etc.
      - /docker/grafana:/var/lib/grafana
      # Add provisioning for Loki datasource
      - ./monitoring/grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml:ro
    networks:
      - monitoring
    depends_on:
      - prometheus # Ensure Prometheus starts before Grafana (optional but good practice)

  node-exporter:
    image: quay.io/prometheus/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    # Node exporter needs access to host system details
    pid: host
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      # Mount host root filesystem to `/rootfs`
      - '--path.rootfs=/rootfs'
      # Disable collectors that might not work well or be needed in Docker
      - '--no-collector.wifi'
      - '--no-collector.zfs'
      # Port 9100 is default, no need to expose externally usually
    networks:
      - monitoring

  postgres-exporter:
    image: quay.io/prometheuscommunity/postgres-exporter:latest
    container_name: postgres-exporter
    restart: unless-stopped
    environment:
      # Connection string for the exporter to connect to PostgreSQL
      # Uses the service name 'postgresql' because they are on the same network
      - DATA_SOURCE_NAME=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgresql:5432/${POSTGRES_DB}?sslmode=disable
      # Optionally add custom metrics via PG_EXPORTER_EXTEND_QUERY_PATH
    # Port 9187 is default, no need to expose externally usually
    networks:
      - monitoring
    depends_on:
      - postgresql # Ensure postgres is ready before exporter starts

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080" # cAdvisor UI (optional, useful for debug)
    volumes:
      # cAdvisor needs access to host system details for container metrics
      - /:/rootfs:ro
      - /var/run:/var/run:rw # Needs rw for Docker socket access
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      # Use docker.sock if needed (e.g., Docker Desktop)
      # - /var/run/docker.sock:/var/run/docker.sock:ro
    # Port 8080 is default
    networks:
      - monitoring

  blackbox_exporter:
    image: prom/blackbox-exporter:latest
    container_name: blackbox_exporter
    restart: unless-stopped
    volumes:
      - ./blackbox.yml:/config/blackbox.yml:ro
    command:
      - '--config.file=/config/blackbox.yml'
    networks:
      - monitoring

  loki:
    image: grafana/loki:latest
    container_name: loki
    restart: unless-stopped
    ports:
      - "3100:3100" # Loki API
    volumes:
      - ./loki-config.yml:/etc/loki/local-config.yaml:ro
      - /docker/loki:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - monitoring

  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - ./promtail-config.yml:/etc/promtail/config.yml:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring

  alist:
    image: xhofe/alist:latest
    container_name: alist
    restart: unless-stopped
    ports:
      - "5244:5244"            # keep if cloudflared points here
    volumes:
      - /mnt/4tb/maidalv_data:/data           # your files
      - alist_data:/opt/alist/data            # AList state (config.json where the database connection is configured)
    depends_on:
      - postgresql
    networks:
      - monitoring
    environment:
      - TZ=America/New_York

# Define the network connecting all services
networks:
  monitoring:
    driver: bridge # Standard Docker bridge network
    name: qdrant_monitoring

volumes:
  alist_data:
    external: true