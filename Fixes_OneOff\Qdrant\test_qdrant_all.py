#!/usr/bin/env python3
"""
Test script for the RAG process using Qdrant.
This script tests the RAG process for copyright, patent, and trademark detection using Qdrant.
"""

import os
import asyncio
import pandas as pd
from dotenv import load_dotenv
from Check.Do_Check_Copyright import check_copyrights
from Check.Do_Check_Patent import check_patents
from Check.Do_Check_Trademark import check_trademarks
from Check.RAG.RAG_Inference import load_embedding_models
from Check.RAG.qdrant_copyright import find_most_similar_copyrights_qdrant
from Check.RAG.qdrant_patent import find_most_similar_patent_image_qdrant
from Check.RAG.qdrant_trademark import find_most_similar_trademark_logo_qdrant

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Test image paths
COPYRIGHT_TEST_IMAGE = "D:\\Win10User\\Downloads\\313dff300cbe725038eaaa7788413ed66ea9139385690b40bcf0961743c8bc9a_part_14.jpg"
PATENT_TEST_IMAGE = "D:\\Win10User\\Downloads\\71TBgWmo6mL._AC_SX679_.jpg"
TRADEMARK_TEST_IMAGE = "D:\\Win10User\\Downloads\\US_DIS_ILND_1_24cv8376_d324899171e432_ 0_Exhibit_1_page27_0.webp"

async def test_copyright_qdrant():
    """
    Test the copyright detection using Qdrant.
    """
    print("Testing copyright detection using Qdrant...")
    
    # Ensure models are loaded
    load_embedding_models()
    
    # Create a mock plaintiff dataframe
    plaintiff_df = pd.DataFrame({
        'id': [1, 2, 3],
        'plaintiff_name': ['Test Plaintiff 1', 'Test Plaintiff 2', 'Test Plaintiff 3']
    })
    
    # Test direct Qdrant copyright search
    results = await find_most_similar_copyrights_qdrant(
        query_image_paths=[COPYRIGHT_TEST_IMAGE],
        check_id="test_check",
        client_id="test_client",
        plaintiff_df=plaintiff_df,
        top_n=5,
        similarity_threshold=0.4
    )
    
    print(f"Found {len(results)} potential copyright infringements:")
    for i, match in enumerate(results, 1):
        print(f"Match {i}:")
        print(f"  Filename: {match['filename']}")
        print(f"  Registration Number: {match['reg_no']}")
        print(f"  Similarity: {match['similarity']}")
        print(f"  Plaintiff: {match['plaintiff_name']}")
        print(f"  Approach: {match['approach']}")
        print("-" * 50)

async def test_patent_qdrant():
    """
    Test the patent detection using Qdrant.
    """
    print("Testing patent detection using Qdrant...")
    
    # Ensure models are loaded
    load_embedding_models()
    
    # Create a mock plaintiff dataframe
    plaintiff_df = pd.DataFrame({
        'id': [1, 2, 3],
        'plaintiff_name': ['Test Plaintiff 1', 'Test Plaintiff 2', 'Test Plaintiff 3']
    })
    
    # Test direct Qdrant patent search
    results = await find_most_similar_patent_image_qdrant(
        query_image_paths=[PATENT_TEST_IMAGE],
        check_id="test_check",
        client_id="test_client",
        plaintiff_df=plaintiff_df,
        top_n=5,
        similarity_threshold_images=0.6,
        similarity_threshold_text=0.25
    )
    
    print(f"Found {len(results)} potential patent infringements:")
    for i, match in enumerate(results, 1):
        print(f"Match {i}:")
        print(f"  Patent Number: {match['patent_number']}")
        print(f"  Text: {match['text']}")
        print(f"  Similarity: {match['similarity']}")
        print(f"  Plaintiff: {match['plaintiff_name']}")
        print("-" * 50)

async def test_trademark_qdrant():
    """
    Test the trademark detection using Qdrant.
    """
    print("Testing trademark detection using Qdrant...")
    
    # Ensure models are loaded
    load_embedding_models()
    
    # Create a mock plaintiff dataframe
    plaintiff_df = pd.DataFrame({
        'id': [1, 2, 3],
        'plaintiff_name': ['Test Plaintiff 1', 'Test Plaintiff 2', 'Test Plaintiff 3']
    })
    
    # Test direct Qdrant trademark search
    result = await find_most_similar_trademark_logo_qdrant(
        product_image_path=TRADEMARK_TEST_IMAGE,
        check_id="test_check",
        client_id="test_client",
        plaintiff_df=plaintiff_df
    )
    
    if result:
        print("Found a potential trademark infringement:")
        print(f"  Registration Number: {result['reg_no']}")
        print(f"  Match Count: {result['match_count']}")
        print(f"  Average Distance: {result['avg_distance']}")
        print(f"  Plaintiff: {result['plaintiff_name']}")
    else:
        print("No trademark infringement found.")

async def test_do_check_copyright_with_qdrant():
    """
    Test the Do_Check_Copyright function with Qdrant.
    """
    print("Testing Do_Check_Copyright with Qdrant...")
    
    # Mock client and bucket
    client = None
    bucket = None
    
    # Create a temporary directory
    temp_dir = os.path.join(os.getcwd(), "temp")
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create a mock plaintiff dataframe
    plaintiff_df = pd.DataFrame({
        'id': [1, 2, 3],
        'plaintiff_name': ['Test Plaintiff 1', 'Test Plaintiff 2', 'Test Plaintiff 3']
    })
    
    # Set up test parameters
    check_id = "test_check"
    local_product_images = [COPYRIGHT_TEST_IMAGE]
    local_ip_images = []
    local_reference_images = []
    query_image_urls = {os.path.basename(COPYRIGHT_TEST_IMAGE): "http://example.com/test.jpg"}
    
    # Call check_copyrights with use_qdrant=True
    results = await check_copyrights(
        client=client,
        bucket=bucket,
        temp_dir=temp_dir,
        check_id=check_id,
        local_product_images=local_product_images,
        local_ip_images=local_ip_images,
        local_reference_images=local_reference_images,
        plaintiff_df=plaintiff_df,
        query_image_urls=query_image_urls,
        use_qdrant=True
    )
    
    print(f"Found {len(results) if results else 0} potential copyright infringements")
    
    # Clean up
    if os.path.exists(temp_dir):
        import shutil
        shutil.rmtree(temp_dir)

async def test_do_check_patent_with_qdrant():
    """
    Test the Do_Check_Patent function with Qdrant.
    """
    print("Testing Do_Check_Patent with Qdrant...")
    
    # Mock client and bucket
    client = None
    bucket = None
    
    # Create a temporary directory
    temp_dir = os.path.join(os.getcwd(), "temp")
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create mock dataframes
    plaintiff_df = pd.DataFrame({
        'id': [1, 2, 3],
        'plaintiff_name': ['Test Plaintiff 1', 'Test Plaintiff 2', 'Test Plaintiff 3']
    })
    
    cases_df = pd.DataFrame({
        'id': [1, 2, 3],
        'case_name': ['Test Case 1', 'Test Case 2', 'Test Case 3']
    })
    
    # Set up test parameters
    check_id = "test_check"
    local_product_images = [PATENT_TEST_IMAGE]
    local_ip_images = []
    local_reference_images = []
    description = "Test description"
    ip_keywords = ["test", "patent"]
    reference_text = "Test reference text"
    query_image_urls = {os.path.basename(PATENT_TEST_IMAGE): "http://example.com/test.jpg"}
    
    # Call check_patents with use_qdrant=True
    results = await check_patents(
        client=client,
        bucket=bucket,
        temp_dir=temp_dir,
        check_id=check_id,
        local_product_images=local_product_images,
        local_ip_images=local_ip_images,
        local_reference_images=local_reference_images,
        description=description,
        ip_keywords=ip_keywords,
        reference_text=reference_text,
        cases_df=cases_df,
        plaintiff_df=plaintiff_df,
        query_image_urls=query_image_urls,
        use_qdrant=True
    )
    
    print(f"Found {len(results) if results else 0} potential patent infringements")
    
    # Clean up
    if os.path.exists(temp_dir):
        import shutil
        shutil.rmtree(temp_dir)

async def test_do_check_trademark_with_qdrant():
    """
    Test the Do_Check_Trademark function with Qdrant.
    """
    print("Testing Do_Check_Trademark with Qdrant...")
    
    # Mock client and bucket
    client = None
    bucket = None
    
    # Create a temporary directory
    temp_dir = os.path.join(os.getcwd(), "temp")
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create a mock plaintiff dataframe
    plaintiff_df = pd.DataFrame({
        'id': [1, 2, 3],
        'plaintiff_name': ['Test Plaintiff 1', 'Test Plaintiff 2', 'Test Plaintiff 3']
    })
    
    # Set up test parameters
    check_id = "test_check"
    local_product_images = [TRADEMARK_TEST_IMAGE]
    local_ip_images = []
    local_reference_images = []
    description = "Test description"
    ip_keywords = ["test", "trademark"]
    reference_text = "Test reference text"
    query_image_urls = {os.path.basename(TRADEMARK_TEST_IMAGE): "http://example.com/test.jpg"}
    
    # Call check_trademarks with use_qdrant=True
    results = await check_trademarks(
        client=client,
        bucket=bucket,
        temp_dir=temp_dir,
        check_id=check_id,
        local_product_images=local_product_images,
        local_ip_images=local_ip_images,
        local_reference_images=local_reference_images,
        description=description,
        ip_keywords=ip_keywords,
        reference_text=reference_text,
        plaintiff_df=plaintiff_df,
        query_image_urls=query_image_urls,
        use_qdrant=True
    )
    
    print(f"Found {len(results) if results else 0} potential trademark infringements")
    
    # Clean up
    if os.path.exists(temp_dir):
        import shutil
        shutil.rmtree(temp_dir)

async def main():
    """
    Main function to run all tests.
    """
    # Test copyright detection using Qdrant
    await test_copyright_qdrant()
    
    # Test patent detection using Qdrant
    await test_patent_qdrant()
    
    # Test trademark detection using Qdrant
    await test_trademark_qdrant()
    
    # Test Do_Check_Copyright with Qdrant
    await test_do_check_copyright_with_qdrant()
    
    # Test Do_Check_Patent with Qdrant
    await test_do_check_patent_with_qdrant()
    
    # Test Do_Check_Trademark with Qdrant
    await test_do_check_trademark_with_qdrant()

if __name__ == "__main__":
    asyncio.run(main())
