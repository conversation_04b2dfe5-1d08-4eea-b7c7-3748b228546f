from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Run(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    start_time = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(50))
    steps = db.relationship('Step', backref='run', lazy=True)

class Step(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    run_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('run.id'), nullable=False)
    name = db.Column(db.String(100))
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(50))
    logs = db.relationship('Log', backref='step', lazy=True)

class Log(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    step_id = db.Column(db.Inte<PERSON>, db.<PERSON>ey('step.id'), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    level = db.Column(db.String(50))
    message = db.Column(db.Text)