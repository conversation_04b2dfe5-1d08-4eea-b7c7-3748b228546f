import os
import re
import sys
from datetime import datetime
import asyncio
from bs4 import BeautifulSoup
from dateutil.parser import parse

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from Scraper.ChineseWebsite_OutOfTheFlow.common_scraper import run_scraper, setup_logger, CHINA_TZ

def _parse_date_sellerdefence(date_str: str) -> datetime | None:
    """
    Parses a date string from sellerdefense.cn, e.g., '8月 6, 2025'.
    """
    if not date_str:
        return None
    try:
        # Replace Chinese characters for month and remove spaces
        date_str = date_str.replace('月', '').replace(',', '').replace(' ', '')
        # Use regex to extract parts, assuming format MonthDayYear
        match = re.match(r'(\d+)(\d+)(\d{4})', date_str)
        if match:
            month, day, year = match.groups()
            return CHINA_TZ.localize(datetime(int(year), int(month), int(day)))
        return None
    except (ValueError, TypeError, AttributeError):
        return None

def parse_html_sellerdefence(html_content: str) -> list[dict]:
    """
    Parses the HTML content from sellerdefense.cn and extracts data from each article.
    """
    if not html_content:
        return []
    soup = BeautifulSoup(html_content, 'html.parser')
    articles = []
    for item in soup.select('.post-entry-content'):
        title_element = item.select_one('h3.entry-title a')
        date_element = item.select_one('time.entry-date')

        if not all([title_element, date_element]):
            continue

        title = title_element.get_text(strip=True)
        url = title_element.get('href')
        date_str = date_element.get_text(strip=True)

        posting_date = _parse_date_sellerdefence(date_str)

        articles.append({
            'posting_date': posting_date.date() if posting_date else None,
            'docket_in_title': None,
            'views': 0,
            'url': url,
            'title': title,
        })
    return articles

SELLERDEFENCE_CONFIG = {
    'site_name': "SellerDefense",
    'base_url': "https://sellerdefense.cn/page/{}/",
    'log_file': os.path.join(os.path.dirname(__file__), 'logs', 'sellerdefence_scraper.log'),
    'image_dir': os.path.abspath(os.path.join(os.getcwd(), "..", 'Documents', 'IP', 'SellerDefense')),
    'parse_html_func': parse_html_sellerdefence,
    'last_page_check': lambda html: "404 Not Found" in html,
}
SELLERDEFENCE_CONFIG['logger'] = setup_logger(SELLERDEFENCE_CONFIG['log_file'])

async def get_new_case_from_sellerdefence():
    """Main async function to run the sellerdefence.cn scraper."""
    return await run_scraper(SELLERDEFENCE_CONFIG)

if __name__ == '__main__':
    asyncio.run(get_new_case_from_sellerdefence())