**Question:** 

When we use the "resume" mode, we delete the image folder (shutil.rmtree(local_case_images_dir)), then use "resume mode" to process new steps and registration numbers not yet found then upload the folder to NAS (do we de.
Does it mean that we are loosing the images from steps already processed, images we got from chinese websites (directly or through USPTO), copyright images we got from google ? All those are stored in "images"

Are we loosing only these images locally? or also on the NAS? f'7z x "/volume1{remote_zip_path}" -o"/volume1{remote_folder}" -y' does not delete the existing images in the folder, just overwrite, correct?

Based on these conclusion should we only delete the "images" folder in full_reprocess mode? Is there any negative to this decision?



if copyright already in DB for wholset -> get that. Not urgent becasue now I do I per plaintiff.

1. Fill the copyright database retroactivly by going through case_df and save files in IP/copyright folder and sync with nas
2. In the flow: when picture is found is should be saved as reg_no_source.webp in IP/copyright and synced to nas
3. In the flow, before we hunt for a copyright, we should check if we already have it  (not before exhibit, but right after exhibit)

change plaintiff id, do not reprocess the case, do the transfer, and also in tb_trademark, tb_copyright, and tb_patent

!!! Need to update the IPmanager with what was found on google


1. Do not search VAu on google! it is unpublished!
3. Are we missing the plaintiff overview and translation?
4. We delete the image folder, then use "resume mode" then upload the folder to NAS. Are we loosing the images from chinese website?
5. In a given step, if all the PDFs are local already, we open the download modal 3 times!
6. Platform to review copyright and approave pics by pics
7. For scrape_date_range: we need to save the cases that do not meet the conditions? Do that we dont need to open the page for nothing?

When save_to_db is false, we are still inserting data into database for : inserting new case (get ID) and new plaintiff?

resume mode:

- still sending pictures to COS and into
- steps_df:
  - files_downloaded and failed get reinit to 0
  - all translation is redone
  - case_id is missing

steps_df.loc[steps_df['row_index'] == target_step_row_index, 'files_failed'] += 1 - num_downloaded  # Expecting 1 main file  : This is for steps with exhibit. How about if they have multiple files? The result will be negative

Merge process_copyright_regno and process_copyright_registration_numbers, and same for trademark and patent

suggestion on the 1+Deep improvement. download the exhibit when there is "unredacted". even though there is a "sealed"
Sealed: sometimes: the sealed steps has some documents available
Sometime the Answer is "Motion by Defandant"
Sometimes the IP is in exhibits to the "Motion by Defandant"

**Trademark by name:**

- When we have multiple Owners on USPTO we could ask the AI which one it is

**Copyright by name:**

I see 2 problems:

1. We save the data scaped from USCO in the database. Suposedly so that next time, we can source it from the database (using the plaintiff_ids field which is a list of plaintiff_ids) instead of searching on USCO.
2. When we save the fact that image was found on google inside the database, we then dont use that info later to avoid redoing the google search.

**Fix this:**
case ID 13677, 1:25-cv-04962...
INFO: Relevant steps to be considered:
    step_nb               priority_name                                    proceeding_text
1     2.00          PRIORITY_COMPLAINT  COMPLAINT against 2foxieh, ABQP WHHR Bags Stor...
26   25.00  PRIORITY_AMENDED_COMPLAINT  AMENDED COMPLAINT against 2FOXIEH, ABQP WHHR B...
32   31.00                PRIORITY_TRO  MOTION to Dissolve Temporary Restraining Order...
2     3.00  PRIORITY_PRELIM_INJUNCTION  MOTION for Temporary Restraining Order, MOTION...

**Need to build:**

a function to remove from COS images not associated with cases anymore

a function to remove from database (or turn TRO to False) copyright/patent/trademark that are not associated with cases anymore

**Path:**
Main doc => relevant and reg_no
    Trademark reg_no whole_set => get from USPTO
    Patent reg_no whole_set => get from USPTO
    Copyright target main doc is True and target exhibit is emoty => process_copyright_regno
    Get exhibit (either mentionned or all)
        Trademark: Objective: get the whole_set of reg_no using LLM => get from USPTO
            if cannot find on USPTO -> extract form Exhibit (need to get rid of that!)
        Patent: Objective: get the whole_set of reg_no using LLM => get from USPTO
        Copyright: try to extract images
    Chinese Websites:

    By name:

Note:
process_copyright_regno =
    search for picture inside the provided PDF, e.g. as a table
    if not found:
        get the LLM to extract the artist and title
        if not found:
            get artist and title on USCO
        Search google
