#!/usr/bin/env python3
"""
Unified Trademark Statistics

This script fetches data from DB, Qdrant, and HDD, computes key statistics, and optionally pushes them to the database.
Uses verified logic from find_orphaned_embeddings.py and trademark_image.py.
"""
import os
import sys
import json
import datetime
from tqdm import tqdm
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from trademark_db import get_db_connection
from Common.uuid_utils import generate_uuid

DB_PUSH = True  # Set to False to only print stats
STATISTICS_TABLE = "statistics_log"

# --- 1. Fetch DB trademark UUIDs ---
def get_database_trademark_ids():
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            print("❌ Could not establish database connection")
            return set()
        print("🔍 Fetching trademark IDs from database...")
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT ser_no FROM trademarks 
                WHERE mark_feature_code IN (2, 3, 5)
                ORDER BY ser_no
            """)
            records = cursor.fetchall()
            print(f"   Converting {len(records):,} serial numbers to UUIDs...")
            db_trademark_ids = set()
            for record in tqdm(records, desc="Converting to UUIDs", unit="record"):
                ser_no = record[0]
                uuid = generate_uuid(ser_no)
                db_trademark_ids.add(uuid)
        print(f"✅ Successfully processed {len(db_trademark_ids):,} trademark UUIDs from database")
        return db_trademark_ids
    except Exception as e:
        print(f"❌ Error fetching database trademark IDs: {str(e)}")
        return set()
    finally:
        if conn:
            conn.close()

# --- 2. Fetch Qdrant points ---
def get_qdrant_trademark_points():
    QDRANT_URL = os.environ.get("QDRANT_URL")
    QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
    COLLECTION_NAME = "IP_Assets"
    try:
        client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
        print("🔍 Fetching trademark points from Qdrant...")
        count_result = client.count(
            collection_name=COLLECTION_NAME,
            count_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="ip_type",
                        match=models.MatchValue(value="Trademark")
                    )
                ]
            ),
            exact=True
        )
        total_points = count_result.count
        print(f"   Total trademark points in Qdrant: {total_points:,}")
        qdrant_points = {}
        offset = None
        points_fetched = 0
        while True:
            points, next_offset = client.scroll(
                collection_name=COLLECTION_NAME,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="ip_type",
                            match=models.MatchValue(value="Trademark")
                        )
                    ]
                ),
                limit=10000,
                offset=offset,
                with_payload=True,
                with_vectors=False
            )
            points_in_batch = len(points)
            points_fetched += points_in_batch
            if total_points > 0:
                progress = (points_fetched / total_points) * 100
                print(f"   Progress: {points_fetched:,}/{total_points:,} ({progress:.1f}%)")
            for point in points:
                qdrant_points[point.id] = point.payload
            if next_offset is None:
                break
            offset = next_offset
        print(f"✅ Successfully fetched {len(qdrant_points):,} trademark points from Qdrant")
        return qdrant_points
    except Exception as e:
        print(f"❌ Error fetching Qdrant trademark points: {str(e)}")
        return {}

# --- 3. Fetch HDD serial numbers ---
def get_all_hdd_serials(images_dir):
    serials = set()
    # Use the same logic as recover_missing_images.py and find_missing_embeddings.py
    for root, dirs, files in os.walk(images_dir):
        for file in files:
            if file.endswith('.webp'):
                # Some scripts use os.path.splitext, but .replace('.webp', '') is fine
                ser_no = file.replace('.webp', '')
                # Some scripts check for numeric serials only, but we keep all for stats
                serials.add(ser_no)
    print(f"Found {len(serials)} trademark images on HDD in {images_dir}.")
    return serials


# --- 4. DB vs HDD: Missing Images (use get_statistics from recover_missing_images.py) ---
def get_db_missing_images_stat():
    # Inline the logic from get_statistics in recover_missing_images.py
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT COUNT(*) FROM trademarks
            WHERE mark_feature_code IN (2, 3, 5)
        """)
        total_eligible = cursor.fetchone()[0]
        cursor.execute("""
            SELECT COUNT(*) FROM trademarks
            WHERE mark_feature_code IN (2, 3, 5)
            AND (image_source IS NULL OR image_source = '' OR image_source = 'NotFound')
        """)
        missing_images = cursor.fetchone()[0]
        print(f"NotFound: {missing_images}")
        return missing_images, total_eligible
    except Exception as e:
        print(f"Error getting DB missing images stat: {str(e)}")
        return 0, 0
    finally:
        if conn:
            conn.close()

# --- 5. Qdrant vs DB: Orphaned Embeddings ---
def compute_qdrant_vs_db_orphans(db_trademark_ids, qdrant_points):
    print("🔍 Analyzing embeddings for orphaned records...")
    qdrant_ids = set(qdrant_points.keys())
    orphaned_ids = qdrant_ids - db_trademark_ids
    print(f"   Qdrant trademark embeddings: {len(qdrant_ids):,}")
    print(f"   Database trademark records: {len(db_trademark_ids):,}")
    print(f"   Orphaned embeddings found: {len(orphaned_ids):,}")
    with_plaintiff = 0
    without_plaintiff = 0
    for point_id in tqdm(orphaned_ids, desc="Processing orphaned embeddings", unit="embedding"):
        payload = qdrant_points.get(point_id, {})
        if payload.get('plaintiff_id'):
            with_plaintiff += 1
        else:
            without_plaintiff += 1
    total = with_plaintiff + without_plaintiff
    percent_with = (with_plaintiff / total * 100) if total else 0
    percent_without = (without_plaintiff / total * 100) if total else 0
    print(f"Orphaned Embeddings With plaintiff_id: {with_plaintiff} ({percent_with:.1f}%)")
    print(f"Orphaned Embeddings Without plaintiff_id: {without_plaintiff} ({percent_without:.1f}%)")
    return total, with_plaintiff, without_plaintiff

# --- 6. HDD vs Qdrant: Missing Embeddings ---
def compute_hdd_vs_qdrant_missing(hdd_serials, qdrant_serials):
    missing_embeddings = hdd_serials - qdrant_serials
    print(f"HDD vs Qdrant: Missing Embeddings: {len(missing_embeddings)}")
    return len(missing_embeddings), missing_embeddings

# --- 7. DB Insert ---
def log_stat_to_db(conn, metric_name, metric_value, details_preview):
    now = datetime.datetime.now()
    report_time = now.strftime('%Y-%m-%d %H:%M:%S')
    details_json = json.dumps(details_preview)
    with conn.cursor() as cur:
        cur.execute(f"""
            INSERT INTO {STATISTICS_TABLE} (report_timestamp, metric_name, metric_value, details_preview, created_at)
            VALUES (%s, %s, %s, %s, %s)
        """, (report_time, metric_name, metric_value, details_json, now))
    conn.commit()
    print(f"Pushed stat '{metric_name}' to DB.")

# --- Main ---
def get_trademark_statistics(db_push=DB_PUSH):
    load_dotenv()
    db_trademark_ids = get_database_trademark_ids()
    qdrant_points = get_qdrant_trademark_points()

    # 1. DB missing images stat (from get_statistics logic)
    missing_images_count, total_eligible = get_db_missing_images_stat()
    print(f"NotFound: {missing_images_count} (out of {total_eligible} eligible)")

    # 2. Qdrant vs DB (orphaned embeddings)
    orphaned_total, orphaned_with, orphaned_without = compute_qdrant_vs_db_orphans(db_trademark_ids, qdrant_points)
    print(f"ORPHANED EMBEDDINGS With plaintiff_id: {orphaned_with} ({(orphaned_with/(orphaned_total or 1)*100):.1f}%)")
    print(f"ORPHANED EMBEDDINGS Without plaintiff_id: {orphaned_without} ({(orphaned_without/(orphaned_total or 1)*100):.1f}%)")

    if db_push:
        conn = get_db_connection()
        log_stat_to_db(conn, "trademark_images_not_found_on_hdd", missing_images_count, {"description": f"Trademarks in DB missing images on HDD (out of {total_eligible})"})
        log_stat_to_db(conn, "orphaned_embeddings_with_plaintiff_id", orphaned_with, {"description": "Orphaned embeddings with plaintiff_id", "percent": f"{orphaned_with/(orphaned_total or 1)*100:.1f}%"})
        log_stat_to_db(conn, "orphaned_embeddings_without_plaintiff_id", orphaned_without, {"description": "Orphaned embeddings without plaintiff_id", "percent": f"{orphaned_without/(orphaned_total or 1)*100:.1f}%"})
        conn.close()
    else:
        print("DB push disabled. Stats shown above.")

if __name__ == "__main__":
    db_push = False  # Set to False to only print stats
    get_trademark_statistics(db_push=db_push)