import requests
import base64
import os
import mimetypes

API_URL = "http://JSLAWNY:5006/search_image"  # Change to your server's IP if not local
IMAGE_PATH = "D:\\Documents\\Programing\\TRO\\USSideRefactoring\\Documents\\IP\\Copyrights\\2025-03-14 - 1_25-cv-21509\\copyright_cn_allpictures\\multi_7_part_13.jpg"
OUTPUT_DIR = "D:\\Documents\\Programing\\TRO\\USSideRefactoring\\Documents\\IP\\api_test"

def call_api_and_save_images(image_path_to_upload, output_directory, api_endpoint, num_results=5):

    if not os.path.exists(output_directory):
        os.makedirs(output_directory)
        print(f"Created output directory: {output_directory}")

    # Prepare the file for upload
    # Correctly get mime type or default
    mime_type, _ = mimetypes.guess_type(image_path_to_upload)
    if mime_type is None:
        mime_type = 'application/octet-stream' # Default if type can't be guessed

    payload = {'max_results': str(num_results)}
    with open(image_path_to_upload, 'rb') as f:
        files = {'image': (os.path.basename(image_path_to_upload), f, mime_type)}
        
        print(f"Sending request to {api_endpoint} with image: {os.path.basename(image_path_to_upload)}, requesting {num_results} results.")
        response = requests.post(api_endpoint, files=files, data=payload, timeout=300) # Increased timeout for potentially long scraper operations

    if response.status_code == 200:
        data = response.json()
        if data.get("success"):
            results = data.get("results", [])
            print(f"API returned {len(results)} images.")
            for item in results:
                image_data_base64 = item.get("image_data")
                original_filename = item.get("filename", f"unknown_image_{item.get('source', 'src')}.jpg") # Default filename
                
                save_path = os.path.join(output_directory, original_filename)
                try:
                    image_bytes = base64.b64decode(image_data_base64)
                    with open(save_path, 'wb') as img_file:
                        img_file.write(image_bytes)
                    print(f"Saved: {save_path}")
                except Exception as e:
                    print(f"Error decoding/saving image {original_filename}: {e}")
        else:
            print(f"API Error: {data.get('error')}")
    else:
        print(f"HTTP Error: {response.status_code} - {response.text}")

if __name__ == "__main__":    
    # Example: Request 7 results
    call_api_and_save_images(IMAGE_PATH, OUTPUT_DIR, API_URL, num_results=7)
