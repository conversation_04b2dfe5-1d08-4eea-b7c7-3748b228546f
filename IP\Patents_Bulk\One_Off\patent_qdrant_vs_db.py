import os
import sys
sys.path.append(os.getcwd())
import psycopg2
import psycopg2.extras
import logging
from collections import Counter
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models
from IP.Patents_Bulk.patent_db_grant import get_db_connection
from Common.uuid_utils import generate_uuid

# Load environment variables
load_dotenv()

# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_qdrant_vs_db.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_db_patent_files(conn):
    """
    Fetches all patent fig_files from the database.
    """
    logger.info("Fetching patent fig_files from the database...")
    db_files = {}
    total_files_count = 0
    patents_processed = 0
    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as pre_cursor:
        pre_cursor.execute("SELECT count(*) FROM patents WHERE fig_files IS NOT NULL AND array_length(fig_files, 1) > 0;")
        total_patents_to_fetch = pre_cursor.fetchone()[0]
        logger.info(f"Total patents to fetch from DB: {total_patents_to_fetch}")

    with conn.cursor('db_files_cursor', cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute("SELECT reg_no, fig_files, date_published FROM patents WHERE fig_files IS NOT NULL AND array_length(fig_files, 1) > 0;")
        for patent in cursor:
            patents_processed += 1
            if patents_processed % 50000 == 0:
                logger.info(f"DB progress: {patents_processed}/{total_patents_to_fetch} patents processed.")
            reg_no = patent['reg_no']
            files = patent['fig_files']
            date_published = patent['date_published']
            total_files_count += len(files)
            for f in files:
                file_basename = os.path.basename(f)
                file_id = os.path.splitext(file_basename)[0]
                uuid = generate_uuid(file_id)
                db_files[uuid] = (reg_no, file_basename, date_published)

    logger.info(f"Found {total_files_count} total files in the database for {patents_processed} patents.")
    return total_files_count, db_files

def get_qdrant_patent_points(client, collection_name):
    """
    Fetches all patent points from Qdrant.
    """
    logger.info("Fetching patent points from Qdrant...")
    qdrant_points = {}
    
    count_result = client.count(
        collection_name=collection_name,
        count_filter=models.Filter(
            must=[
                models.FieldCondition(
                    key="ip_type",
                    match=models.MatchValue(value="Patent")
                )
            ]
        ),
        exact=True
    )
    total_patent_points = count_result.count
    logger.info(f"Total patent points to fetch from Qdrant: {total_patent_points}")
    
    offset = None
    points_fetched = 0
    while True:
        points, next_offset = client.scroll(
            collection_name=collection_name,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="ip_type",
                        match=models.MatchValue(value="Patent")
                    )
                ]
            ),
            limit=50000,
            offset=offset,
            with_payload=True,
            with_vectors=False
        )
        
        points_in_batch = len(points)
        points_fetched += points_in_batch
        if total_patent_points > 0:
            logger.info(f"Qdrant progress: {points_fetched}/{total_patent_points} points fetched.")

        for point in points:
            reg_no = point.payload.get("reg_no")
            qdrant_points[point.id] = reg_no
        
        if next_offset is None:
            break
        offset = next_offset

    logger.info(f"Found {len(qdrant_points)} points in Qdrant.")
    return total_patent_points, qdrant_points

def run_comparison():
    """
    Runs the comparison between database and Qdrant.
    """
    conn = None
    try:
        # Get DB connection
        conn = get_db_connection()
        if conn is None:
            logger.error("Could not establish database connection. Exiting.")
            return

        # Get Qdrant client
        qdrant_url = os.environ.get("QDRANT_URL")
        qdrant_api_key = os.environ.get("QDRANT_API_KEY")
        qdrant_collection_name = "IP_Assets"

        if not qdrant_url or not qdrant_api_key:
            logger.error("QDRANT_URL or QDRANT_API_KEY environment variables are not set. Exiting.")
            return

        qdrant_client = QdrantClient(url=qdrant_url, api_key=qdrant_api_key, timeout=60)

        # 1. Get data from DB
        total_db_files, db_files = get_db_patent_files(conn)

        # 2. Get data from Qdrant
        total_qdrant_points, qdrant_points = get_qdrant_patent_points(qdrant_client, qdrant_collection_name)

        # 3. Perform comparison
        db_uuids = set(db_files.keys())
        qdrant_uuids = set(qdrant_points.keys())

        db_files_not_in_qdrant = db_uuids - qdrant_uuids
        qdrant_points_not_in_db = qdrant_uuids - db_uuids

        # 4. Print statistics
        print("\n--- Patent Data Comparison: PostgreSQL vs. Qdrant ---")
        print(f"Total files in database ('patents' table, fig_files): {total_db_files}")
        print(f"Total points in Qdrant ('IP_Assets' collection, ip_type='Patent'): {total_qdrant_points}")
        print("-" * 60)
        
        print(f"Found {len(db_files_not_in_qdrant)} files in the database that are not in Qdrant.")
        if db_files_not_in_qdrant:
            dates_not_in_qdrant = [db_files[uuid][2] for uuid in db_files_not_in_qdrant if db_files[uuid][2] is not None]
            if dates_not_in_qdrant:
                date_counts = Counter(dates_not_in_qdrant)
                print("Top 10 dates with most files in DB but not in Qdrant:")
                for date, count in date_counts.most_common(10):
                    print(f"  - Date: {date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else date}, Count: {count}")
            
            print("\n10 examples of files from DB not in Qdrant:")
            for i, file_uuid in enumerate(list(db_files_not_in_qdrant)[:10]):
                reg_no, file_name, _ = db_files[file_uuid]
                print(f"  - UUID: {file_uuid}, Reg No: {reg_no}, File: {file_name}")
        
        print("-" * 60)

        print(f"Found {len(qdrant_points_not_in_db)} points in Qdrant that are not in the database.")
        if qdrant_points_not_in_db:
            reg_nos_not_in_db = [qdrant_points[uuid] for uuid in qdrant_points_not_in_db]
            
            dates_for_reg_nos = {}
            if reg_nos_not_in_db:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
                    cursor.execute("SELECT reg_no, date_published FROM patents_all WHERE reg_no = ANY(%s)", (list(set(reg_nos_not_in_db)),))
                    for row in cursor:
                        dates_for_reg_nos[row['reg_no']] = row['date_published']

            # Identify patents in Qdrant but not found in 'patents_all'
            qdrant_reg_nos_set = set(reg_nos_not_in_db)
            patents_all_reg_nos_set = set(dates_for_reg_nos.keys())
            reg_nos_not_in_patents_all = qdrant_reg_nos_set - patents_all_reg_nos_set

            if reg_nos_not_in_patents_all:
                print(f"\nFound {len(reg_nos_not_in_patents_all)} patents from Qdrant that are not in 'patents_all' table.")
                print("3 examples of reg_no from Qdrant not in 'patents_all':")
                for reg_no in list(reg_nos_not_in_patents_all)[:3]:
                    print(f"  - Reg No: {reg_no}")

            dates_not_in_db = [dates_for_reg_nos.get(reg_no) for reg_no in reg_nos_not_in_db if dates_for_reg_nos.get(reg_no) is not None]
            if dates_not_in_db:
                date_counts = Counter(dates_not_in_db)
                print("\nTop 10 dates with most points in Qdrant but not in DB (and present in patents_all):")
                for date, count in date_counts.most_common(10):
                    print(f"  - Date: {date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else date}, Count: {count}")

            print("\n10 examples of points from Qdrant not in DB:")
            for i, point_uuid in enumerate(list(qdrant_points_not_in_db)[:10]):
                reg_no = qdrant_points[point_uuid]
                print(f"  - UUID: {point_uuid}, Reg No: {reg_no}")
        print("-" * 60)


    except Exception as e:
        logger.error(f"An error occurred during the comparison: {e}", exc_info=True)
    finally:
        if conn:
            conn.close()
            logger.info("Database connection closed.")

if __name__ == "__main__":
    run_comparison()