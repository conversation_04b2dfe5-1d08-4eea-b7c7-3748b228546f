import os
import sys
sys.path.append(os.getcwd())
import pandas as pd
import fitz
import tempfile
import zlib
import base64
import json
import shutil
from logdata import log_message
from Common.Constants import sanitize_name
from FileManagement.SendToNAS import transfer_nas_to_local, sftp_exists
from Common.Constants import nas_case_folder, local_case_folder

def get_file_processed_before(df_db, df, index, sftp):
    plaintiff_id = df.at[index, 'plaintiff_id']

    # Get all previous cases with the same plaintiff_id (from df_db which are the cases already in database, and from df which are the cases in the current alert, not pused to database yet)
    plaintiff_cases = pd.concat([
        df_db[df_db['plaintiff_id'] == plaintiff_id],
        df[df['plaintiff_id'] == plaintiff_id]
    ])
    plaintiff_cases = plaintiff_cases.drop_duplicates(subset=['docket', 'court'])
    plaintiff_cases = plaintiff_cases.drop(index)  # Remove the current case from plaintiff_cases
    if plaintiff_cases.empty:
        return False

    # Initialize lists to collect data
    plaintiff_cases['images_folder_first_file_creation_date'] = None
    plaintiff_cases['old_case_directory'] = None
    plaintiff_cases['old_case_images_folder'] = None
    plaintiff_cases['filenames'] = None
    plaintiff_cases['is_local'] = None

    # Collect creation dates and related data
    for idx, case in plaintiff_cases.iterrows():
        old_case_date = pd.to_datetime(case['date_filed'], errors='coerce').strftime('%Y-%m-%d')
        old_case_docket = case['docket']
        old_case_directory_nas = f"{nas_case_folder}/{sanitize_name(f'{old_case_date} - {old_case_docket}')}"
        old_case_images_folder_nas = f"{old_case_directory_nas}/images"
        old_case_directory_local = os.path.join(local_case_folder, sanitize_name(f"{old_case_date} - {old_case_docket}"))
        old_case_images_folder_local = os.path.join(old_case_directory_local, "images")

        # Initialize variables
        creation_date = old_case_directory = old_case_images_folder = is_local = None
        filenames = []

        # Check if images folder exists locally
        if os.path.exists(old_case_images_folder_local):
            filenames = os.listdir(old_case_images_folder_local)
            if filenames:
                first_file = os.path.join(old_case_images_folder_local, filenames[0])
                creation_date = os.path.getctime(first_file)
                old_case_directory = old_case_directory_local
                old_case_images_folder = old_case_images_folder_local
                is_local = True
        elif sftp_exists(sftp, old_case_images_folder_nas):
            filenames = sftp.listdir(old_case_images_folder_nas)
            if filenames:
                first_file = f"{old_case_images_folder_nas}/{filenames[0]}"
                attrs = sftp.lstat(first_file)
                creation_date = attrs.st_mtime  # Using modification time as creation time may not be available
                old_case_directory = old_case_directory_nas
                old_case_images_folder = old_case_images_folder_nas
                is_local = False

        plaintiff_cases.at[idx, 'images_folder_first_file_creation_date'] = creation_date
        plaintiff_cases.at[idx, 'old_case_directory'] = old_case_directory
        plaintiff_cases.at[idx, 'old_case_images_folder'] = old_case_images_folder
        plaintiff_cases.at[idx, 'filenames'] = filenames
        plaintiff_cases.at[idx, 'is_local'] = is_local

    # Remove cases without a valid creation date
    plaintiff_cases = plaintiff_cases[plaintiff_cases['images_folder_first_file_creation_date'].notnull()]
    if plaintiff_cases.empty:
        return False

    # Sort the cases by the creation date of the first file in the images folder
    plaintiff_cases = plaintiff_cases.sort_values(by='images_folder_first_file_creation_date', ascending=False)

    new_case = df.loc[index]
    new_case_date = pd.to_datetime(new_case['date_filed'], errors='coerce').strftime('%Y-%m-%d')
    new_case_docket = new_case['docket']
    new_case_directory = os.path.join(local_case_folder, sanitize_name(f"{new_case_date} - {new_case_docket}"))
    new_case_pdf_folder = os.path.join(new_case_directory, "1")

    found = False

    try:
        # For each old case of this plaintiff
        for idx, old_case in plaintiff_cases.iterrows():
            old_case_directory = old_case['old_case_directory']
            old_case_images_folder = old_case['old_case_images_folder']
            filenames = old_case['filenames']
            is_local = old_case['is_local']

            if not filenames:
                continue

            filename = next((f for f in filenames if f.endswith('.webp')), None)
            if filename is None:
                continue

            old_pdfname = filename.split("_page")[0] + ".pdf"

            if is_local:
                old_pdf_path = os.path.join(old_case_directory, "1", old_pdfname)
                if not os.path.exists(old_pdf_path):
                    continue
            else:
                old_pdf_path = f"{old_case_directory}/1/{old_pdfname}"
                if not sftp_exists(sftp, old_pdf_path):
                    continue

            if os.path.exists(new_case_pdf_folder):
                new_pdf_files = [f for f in os.listdir(new_case_pdf_folder) if f.endswith(".pdf")]
                for f in new_pdf_files:
                    new_pdf_path = os.path.join(new_case_pdf_folder, f)

                    # Download the old PDF to a temporary file if necessary
                    if is_local:
                        old_pdf_local_path = old_pdf_path
                    else:
                        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                            old_pdf_local_path = temp_file.name
                            sftp.get(old_pdf_path, old_pdf_local_path)

                    try:
                        # Open PDFs and compare page counts
                        new_pdf = fitz.open(new_pdf_path)
                        old_pdf = fitz.open(old_pdf_local_path)
                        new_pdf_page_numbers = new_pdf.page_count
                        old_pdf_page_numbers = old_pdf.page_count
                        new_pdf.close()
                        old_pdf.close()
                    except Exception as e:
                        print(f"An error occurred opening the PDF: {e}")
                        continue
                    finally:
                        if not is_local:
                            os.unlink(old_pdf_local_path)  # Clean up temporary files

                    # Compare page counts
                    if new_pdf_page_numbers == old_pdf_page_numbers:
                        # Copy the images folder from old case to new case
                        log_message(f"   + Found an existing case for this plaintiff that matches this case! The old case pdf is {old_pdf_path}")
                        new_case_images_folder = os.path.join(new_case_directory, "images")
                        if is_local:
                            shutil.copytree(old_case_images_folder, new_case_images_folder)
                        else:
                            transfer_nas_to_local(sftp, old_case_images_folder, new_case_images_folder)
                        found = True

                        # Update the 'images' field
                        if isinstance(old_case['images'], str):
                            df.at[index, 'images'] = json.loads(old_case['images'])
                        elif isinstance(old_case['images'], dict):
                            df.at[index, 'images'] = old_case['images']
                        break  # Exit the loop if found
                if found:
                    break
        return found
    except Exception as e:
        print(f"get_file_processed_before: An error occurred: {e}")
        return False
