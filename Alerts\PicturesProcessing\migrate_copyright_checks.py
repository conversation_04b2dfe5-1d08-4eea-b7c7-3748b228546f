#!/usr/bin/env python3
"""
This script performs a data consistency analysis between tb_case data and the copyrights database table.
"""
import sys
import os
import pandas as pd
import re

sys.path.append(os.getcwd())

from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Patents_Bulk.patent_db_grant import get_db_connection

def get_tb_case_copyright_info():
    """
    Retrieves copyright information from tb_case.
    """
    df = get_table_from_GZ("tb_case", force_refresh=True)
    copyright_filenames = set()
    copyright_reg_nos = set()
    tb_case_details = []

    for index, row in df.iterrows():
        images_data = row.get('images', {})
        if images_data and 'copyrights' in images_data and images_data['copyrights']:
            for filename, details in images_data['copyrights'].items():
                copyright_filenames.add(filename)
                if 'reg_no' in details and details['reg_no']:
                    # Assuming reg_no is a list
                    for reg_no in details['reg_no']:
                        copyright_reg_nos.add(reg_no)
                        tb_case_details.append({
                            "docket": row.get('docket'),
                            "filename": filename,
                            "reg_no": reg_no,
                            "details": details
                        })
    
    return copyright_filenames, copyright_reg_nos, tb_case_details

def get_db_copyright_info():
    """
    Retrieves copyright information from the database.
    """
    db_conn = get_db_connection()
    df = pd.read_sql("SELECT filenames, registration_number, method FROM copyrights", db_conn)
    db_conn.close()

    db_filenames = set()
    for filenames_list in df['filenames'].dropna():
        for filename in filenames_list:
            db_filenames.add(filename)
            
    db_reg_nos = set(df['registration_number'].dropna())
    
    return db_filenames, db_reg_nos, df

def analyze_data_consistency():
    """
    Analyzes the consistency of copyright data between tb_case and the database.
    """
    print("Starting data consistency analysis...")
    
    # Get data from tb_case
    tb_case_filenames, tb_case_reg_nos, tb_case_details = get_tb_case_copyright_info()
    print(f"Found {len(tb_case_filenames)} filenames and {len(tb_case_reg_nos)} registration numbers in tb_case.")

    # Get data from database
    db_filenames, db_reg_nos, df_db = get_db_copyright_info()
    print(f"Found {len(db_filenames)} filenames and {len(db_reg_nos)} registration numbers in the database.")

    # 1. tb_case filenames not in database
    tb_case_filenames_not_in_db = [f for f in tb_case_filenames if f not in db_filenames]
    print(f"\n1. Found {len(tb_case_filenames_not_in_db)} tb_case filenames not in the database.")
    print("First 10:", tb_case_filenames_not_in_db[:10])

    # Create a mapping from reg_no to associated filenames from tb_case
    tb_case_reg_to_filenames = {}
    for detail in tb_case_details:
        reg_no = detail['reg_no']
        filename = detail['filename']
        if reg_no not in tb_case_reg_to_filenames:
            tb_case_reg_to_filenames[reg_no] = set()
        tb_case_reg_to_filenames[reg_no].add(filename)

    # Find filenames in tb_case that are not in the db record's filenames list
    missing_filenames_map = {}
    for index, row in df_db.iterrows():
        reg_no = row['registration_number']
        db_filenames_list = row['filenames']
        if reg_no in tb_case_reg_to_filenames:
            tb_case_files = tb_case_reg_to_filenames[reg_no]
            missing_files = [f for f in tb_case_files if f not in db_filenames_list]
            if missing_files:
                missing_filenames_map[reg_no] = missing_files
    
    print(f"\nFound {len(missing_filenames_map)} registration numbers with missing filenames in the database.")


    # 2. tb_case reg_nos not in database
    tb_case_reg_nos_not_in_db = [r for r in tb_case_reg_nos if r not in db_reg_nos]
    print(f"\n2. Found {len(tb_case_reg_nos_not_in_db)} tb_case registration numbers not in the database.")
    print("First 10:", tb_case_reg_nos_not_in_db[:10])

    if tb_case_reg_nos_not_in_db:
        print("\n--- Details for tb_case registration numbers not in database ---")
        for detail in tb_case_details:
            if detail['reg_no'] in tb_case_reg_nos_not_in_db:
                print(f"  Case: {detail['docket']}")
                print(f"    Key: {detail['filename']}")
                print(f"    Copyright Info: {detail['details']}")

    # 3. Database filenames not in tb_case
    db_filenames_not_in_tb_case = [f for f in db_filenames if f not in tb_case_filenames]
    print(f"\n3. Found {len(db_filenames_not_in_tb_case)} database filenames not in tb_case.")
    print("First 10:", db_filenames_not_in_tb_case[:10])

    # Analysis of single-entry filenames
    count_single_filename = 0
    for index, row in df_db.iterrows():
        filenames = row['filenames']
        if isinstance(filenames, list) and len(filenames) == 1:
            if filenames[0] in db_filenames_not_in_tb_case:
                count_single_filename += 1
    
    print(f"\n   Out of these, {count_single_filename} are the only item in their 'filenames' list.")

    # 4. Database reg_nos not in tb_case
    db_reg_nos_not_in_tb_case = [r for r in db_reg_nos if r not in tb_case_reg_nos]
    print(f"\n4. Found {len(db_reg_nos_not_in_tb_case)} database registration numbers not in tb_case.")
    print("First 10:", db_reg_nos_not_in_tb_case[:10])
 
    # 5. Method mismatch analysis
    print("\n--- Starting Method Mismatch Analysis ---")
    mismatches = []
    for index, row in df_db.iterrows():
        method = row['method']
        filenames = row['filenames']
        
        if not method or not isinstance(filenames, list) or not filenames:
            print(f"!!!!! Skipping row {index} due to missing method or filenames.")
            continue

        # Check if the method is not in any of the filenames
        if not any(method in filename for filename in filenames):
            mismatches.append({
                "registration_number": row['registration_number'],
                "method": method,
                "filenames": filenames
            })

    print(f"\n5. Found {len(mismatches)} records with method mismatch.")
    if mismatches:
        print("First 5 mismatches:")
        for mismatch in mismatches[:5]:
            print(f"  Reg No: {mismatch['registration_number']}, Method: {mismatch['method']}, Filenames: {mismatch['filenames']}")

    return db_reg_nos_not_in_tb_case, db_filenames_not_in_tb_case, missing_filenames_map, mismatches
 
def delete_extra_reg_nos(reg_nos_to_delete):
    """
    Deletes registration numbers from the copyrights table.
    """
    if not reg_nos_to_delete:
        print("No registration numbers to delete.")
        return

    print(f"Deleting {len(reg_nos_to_delete)} registration numbers from the database...")
    db_conn = get_db_connection()
    cursor = db_conn.cursor()
    try:
        # Convert list to a tuple for the SQL query
        reg_nos_tuple = tuple(reg_nos_to_delete)
        
        # Use a placeholder for each item in the tuple
        placeholders = ','.join(['%s'] * len(reg_nos_tuple))
        
        query = f"DELETE FROM copyrights WHERE registration_number IN ({placeholders})"
        
        cursor.execute(query, reg_nos_tuple)
        db_conn.commit()
        print(f"Successfully deleted {cursor.rowcount} records.")
    except Exception as e:
        print(f"An error occurred during deletion: {e}")
        db_conn.rollback()
    finally:
        cursor.close()
        db_conn.close()

def delete_extra_filenames(filenames_to_delete):
    """
    Deletes filenames from the copyrights table.
    """
    if not filenames_to_delete:
        print("No filenames to delete.")
        return

    print(f"Deleting {len(filenames_to_delete)} filenames from the database...")
    db_conn = get_db_connection()
    cursor = db_conn.cursor()
    try:
        # Inefficiently find all records that contain any of the filenames to delete.
        # This is necessary because we need to read the list, modify it, and write it back.
        query_select = "SELECT id, filenames FROM copyrights WHERE EXISTS (SELECT 1 FROM unnest(filenames) AS f WHERE f = ANY(%s))"
        cursor.execute(query_select, (filenames_to_delete,))
        records_to_update = cursor.fetchall()

        for record_id, filenames_list in records_to_update:
            # Remove the extra filenames from the list
            updated_filenames = [f for f in filenames_list if f not in filenames_to_delete]
            
            # If the list is now empty, we could delete the record, or leave it.
            # For now, we'll just update it to an empty list.
            query_update = "UPDATE copyrights SET filenames = %s WHERE id = %s"
            cursor.execute(query_update, (updated_filenames, record_id))

        db_conn.commit()
        print(f"Updated {len(records_to_update)} records by removing extra filenames.")
    except Exception as e:
        print(f"An error occurred during filename deletion: {e}")
        db_conn.rollback()
    finally:
        cursor.close()
        db_conn.close()


def add_missing_filenames(missing_map):
    """
    Adds missing filenames to the copyrights table based on registration number.
    """
    if not missing_map:
        print("No missing filenames to add.")
        return

    print(f"Adding missing filenames for {len(missing_map)} registration numbers...")
    db_conn = get_db_connection()
    cursor = db_conn.cursor()
    try:
        for reg_no, files_to_add in missing_map.items():
            for filename in files_to_add:
                # Use array_append to add the new filename to the list.
                # The WHERE clause prevents adding a duplicate.
                query = """
                    UPDATE copyrights
                    SET filenames = array_append(filenames, %s)
                    WHERE registration_number = %s AND NOT (%s = ANY(filenames))
                """
                cursor.execute(query, (filename, reg_no, filename))

        db_conn.commit()
        print(f"Successfully updated records with missing filenames using array_append.")
    except Exception as e:
        print(f"An error occurred during filename addition: {e}")
        db_conn.rollback()
    finally:
        cursor.close()
        db_conn.close()


def fix_method_mismatches(mismatches):
    """
    Fixes method mismatches in the copyrights table.
    """
    print(f"Fixing {len(mismatches)} method mismatches...")
    db_conn = get_db_connection()
    cursor = db_conn.cursor()
    
    # Regex to extract method from filename like '{reg_no}_{method}.webp' or '{reg_no}_{method}_{i}.webp'
    # It captures the part between the first and last underscore, or between the first underscore and the dot.
    method_regex = re.compile(r'^[^_]+_([^_]+)(?:_\d+)?\.webp$')

    try:
        for mismatch in mismatches:
            reg_no = mismatch.get('registration_number')
            filenames = mismatch.get('filenames')

            if not reg_no or not isinstance(filenames, list) or not filenames:
                print(f"!!!!! Skipping mismatch for registration_number {reg_no} due to missing data.")
                continue

            first_filename = filenames[0]
            match = method_regex.match(first_filename)

            if match:
                new_method = match.group(1)
                
                query = "UPDATE copyrights SET method = %s WHERE registration_number = %s"
                cursor.execute(query, (new_method, reg_no))
                print(f"  Updated record with registration_number {reg_no} with new method: '{new_method}'")
            else:
                print(f"  Could not extract method from filename for registration_number {reg_no}: {first_filename}")

        db_conn.commit()
        print("Successfully fixed method mismatches.")
    except Exception as e:
        print(f"An error occurred during method mismatch correction: {e}")
        db_conn.rollback()
    finally:
        cursor.close()
        db_conn.close()


if __name__ == "__main__":
    extra_reg_nos, extra_filenames, missing_filenames, method_mismatches = analyze_data_consistency()
    if extra_reg_nos:
        delete_extra_reg_nos(extra_reg_nos)
    if extra_filenames:
        delete_extra_filenames(extra_filenames)
    if missing_filenames:
        add_missing_filenames(missing_filenames)
    if method_mismatches:
        fix_method_mismatches(method_mismatches)