# 1000 free per month
# https://cloud.google.com/vision/docs/detecting-web#vision_web_detection-python

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Add the project root directory to the Python path
import cv2
from google.cloud import vision

# Free 1000 per month then 1.5 usd / 1000
# https://cloud.google.com/vision/pricing
def detect_crop_hints(path):
    """Detects crop hints in an image."""

    client = vision.ImageAnnotatorClient()

    with open(path, "rb") as image_file:
        content = image_file.read()
    image = vision.Image(content=content)

    # crop_hints_params = vision.CropHintsParams(aspect_ratios=[1.77])
    # image_context = vision.ImageContext(crop_hints_params=crop_hints_params)

    # response = client.crop_hints(image=image, image_context=image_context)
    response = client.crop_hints(image=image)
    hints = response.crop_hints_annotation.crop_hints

    for n, hint in enumerate(hints):
        print(f"\nCrop Hint: {n}")

        vertices = [
            f"({vertex.x},{vertex.y})" for vertex in hint.bounding_poly.vertices
        ]

        print("bounds: {}".format(",".join(vertices)))

        # now crop the image using the crop hints and save the image with the crop hints
        # Read the original image with OpenCV
        img = cv2.imread(path)

        # Extract coordinates and convert to integers
        vertices_array = [(int(vertex.x), int(vertex.y)) 
                            for vertex in hint.bounding_poly.vertices]
        
        # Get bounding rectangle coordinates
        x = min(vertex[0] for vertex in vertices_array)
        y = min(vertex[1] for vertex in vertices_array)
        w = max(vertex[0] for vertex in vertices_array) - x
        h = max(vertex[1] for vertex in vertices_array) - y

        # Crop the image
        cropped_image = img[y:y+h, x:x+w]
        
        # Save the cropped image
        output_path = f"D:/Win10User/Downloads/crop_hints_{n}.jpg"
        cv2.imwrite(output_path, cropped_image)

    if response.error.message:
        raise Exception(
            "{}\nFor more info on error messages, check: "
            "https://cloud.google.com/apis/design/errors".format(response.error.message)
        )
    
# This is also possible, check the documentation
def detect_web_uri(uri):
    pass



if __name__ == "__main__":
    image_path = "D:/Win10User/Downloads/crop.jpg"
    detect_crop_hints(image_path)
