import os
import sys
sys.path.append(os.getcwd())

from DatabaseManagement.ImportExport import get_table_from_GZ
from Common.Constants import sanitize_name

def print_copyright_filenames_with_page():
    """
    This script prints the file names of all the copyrights in tb_case (i.e df) 
    that would otherwise get downloaded in collect_all_images.
    Only prints the name and counts the filename if the word "page" is part of the file name.
    """
    df = get_table_from_GZ("tb_case", force_refresh=True)
    count_pages = 0
    count_VA = 0
    count_MD = 0
    count_other = 0
    uniqueReg_no = set()
    case_type = "copyrights"

    for index, row in df.iterrows():
        if row['images'] and isinstance(row['images'], dict) and case_type in row['images']:
            docket_sanitized = row['docket'].replace(':', '_')
            if row['images'][case_type] and isinstance(row['images'][case_type], dict):
                for image in row['images'][case_type].keys():
                    if "full_filename" in row['images'][case_type][image] and row['images'][case_type][image]['full_filename'] == image:
                        continue

                    plaintiff_id = int(row['plaintiff_id'])
                    
                    if "product_name" in row['images'][case_type][image] and row['images'][case_type][image]['product_name'] != "":
                        file_name = f"{plaintiff_id}_{row['images'][case_type][image]['product_name']}_{docket_sanitized}_{image}"
                    else:
                        file_name = f"{plaintiff_id}_{docket_sanitized}_{image}"

                    file_name = sanitize_name(file_name)

                    if "page" in file_name.lower():
                        print(file_name)
                        count_pages += 1
                    else:
                        if file_name not in uniqueReg_no:
                            uniqueReg_no.add(file_name)
                            if "VA" in file_name.lower():
                                count_VA +=1
                            elif "MD" in file_name.lower():
                                count_MD +=1
                            else:
                                count_other +=1
    
    print(f"\nTotal number of files with 'page' in the name: {count_pages}")

if __name__ == "__main__":
    print_copyright_filenames_with_page()