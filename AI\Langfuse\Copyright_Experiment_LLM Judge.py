import os
import asyncio
import tempfile
import re
import sys
# V3 uses a global client accessed via get_client
from langfuse import get_client
# Assuming these are your custom modules for AI calls and downloads
from AI.GC_VertexAI import vertex_genai_multi_async
from Check.Do_Check_Download import download_from_url

# Ensure the project root is in the Python path to resolve imports correctly.
sys.path.append(os.getcwd())

# --- Configuration ---
# Environment variables are still the primary way to configure the client
os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-a81ed7b3-cbf2-494a-a6ff-c00b21778891"

DATASET_NAME = "CopyrightReports"
PROMPT_FILES = [
    "Report_Copyright.txt",
    "Report_CopyrightJulie.txt",
    "Report_CopyrightSamreen.txt"
]
PROMPTS_DIR = os.path.join(os.getcwd(), "Check", "Prompts")

def parse_input_data(input_str):
    """
    Parses the input string from a Langfuse dataset item to extract structured data.

    Args:
        input_str (str): The raw input string from the dataset item.

    Returns:
        dict: A dictionary containing the extracted information, or None if parsing fails.
    """
    try:
        # Extract the main text content, which is everything before the first image link
        main_text_match = re.search(r'^(.*?)Product Image:', input_str, re.DOTALL)
        main_text = main_text_match.group(1).strip() if main_text_match else ""

        # Regex to find the product image URL
        product_image_match = re.search(r"Product Image:\s*!\[Alt text\]\((.*?)\)", input_str)
        
        # Regex to find copyright info and image URL
        copyright_info_match = re.search(
            r"Copyright Registered Image from '(.*?)' with registration number '(.*?)':\s*!\[Alt text\]\((.*?)\)", 
            input_str
        )

        if not product_image_match or not copyright_info_match:
            print("Error: Could not parse one or more required fields from the input string.")
            return None

        return {
            "main_text": main_text,
            "product_image_url": product_image_match.group(1),
            "ip_owner": copyright_info_match.group(1),
            "reg_no": copyright_info_match.group(2),
            "copyright_image_url": copyright_info_match.group(3)
        }
    except Exception as e:
        print(f"An error occurred during input parsing: {e}")
        return None

async def run_prompt_on_item(experiment_name, prompt_name, prompt_content, item, temp_dir):
    """
    Runs a single prompt on a single dataset item using Langfuse SDK v3.
    """
    parsed_data = parse_input_data(item.input)
    if not parsed_data:
        print(f"Skipping item {item.id} due to parsing failure.")
        return None

    try:
        # Download images
        product_local_path = os.path.join(temp_dir, os.path.basename(parsed_data["product_image_url"]))
        ip_local_path = os.path.join(temp_dir, os.path.basename(parsed_data["copyright_image_url"]))

        await download_from_url(parsed_data["product_image_url"], product_local_path)
        await download_from_url(parsed_data["copyright_image_url"], ip_local_path)

        # Reconstruct the prompt list for the AI call
        prompt_list = [
            ("text", f'\n\n3 different lawyers wrote an infringement assessment report to assess this product image: '),
            ("image_path", product_local_path),
            ("text", f"\n\ninfringing on this copyrighted picture:"),
            ("image_path", ip_local_path),
            ("text", f'\n\nYour job is to score each of the 3 reportsbased on these critira: 
            Report depth – Do they identify protected elements, compare them to your product, cite case law, and analyze fair-use/exceptions?				
Clarity & plain-English explanations – Can a non-lawyer on your team understand the risk and next steps?				
Actionable recommendations – Concrete design changes, licensing leads, or compliance steps.'),
        ]
        
        i = 1
        for run in runs:
            for observation in run.observations:
                if item == item:
                    prompt_list.append(("text", f"Report_{i}"))
                    prompt_list.append(("text", observation.output))
                    i = i+1
                    
        prompt_list.append(("text", 'Provide your conclusion in this format {"Report_1": xx, "Report_2": yy, "Report_3":zz}'))

        
        image_urls = ["", "", "", parsed_data["product_image_url"], "", parsed_data["copyright_image_url"]]
        
        # V3 Change: Use item.run() to link the execution to the dataset item.
        # This creates a 'run' which is the root span of the new trace.
        with item.run(run_name=experiment_name) as run:
            ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_urls, model_name="gemini-2.5-pro", useVertexAI=False)
            
            # V3 Change: Update the run (span object) directly.
            run.update(
                input={'prompt_list': [p[1] for p in prompt_list if p[0] == 'text'], 'image_urls': image_urls},
                output=ai_answer
            )
            # V3 Change: Use the global client to update trace-level attributes.
            get_client().update_current_trace(
                session_id=f"Prompt={prompt_name}, Dataset={DATASET_NAME}"
            )
        return ai_answer

    except Exception as e:
        print(f"Error processing item {item.id} for prompt {prompt_name}: {e}")
        return None

async def run_copyright_experiment(experiment_name, prompt_name, prompt_content):
    """
    Runs a full experiment for a given prompt against the entire dataset.
    """
    # V3 Change: Get the global singleton client.
    langfuse_client = get_client()
    
    # Optional but recommended check.
    if not langfuse_client.auth_check():
        print("Langfuse authentication failed. Please check your credentials.")
        return

    try:
        dataset = langfuse_client.get_dataset(DATASET_NAME)
    except Exception as e:
        print(f"Failed to get dataset '{DATASET_NAME}': {e}")
        return

    tasks = []
    with tempfile.TemporaryDirectory() as temp_dir:
        for item in dataset.items:
            tasks.append(run_prompt_on_item(experiment_name, prompt_name, prompt_content, item, temp_dir))
        
        await asyncio.gather(*tasks)

    langfuse_client.flush()

async def main():
    """Main function to run the copyright prompt experiments."""
    print("--- Running Copyright Prompt Experiment ---")
    for prompt_file in PROMPT_FILES:
        prompt_path = os.path.join(PROMPTS_DIR, prompt_file)
        prompt_name = os.path.splitext(prompt_file)[0]
        
        try:
            with open(prompt_path, "r", encoding="utf-8") as f:
                prompt_content = f.read()
        except FileNotFoundError:
            print(f"Error: Prompt file not found at {prompt_path}. Skipping.")
            continue
            
        experiment_name = f"Copyright Experiment - {prompt_name}"
        print(f"\n--- Starting Experiment: {experiment_name} ---")
        
        await run_copyright_experiment(experiment_name, prompt_name, prompt_content)
        
        print(f"--- Finished Experiment: {experiment_name} ---")

if __name__ == "__main__":
    asyncio.run(main())