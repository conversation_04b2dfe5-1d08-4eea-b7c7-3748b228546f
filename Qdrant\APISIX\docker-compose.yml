networks:
  qdrant_monitoring:
    external: true
    name: qdrant_monitoring

services:
  # etcd for APISIX config storage (single node)
  etcd:
    image: quay.io/coreos/etcd:v3.5.12
    command: ["/usr/local/bin/etcd","--data-dir=/etcd-data","--advertise-client-urls=http://0.0.0.0:2379","--listen-client-urls=http://0.0.0.0:2379"]
    volumes:
      - etcd-data:/etcd-data
    networks: [qdrant_monitoring]
    restart: unless-stopped

  # Redis for shared rate-limit counters across APISIX nodes
  redis:
    image: redis:7
    command: ["redis-server", "--appendonly", "yes"]
    networks: [qdrant_monitoring]
    restart: unless-stopped

  # Apache APISIX (edge listeners on 80/443; Admin internal but published)
  apisix:
    image: apache/apisix:3.13.0-debian
    depends_on: [etcd, redis]
    ports:
      - "80:9080"     # HTTP edge
      - "443:9443"    # HTTPS edge (HTTP/2)
      - "9180:9180"   # Admin API (secure in prod)
      - "9091:9091"   # Prometheus metrics (plugin exposes this)
    environment:
      # Read admin key etc. from env-file instead of hardcoding
      - APISIX_ADMIN_KEY=${APISIX_ADMIN_KEY}
      - APISIX_DEPLOYMENT_ETCD_HOST=["http://etcd:2379"]
    volumes:
      - ./config.yaml:/usr/local/apisix/conf/config.yaml:ro
      - apisix-logs:/usr/local/apisix/logs
      - /ssl:/ssl:ro
    networks: [qdrant_monitoring]
    restart: unless-stopped

volumes:
  etcd-data:
  apisix-logs: