FROM python:3.12-bullseye

ENV DEBIAN_FRONTEND=noninteractive


# Install system & GUI dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    xvfb \
    libxi6 \
    libgconf-2-4 \
    libnss3 \
    libxss1 \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libgtk-3-0 \
    libgbm1 \
    x11-utils \
    fonts-dejavu \
    fonts-liberation \
    fonts-freefont-ttf \
    locales \
    net-tools \
    apt-transport-https \
    ca-certificates \
    tmux \
    openssh-client \
    git \
    openssh-server \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Set timezone & locale
ENV TZ=America/New_York
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8
RUN sed -i -e "s/# $LANG.*/$LANG UTF-8/" /etc/locale.gen && \
    dpkg-reconfigure --frontend=noninteractive locales && \
    update-locale LANG=$LANG

# Install Google Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" \
    > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && apt-get install -y google-chrome-stable && \
    rm -rf /var/lib/apt/lists/*

ENV CHROME_BIN=/usr/bin/google-chrome
ENV CHROME_DRIVER=/usr/bin/chromedriver

# SSH config
RUN mkdir /var/run/sshd && \
    echo 'root:trosdc2024' | chpasswd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && \
    sed -i 's/#ListenAddress 0.0.0.0/ListenAddress 0.0.0.0/' /etc/ssh/sshd_config && \
    sed -i 's/#Port 22/Port 2223/' /etc/ssh/sshd_config

# Working directory
WORKDIR /app
ENV PYTHONPATH=/app

# Install Python dependencies
COPY requirements_spoof.txt .
RUN pip install --prefer-binary --default-timeout=100 --retries=10 --no-cache-dir -r requirements_spoof.txt


# Copy app code
COPY . .

# Create directories
RUN mkdir -p /app/data/chrome_user_data /app/data/db
RUN mkdir -p /root/.local/share/undetected_chromedriver && \
    cp /usr/bin/google-chrome /root/.local/share/undetected_chromedriver/chromedriver_copy || true

# Expose ports
EXPOSE 5000 2223 5555

# Entry script
RUN chmod +x start.sh
# CMD ["xvfb-run", "-a", "--server-args=-screen 0 1920x1080x24", "./start.sh"]
CMD xvfb-run -a --server-args="-screen 0 1920x1080x24" ./start.sh