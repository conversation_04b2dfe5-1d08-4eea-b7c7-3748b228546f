#!/usr/bin/env bash
# -----------------------------------------------------------------------------
#  Sync Vast.ai public IP + HostPort ➜ NGINX include file
#  - Fetches instance details via Vast REST API
#  - Extracts .public_ipaddr and HostPort for INTERNAL_PORT (default: 5000)
#  - Updates /etc/nginx/conf.d/vast-dynamic.conf atomically
#  - Reloads NGINX only when values change
#
#  Requirements: curl, jq, nginx in $PATH
#  chmod +x /root/qdrant/vast_updater.sh
#  echo "*/2 * * * * /root/qdrant/vast_updater.sh >> /var/log/vast-sync.log 2>&1" | crontab -
#  Check the logs: tail -n 5 /var/log/vast-sync.log
# -----------------------------------------------------------------------------
set -euo pipefail

### --- user-specific settings -----------------------------------------------
VAST_API_KEY="041d67de00859f74fd6c917169213ae9782535880a323348f57de8fcdd9f0b97"
# leave empty to auto-discover “the only running instance” on the account
# INSTANCE_ID="${INSTANCE_ID:-********}"
INTERNAL_PORT="${INTERNAL_PORT:-5000}"

# Need absolute path because cron job might run from another directory
NGINX_INC="/root/qdrant/vast-dynamic.conf"
CACHE_DIR="/var/cache/vast-sync"
mkdir -p "$CACHE_DIR"
STATE_FILE="$CACHE_DIR/last_ip_port"

COMPOSE_SERVICE="nginx-proxy"                 # service name in compose.yml
DC="/usr/bin/docker compose"

### --- helper ---------------------------------------------------------------
fatal() { echo "[vast-sync] $*" >&2; exit 1; }

api_get() {
  local url=$1
  curl -sS --fail -H "Authorization: Bearer $VAST_API_KEY" \
       -H "Accept: application/json" "$url"
}

### 2. Determine instance id if not provided ----------------------------------
if [[ -z "${INSTANCE_ID:-}" ]]; then
  INSTANCE_ID=$(api_get "https://console.vast.ai/api/v0/instances/" \
               | jq -r '.instances[] | select(.actual_status=="running") | .id' \
               | head -n1)
  [[ -n "$INSTANCE_ID" ]] || fatal "No running instance found"
fi

### 3. Fetch instance JSON ----------------------------------------------------
json_raw=$(api_get "https://console.vast.ai/api/v0/instances/${INSTANCE_ID}/")

# Vast sometimes returns trailing commas – strip them to keep jq happy
json=$(echo "$json_raw" | sed -Ee 's/,\s*([}\]])/\1/g')

### 4. Parse public IP and HostPort ------------------------------------------
PUBLIC_IP=$(echo "$json" | jq -r '.instances.public_ipaddr // .public_ipaddr')
PORT=$(echo "$json" \
       | jq -r --arg p "${INTERNAL_PORT}/tcp" \
             '.instances.ports[$p][0].HostPort
              // .ports[$p][0].HostPort')

[[ $PUBLIC_IP =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]      || fatal "Bad IP: $PUBLIC_IP"
[[ $PORT =~ ^[0-9]+$ ]]                                   || fatal "Bad port: $PORT"

CURRENT="${PUBLIC_IP}:${PORT}"
LAST=$(cat "$STATE_FILE" 2>/dev/null || echo "none")

if [[ "$CURRENT" == "$LAST" ]]; then
  echo "[vast-sync] unchanged (${CURRENT})"
  exit 0
fi

### 5. Write include with locking & rollback ----------------------------------
# --- in-place writer for single-file mount ---
LOCK=/var/lock/vast-sync.lock
exec 9>"$LOCK"
flock -n 9 || { echo "[vast-sync] another run active"; exit 0; }

tmp=$(mktemp)
printf 'set $VAST_IP   %s;\nset $VAST_PORT %s;\n' "$PUBLIC_IP" "$PORT" > "$tmp"

# No-op if unchanged
if [ -f "$NGINX_INC" ] && cmp -s "$tmp" "$NGINX_INC"; then
  echo "[vast-sync] unchanged ($CURRENT)"; rm -f "$tmp"; exit 0
fi

# Backup current contents for rollback
bak=$(mktemp)
[ -f "$NGINX_INC" ] && cat "$NGINX_INC" > "$bak" || :

# In-place overwrite (keeps inode)
truncate -s 0 "$NGINX_INC"
dd if="$tmp" of="$NGINX_INC" conv=fsync status=none

# Validate & reload inside container
if $DC exec -T "$COMPOSE_SERVICE" nginx -t >/dev/null; then
  $DC exec -T "$COMPOSE_SERVICE" nginx -s reload
  echo "$CURRENT" > "$STATE_FILE"
  echo "[vast-sync] updated → $CURRENT (reloaded)"
  rm -f "$tmp" "$bak"
else
  echo "[vast-sync] nginx -t failed; rolling back"
  dd if="$bak" of="$NGINX_INC" conv=fsync status=none
  rm -f "$tmp" "$bak"
  exit 1
fi
