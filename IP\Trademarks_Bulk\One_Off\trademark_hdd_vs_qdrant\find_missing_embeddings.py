#!/usr/bin/env python3
"""
Find Missing Embeddings Tool

This script identifies trademark records with mark_feature_code 2, 3, 5 that:
- Exist in database
- Have image files on HDD
- Are missing embeddings in Qdrant

Output:
- Terminal summary with table (similar to lookup_serial_number.py)
- CSV file with detailed records

Reuses existing functions to avoid code duplication.
"""

import os
import sys
import time
import datetime
import csv
import concurrent.futures
from logdata import log_message
from dotenv import load_dotenv
from qdrant_client import QdrantClient

# Import database functions (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from trademark_db import get_db_connection

# Import image utility functions (reusing existing code)
from trademark_image import get_image_subdirectory

# Import Common constants and UUID utilities (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..','..', '..'))
from Common.Constants import local_ip_folder

# Import UUID generation function (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..','..','..', '..', 'Qdrant', 'api', 'Common'))
from Common.uuid_utils import generate_uuid

# Load environment variables
load_dotenv()

# Configuration
BASE_DIR = os.path.join(local_ip_folder, "Trademarks")
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")

# Qdrant configuration
QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = "IP_Assets"

# Output configuration
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), 'missing_embeddings_results')
os.makedirs(OUTPUT_DIR, exist_ok=True)


def get_total_eligible_records():
    """
    Get total count of trademark records that have mark_feature_code in [2, 3, 5].
    Reuses pattern from existing scripts.
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT COUNT(*) FROM trademarks 
            WHERE mark_feature_code IN (2, 3, 5)
        """)
        
        total = cursor.fetchone()[0]
        return total
        
    except Exception as e:
        log_message(f"Error getting total eligible records: {str(e)}", level='ERROR')
        return 0
    finally:
        if conn:
            conn.close()


def get_trademark_records_batch(limit=5000, offset=0):
    """
    Get a batch of trademark records efficiently.
    Reuses pattern from check_existing_images.py
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT ser_no, mark_feature_code, image_source, mark_text
            FROM trademarks
            WHERE mark_feature_code IN (2, 3, 5)
            ORDER BY ser_no
            LIMIT %s OFFSET %s
        """, (limit, offset))

        records = cursor.fetchall()

        # Convert to list of dictionaries
        result = []
        for record in records:
            result.append({
                'ser_no': record[0],
                'mark_feature_code': record[1],
                'image_source': record[2],
                'mark_text': record[3]
            })
            
        return result
        
    except Exception as e:
        log_message(f"Error fetching batch records: {str(e)}", level='ERROR')
        return []
    finally:
        if conn:
            conn.close()


def check_hdd_existence(ser_no):
    """
    Check if image file exists on HDD.
    Reuses logic from lookup_serial_number.py
    """
    try:
        image_sub_dir = get_image_subdirectory(ser_no)
        if not image_sub_dir:
            return False
            
        image_path = os.path.join(IMAGES_DIR, image_sub_dir, f"{ser_no}.webp")
        return os.path.exists(image_path)
        
    except Exception as e:
        return False


def check_qdrant_existence(ser_no):
    """
    Check if embedding exists in Qdrant.
    Reuses logic from lookup_serial_number.py
    """
    try:
        client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=30)
        point_id = generate_uuid(ser_no)
        
        # Try to retrieve the point
        points = client.retrieve(
            collection_name=COLLECTION_NAME,
            ids=[point_id],
            with_payload=False,
            with_vectors=False
        )
        
        return points and len(points) > 0
        
    except Exception as e:
        return False


def find_missing_embedding_record(record):
    """
    Check if record has HDD file but missing Qdrant embedding.
    
    Args:
        record (dict): Database record
        
    Returns:
        dict or None: Record info if missing embedding, None otherwise
    """
    ser_no = record['ser_no']
    
    # Check HDD and Qdrant existence
    hdd_exists = check_hdd_existence(ser_no)
    qdrant_exists = check_qdrant_existence(ser_no)
    
    # Only return if HDD exists but Qdrant doesn't
    if hdd_exists and not qdrant_exists:
        return {
            'ser_no': ser_no,
            'mark_feature_code': record['mark_feature_code'],
            'image_source': record['image_source'],
            'mark_text': record['mark_text'],
            'db_status': '✅ Found',
            'hdd_status': '✅ Found',
            'qdrant_status': '❌ Missing'
        }
    
    return None


def process_batch_for_missing_embeddings(records):
    """
    Process a batch of records to find missing embeddings.
    Uses parallel processing for I/O operations.
    """
    missing_embeddings = []
    
    # Use ThreadPoolExecutor for I/O bound operations
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # Submit all analysis tasks
        future_to_record = {
            executor.submit(find_missing_embedding_record, record): record 
            for record in records
        }
        
        # Collect results
        for future in concurrent.futures.as_completed(future_to_record):
            try:
                result = future.result()
                if result:  # Only add if missing embedding found
                    missing_embeddings.append(result)
            except Exception as e:
                record = future_to_record[future]
                log_message(f"Error analyzing record {record['ser_no']}: {str(e)}", level='WARNING')
    
    return {
        'total_processed': len(records),
        'missing_embeddings_found': len(missing_embeddings),
        'missing_embeddings': missing_embeddings
    }


def print_table_summary(results):
    """
    Print results in a table format similar to lookup_serial_number.py
    """
    if not results:
        print("\n🎉 No missing embeddings found!")
        return

    # Table headers
    headers = ["Serial Number", "Mark Feature", "Database", "HDD", "Qdrant"]

    # Calculate column widths
    col_widths = [max(len(str(header)), 12) for header in headers]
    for result in results:
        col_widths[0] = max(col_widths[0], len(result['ser_no']))
        col_widths[1] = max(col_widths[1], len(str(result['mark_feature_code'])))
        col_widths[2] = max(col_widths[2], 8)  # "✅ Found"
        col_widths[3] = max(col_widths[3], 8)
        col_widths[4] = max(col_widths[4], 10)  # "❌ Missing"

    # Print table
    print("\n" + "=" * (sum(col_widths) + len(headers) * 3 + 1))
    print(f"📊 MISSING EMBEDDINGS SUMMARY TABLE")
    print("=" * (sum(col_widths) + len(headers) * 3 + 1))

    # Print headers
    header_row = "| "
    for i, header in enumerate(headers):
        header_row += f"{header:<{col_widths[i]}} | "
    print(header_row)

    # Print separator
    separator = "|-"
    for width in col_widths:
        separator += f"{'-'*width}-|-"
    print(separator[:-1])

    # Print data rows (limit to first 20 for readability)
    display_count = min(len(results), 20)
    for i, result in enumerate(results[:display_count]):
        row = f"| {result['ser_no']:<{col_widths[0]}} | "
        row += f"{result['mark_feature_code']:<{col_widths[1]}} | "
        row += f"{result['db_status']:<{col_widths[2]}} | "
        row += f"{result['hdd_status']:<{col_widths[3]}} | "
        row += f"{result['qdrant_status']:<{col_widths[4]}} |"
        print(row)

    if len(results) > 20:
        print(f"| ... and {len(results) - 20} more records (see CSV file for complete list)")

    print(f"{'=' * (sum(col_widths) + len(headers)*3 + 1)}")


def save_missing_embeddings_to_csv(missing_embeddings, filename):
    """
    Save missing embeddings to CSV file.
    """
    if not missing_embeddings:
        log_message("No missing embeddings to save", level='INFO')
        return
    
    csv_path = os.path.join(OUTPUT_DIR, filename)
    
    try:
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'ser_no', 'mark_feature_code', 'image_source', 'mark_text',
                'db_status', 'hdd_status', 'qdrant_status'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in missing_embeddings:
                writer.writerow(record)
        
        log_message(f"✅ Missing embeddings saved to: {csv_path}", level='INFO')
        log_message(f"📊 Total records saved: {len(missing_embeddings)}", level='INFO')
        
    except Exception as e:
        log_message(f"❌ Error saving CSV: {str(e)}", level='ERROR')


def print_summary_statistics(total_processed, missing_embeddings):
    """
    Print summary statistics in terminal.
    """
    print(f"\n{'='*80}")
    print(f"🧠 MISSING EMBEDDINGS ANALYSIS SUMMARY")
    print(f"{'='*80}")

    print(f"\n📋 PROCESSING STATISTICS:")
    print(f"   Total records analyzed: {total_processed:,}")
    print(f"   Records with missing embeddings: {len(missing_embeddings):,}")

    if missing_embeddings:
        # Count by mark_feature_code
        feature_counts = {}
        for record in missing_embeddings:
            code = record['mark_feature_code']
            feature_counts[code] = feature_counts.get(code, 0) + 1

        print(f"\n🏷️ BREAKDOWN BY MARK FEATURE CODE:")
        for code in sorted(feature_counts.keys()):
            count = feature_counts[code]
            percentage = (count / len(missing_embeddings)) * 100
            print(f"   Mark Feature Code {code}: {count:,} ({percentage:.1f}%)")
    else:
        print(f"\n🎉 EXCELLENT! All records with HDD images have embeddings in Qdrant!")


def process_all_records_in_batches(batch_size=5000):
    """
    Process all eligible trademark records in batches to find missing embeddings.
    """
    # Get total count first
    total_records = get_total_eligible_records()
    if total_records == 0:
        log_message("No eligible records found", level='WARNING')
        return

    log_message(f"Found {total_records:,} total eligible records", level='INFO')

    # Calculate number of batches
    total_batches = (total_records + batch_size - 1) // batch_size

    # Initialize counters
    total_processed = 0
    all_missing_embeddings = []

    # Process each batch
    current_offset = 0
    start_time = time.time()

    for batch_num in range(total_batches):
        batch_start_time = time.time()

        log_message(f"\n===== BATCH {batch_num+1}/{total_batches} =====", level='INFO')
        log_message(f"Processing records {current_offset+1:,}-{min(current_offset+batch_size, total_records):,}", level='INFO')

        # Get this batch of records
        records = get_trademark_records_batch(limit=batch_size, offset=current_offset)

        if not records:
            log_message("No more records to process", level='INFO')
            break

        # Process this batch for missing embeddings
        batch_stats = process_batch_for_missing_embeddings(records)

        # Update totals
        total_processed += batch_stats['total_processed']
        all_missing_embeddings.extend(batch_stats['missing_embeddings'])

        # Calculate progress and ETA
        batch_time = time.time() - batch_start_time
        elapsed_time = time.time() - start_time
        progress_percent = ((batch_num + 1) / total_batches) * 100

        if batch_num > 0:  # Avoid division by zero
            avg_batch_time = elapsed_time / (batch_num + 1)
            remaining_batches = total_batches - (batch_num + 1)
            eta_seconds = remaining_batches * avg_batch_time
            eta_str = str(datetime.timedelta(seconds=int(eta_seconds)))
        else:
            eta_str = "Calculating..."

        log_message(f"Batch completed in {batch_time:.1f}s", level='INFO')
        log_message(f"Found {batch_stats['missing_embeddings_found']} missing embeddings in this batch", level='INFO')
        log_message(f"Progress: {progress_percent:.1f}% | ETA: {eta_str}", level='INFO')

        current_offset += batch_size

    # Generate timestamp for output files
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"missing_embeddings_{timestamp}.csv"

    # Save results to CSV
    save_missing_embeddings_to_csv(all_missing_embeddings, csv_filename)

    # Print summary statistics
    print_summary_statistics(total_processed, all_missing_embeddings)

    # Print table summary
    print_table_summary(all_missing_embeddings)

    # Final summary
    total_time = time.time() - start_time
    log_message(f"\n✅ Analysis completed in {total_time:.1f} seconds", level='INFO')
    log_message(f"📁 Results saved to: {OUTPUT_DIR}/{csv_filename}", level='INFO')


if __name__ == "__main__":
    # Configuration parameters
    BATCH_SIZE = 10000       # Number of records per batch

    log_message("🧠 Starting Missing Embeddings Analysis", level='INFO')
    log_message(f"📁 Output directory: {OUTPUT_DIR}", level='INFO')
    print("=" * 50)

    # Run full analysis
    process_all_records_in_batches(batch_size=BATCH_SIZE)

    log_message("\n🎉 Missing embeddings analysis completed!", level='INFO')
