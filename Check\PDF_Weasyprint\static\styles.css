/* ======== A4 + Theme ======== */
:root{
  --page-w:210mm;
  --page-h:297mm;

  --navy:#0E2741;
  --orange:#EF7A1A;
  --ink:#111827;
  --muted:#4B5563;
  --line:#D1D5DB;
  --bg:#5c5c5c;
  --accent-bg: #F3F4F6;

  --hpad:18mm;
  --vpad:16mm;
  --header-h:12mm;
  --footer-h:10mm;

  --radius:3px;
}

/* Hard A4 pages — margins account for header/footer boxes */
@page{
  size: A4;
  margin: 10mm;
}

@page:first {
  @top-center   { content: none; }
  @bottom-center{ content: none; }
}

/* @page {
  @top-center   { content: element(pdf-header); }
  @bottom-center{ content: element(pdf-footer); }
} */

@page {
  @top-center   { content: none; }
  @bottom-center{ content: none; }
}

/* Screen preview: make pages look like A4 sheets */
/* @media screen{
  html,body{background:#f3f4f6;}
  .result-page{
    Page content area simulation
    padding: var(--vpad) var(--hpad);
  }
} */

.result-page{ 
  border: 1mm solid var(--navy);
  height: 275mm; /* 297mm - 2*10mm page margin - 2*1mm border */
  width: 188mm; /* 210mm - 2*10mm page margin - 2*1mm border */
  background:#e7e7e7;
  margin:0mm auto;
  box-sizing: border-box;
  orphans:3; 
  widows:3; 
}

/* Body text */
body{
  font-family: "Helvetica", Arial, sans-serif;  /* core PDF font → tiny files */
  color:var(--ink);
  font-size:10pt;
  line-height:1.45;
}

/* ======== Running header/footer ======== */
.pdf-header{
  position: running(pdf-header);
  height: var(--header-h);
  padding-top: 2mm;
  margin: 0;
  display:flex; align-items:center; justify-content:space-between;
  border-bottom: 0.3mm solid var(--line);
  box-sizing: border-box;
  background: var(--bg);
  padding-left: var(--hpad);
  padding-right: var(--hpad);
}
.hdr-left{ display:flex; align-items:center; gap:6mm; }
.hdr-logo{ height:9mm; }
.hdr-company{
  font-weight:700; font-size:9.5pt; color:var(--navy);
  white-space:nowrap; overflow:hidden; text-overflow:ellipsis;
  max-width: 130mm;  /* prevents wrap → keeps header height stable */
}
.confidential{ color:var(--orange); font-weight:800; letter-spacing:.6px; }

.pdf-footer{
  position: running(pdf-footer);
  height: var(--footer-h);
  margin: 0 var(--hpad);
  display:flex; align-items:center; justify-content:space-between;
  border-top: 0.3mm solid var(--line);
  color: var(--muted);
  font-size: 9pt;
  box-sizing: border-box;
}
.page-number:before{ content: counter(page); }
.page-count:before{ content: counter(pages); }

hr{ border:none; border-top: 0.3mm solid var(--line); margin: 6mm 0; }

/* ======== Result page ======== */
.page{ page-break-after: always; }

.result-page .result-header{
  display:grid; grid-template-columns: 1.5fr 2fr; gap:6mm;
  /* border: 0.3mm solid var(--line); */
  /* padding: 6mm; */
  border-radius: var(--radius);
  background: var(--accent-bg);
}
.result-header .left{ display:flex; align-items:center; gap:6mm; }
.chip{
  border:0.3mm solid var(--navy); color:var(--navy);
  padding:2pt 6pt; border-radius:9999px; font-weight:800; font-size:9pt; text-transform:uppercase;
}
.result-header .row{ display:flex; gap:4mm; margin:2pt 0; }
.result-header .key{ color:var(--muted); min-width:32mm; }

.ip-and-product{
  display:grid; grid-template-columns: 1.5fr 1fr; gap:6mm; margin: 6mm 0;
  break-inside: avoid;
}
.ip-grid{ display:grid; grid-template-columns: repeat(3, 1fr); gap:4mm; }
.ip-thumb{ border:0.3mm solid var(--line); border-radius:var(--radius); overflow:hidden; background:#fff; }
.ip-thumb img{
  display:block; width:100%;
  height: 36mm;          /* fixed → consistent figure strip */
  object-fit: contain; background:white;
}
.product-shot{ border:0.3mm solid var(--line); border-radius:var(--radius); overflow:hidden; background:#fff; }
.product-shot img{
  display:block; width:100%;
  height: 72mm;          /* fixed → tidy */
  object-fit: cover;
}
.report-block{ margin-top: 5mm; break-inside: avoid; }
.report-block .md h1,.report-block .md h2,.report-block .md h3{ color:var(--navy); margin: 4pt 0 2pt 0; }
.report-block .md table{ border-collapse: collapse; width:100%; font-size:9.5pt; }
.report-block .md th,.report-block .md td{ border:0.3mm solid var(--line); padding:3pt 5pt; }
.report-block .md blockquote{ border-left:1mm solid var(--navy); padding-left:4mm; color:#374151; }
.report-block .md p{ margin: 4pt 0; }



/* ======== Disclaimer ======== */
.disclaimer{
  padding-top: 5mm;
  border-top: 0.3mm solid var(--line);
}
.disclaimer h2{ color:var(--navy); margin-top:0; }
