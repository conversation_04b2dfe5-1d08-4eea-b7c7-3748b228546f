#!/bin/bash
touch ~/.no_auto_tmux
cd /workspace/
git init
git remote add origin https://sergedc%40sergedc.com:<EMAIL>/sergedc/TRO-USside.git
git fetch origin
git reset --hard origin/Refactoring
chmod 600 /workspace/.ssh/id_rsa
mkdir /workspace/AI/GoogleCouldServiceAccountKey
cp /workspace/Check/Documentation/vast_reboot.sh /root/onstart.sh
chmod +x /root/onstart.sh
source ./set_env.sh
apt-get update
apt-get install -y redis-server
apt-get install -y speedtest-cli
apt-get install -y ncdu
pip install -r requirementsGPU.txt
python3 -m pip install paddlepaddle-gpu==3.2 -i https://www.paddlepaddle.org.cn/packages/stable/cu118/
speedtest
cloudflared service install eyJhIjoiYzA3NWM0MzIxMjJkZjliY2ZmMjJkZGVmMzlkZjAxNzAiLCJ0IjoiYzNlMGRiZWItZDdiNi00ODViLTlkNzctOGRmNGFiNTc3YjM2IiwicyI6Ik4ySTVPRFE1TlRJdE5XUXhNaTAwTmpVNUxUZ3haalF0TlRZMk1ETXlZVEV5TWpZeCJ9
pip cache purge
echo "copy .env, google cloud service accounts, Create ~/.cloudflared/config.yaml"