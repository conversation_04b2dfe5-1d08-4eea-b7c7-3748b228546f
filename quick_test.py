#!/usr/bin/env python3
"""
Quick test to submit an invalid URL and check the response
"""

import requests
import json
import time

# Test configuration
BASE_URL = "https://api.maidalv.com:5088"
API_KEY = "37457-48774-8887-1882"

def test_invalid_url():
    """Submit invalid URL and check response"""
    
    test_data = {
        "api_key": API_KEY,
        "main_product_image": "https://au.triumph.com/cdn/shop/files/Pure-Invisible-Wired-Pad",  # Invalid - no extension
        "other_product_images": [],
        "ip_images": [],
        "ip_keywords": ["test"],
        "description": "Test product",
        "reference_text": "",
        "reference_images": [],
        "language": "en"
    }
    
    print("Submitting invalid URL test...")
    
    # Submit the check request
    response = requests.post(f"{BASE_URL}/check_api", 
                           headers={"Content-Type": "application/json"},
                           json=test_data)
    
    if response.status_code != 200:
        print(f"❌ Initial request failed with status {response.status_code}")
        print(f"Response: {response.text}")
        return
        
    result = response.json()
    check_id = result.get('check_id')
    
    print(f"✅ Check submitted. Check ID: {check_id}")
    
    # Poll a few times to see the response
    for i in range(5):
        time.sleep(3)
        
        status_response = requests.get(f"{BASE_URL}/check_status/{check_id}")
        
        if status_response.status_code != 200:
            print(f"Poll {i+1}: Status check failed with {status_response.status_code}")
            print(f"Response: {status_response.text}")
            continue
            
        status_result = status_response.json()
        print(f"Poll {i+1}: {json.dumps(status_result, indent=2)}")
        
        if status_result.get('status') in ['completed', 'error']:
            break

if __name__ == "__main__":
    test_invalid_url()
