document.addEventListener('DOMContentLoaded', function() {
    const tableBody = document.querySelector('#api-keys-table tbody');
    const addKeyForm = document.getElementById('add-key-form');

    // Fetch and display API keys on page load
    fetchApiKeys();

    // Handle form submission for adding a new key
    addKeyForm.addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(addKeyForm);
        const data = {
            client_type: formData.get('client_type'),
            client_name: formData.get('client_name'),
            rate_limit: formData.get('rate_limit'),
            daily_limit: formData.get('daily_limit')
        };

        fetch('/api/api_keys', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        })
        .then(response => response.json())
        .then(result => {
            if (result.status === 'success') {
                fetchApiKeys(); // Refresh the list
                addKeyForm.reset();
            } else {
                alert('Error adding key: ' + result.error);
            }
        })
        .catch(error => console.error('Error:', error));
    });

    // Function to fetch and render API keys
    function fetchApiKeys() {
        fetch('/api/api_keys')
            .then(response => response.json())
            .then(data => {
                tableBody.innerHTML = ''; // Clear existing rows
                data.sort((a, b) => a.client_id - b.client_id);
                data.forEach(key => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${key.api_key}</td>
                        <td>${key.client_name}</td>
                        <td>${key.client_id}</td>
                        <td>${new Date(key.created_at).toLocaleString()}</td>
                        <td>${key.total_usage}</td>
                        <td>${key.current_month_usage}</td>
                        <td>${key.previous_month_usage}</td>
                        <td>
                            <button type="button" class="btn danger small delete-btn" data-api-key="${key.api_key}" ${key.total_usage > 0 ? 'disabled' : ''}>Delete</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                // Add event listeners to delete buttons
                document.querySelectorAll('.delete-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const apiKey = this.dataset.apiKey;
                        if (confirm(`Are you sure you want to delete API key: ${apiKey}?`)) {
                            deleteApiKey(apiKey);
                        }
                    });
                });
            })
            .catch(error => console.error('Error:', error));
    }

    // Function to delete an API key
    function deleteApiKey(apiKey) {
        fetch(`/api/api_keys/${apiKey}`, {
            method: 'DELETE',
        })
        .then(response => response.json())
        .then(result => {
            if (result.status === 'success') {
                fetchApiKeys(); // Refresh the list
            } else {
                alert('Error deleting key: ' + result.error);
            }
        })
        .catch(error => console.error('Error:', error));
    }

    // Handle refresh keys button click
    const refreshBtn = document.getElementById('refresh-keys-btn');
    const refreshStatusDiv = document.getElementById('refresh-status');

    refreshBtn.addEventListener('click', function() {
        if (!confirm('Are you sure you want to refresh the API keys on all servers? This will reload the keys from the database.')) {
            return;
        }

        // Disable the button and show a loading indicator
        refreshBtn.disabled = true;
        refreshBtn.textContent = 'Refreshing...';
        refreshStatusDiv.innerHTML = '<p>Contacting servers...</p>';

        fetch('/api/api_keys/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(result => {
            let statusHtml = '<h4>Refresh Status:</h4>';
            for (const [server, res] of Object.entries(result)) {
                const statusClass = res.status === 'success' ? 'status-success' : 'status-error';
                statusHtml += `
                    <div class="${statusClass}">
                        <strong>${server.charAt(0).toUpperCase() + server.slice(1)} Server:</strong> ${res.status.toUpperCase()}<br>
                        <em>${res.message}</em>
                    </div>`;
            }
            refreshStatusDiv.innerHTML = statusHtml;
        })
        .catch(error => {
            console.error('Error:', error);
            refreshStatusDiv.innerHTML = `<div class="status-error"><strong>Error:</strong> An unexpected error occurred while refreshing the keys. Check the console for details.</div>`;
        })
        .finally(() => {
            // Re-enable the button
            refreshBtn.disabled = false;
            refreshBtn.textContent = 'Refresh Keys on Servers';
        });
    });
});