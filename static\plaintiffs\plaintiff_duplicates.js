// Menu activation is handled by common.js activateMenuItem('menu-plaintiffs')

// Main functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add back button event handler
    const backButton = document.getElementById('back-to-plaintiffs');
    if (backButton) {
        backButton.addEventListener('click', function() {
            // Assuming url_for('plaintiffs') generates '/plaintiffs' or similar
            // We need to hardcode or pass this URL from the template if needed
            // For now, let's assume it's '/plaintiffs'
            window.location.href = '/plaintiffs';
        });
    }

    // Load duplicate pairs
    loadDuplicates();
});

function loadDuplicates() {
    const loadingElement = document.querySelector('.loading');
    fetch('/get_duplicate_plaintiffs')
        .then(response => response.json())
        .then(data => {
            renderDuplicates(data.duplicates);
        })
        .catch(error => {
            console.error('Error fetching duplicates:', error);
            if (loadingElement) {
                loadingElement.textContent = 'Error loading duplicate plaintiffs. Please try again.';
            }
        });
}

function renderDuplicates(duplicates) {
    const container = document.getElementById('duplicates-list');
    const loadingElement = document.querySelector('.loading');
    if (!container) return; // Exit if container not found
    container.innerHTML = '';

    if (!duplicates || duplicates.length === 0) {
        if (loadingElement) {
            loadingElement.textContent = 'No duplicate plaintiffs found.';
        }
        return;
    }

    // Hide loading message
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }

    duplicates.forEach((pair, index) => {
        const card = document.createElement('div');
        card.className = 'duplicate-card';
        card.id = `duplicate-pair-${index}`;

        const header = document.createElement('div');
        header.className = 'duplicate-header';
        header.innerHTML = `<h3>Potential Duplicate ${index + 1}</h3>`;

        const content = document.createElement('div');
        content.className = 'duplicate-content';

        // Create comparison table
        const table = document.createElement('table');
        table.className = 'duplicate-table';
        table.innerHTML = `
            <thead>
                <tr>
                    <th></th>
                    <th>Option 1</th>
                    <th>Option 2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Name</td>
                    <td>${escapeHtml(pair.name1)}</td>
                    <td>${escapeHtml(pair.name2)}</td>
                </tr>
                <tr>
                    <td>ID</td>
                    <td>${pair.id1}</td>
                    <td>${pair.id2}</td>
                </tr>
                <tr>
                    <td>Case Count</td>
                    <td>${pair.cases1}</td>
                    <td>${pair.cases2}</td>
                </tr>
                <tr>
                    <td>Action</td>
                    <td>
                        <button class="btn primary keep-btn"
                            data-keep-id="${pair.id1}" data-delete-id="${pair.id2}" data-pair-index="${index}">
                            Keep This
                        </button>
                    </td>
                    <td>
                        <button class="btn primary keep-btn"
                            data-keep-id="${pair.id2}" data-delete-id="${pair.id1}" data-pair-index="${index}">
                            Keep This
                        </button>
                    </td>
                </tr>
            </tbody>
        `;

        content.appendChild(table);

        const resultDiv = document.createElement('div');
        resultDiv.className = 'merge-result';
        resultDiv.id = `result-${index}`;
        content.appendChild(resultDiv);

        card.appendChild(header);
        card.appendChild(content);
        container.appendChild(card);
    });

    // Add event listeners to the new buttons after they are added to the DOM
    container.querySelectorAll('.keep-btn').forEach(button => {
        button.addEventListener('click', handleMergeClick);
    });
}

function handleMergeClick(event) {
    const button = event.target;
    const keepId = parseInt(button.dataset.keepId, 10);
    const deleteId = parseInt(button.dataset.deleteId, 10);
    const pairIndex = parseInt(button.dataset.pairIndex, 10);
    mergePlaintiffs(keepId, deleteId, pairIndex);
}


function mergePlaintiffs(keepId, deleteId, pairIndex) {
    // Confirm action
    if (!confirm(`Are you sure you want to keep plaintiff ID ${keepId} and merge plaintiff ID ${deleteId} into it?`)) {
        return;
    }

    // Disable both buttons in the pair
    const buttons = document.querySelectorAll(`#duplicate-pair-${pairIndex} .keep-btn`);
    buttons.forEach(btn => {
        btn.disabled = true;
    });

    // Show loading message
    const resultDiv = document.getElementById(`result-${pairIndex}`);
    if (!resultDiv) return; // Exit if resultDiv not found
    resultDiv.className = 'merge-result loading';
    resultDiv.textContent = 'Merging plaintiffs...';

    // Call API to merge
    fetch('/merge_plaintiffs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            keep_id: keepId,
            delete_id: deleteId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            resultDiv.className = 'merge-result success';
            resultDiv.textContent = data.message;

            // Disable the whole card
            const card = document.getElementById(`duplicate-pair-${pairIndex}`);
            if (card) {
                card.classList.add('completed');
            }
        } else {
            resultDiv.className = 'merge-result error';
            resultDiv.textContent = data.message || 'An unknown error occurred.';

            // Re-enable buttons
            buttons.forEach(btn => {
                btn.disabled = false;
            });
        }
    })
    .catch(error => {
        resultDiv.className = 'merge-result error';
        resultDiv.textContent = `Error: ${error.message || 'Network error'}`;

        // Re-enable buttons
        buttons.forEach(btn => {
            btn.disabled = false;
        });
    });
}
// escapeHtml function is available in common.js