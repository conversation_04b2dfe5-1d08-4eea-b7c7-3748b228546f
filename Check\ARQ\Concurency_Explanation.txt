
ARQ: concurrency is async-IO based
arq Check.ARQ.worker.WorkerSettings launches a single worker using the WorkerSettings. 
The --watch Check flag just auto-reloads on file changes in that path; it doesn’t spin up multiple workers. 

Concurrency inside that one worker is controlled by max_jobs, which by default is 10 (“maximum number of jobs to run at a time”). 
So by default you get up to 10 concurrent asyncio tasks in a single process. 

ARQ’s concurrency is async-IO based (many tasks in one event loop), not CPU-core based. 
To run multiple workers: --workers 4 flag, it would start four separate worker processes, each with its own event loop and task limit (10 by default)
So ARQ can also be good for CPU intensive by using multiple workers.


Hypercorn: 
hypercorn app_apistudio:app --workers 4: means 4 processes. These processes are async in nature, so we get 4 event loops.
Each worker / process / event loop can handle multiple concurrent requests (no build in cap on the number of requests). 





Celery: concurrency is process based
Can run in thread or processes (process prefork by default, best for CPU intensive tasks)
