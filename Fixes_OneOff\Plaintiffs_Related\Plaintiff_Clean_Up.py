from IP.Patents_Bulk.patent_db_grant import get_db_connection
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from DatabaseManagement.Connections import get_gz_connection
from Alerts.Plaintiff import clean_plaintiff_name, add_plaintiff_id
from Alerts.ReprocessCases import reprocess_cases
from AI.GC_VertexAI import vertex_genai_multi, vertex_genai_multi_async
from AI.LLM_shared import get_json
import pandas as pd
import os
from logdata import log_message
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
import json, time, asyncio, os, fitz
import pandas as pd
from Alerts.DocketBird.Login import docketbird_get_logged_in_browser
from Alerts.DocketBird.Search_Cases import build_docketbird_url
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from Common import Constants
import numpy as np




# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗
def edit_plaintiff_id_in_postgresql_tables(original_plaintiff_id, new_plaintiff_id):
    """
    Edits the plaintiff_id field in copyrights, patents, and trademarks tables,
    and deletes records from domain_scrape for a given plaintiff_id.

    Args:
        original_plaintiff_id: The plaintiff ID to be replaced or deleted.
        new_plaintiff_id: The new plaintiff ID to set (or None for deletion in some tables).
    """
    connection = get_db_connection()
    cursor = connection.cursor()

    tables_to_update = ["copyrights", "patents", "trademarks"]
    for table in tables_to_update:
        if new_plaintiff_id is None:
            # If new_plaintiff_id is None, we are effectively deleting the association
            # For these tables, we'll set plaintiff_id to NULL
            print(f"Setting plaintiff_id to NULL for original_plaintiff_id {original_plaintiff_id} in {table}")
            cursor.execute(f"""UPDATE {table} SET plaintiff_id = NULL WHERE plaintiff_id = %s""", (original_plaintiff_id,))
        else:
            print(f"Updating plaintiff_id from {original_plaintiff_id} to {new_plaintiff_id} in {table}")
            cursor.execute(f"""UPDATE {table} SET plaintiff_id = %s WHERE plaintiff_id = %s""", (new_plaintiff_id, original_plaintiff_id))

    # For domain_scrape, delete the records
    print(f"Deleting records for plaintiff_id {original_plaintiff_id} from tb_domain_scrape")
    cursor.execute("""DELETE FROM tb_domain_scrape WHERE plaintiff_id = %s""", (original_plaintiff_id,))

    connection.commit()
    cursor.close()
    connection.close()
    print(f"Finished updating related tables for plaintiff_id {original_plaintiff_id}")



def delete_plaintiff_without_cases(df_cases, df_plaintiff, perform_delete=True):
    """
    Find plaintiffs without cases and optionally delete them

    Args:
        df_cases: DataFrame containing case data
        df_plaintiff: DataFrame containing plaintiff data
        perform_delete: If True, actually delete the plaintiffs; if False, just return the list

    Returns:
        List of dictionaries with id and plaintiff_name of plaintiffs without cases
    """
    plaintiffs_without_cases = []

    # Find all plaintiffs without cases
    for index, row in df_plaintiff.iterrows():
        nb_cases = df_cases[df_cases["plaintiff_id"] == row["id"]].shape[0]
        if nb_cases == 0:
            plaintiffs_without_cases.append({
                "id": int(row["id"]),
                "plaintiff_name": row["plaintiff_name"]
            })

    # If we're just returning the list, do that now
    if not perform_delete:
        return plaintiffs_without_cases

    # Otherwise, proceed with deletion
    if plaintiffs_without_cases:
        connection = get_gz_connection()
        cursor = connection.cursor()

        indices_to_drop = []

        # 1. Delete from the database
        for plaintiff in plaintiffs_without_cases:
            print(f"Deleting plaintiff: {plaintiff['id']} - {plaintiff['plaintiff_name']}")
            # Update related tables and then delete from tb_plaintiff
            edit_plaintiff_id_in_postgresql_tables(plaintiff["id"], None)
            cursor.execute("""DELETE FROM tb_plaintiff WHERE id = %s""", (plaintiff["id"],))
            indices_to_drop.append(plaintiff["id"])

        connection.commit()
        cursor.close()
        connection.close()

        # 2. Delete from the DataFrame in place using collected indices
        df_plaintiff.drop(indices_to_drop, inplace=True)

        print(f"There were {len(indices_to_drop)} plaintiffs without cases. All of them have been deleted.")

    return plaintiffs_without_cases


def list_cases_without_plaintiff(df_cases, df_plaintiff):
    count = 0
    for index, row in df_cases.iterrows():
        df_plaintiff_row = df_plaintiff[df_plaintiff["id"] == row["plaintiff_id"]]
        if df_plaintiff_row.empty:
            print(f"Case (id: {row['id']}): {row['docket']} - {row['plaintiff_id']} - {row['title']}")
            count += 1
    print(f"There are {count} cases without a plaintiff.")

def change_plaintiff_id_for_case(case_id, new_plaintiff_id, df_cases, df_plaintiff):
    case_row_df = df_cases[df_cases["id"] == case_id]
    df_cases.loc[df_cases["id"] == case_id, "plaintiff_id"] = new_plaintiff_id
    insert_and_update_df_to_GZ_batch(case_row_df, "tb_case", "id")

    # Use the centralized reprocessing function
    success, tracking_dict = asyncio.run(reprocess_cases(
        cases_to_reprocess=case_row_df,
        trace_name="Change Plaintiff ID",
        full_cases_df=df_cases,
        plaintiff_df=df_plaintiff
    ))

    if success:
        print(f"Successfully reprocessed case {case_id} with new plaintiff ID {new_plaintiff_id}")
    else:
        print(f"Reprocessing failed for case {case_id}")



def review_cases_mapped_to_nine(df_cases, df_plaintiff):
    df_cases_nine = df_cases[df_cases["plaintiff_id"] == 9]
    cases_df_need_to_be_updated = pd.DataFrame()
    for index, row in df_cases_nine.iterrows():
        plaintiff_name = clean_plaintiff_name(df_plaintiff, row["title"], row["plaintiff_names"])
        if plaintiff_name == "Unidentified Claimant":
            print(f"Case: {row['docket']} - {row['plaintiff_id']} - {row['title']} => \033[92m{plaintiff_name}\033[0m")  # Green
        else:
            print(f"Case: {row['docket']} - {row['plaintiff_id']} - {row['title']} => \033[91m{plaintiff_name}\033[0m")  # Red
            cases_df_need_to_be_updated = pd.concat([cases_df_need_to_be_updated, pd.DataFrame([row])], ignore_index=True)
    # If we improved the logic of "clean_plaintiff_name" then we must update the database: create new plaintiff and reprocess the cases to have the pictures in the right folder
    print(f"Cases to be updated: {cases_df_need_to_be_updated.shape[0]}")
    cases_df_need_to_be_updated["plaintiff_id"] = None
    asyncio.run(add_plaintiff_id(cases_df_need_to_be_updated, df_plaintiff))
    cases_df_need_to_be_updated.to_pickle("cases_df_need_to_be_updated.pkl")
    insert_and_update_df_to_GZ_batch(cases_df_need_to_be_updated, "tb_case", "id")

    # Use the centralized reprocessing function
    success, tracking_dict = asyncio.run(reprocess_cases(
        cases_to_reprocess=cases_df_need_to_be_updated,
        trace_name="Review Cases Mapped to Nine",
        full_cases_df=df_cases,
        plaintiff_df=df_plaintiff
    ))

    if success:
        print(f"Successfully reprocessed {len(cases_df_need_to_be_updated)} cases")
    else:
        print(f"Reprocessing completed with some failures")


def update_names_based_on_latest_sanitization(df_plaintiff):
    # Might need to run twice!
    for index, row in df_plaintiff.iterrows():
        current_name = row["plaintiff_name"]
        new_name = clean_plaintiff_name(df_plaintiff, row["plaintiff_name"] + " v. Schedule A")
        if new_name.lower() != current_name.lower():
            print(f"Updating {current_name} to {new_name}")
            df_plaintiff.at[index, "plaintiff_name"] = new_name

    insert_and_update_df_to_GZ_batch(df_plaintiff, "tb_plaintiff", "id")


def fix_duplicate_names_auto(df_cases, df_plaintiff):
    # Merge names that are already the same

    duplicates = {}
    for index, row in df_plaintiff.iterrows():
        for index2, row2 in df_plaintiff.iloc[index + 1:].iterrows():
            if row["plaintiff_name"].lower() == row2["plaintiff_name"].lower():
                if row["plaintiff_name"].lower() not in duplicates:
                    duplicates[row["plaintiff_name"].lower()] = set()
                duplicates[row["plaintiff_name"].lower()].add(row["id"])
                duplicates[row["plaintiff_name"].lower()].add(row2["id"])

    for name, ids in duplicates.items():
        ids_not_none = [id for id in ids if df_plaintiff[df_plaintiff["id"] == id]["plaintiff_overview"].notna().any()]
        if len(ids_not_none) == 0:
            id_to_keep = min(ids, key=lambda x: sum(1 for char in df_plaintiff[df_plaintiff["id"] == x]["plaintiff_name"] if char.isupper()))
        else:
            ids_with_overview = [id for id in ids_not_none if df_plaintiff[df_plaintiff["id"] == id]["plaintiff_overview"].notna().any()]
            ids_with_overview = [id for id in ids_with_overview if len(df_plaintiff[df_plaintiff["id"] == id]["plaintiff_overview"].iloc[0]) > 50]
            if len(ids_with_overview) == 0:
                id_to_keep = min(ids_not_none, key=lambda x: sum(1 for char in df_plaintiff[df_plaintiff["id"] == x]["plaintiff_name"] if char.isupper()))
            else:
                id_to_keep = min(ids_with_overview, key=lambda x: sum(1 for char in df_plaintiff[df_plaintiff["id"] == x]["plaintiff_name"] if char.isupper()))

        name_to_keep = df_plaintiff[df_plaintiff["id"] == id_to_keep]["plaintiff_name"].iloc[0]
        name_to_delete = df_plaintiff[(df_plaintiff["id"].isin(ids)) & (df_plaintiff["id"] != id_to_keep)]["plaintiff_name"].unique()
        cases_with_plaintiff_id_to_delete = df_cases[(df_cases["plaintiff_id"].isin(ids)) & (df_cases["plaintiff_id"] != id_to_keep)]

        joined_name_to_delete = " / ".join(name_to_delete)
        print(f"Keeping '{name_to_keep}' and deleting '{joined_name_to_delete}'. Reprocessing {cases_with_plaintiff_id_to_delete.shape[0]} cases.")
        print(f"")

        cases_with_plaintiff_id_to_delete.loc[:, "plaintiff_id"] = id_to_keep

        # Use the centralized reprocessing function
        success, tracking_dict = asyncio.run(reprocess_cases(
            cases_to_reprocess=cases_with_plaintiff_id_to_delete,
            trace_name="Fix Duplicate Names Auto",
            full_cases_df=df_cases,
            plaintiff_df=df_plaintiff
        ))

        if success:
            print(f"Successfully reprocessed {len(cases_with_plaintiff_id_to_delete)} cases")
        else:
            print(f"Reprocessing completed with some failures")

        insert_and_update_df_to_GZ_batch(cases_with_plaintiff_id_to_delete, "tb_case", "id") # will send all the new plaintiff_ids to database

    print("Deleting plaintiffs without cases")
    delete_plaintiff_without_cases(df_cases, df_plaintiff)
    print("Done")

def fix_duplicate_names_manual(df_cases, df_plaintiff):
    # This function will ask the user to input the name to keep and the name to delete

    names = df_plaintiff["plaintiff_name"]
    prompt_list = [("text", f'Identify likelly duplicate company names in this list:\n {("\\n".join(names))}\n\n Return the list of all duplicate pairs in a JSON format: {{"plaintiff_name": "duplicate_names", "plaintiff_name": "duplicate_names", ...}}')]
    duplicates = vertex_genai_multi(prompt_list)
    duplicates_json = get_json(duplicates)
    print(duplicates_json)

    plaintiff_ids_to_reprocess = set()
    for key, value in duplicates_json.items():
        # Add checks to ensure key and value are strings
        if not isinstance(key, str):
            print(f"Warning: AI returned a non-string key: {key}. Type: {type(key)}. Skipping pair.")
            continue
        if not isinstance(value, str):
            print(f"Warning: AI returned a non-string value: {value}. Type: {type(value)}. Skipping pair ('{key}', {value}).")
            continue

        plaintiff_id_key = df_plaintiff[df_plaintiff["plaintiff_name"] == key]["id"].iloc[0]
        plaintiff_id_value = df_plaintiff[df_plaintiff["plaintiff_name"] == value]["id"].iloc[0]

        key_number_of_cases = df_cases[df_cases["plaintiff_id"] == plaintiff_id_key].shape[0]
        value_number_of_cases = df_cases[df_cases["plaintiff_id"] == plaintiff_id_value].shape[0]
        print(f'Which one to keep: 0: "Both"  \n   1: {key} ({key_number_of_cases} cases)  \n   2: {value} ({value_number_of_cases} cases)')
        answer = input("Enter your choice (0, 1, or 2): ")
        answer = int(answer)

        if answer == 1:
            plaintiff_id_to_keep = plaintiff_id_key
            plaintiff_id_to_delete = plaintiff_id_value
        elif answer == 2:
            plaintiff_id_to_keep = plaintiff_id_value
            plaintiff_id_to_delete = plaintiff_id_key
        else:
            print("Skipping this pair.\n")
            continue

        plaintiff_ids_to_reprocess.add(plaintiff_id_to_delete)
        df_cases.loc[df_cases["plaintiff_id"] == plaintiff_id_to_delete, "plaintiff_id"] = plaintiff_id_to_keep  # should also update case_df

    cases_with_plaintiff_id_to_delete = df_cases[df_cases["plaintiff_id"].isin(plaintiff_ids_to_reprocess)]

    # Use the centralized reprocessing function
    success, tracking_dict = asyncio.run(reprocess_cases(
        cases_to_reprocess=cases_with_plaintiff_id_to_delete,
        trace_name="Fix Duplicate Names Manual",
        full_cases_df=df_cases,
        plaintiff_df=df_plaintiff
    ))

    if success:
        print(f"Successfully reprocessed {len(cases_with_plaintiff_id_to_delete)} cases")
    else:
        print(f"Reprocessing completed with some failures")

    insert_and_update_df_to_GZ_batch(cases_with_plaintiff_id_to_delete, "tb_case", "id")

    print("Deleting plaintiffs without cases")
    delete_plaintiff_without_cases(df_cases, df_plaintiff)
    print("Done")



def get_duplicate_plaintiff_names(df_plaintiff, df_cases):
    """Identify likely duplicate plaintiff names without making changes"""
    names = df_plaintiff["plaintiff_name"]
    prompt_list = [("text", f'Identify likelly duplicate company names in this list:\n {("\\n".join(names))}\n\n Return the list of all duplicate pairs in a JSON format: {{"plaintiff_name": "duplicate_names", "plaintiff_name": "duplicate_names", ...}}')]
    duplicates = vertex_genai_multi(prompt_list)
    duplicates_json = get_json(duplicates)

    # Format results for display
    result_pairs = []
    if not duplicates_json:
        print("AI did not return any duplicate pairs or the format was invalid.")
        return result_pairs

    for key, value in duplicates_json.items():
        # Add checks to ensure key and value are strings
        if not isinstance(key, str):
            print(f"Warning: AI returned a non-string key: {key}. Type: {type(key)}. Skipping pair.")
            continue
        if not isinstance(value, str):
            print(f"Warning: AI returned a non-string value: {value}. Type: {type(value)}. Skipping pair ('{key}', {value}).")
            continue

        try:
            plaintiff_id_key = df_plaintiff[df_plaintiff["plaintiff_name"] == key]["id"].iloc[0]
        except IndexError:
            print(f"Warning: AI suggested duplicate name '{key}' not found in plaintiff list. Skipping pair ('{key}', '{value}').")
            continue

        try:
            plaintiff_id_value = df_plaintiff[df_plaintiff["plaintiff_name"] == value]["id"].iloc[0]
        except IndexError:
            print(f"Warning: AI suggested duplicate name '{value}' not found in plaintiff list. Skipping pair ('{key}', '{value}').")
            continue

        if plaintiff_id_key == plaintiff_id_value:
            print(f"Warning: AI suggested duplicate name '{key}' and '{value}' are the same. Skipping pair ('{key}', '{value}').")
            continue

        key_cases_count = sum(df_cases["plaintiff_id"] == plaintiff_id_key)
        value_cases_count = sum(df_cases["plaintiff_id"] == plaintiff_id_value)
        names1 = df_cases[df_cases["plaintiff_id"] == plaintiff_id_key]["plaintiff_names"].unique()
        names2 = df_cases[df_cases["plaintiff_id"] == plaintiff_id_value]["plaintiff_names"].unique()
        if isinstance(names1, np.ndarray):
            names1 = json.dumps(list(names1))
        if isinstance(names2, np.ndarray):
            names2 = json.dumps(list(names2))

        result_pairs.append({
            "name1": key,
            "name2": value,
            "names1": names1,
            "names2": names2,
            "id1": int(plaintiff_id_key),
            "id2": int(plaintiff_id_value),
            "cases1": int(key_cases_count),
            "cases2": int(value_cases_count)
        })

    return result_pairs


def merge_plaintiff_pair(df_cases, df_plaintiff, keep_id, delete_id):
    """
    Merge a pair of duplicate plaintiffs by keeping one and deleting the other.
    Only reprocesses and updates the cases originally belonging to the delete_id.
    Modifies the passed df_cases DataFrame in place.
    """
    # 1. Find the indices of rows to update in the main DataFrame
    indices_to_update = df_cases[df_cases["plaintiff_id"] == delete_id].index

    # 2. Proceed only if there are matching cases
    if not indices_to_update.empty:
        # 3. Create a separate DataFrame *specifically* for reprocessing and DB update.
        #    Make sure it reflects the *new* plaintiff_id
        df_cases.loc[indices_to_update, "plaintiff_id"] = keep_id
        #    Use .copy() to avoid SettingWithCopyWarning.
        cases_to_process = df_cases.loc[indices_to_update].copy()

        # 5. Reprocess using the centralized reprocessing function
        success, tracking_dict = asyncio.run(reprocess_cases(
            cases_to_reprocess=cases_to_process,
            trace_name="Merge Plaintiff Pair",
            full_cases_df=df_cases,
            plaintiff_df=df_plaintiff
        ))

        if not success:
            print(f"Warning: Reprocessing completed with some failures for plaintiff merge")

        # 7. Update related tables for the deleted plaintiff
        edit_plaintiff_id_in_postgresql_tables(delete_id, keep_id)

        # 8. Delete the plaintiff without cases
        delete_plaintiff_without_cases(df_cases, df_plaintiff)

        # 9. Return count of affected cases
        return len(cases_to_process)
    else:
        # No cases found with the delete_id
        return 0



# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

def improve_plaintiff_9_and_2_letter_names(df_cases, df_plaintiff, df_reviews):
    """
    Bulk process to improve plaintiff names for cases where:
    - The plaintiff name is only 2 letters, or
    - The plaintiff_id is 9

    Stores proposed name changes in tbi_case_plaintiff_name_review table for later review
    through the web interface.
    """

    weak_cases = df_cases[(df_cases['plaintiff_id'] == 9) |
                        (df_cases['plaintiff_id'].apply(
                            lambda pid: len(df_plaintiff[df_plaintiff['id'] == pid]['plaintiff_name'].iloc[0]) <= 2
                            if pid in df_plaintiff['id'].values else False))]

    log_message(f"Found {len(weak_cases)} cases with weak plaintiff names to improve")
    if len(weak_cases) == 0:
        return

    # Process all weak cases
    case_ids_to_update = process_weak_cases(weak_cases, df_plaintiff, df_reviews)

    # Update the database with only the modified rows
    if case_ids_to_update:
        df_to_update = df_reviews[df_reviews['case_id'].isin(case_ids_to_update)]
        insert_and_update_df_to_GZ_batch(df_to_update, "tbi_case_plaintiff_name_review", "case_id")
        log_message(f"Updated {len(case_ids_to_update)} cases in the plaintiff name review table")
    else:
        log_message("No new cases were added to the plaintiff name review table")


def improve_single_plaintiff_name(case_id, df_cases, df_plaintiff, df_reviews):
    """
    Improve plaintiff name for a single case

    Args:
        case_id: ID of the case to process

    Returns:
        Dictionary with the proposed name and method_info if successful
    """

    # Get the specific case
    case_row = df_cases[df_cases['id'] == case_id]
    if case_row.empty:
        log_message(f"Case ID {case_id} not found")
        return {"error": "Case not found"}

    log_message(f"Processing single case {case_id} for plaintiff name improvement")

    # Process this single case
    case_ids_to_update = process_weak_cases(case_row, df_plaintiff, df_reviews)

    # Update the database with the modified row
    if case_ids_to_update:
        df_to_update = df_reviews[df_reviews['case_id'].isin(case_ids_to_update)]
        insert_and_update_df_to_GZ_batch(df_to_update, "tbi_case_plaintiff_name_review", "case_id")
        log_message(f"Updated plaintiff name for case {case_id} in the plaintiff name review table")

        # Return the proposed name
        updated_row = df_reviews[df_reviews['case_id'] == case_id]
        if not updated_row.empty:
            return {
                "proposed_name": updated_row['proposed_name'].iloc[0],
                "method_info": updated_row['method_info'].iloc[0]
            }
        return {"proposed_name": None, "method_info": None}
    else:
        log_message(f"No improvements found for case {case_id}")
        return {"proposed_name": None, "method_info": None}

def process_weak_cases(weak_cases, df_plaintiff, df_reviews):
    """
    Common function to process weak cases and improve plaintiff names

    Args:
        weak_cases: DataFrame containing cases to process
        df_plaintiff: DataFrame containing plaintiff data
        df_reviews: DataFrame containing existing reviews

    Returns:
        Set of case IDs that have been updated
    """

    # Track case_ids that need to be updated in the database
    case_ids_to_update = set()
    remaining_cases = []

    # Helper function to update df_reviews with new plaintiff name
    def update_reviews_with_name(case_id, new_plaintiff_name, method_info, rejected_names):
        nonlocal df_reviews, case_ids_to_update

        if new_plaintiff_name and new_plaintiff_name != "Unidentified Claimant" and len(new_plaintiff_name) > 2 and new_plaintiff_name not in rejected_names:
            # Update or add review entry directly in df_reviews
            if case_id in df_reviews['case_id'].values:
                idx = df_reviews[df_reviews['case_id'] == case_id].index[0]
                df_reviews.at[idx, 'proposed_name'] = new_plaintiff_name
                df_reviews.at[idx, 'method_info'] = method_info
            else:
                new_row = pd.DataFrame({
                    'case_id': [case_id],
                    'proposed_name': [new_plaintiff_name],
                    'method_info': [method_info],
                })
                df_reviews.loc[len(df_reviews)] = new_row.iloc[0]  # inplace!

            case_ids_to_update.add(case_id)
            return True
        return False

    # PASS 1: Check plaintiff_names field for all cases
    log_message("PASS 1: Checking plaintiff_names field for all cases")
    for i, (index, case) in enumerate(weak_cases.iterrows()):
        case_id = case['id']
        current_plaintiff_id = case['plaintiff_id']
        current_title = case['title']
        current_name = df_plaintiff[df_plaintiff['id'] == current_plaintiff_id]['plaintiff_name'].iloc[0] if current_plaintiff_id in df_plaintiff['id'].values else "Unknown"

        log_message(f"\n\n{i+1}/{len(weak_cases)}: Processing case {case['docket']} with plaintiff= {current_name},  title= {current_title} and plaintiff_names= {case['plaintiff_names']}")

        # Check if the case is already in the review table with a proposed name
        existing_review = df_reviews[df_reviews['case_id'] == case_id]
        if not existing_review.empty and pd.notna(existing_review['proposed_name'].iloc[0]):
            log_message(f"Case {case_id} already has a proposed name in the review table. Skipping.")
            continue

        # Get the list of previously rejected names, if any
        rejected_names = []
        if not existing_review.empty and pd.notna(existing_review['rejected_names'].iloc[0]):
            try:
                rejected_str = existing_review['rejected_names'].iloc[0]
                if isinstance(rejected_str, str):
                    rejected_names = json.loads(rejected_str)
            except:
                log_message(f"Error parsing rejected names for case {case_id}")

        new_plaintiff_name = None
        method_info = None

        # Check plaintiff_names field
        if pd.notna(case['plaintiff_names']):
            try:
                plaintiff_names = json.loads(case['plaintiff_names'])
                if len(plaintiff_names) > 0:
                    if current_plaintiff_id == 9:
                        # For plaintiff_id 9, use the first item
                        candidate_name = plaintiff_names[0]
                        new_plaintiff_name = clean_plaintiff_name(df_plaintiff, candidate_name + " v. Schedule A")
                        method_info = f"First entry from plaintiff_names: {candidate_name}"
                        log_message(f"From plaintiff_names {plaintiff_names}, candidate: {candidate_name} -> {new_plaintiff_name}")
                    else:
                        # For 2-letter names, check if longer version exists in plaintiff_names
                        for name in plaintiff_names:
                            if current_name.lower() in name.lower():
                                candidate_name = name
                                new_plaintiff_name = clean_plaintiff_name(df_plaintiff, candidate_name + " v. Schedule A")
                                method_info = f"Longer version found in plaintiff_names: {candidate_name}"
                                log_message(f"From plaintiff_names {plaintiff_names}, match: {current_name} -> {new_plaintiff_name}")
                                break
            except (json.JSONDecodeError, ValueError) as e:
                log_message(f"Error parsing plaintiff_names for case {case_id}: {e}")

        # If we found a valid plaintiff name, update reviews
        if update_reviews_with_name(case_id, new_plaintiff_name, method_info, rejected_names):
            log_message(f"✅ Found valid plaintiff name from plaintiff_names: {new_plaintiff_name}")
        else:
            remaining_cases.append((index, case, rejected_names))

    # PASS 2: Check DocketBird for updated case title for remaining cases
    if remaining_cases:
        log_message(f"PASS 2: Checking DocketBird for {len(remaining_cases)} remaining cases")

        # Login to DocketBird for URL checks
        driver = docketbird_get_logged_in_browser()
        docketbird_remaining = []

        for i, (index, case, rejected_names) in enumerate(remaining_cases):
            case_id = case['id']
            current_plaintiff_id = case['plaintiff_id']
            current_title = case['title']
            current_name = df_plaintiff[df_plaintiff['id'] == current_plaintiff_id]['plaintiff_name'].iloc[0] if current_plaintiff_id in df_plaintiff['id'].values else "Unknown"

            log_message(f"\n\n{i+1}/{len(remaining_cases)}: Checking DocketBird for case {case['docket']}")

            new_plaintiff_name = None
            method_info = None

            # Check DocketBird for updated case title
            try:
                url = build_docketbird_url(case['court'], case['docket'])
                if url:
                    driver.get(url)
                    time.sleep(1)  # Allow page to load

                    case_name_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.ID, "case_name"))
                    )
                    docketbird_title = case_name_element.text.strip()

                    # Compare with current title
                    if docketbird_title[:3].lower() != current_title[:3].lower():
                        log_message(f"DocketBird title differs: {docketbird_title}")
                        new_plaintiff_name = clean_plaintiff_name(df_plaintiff, docketbird_title)
                        method_info = f"Updated title from DocketBird: {docketbird_title}"
                        log_message(f"From DocketBird title, candidate: {new_plaintiff_name}")
            except Exception as e:
                log_message(f"Error checking DocketBird for case {case_id}: {e}")

            # If we found a valid plaintiff name, update reviews
            if update_reviews_with_name(case_id, new_plaintiff_name, method_info, rejected_names):
                log_message(f"✅ Found valid plaintiff name from DocketBird: {new_plaintiff_name}")
            else:
                docketbird_remaining.append((index, case, rejected_names))

        # Close DocketBird browser
        driver.quit()

        # Update remaining_cases for the next pass
        remaining_cases = docketbird_remaining

    # PASS 3: Analyze PDFs with AI for remaining cases asynchronously
    if remaining_cases:
        log_message(f"PASS 3: Analyzing PDFs with AI for {len(remaining_cases)} remaining cases")

        async def process_with_ai():
            # Use a semaphore to limit concurrent AI tasks
            sem = asyncio.Semaphore(5)  # Adjust based on API limits
            running_tasks = []

            async def process_case_with_ai(case_id, pdfs, rejected_names):
                async with sem:
                    prompt = f'Based on these legal documents, try to identify the plaintiff name. If you can identify the plaintiff name, return it in the format: {{"plaintiff_name": "....."}}, else (or if you are not sure) return {{"plaintiff_name": "Unidentified Claimant"}}.\n\n'
                    prompt_list = [("text", prompt)]
                    for pdf_path in pdfs:
                        prompt_list.append(("pdf_path", pdf_path))

                    # Execute AI task
                    try:
                        result = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE, useVertexAI=True)
                        result_json = get_json(result)

                        if result_json and "plaintiff_name" in result_json and result_json["plaintiff_name"] != "Unidentified Claimant":
                            new_plaintiff_name = clean_plaintiff_name(df_plaintiff, result_json["plaintiff_name"] + " v. Schedule A")
                            method_info = f"AI analysis from PDF: {result_json['plaintiff_name']}"
                            log_message(f"✅ From PDF AI analysis, extracted: {new_plaintiff_name}")

                            # Update reviews with the new name
                            update_reviews_with_name(case_id, new_plaintiff_name, method_info, rejected_names)
                        else:
                            log_message(f"No answer from PDF AI analysis for case {case_id}")
                    except Exception as e:
                        log_message(f"Error in AI analysis for case {case_id}: {e}")

            # Process each case, starting OCR and AI tasks immediately
            for i, (index, case, rejected_names) in enumerate(remaining_cases):
                case_id = case['id']
                current_plaintiff_id = case['plaintiff_id']
                current_name = df_plaintiff[df_plaintiff['id'] == current_plaintiff_id]['plaintiff_name'].iloc[0] if current_plaintiff_id in df_plaintiff['id'].values else "Unknown"

                log_message(f"\n\n{i+1}/{len(remaining_cases)}: Analyzing PDFs for case {case['docket']}")

                # Find PDFs for this case
                good_pdfs = []
                case_folder = os.path.join(Constants.local_case_folder, Constants.sanitize_name(f"{case['date_filed'].strftime('%Y-%m-%d')} - {case['docket']}"))

                if os.path.exists(case_folder):
                    log_message(f"Found case folder: {case_folder}")

                    # Get all folders except "images"
                    pdf_folders = [f for f in os.listdir(case_folder) if os.path.isdir(os.path.join(case_folder, f)) and f != "images"]

                    for pdf_folder_name in pdf_folders:
                        pdf_folder = os.path.join(case_folder, pdf_folder_name)
                        log_message(f"Checking PDF folder: {pdf_folder}")
                        pdf_files = [f for f in os.listdir(pdf_folder) if f.lower().endswith('.pdf')]

                        if pdf_files:
                            log_message(f"Found {len(pdf_files)} PDFs to analyze")
                            for pdf_file in pdf_files:
                                pdf_path = os.path.join(pdf_folder, pdf_file)

                                # Use the OCR processor instead of extract_text
                                try:
                                    pdf_document = fitz.open(pdf_path)
                                    full_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document, [0, 1, 2, 3])

                                    # Skip if text extraction failed
                                    if not full_text:
                                        continue

                                    # Keywords to identify rubbish reports
                                    rubbish_keywords = ["Security Imperative", "illicit trade of goods", "heavy recruitment of chinese",
                                                        "counterfeit and pirated goods", "office of strategy", "counterfeiting in the age of the internet",
                                                        "accreditation", "department of commerce", "white paper", "chamber of commerce",
                                                        "office of trade", "border protection", "hague convention", "civil procedure",
                                                        "convention on the service", "seizure statistics", "notorious markets", " inta ",
                                                        "traffic report", "silk road", "state of the", "briefing papers"]

                                    # Skip rubbish reports
                                    if any(keyword.lower() in full_text.lower() for keyword in rubbish_keywords):
                                        log_message(f"Skipping rubbish report: {pdf_file}")
                                        continue

                                    good_pdfs.append(pdf_path)
                                except Exception as e:
                                    log_message(f"Error processing PDF {pdf_path}: {e}")

                    if good_pdfs:
                        log_message(f"Found {len(good_pdfs)} usable PDFs for AI analysis - starting AI task")
                        # Start the AI task immediately and add to running tasks
                        task = asyncio.create_task(process_case_with_ai(case_id, good_pdfs, rejected_names))
                        running_tasks.append(task)
                    else:
                        log_message(f"No usable PDFs found for case {case_id}")
                else:
                    log_message(f"No case folder found for case {case_id}")

            # Wait for all tasks to complete
            if running_tasks:
                log_message(f"Waiting for {len(running_tasks)} AI tasks to complete")
                await asyncio.gather(*running_tasks)

        # Run the async process
        asyncio.run(process_with_ai())

    return case_ids_to_update

if __name__ == "__main__":
    df_cases = get_table_from_GZ("tb_case", force_refresh=False)
    df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=False)
    delete_plaintiff_without_cases(df_cases, df_plaintiff)

    list_cases_without_plaintiff(df_cases, df_plaintiff)
    # change_plaintiff_id_for_case(8, 479, df_cases, df_plaintiff)



    # update_names_based_on_latest_sanitization(df_plaintiff)
    # review_cases_mapped_to_nine(df_cases, df_plaintiff)
    # fix_duplicate_names_auto(df_cases, df_plaintiff)
    # fix_duplicate_names_manual(df_cases, df_plaintiff)
    # improve_weak_plaintiff_names(df_cases, df_plaintiff)
