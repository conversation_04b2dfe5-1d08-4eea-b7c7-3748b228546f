"""
Test script for the similarity service functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'api'))

from services.similarity_service import apply_similarity_filtering


def test_copyright_filtering():
    """Test copyright filtering logic."""
    print("Testing Copyright Filtering...")
    
    # Sample copyright infringements
    infringements = [
        {
            "ip_type": "Copyright",
            "ip_asset_id": "copy_1",
            "score": 0.8,
            "metadata": {"filename": "image1.jpg", "reg_no": "REG001"}
        },
        {
            "ip_type": "Copyright", 
            "ip_asset_id": "copy_2",
            "score": 0.6,
            "metadata": {"filename": "image2.jpg", "reg_no": "REG002"}
        },
        {
            "ip_type": "Copyright",
            "ip_asset_id": "copy_3", 
            "score": 0.9,
            "metadata": {"filename": "image1.jpg", "reg_no": "REG001"}  # Duplicate filename
        }
    ]
    
    # Test with default parameters
    result = apply_similarity_filtering(infringements, "copyright")
    print(f"Default filtering result: {len(result)} items")
    for item in result:
        print(f"  - {item['metadata']['filename']}: {item['score']}")
    
    # Test with custom threshold and top_n
    result = apply_similarity_filtering(infringements, "copyright", threshold=0.7, top_n=1)
    print(f"Custom filtering (threshold=0.7, top_n=1): {len(result)} items")
    for item in result:
        print(f"  - {item['metadata']['filename']}: {item['score']}")
    
    print()


def test_patent_filtering():
    """Test patent filtering logic."""
    print("Testing Patent Filtering...")
    
    # Sample patent infringements
    infringements = [
        {
            "ip_type": "Patent",
            "ip_asset_id": "patent_1",
            "score": 0.7,
            "metadata": {"text": "Method for processing data", "patent_number": "US123456"}
        },
        {
            "ip_type": "Patent",
            "ip_asset_id": "patent_2", 
            "score": 0.5,
            "metadata": {"text": "System for data analysis", "patent_number": "US789012"}
        },
        {
            "ip_type": "Patent",
            "ip_asset_id": "patent_3",
            "score": 0.8,
            "metadata": {"text": "Method for processing data", "patent_number": "US345678"}  # Same text
        }
    ]
    
    # Test with default parameters
    result = apply_similarity_filtering(infringements, "patent")
    print(f"Default filtering result: {len(result)} items")
    for item in result:
        print(f"  - {item['metadata']['text']}: {item['score']}")
    
    print()


def test_trademark_filtering():
    """Test trademark filtering logic."""
    print("Testing Trademark Filtering...")
    
    # Sample trademark infringements
    infringements = [
        {
            "ip_type": "Trademark",
            "ip_asset_id": "tm_1",
            "score": 0.8,
            "metadata": {"reg_no": "TM001", "trademark_text": "BRAND NAME"}
        },
        {
            "ip_type": "Trademark",
            "ip_asset_id": "tm_2",
            "score": 0.6,
            "metadata": {"reg_no": "TM002", "trademark_text": "OTHER BRAND"}
        },
        {
            "ip_type": "Trademark",
            "ip_asset_id": "tm_3",
            "score": 0.9,
            "metadata": {"reg_no": "TM003", "trademark_text": "TOP BRAND"}
        }
    ]
    
    # Test with default parameters
    result = apply_similarity_filtering(infringements, "trademark")
    print(f"Default filtering result: {len(result)} items")
    for item in result:
        print(f"  - {item['metadata']['trademark_text']}: {item['score']} (match_count: {item.get('match_count', 'N/A')})")
    
    # Test with custom threshold
    result = apply_similarity_filtering(infringements, "trademark", threshold=0.65, top_n=2)
    print(f"Custom filtering (threshold=0.65, top_n=2): {len(result)} items")
    for item in result:
        print(f"  - {item['metadata']['trademark_text']}: {item['score']} (match_count: {item.get('match_count', 'N/A')})")
    
    print()


def main():
    """Run all tests."""
    print("=== Similarity Service Tests ===\n")
    
    test_copyright_filtering()
    test_patent_filtering() 
    test_trademark_filtering()
    
    print("=== Tests Complete ===")


if __name__ == "__main__":
    main()
