"""
<PERSON><PERSON><PERSON> to identify large patent folders (>20MB) and analyze their CPC classifications.

This script:
1. Scans the patent image directory structure (/xx/yy/) for folders larger than 20MB
2. Extracts patent registration numbers from folder names (format: US012345678-xxxxx)
3. Queries the database to get CPC assignments for these patents
4. Filters CPC assignments to only include those matching TRO CPC classifications
5. Saves results to a CSV file

The goal is to understand why these large patent folders were selected for the TRO subset
by identifying which CPC classifications they match.
"""

import os
import sys
import psycopg2
import psycopg2.extras
import logging
import re
from pathlib import Path
import csv

# Add the parent directory to the path to import patent_db_grant
sys.path.append(os.getcwd())
from IP.Patents_Bulk.patent_db_grant import get_db_connection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_folder_size(folder_path):
    """
    Calculate the total size of a folder in bytes.

    Args:
        folder_path (Path): Path to the folder

    Returns:
        int: Total size in bytes
    """
    total_size = 0
    try:
        for file_path in folder_path.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
    except (OSError, PermissionError) as e:
        logger.warning(f"Error calculating size for {folder_path}: {e}")
    return total_size

def extract_patent_reg_number(folder_name):
    """
    Extract patent registration number from folder name.

    Expected format: US012345678-xxxxx
    Returns the part between 'US' and the hyphen.

    Args:
        folder_name (str): Name of the folder

    Returns:
        str or None: Patent registration number or None if not found
    """
    match = re.match(r'US(\d+)-', folder_name)
    if match:
        return match.group(1)
    return None

def find_large_patent_folders(base_directory, size_threshold_mb=20):
    """
    Find all patent folders larger than the specified threshold.

    Args:
        base_directory (str): Base directory containing /xx/yy/ structure
        size_threshold_mb (int): Size threshold in MB

    Returns:
        list: List of tuples (folder_path, patent_reg_number, size_mb)
    """
    large_folders = []
    size_threshold_bytes = size_threshold_mb * 1024 * 1024

    base_path = Path(base_directory)
    if not base_path.exists():
        logger.error(f"Base directory does not exist: {base_directory}")
        return large_folders

    logger.info(f"Scanning directory structure in {base_directory}")
    logger.info(f"Looking for folders larger than {size_threshold_mb} MB")

    # Scan through /xx/yy/ directory structure
    for xx_dir in base_path.iterdir():
        if not xx_dir.is_dir() or not re.match(r'^\d{2}$', xx_dir.name):
            continue

        for yy_dir in xx_dir.iterdir():
            if not yy_dir.is_dir() or not re.match(r'^\d{2}$', yy_dir.name):
                continue

            # Check each patent folder in /xx/yy/
            for patent_folder in yy_dir.iterdir():
                if not patent_folder.is_dir():
                    continue

                # Extract patent registration number from folder name
                patent_reg_number = extract_patent_reg_number(patent_folder.name)
                if not patent_reg_number:
                    continue

                # Calculate folder size
                folder_size = get_folder_size(patent_folder)

                if folder_size > size_threshold_bytes:
                    size_mb = folder_size / (1024 * 1024)
                    large_folders.append((str(patent_folder), patent_reg_number, size_mb))
                    logger.info(f"Found large folder: {patent_folder.name} ({size_mb:.2f} MB)")

    logger.info(f"Found {len(large_folders)} folders larger than {size_threshold_mb} MB")
    return large_folders

def get_patent_cpc_assignments(patent_reg_numbers):
    """
    Get CPC assignments for the given patent registration numbers.

    Args:
        patent_reg_numbers (list): List of patent registration numbers

    Returns:
        list: List of dictionaries with patent and CPC information
    """
    if not patent_reg_numbers:
        return []

    conn = None
    results = []

    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection")
            return results

        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Query to get patent info and CPC assignments
        sql_query = """
            SELECT
                p.reg_no,
                p.patent_title,
                p.date_published,
                p.patent_type,
                cpc.section,
                cpc.class,
                cpc.subclass,
                cpc.main_group,
                cpc.sub_group,
                cpc.definition
            FROM patents_all p
            JOIN patents_cpc_ipc_assignments_all pca ON p.id = pca.patents_id
            JOIN patents_cpc_ipc_definitions cpc ON pca.cpc_ipc_id = cpc.id
            WHERE p.reg_no = ANY(%s)
            AND cpc.classification_type = 'CPC'
            ORDER BY p.reg_no, cpc.section, cpc.class, cpc.subclass
        """

        cursor.execute(sql_query, (patent_reg_numbers,))
        results = cursor.fetchall()

        logger.info(f"Retrieved {len(results)} CPC assignments for {len(patent_reg_numbers)} patents")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error: {db_err}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        if conn:
            conn.close()

    return results

def get_tro_cpc_classifications():
    """
    Get the TRO CPC classifications from the database.

    Returns:
        set: Set of CPC classification strings (section + class + subclass)
    """
    conn = None
    tro_classifications = set()

    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection")
            return tro_classifications

        cursor = conn.cursor()

        sql_query = """
            SELECT section, class, subclass
            FROM tro_cpc_classifications
        """

        cursor.execute(sql_query)
        results = cursor.fetchall()

        # Create set of concatenated classification strings
        for row in results:
            section, class_code, subclass = row
            # Concatenate section + class (zero-padded to 2 digits) + subclass
            classification_key = f"{section}{class_code.zfill(2)}{subclass}"
            tro_classifications.add(classification_key)

        logger.info(f"Retrieved {len(tro_classifications)} TRO CPC classifications")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error: {db_err}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        if conn:
            conn.close()

    return tro_classifications

def filter_cpc_assignments_by_tro(cpc_assignments, tro_classifications):
    """
    Filter CPC assignments to only include those matching TRO classifications.

    Args:
        cpc_assignments (list): List of CPC assignment dictionaries
        tro_classifications (set): Set of TRO CPC classification strings

    Returns:
        list: Filtered list of CPC assignments
    """
    filtered_assignments = []

    for assignment in cpc_assignments:
        # Create classification key from assignment
        section = assignment['section']
        class_code = assignment['class']
        subclass = assignment['subclass']

        classification_key = f"{section}{class_code.zfill(2)}{subclass}"

        if classification_key in tro_classifications:
            # Add the classification key to the assignment for reference
            assignment_copy = dict(assignment)
            assignment_copy['tro_classification_key'] = classification_key
            filtered_assignments.append(assignment_copy)

    logger.info(f"Filtered {len(filtered_assignments)} CPC assignments matching TRO classifications from {len(cpc_assignments)} total")
    return filtered_assignments

def save_results_to_csv(large_folders, filtered_assignments, output_file):
    """
    Save the analysis results to a CSV file.

    Args:
        large_folders (list): List of large folder information
        filtered_assignments (list): List of filtered CPC assignments
        output_file (str): Path to output CSV file
    """
    # Create a mapping of patent reg numbers to folder information
    folder_info = {reg_no: (folder_path, size_mb) for folder_path, reg_no, size_mb in large_folders}

    # Prepare data for CSV
    csv_data = []

    for assignment in filtered_assignments:
        reg_no = assignment['reg_no']
        folder_path, size_mb = folder_info.get(reg_no, ('Unknown', 0))

        csv_row = {
            'patent_reg_no': reg_no,
            'folder_path': folder_path,
            'folder_size_mb': f"{size_mb:.2f}",
            'patent_title': assignment.get('patent_title', ''),
            'date_published': assignment.get('date_published', ''),
            'patent_type': assignment.get('patent_type', ''),
            'cpc_section': assignment['section'],
            'cpc_class': assignment['class'],
            'cpc_subclass': assignment['subclass'],
            'cpc_main_group': assignment.get('main_group', ''),
            'cpc_sub_group': assignment.get('sub_group', ''),
            'tro_classification_key': assignment['tro_classification_key'],
            'cpc_definition': assignment.get('definition', '')
        }
        csv_data.append(csv_row)

    # Write to CSV
    if csv_data:
        fieldnames = [
            'patent_reg_no', 'folder_path', 'folder_size_mb', 'patent_title',
            'date_published', 'patent_type', 'cpc_section', 'cpc_class',
            'cpc_subclass', 'cpc_main_group', 'cpc_sub_group',
            'tro_classification_key', 'cpc_definition'
        ]

        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)

        logger.info(f"Saved {len(csv_data)} records to {output_file}")
    else:
        logger.warning("No data to save to CSV")

def main():
    """
    Main function to execute the analysis.
    """
    # Configuration - you may need to adjust these paths
    from Common.Constants import local_ip_folder
    BASE_DIRECTORY = os.path.join(local_ip_folder, 'Patents', 'USPTO_Grants', 'Extracted')

    SIZE_THRESHOLD_MB = 20
    OUTPUT_FILE = os.path.join(os.path.dirname(__file__), 'large_patent_folders_cpc_analysis.csv')

    logger.info("Starting analysis of large patent folders and their CPC classifications")

    # Step 1: Find large patent folders
    logger.info("Step 1: Finding large patent folders...")
    large_folders = find_large_patent_folders(BASE_DIRECTORY, SIZE_THRESHOLD_MB)

    if not large_folders:
        logger.info("No large folders found. Exiting.")
        return

    # Step 2: Extract patent registration numbers
    patent_reg_numbers = [reg_no for _, reg_no, _ in large_folders]
    logger.info(f"Step 2: Extracted {len(patent_reg_numbers)} patent registration numbers")

    # Step 3: Get CPC assignments from database
    logger.info("Step 3: Retrieving CPC assignments from database...")
    cpc_assignments = get_patent_cpc_assignments(patent_reg_numbers)

    if not cpc_assignments:
        logger.info("No CPC assignments found for the patents. Exiting.")
        return

    # Step 4: Get TRO CPC classifications
    logger.info("Step 4: Retrieving TRO CPC classifications...")
    tro_classifications = get_tro_cpc_classifications()

    if not tro_classifications:
        logger.info("No TRO CPC classifications found. Exiting.")
        return

    # Step 5: Filter CPC assignments by TRO classifications
    logger.info("Step 5: Filtering CPC assignments by TRO classifications...")
    filtered_assignments = filter_cpc_assignments_by_tro(cpc_assignments, tro_classifications)

    if not filtered_assignments:
        logger.info("No CPC assignments match TRO classifications.")
        return

    # Step 6: Save results to CSV
    logger.info("Step 6: Saving results to CSV...")
    save_results_to_csv(large_folders, filtered_assignments, OUTPUT_FILE)

    # Summary
    logger.info("Analysis completed!")
    logger.info(f"Found {len(large_folders)} large folders")
    logger.info(f"Retrieved {len(cpc_assignments)} total CPC assignments")
    logger.info(f"Found {len(filtered_assignments)} CPC assignments matching TRO classifications")
    logger.info(f"Results saved to: {OUTPUT_FILE}")

if __name__ == "__main__":
    main()