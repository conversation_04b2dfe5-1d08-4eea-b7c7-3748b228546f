# Use an ARM-compatible base image
FROM python:3.12-slim-bullseye

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libleptonica-dev \
    pkg-config \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    wget \
    chromium \
    chromium-driver \
    unzip \   
    curl \
    net-tools \  
    apt-transport-https \
    ca-certificates \
    gnupg \
    tmux \ 
    openssh-client \
    git \
    fonts-liberation \
    ttf-mscorefonts-installer

# RUN echo "ttf-mscorefonts-installer msttcorefonts/accepted-mscorefonts-eula select true" | debconf-set-selections && apt-get install -y ttf-mscorefonts-installer

# Install SSH server
RUN apt-get install -y openssh-server \
    && mkdir /var/run/sshd \
    && echo 'root:trosdc2024' | chpasswd \
    && sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
    && sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config \
    && sed -i 's/#ListenAddress 0.0.0.0/ListenAddress 0.0.0.0/' /etc/ssh/sshd_config \ 
    && sed -i 's/#Port 22/Port 2223/' /etc/ssh/sshd_config


# Timezone and Locale: Your container's timezone and locale might be default (UTC, en_US.UTF-8) and inconsistent with the supposed location/language of a typical user matching your User-Agent.
ENV TZ=America/New_York
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8
RUN apt-get update && apt-get install -y locales && \
    sed -i -e "s/# $LANG.*/$LANG UTF-8/" /etc/locale.gen && \
    dpkg-reconfigure --frontend=noninteractive locales && \
    update-locale LANG=$LANG && \
    rm -rf /var/lib/apt/lists/*


# Set Chrome options for Selenium
ENV CHROME_BIN=/usr/bin/chromium
ENV CHROME_DRIVER=/usr/bin/chromedriver

# Set the working directory
WORKDIR /app
# ENV PYTHONPATH=/app:$PYTHONPATH 
ENV PYTHONPATH=/app 
# By appending :$PYTHONPATH, we are preserving any existing paths that were already in the PYTHONPATH variable.

# Copy requirements.txt first, so that if the code has changed, the dependencies are not re-installed (different stage)
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY . .

# Create directories for the chrome profile and database
RUN mkdir -p /app/data/chrome_user_data
RUN mkdir -p /app/data/db

# Copy the chromedriver to a directory where it can be modified by undetected_chromedriver
RUN mkdir -p /root/.local/share/undetected_chromedriver
RUN cp /usr/bin/chromedriver /root/.local/share/undetected_chromedriver/chromedriver_copy
RUN chmod +x /root/.local/share/undetected_chromedriver/chromedriver_copy

# Expose the port 5000 for flask and 2223 for SSH and 5555 for Flower
EXPOSE 5000 2223

# Run the application
RUN chmod +x start.sh
CMD ["./start.sh"]