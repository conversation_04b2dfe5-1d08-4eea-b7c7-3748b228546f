# Database Statistics System

This module provides comprehensive database quality metrics and monitoring for the TRO system.

## Overview

The statistics system collects, stores, and visualizes various database quality metrics including:

### From Generate_Report.py:
- **Cases Metrics**: Cases without cause, images, plaintiff IDs, duplicates, AI translations, steps, court mappings
- **Plaintiffs Metrics**: Duplicate plaintiffs, plaintiffs without cases/overviews/translations
- **Steps Metrics**: Steps without cases, duplicates, missing translations

### From Pictures_Clean_Up.py:
- **Pictures in COS not in DataFrame**: Count of orphaned files in cloud storage
- **Cases with pictures missing from COS**: Cases that need reprocessing due to missing images
- **Cases with corrupted image data**: Cases where the images JSON field is corrupted

## Architecture

### Components

1. **StatisticsDB.py**: PostgreSQL database operations
2. **StatisticsCollector.py**: Data collection from various sources
3. **ScheduledTasks.py**: Automated daily/weekly tasks
4. **app_statistics.py**: Flask routes and API endpoints
5. **Frontend**: HTML/CSS/JS with D3.js charts

### Database Schema

```sql
CREATE TABLE statistics_log (
    id SERIAL PRIMARY KEY,
    report_timestamp TIMESTAMPTZ NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    metric_value INTEGER NOT NULL,
    details_preview JSONB NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

## Features

### Frontend Features
- **Real-time Dashboard**: Current values with trend visualization
- **Interactive Charts**: D3.js-powered time series charts
- **Drill-down Details**: Click any metric to see detailed breakdown
- **Time Filtering**: View data for 7 days, 30 days, 90 days, or 1 year
- **Fresh Data**: Details are queried fresh from source databases
- **Direct Links**: Each detail item links to relevant visualizer/plaintiff pages

### Backend Features
- **Automated Collection**: Daily statistics collection at 6 AM EST
- **Data Retention**: Automatic cleanup of old data (90 days retention)
- **API Endpoints**: RESTful API for all statistics operations
- **Error Handling**: Comprehensive error logging and handling
- **Performance**: Optimized queries with database indexing

## API Endpoints

- `GET /statistics` - Statistics page
- `GET /api/statistics/latest` - Get latest statistics for all metrics
- `GET /api/statistics/history` - Get historical data with filtering
- `GET /api/statistics/trend/<metric_name>` - Get trend data for specific metric
- `GET /api/statistics/details/<metric_name>` - Get detailed breakdown (fresh data)
- `POST /api/statistics/collect` - Manually trigger statistics collection

## Installation & Setup

### Prerequisites
- PostgreSQL database with connection credentials in environment variables:
  - `POSTGRES_HOST`
  - `POSTGRES_PORT`
  - `POSTGRES_USER`
  - `POSTGRES_PASSWORD`
  - `POSTGRES_DB`

### Setup Steps

1. **Initialize Database Table**:
   ```python
   from DatabaseManagement.StatisticsDB import init_statistics_table
   init_statistics_table()
   ```

2. **Test the System**:
   ```bash
   python test_statistics.py
   ```

3. **Manual Collection** (optional):
   ```python
   from Statistics.ScheduledTasks import test_statistics_collection
   test_statistics_collection()
   ```

### Scheduled Tasks

The system automatically runs:
- **Daily Collection**: Every day at 6 AM EST
- **Weekly Cleanup**: Every Sunday at 2 AM EST (removes data older than 90 days)

## Usage

### Accessing the Dashboard
Navigate to `/statistics` in your web browser to view the statistics dashboard.

### Manual Statistics Collection
Use the "Collect New Statistics" button on the dashboard or call the API endpoint.

### Viewing Details
Click "View Details" on any metric card to see the detailed breakdown with direct links to cases/plaintiffs.

## Metric Details

### Cases Without Cause
- **Description**: Cases that haven't been scraped yet
- **Details Show**: case_id, docket_number, date_filed, court
- **Links To**: `/visualizer?case_id=xxxx`

### Duplicate Plaintiffs
- **Description**: Plaintiffs with duplicate names
- **Details Show**: plaintiff_id, plaintiff_name
- **Links To**: `/plaintiffs?plaintiff_id=yyyy`

### Pictures in COS Not in DataFrame
- **Description**: Orphaned files in cloud storage
- **Details Show**: plaintiff_id, filename, cos_path
- **Links To**: `/plaintiffs?plaintiff_id=yyyy`

### Cases with Corrupted Image Data
- **Description**: Cases where images JSON is corrupted
- **Details Show**: case_id, docket_number, date_filed
- **Links To**: `/visualizer?case_id=xxxx`

## Customization

### Adding New Metrics

1. **Add Collection Logic** in `StatisticsCollector.py`:
   ```python
   def collect_new_metric():
       # Your collection logic here
       return {
           'metric_name': 'your_metric_name',
           'metric_value': count,
           'details_preview': {'description': 'Your description'}
       }
   ```

2. **Add Detail Handler** in `app_statistics.py`:
   ```python
   elif metric_name == 'your_metric_name':
       # Your detail collection logic
       return {'items': items, 'total': len(items)}
   ```

3. **Update Frontend** in `statistics.js`:
   ```javascript
   const metricConfig = {
       'your_metric_name': {
           title: 'Your Metric Title',
           description: 'Your metric description'
       }
   };
   ```

### Modifying Retention Period
Change the `days_to_keep` parameter in the cleanup functions:
```python
cleanup_old_statistics(days_to_keep=180)  # Keep 6 months instead of 90 days
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify environment variables are set correctly
   - Check PostgreSQL server is running and accessible

2. **Missing Data**
   - Run manual collection: `python Statistics/ScheduledTasks.py`
   - Check logs for collection errors

3. **Frontend Not Loading**
   - Verify static files are accessible
   - Check browser console for JavaScript errors

4. **Slow Performance**
   - Database indexes are created automatically
   - Consider increasing retention cleanup frequency for large datasets

### Debugging

Enable debug logging by setting log level in your application:
```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Future Enhancements

- **Alerting**: Email/Slack notifications for metrics exceeding thresholds
- **Comparative Analysis**: Compare metrics across different time periods
- **Export Functionality**: CSV/Excel export of statistics data
- **Custom Dashboards**: User-configurable metric dashboards
- **Real-time Updates**: WebSocket-based real-time metric updates
