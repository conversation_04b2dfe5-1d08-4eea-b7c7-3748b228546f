# PDF_ReportLab/results_pages.py
from reportlab.platypus import Table, TableStyle, PageBreak
from reportlab.lib.units import mm

from Check.PDF_ReportLab.styles import myStyles, colors
from Check.PDF_ReportLab.helpers import _para, _sp, _hr, _field,  _images_grid, _simple_kv_table, _fit_image, _as_joined, RiskBadge
from Check.PDF_ReportLab.md_to_flowables import markdown_to_flowables

def _results_header(res, styles):
    ip_type = (_field(res, 'ip_type', fallback='').upper())
    risk_level = _field(res, 'risk_level', fallback=_field(res, 'overall_risk', fallback='Low'))
    badge = RiskBadge(risk_level.upper(), risk_level)

    header = Table(
        [[_para(ip_type, styles['SectionTitleLeft']), badge]],
        colWidths=['*', 50*mm]
    )
    header.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), colors['accent_bg']),
        ('VALIGN',     (0, 0), (-1, -1), 'MIDDLE'),
        ('LEFTPADDING',(0, 0), (0, 0), 20),
        ('RIGHTPADDING',(1, 0), (1, 0), 20),
        ('TOPPADDING', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING',(0, 0), (-1, -1), 12),
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
    ]))

    # Risk description (from JSON)
    risk_desc = _field(res, 'risk_description')
    return [header, _sp(6), _para(risk_desc, styles['SummaryBody']), _sp(6), _hr(), _sp(4)]

def _trademark_block(res, styles):
    return [
        ('IP Owner',       _field(res, 'ip_owner', 'owner')),
        ('Text',           _field(res, 'text', 'mark_text', 'trademark_text')),
        ('Registration #', _field(res, 'reg_no', 'registration_no', 'registration_number')),
        ('Serial #',       _field(res, 'ser_no', 'serial_no', 'serial_number')),
        ('Intl. Classes',  _as_joined(_field(res, 'int_cls_list', 'intl_classes', 'classes', fallback=[]))),
    ]

def _patent_block(res, styles):
    return [
        ('IP Owner',       _field(res, 'ip_owner')),
        ('Title',          _field(res, 'text')),
        ('Registration #', _field(res, 'reg_no')),
    ]
def _copyright_block(res, styles):
    return [
        ('IP Owner',       _field(res, 'ip_owner')),
        ('Registration #', _field(res, 'reg_no')),
    ]


def _tro_block(res, styles):
    """TRO section with two modes."""
    # "Has TRO" if any of these hints exist
    has_tro = _field(res, 'plaintiff_id') != '—'

    if has_tro:
        plaintiff = _field(res, 'plaintiff_name')
        last_docket = _field(res, 'last_case_docket')
        filed_date = _field(res, 'last_case_date_filed')
        number_of_cases = _field(res, 'number_of_cases')
        data_rows = [
            ('Plaintiff',  plaintiff),
            ('Last Docket', last_docket),
            ('Filed',      filed_date),
            ('Number of Cases', number_of_cases)
        ]
        table = _simple_kv_table(data_rows, styles, col_widths=[45*mm, None])
        return [
            _sp(6), _hr(), _sp(4),
            _para('TRO Information', styles['SectionTitle']),
            _sp(1.5),
            table
        ]
    else:
        note = 'IP Owner has not initiated TRO in the past'
        return [
            _sp(6), _hr(), _sp(4),
            _para('TRO Information', styles['SectionTitle']),
            _sp(1.5),
            _para(note, styles['SummaryBody'])
        ]

import os

def _images_block_trademark(res, styles, img_cache):
    local_images_count = 0
    url_images_count = 0
    imgs = []

    # Prioritize local paths for IP images
    if 'ip_local_paths' in res and res['ip_local_paths']:
        for path in res['ip_local_paths']:
            if os.path.exists(path):
                imgs.append(path)
        local_images_count = len(imgs)
    
    if not imgs and 'ip_asset_urls' in res:
        imgs = res.get("ip_asset_urls", [])
        url_images_count = len(imgs)

    if not imgs:
        return [], 0, 0
        
    imgs = imgs[:2]
    flowables = [
        _sp(6), _hr(), _sp(4),
        _para('IP Images', styles['SectionTitle']),
        _sp(1.5),
        _images_grid(imgs, img_cache, thumb_w_mm=38, thumb_h_mm=38)
    ]
    return flowables, local_images_count, url_images_count


def _images_block_copyright(res, styles, img_cache):
    local_images_count = 0
    url_images_count = 0
    imgs = []

    # Prioritize local paths for IP images
    if 'ip_local_paths' in res and res['ip_local_paths']:
        for path in res['ip_local_paths']:
            if os.path.exists(path):
                imgs.append(path)
        local_images_count = len(imgs)
    
    if not imgs and 'ip_asset_urls' in res:
        imgs = res.get("ip_asset_urls", [])
        url_images_count = len(imgs)

    if not imgs:
        return [], 0, 0
        
    imgs = imgs[:8]
    flowables = [
        _sp(6), _hr(), _sp(4),
        _para('IP Images', styles['SectionTitle']),
        _sp(1.5),
        _images_grid(imgs, img_cache, thumb_w_mm=50, thumb_h_mm=50)
    ]
    return flowables, local_images_count, url_images_count

def _images_block_patent(res, styles, img_cache, page_width_mm=188):
    local_images_count = 0
    url_images_count = 0
    imgs = []

    # Prioritize local paths for IP images
    if 'ip_local_paths' in res and res['ip_local_paths']:
        for path in res['ip_local_paths']:
            if os.path.exists(path):
                imgs.append(path)
        local_images_count = len(imgs)

    if not imgs and 'ip_asset_urls' in res:
        imgs = res.get("ip_asset_urls", [])
        url_images_count = len(imgs)

    if not imgs:
        return [], 0, 0
        
    imgs = imgs[:9]
    blocks = [_sp(6), _hr(), _sp(4), _para('IP Images', styles['SectionTitle']), _sp(1.5)]
    
    # First large, then a grid
    if imgs:
        big_w = (page_width_mm - 20) * mm  # leave a little breath
        big_h = 60 * mm
        blocks.append(_fit_image(imgs[0], max_w=big_w, max_h=big_h, img_cache=img_cache))
        thumbs = imgs[1:]
        if thumbs:
            blocks += [_sp(2), _images_grid(thumbs, img_cache, thumb_w_mm=38, thumb_h_mm=38)]
            
    return blocks, local_images_count, url_images_count

def create_results_pages(data, img_cache):
    """
    For each result:
      - Header with IP Type + RiskBadge + risk description
      - Pure IP info (different for Trademark vs Patent)
      - TRO information (with switch for 'no plaintiff')
      - IP pictures per type
      - Footer page numbers are drawn via PageTemplate in generate_report.py
      - Next page: the 'report' text (can span multiple pages)
    """
    story = []
    total_local_images = 0
    total_url_images = 0

    results = data.get('results', [])
    for idx, res in enumerate(results, 1):
        ip_type_raw = _field(res, 'ip_type', fallback='').lower()

        # Header
        story += _results_header(res, myStyles)

        # IP Details
        details_title = 'IP Details'
        details_pairs = []
        if 'patent' in ip_type_raw:
            details_title = 'Patent Details'
            details_pairs = _patent_block(res, myStyles)
        elif 'copyright' in ip_type_raw:
            details_title = 'Copyright Details'
            details_pairs = _copyright_block(res, myStyles)
        elif 'trademark' in ip_type_raw:
            details_title = 'Trademark Details'
            details_pairs = _trademark_block(res, myStyles)
        
        story += [
            _para(details_title, myStyles['SectionTitle']),
            _sp(1.5),
            _simple_kv_table(details_pairs, myStyles, col_widths=[45*mm, None]),
        ]

        # TRO
        story += _tro_block(res, myStyles)

        # Images
        if 'patent' in ip_type_raw:
            image_flowables, local_count, url_count = _images_block_patent(res, myStyles, img_cache)
            story += image_flowables
        elif 'copyright' in ip_type_raw: # trademark or copyright, same code for both
            image_flowables, local_count, url_count = _images_block_copyright(res, myStyles, img_cache)
            story += image_flowables
        elif 'trademark' in ip_type_raw:
            image_flowables, local_count, url_count = _images_block_trademark(res, myStyles, img_cache)
            story += image_flowables
            
        total_local_images += local_count
        total_url_images += url_count

        # Report on next page
        story += [PageBreak(), _para('Legal Opinion', myStyles['SectionTitle']), _sp(4)]
        report_md = res.get('report', '')
        story += markdown_to_flowables(report_md, myStyles)

        # Page break before the next result (only if more results)
        if idx < len(results):
            story.append(PageBreak())

    return story, total_local_images, total_url_images
