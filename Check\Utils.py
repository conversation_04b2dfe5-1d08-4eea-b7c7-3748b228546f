import hashlib
import os
import json
from Qdrant.api.utils.db import get_db_connection as get_pg_connection

from Common.uuid_utils import generate_obfuscated_key

# def create_ip_url(plaintiff_id, image_filename):
#     """Create IP URL with proper formatting"""
#     IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_filename}"
#     IP_Url_new = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_filename}"
#     return IP_Url

def create_ip_url(ip_type, seed): # For Trademark the seed is ser_no, for Copyright the seed is reg_no, for Patent the seed is basename(filename) which is the reg_no with the page number
    """Create IP URL with proper formatting"""
    obfuscated_reg_no = generate_obfuscated_key(seed)
    if ip_type == "Patent":
        extension = "png"
    else:
        extension = "webp"
    
    IP_Url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/{ip_type}s/{obfuscated_reg_no}.{extension}"
    
    return IP_Url


def insert_check_api_request_to_pg(request_data):
    """
    Inserts a single record into the check_api_requests table in PostgreSQL.
    """
    conn = get_pg_connection()
    cursor = conn.cursor()
    
    # Convert lists/dicts to JSON strings for JSONB columns
    for key, value in request_data.items():
        if isinstance(value, (dict, list)):
            request_data[key] = json.dumps(value)

    columns = request_data.keys()
    values = [request_data[col] for col in columns]
    
    insert_query = f"""
    INSERT INTO check_api_requests ({', '.join(columns)})
    VALUES ({', '.join(['%s'] * len(values))})
    ON CONFLICT (id) DO UPDATE SET
    {', '.join([f"{col} = EXCLUDED.{col}" for col in columns if col != 'id'])}
    """
    
    try:
        cursor.execute(insert_query, values)
        print(f"Successfully inserted/updated API request to PostgreSQL for check_id: {request_data.get('id')}")
    except Exception as e:
        print(f"Error inserting API request to PostgreSQL: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def insert_check_api_result_to_pg(result_data):
    """
    Inserts a single record into the check_api_results table in PostgreSQL.
    """
    conn = get_pg_connection()
    cursor = conn.cursor()

    # Convert lists/dicts to JSON strings for JSONB columns
    for key, value in result_data.items():
        if isinstance(value, (dict, list)):
            result_data[key] = json.dumps(value)
    
    columns = result_data.keys()
    values = [result_data[col] for col in columns]
    
    # This table doesn't have a unique constraint on check_id, so we just insert.
    insert_query = f"""
    INSERT INTO check_api_results ({', '.join(columns)})
    VALUES ({', '.join(['%s'] * len(values))})
    """
    
    try:
        cursor.execute(insert_query, values)
        print(f"Successfully inserted API result to PostgreSQL for check_id: {result_data.get('check_id')}")
    except Exception as e:
        print(f"Error inserting API result to PostgreSQL: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()