import asyncio
import pandas as pd
import logging
import time
import re
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from Alerts.Chrome_Driver import send_keyboard_input, random_delay, move_mouse_to
from Alerts.LexisNexis.Login import get_logged_in_browser
from Alerts.LexisNexis.Scrape_Date_Range import extract_lexisnexis_permalink
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_id, get_table_from_GZ
from Alerts.ReprocessCases import reprocess_cases
from Common.Constants import court_mapping
from langfuse import observe
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def apply_ip_filter(driver):
    """Apply Intellectual Property practice area filter"""
    try:
        logger.info("Applying 'Intellectual Property' practice area filter...")
        filter_input_pa = WebDriverWait(driver, 10).until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "input.dropdowncontainersearch[placeholder='Type to filter Litigation Areas']"))
        )
        driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", filter_input_pa)
        filter_input_pa.click()
        time.sleep(0.5)
        filter_input_pa.send_keys("Intellectual Property")
        time.sleep(0.5)
        ip_option = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//span[contains(@class, 'markabletextlabel') and normalize-space()='Intellectual Property'] | //span[contains(@class, 'markabletextlabel')][.//mark[contains(text(),'Intellectual')]]"))
        )
        ip_option.click()
        logger.info("Selected 'Intellectual Property' practice area.")
        time.sleep(1)
        driver.find_element(By.TAG_NAME, "body").click()
        time.sleep(0.5)
    except Exception as e_pa:
        logger.error(f"Could not apply Practice Area filter: {e_pa}")

def clean_docket(docket_str):
    """Clean and format docket number"""
    docket_cleaned = docket_str.replace("CV", "cv").replace("cv", "-cv-")
    if "/" in docket_cleaned:
        docket_cleaned = docket_cleaned.split("/")[1]
    while len(docket_cleaned) < 13 and "-cv-" in docket_cleaned:
        docket_cleaned = docket_cleaned.replace("-cv-", "-cv-0")
    return docket_cleaned

def perform_docket_search(driver, docket_number, court=None):
    """Perform docket search directly without helper function"""
    try:
        # Wait for docket input field
        docket_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, 'docketNumberInput'))
        )
        docket_input.clear()
        
        # Put the docket number in the 'docketNumberInput' field
        if "-cv-" in docket_number:
            docket_number = docket_number.replace("-cv-", "cv")
        while "cv0" in docket_number:
            docket_number = docket_number.replace("cv0", "cv")
        
        send_keyboard_input(driver, docket_input, docket_number)
        
        # Click search button
        search_button = driver.find_element(By.ID, 'triggersearch')
        move_mouse_to(driver, search_button)
        search_button.click()
        random_delay()
        return True
    except Exception as e:
        logger.error(f"Error performing search: {e}")
        return False

def extract_metadata_from_result_element(result_element):
    """Extract metadata from a search result element"""
    metadata = {'court': '', 'docket': '', 'date_filed': None}
    try:
        metadata_row = result_element.find_element(By.CSS_SELECTOR, 'metadata-row')
        metadata_element = metadata_row.find_element(By.CSS_SELECTOR, 'div.metaData')
        spans = metadata_element.find_elements(By.CSS_SELECTOR, 'span.metaDataItem')
        
        if len(spans) >= 3:
            court_raw = spans[0].text.strip()
            metadata['court'] = court_mapping.get(court_raw, court_raw)
            
            date_filed_str = spans[1].text.strip()
            metadata['date_filed'] = pd.to_datetime(date_filed_str, errors='coerce').date()
            
            docket_str = spans[2].text.strip()
            metadata['docket'] = clean_docket(docket_str)
    except Exception as e:
        logger.error(f"Error extracting metadata from result element: {e}")
    return metadata

def extract_case_metadata_from_page(driver):
    """Extract case metadata from case detail page"""
    metadata = {'court': '', 'docket': '', 'date_filed': None}
    try:
        # Extract title
        title = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, 'Header'))
        ).text.strip()
        
        # Extract metadata from case info table
        table = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'SS_DataTable'))
        )
        rows = table.find_elements(By.TAG_NAME, 'tr')
        
        for row in rows:
            cols = row.find_elements(By.TAG_NAME, 'td')
            if len(cols) >= 2:
                label = cols[0].text.strip().lower()
                value = cols[1].text.strip()
                
                if 'court' in label:
                    metadata['court'] = court_mapping.get(value, value)
                elif 'docket' in label:
                    metadata['docket'] = clean_docket(value)
                elif 'date filed' in label:
                    metadata['date_filed'] = pd.to_datetime(value, errors='coerce').date()
        
        return title, metadata
    except TimeoutException:
        logger.warning("Timeout while extracting metadata from case page")
        return None, metadata
    except Exception as e:
        logger.error(f"Error extracting case metadata: {e}")
        return None, metadata

@observe(capture_input=False, capture_output=False)
async def process_single_docket(docket_number, article_content, db_case_df=None, plaintiff_df=None):
    """Process a single docket number through LexisNexis"""
    logger.info(f"Starting processing for docket: {docket_number}")
    driver = get_logged_in_browser()
    original_window = driver.current_window_handle
    
    if db_case_df is None:
        db_case_df = get_table_from_GZ("tb_case")
    if plaintiff_df is None:
        plaintiff_df = get_table_from_GZ("tb_plaintiff")
    
    try:
        # Step 1: Navigate to search page and apply filters
        driver.get('https://advance.lexis.com/courtlinksearch')
        apply_ip_filter(driver)  # Apply IP practice area filter
        
        # Perform search directly
        if not perform_docket_search(driver, docket_number):
            raise Exception("Search failed")
        
        # Check if we're on case page directly
        case_data = {}
        try:
            # Check for case page
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header")))
            logger.info(f"Directly on case page for {docket_number}")
            case_title, metadata = extract_case_metadata_from_page(driver)
            ln_url = extract_lexisnexis_permalink(driver, docket_number)
            
            case_data = {
                'title': case_title,
                'docket': docket_number,
                'court': metadata['court'],
                'date_filed': metadata['date_filed'],
                'ln_url': ln_url
            }
        except TimeoutException:
            # Handle multiple results
            logger.info(f"Multiple results for {docket_number}, selecting with LLM")
            results = WebDriverWait(driver, 20).until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, 'result-item[data-id^="sr"]')))
            selected_result = None
            if len(results) == 1:
                title = results[0].text.split("\n")[1]
                plaintiff_name = title.split(" v. ")[0].split(" V. ")[0].split(" v ")[0].split(" V ")[0]
                plaintiff_short_name = plaintiff_name.split(",")[0]
                if plaintiff_short_name in article_content:
                    selected_result = results[0]
            
            if not selected_result:   
                # Prepare input for LLM selection
                llm_input = f"""Please help me select which of these legal cases is the one reference in the article content: 
                -----------------------------
                {article_content}
                -----------------------------

    You must return only a JSON object with the key 'case_index' and the value as the index of the case.
    If no case is a good match, the value for 'case_index' should be -1.
    Note that the name of the plaintiff is not akways the same, e.g. due to different legal entity or different country difference.

    Here are the cases:"""
                for i, result in enumerate(results):
                    title_el = result.find_element(By.CSS_SELECTOR, 'a.titleLink')
                    llm_input += f"\n- Case Index {i}: {title_el.text.strip()}"

                # Call LLM for selection (using article_content as context)
                llm_response_text = await vertex_genai_multi_async([("text", llm_input)])
                
                # Process selected result
                try:
                    # Extract JSON from LLM response
                    json_response = get_json(llm_response_text)
                    if not json_response or 'case_index' not in json_response:
                        raise ValueError("Invalid JSON response from LLM.")

                    selected_index = int(json_response['case_index'])
                    
                    if selected_index == -1:
                        raise ValueError("LLM indicated no matching case.")
                    
                    selected_result = results[selected_index]
                except (ValueError, IndexError, TypeError) as e:
                    logger.error(f"LLM returned an invalid index or no match from response: '{llm_response_text}'. Error: {e}")
                    # Decide how to handle this - for now, we'll just raise the exception
                    raise
                
                
            metadata = extract_metadata_from_result_element(selected_result)
            case_title = selected_result.find_element(By.CSS_SELECTOR, 'a.titleLink').text.strip()
            
            # Open case in new tab
            link = selected_result.find_element(By.CSS_SELECTOR, 'a.titleLink')
            driver.execute_script("arguments[0].scrollIntoView();", link)
            driver.execute_script("window.open(arguments[0].href);", link)
            WebDriverWait(driver, 10).until(EC.number_of_windows_to_be(2))
            driver.switch_to.window(driver.window_handles[-1])
            
            # Get permalink
            ln_url = extract_lexisnexis_permalink(driver, docket_number)
            
            case_data = {
                'title': case_title,
                'docket': metadata['docket'] or docket_number,
                'court': metadata['court'],
                'date_filed': metadata['date_filed'],
                'ln_url': ln_url
            }
            
            # Close case tab and return to main window
            driver.close()
            driver.switch_to.window(original_window)
        
        # Step 4: Save to database
        logger.info(f"Saving case to database: {docket_number}")
        new_case_df = pd.DataFrame([case_data])
        new_case_with_id = insert_and_update_df_to_GZ_id(new_case_df, "tb_case", "docket", "court")
        db_case_df.loc[len(db_case_df)] = new_case_with_id.iloc[0]
        
        # Step 5: Reprocess case
        logger.info(f"Reprocessing case: {docket_number}")
        
        
        processing_options = {
            'update_steps': True,
            'process_pictures': True,
            'upload_files_nas': False,
            'upload_files_cos': True,
            'run_plaintiff_overview': True,
            'run_summary_translation': True,
            'run_step_translation': True,
            'save_to_db': True,
            'processing_mode': 'full_reprocess',
            'refresh_days_threshold': 15
        }
        
        await reprocess_cases(
            cases_to_reprocess=new_case_with_id,
            processing_options=processing_options,
            trace_name=f"Single Docket Processing: {docket_number}",
            full_cases_df=db_case_df,
            plaintiff_df=plaintiff_df
        )
        

        logger.info(f"Successfully processed docket: {docket_number}")
        return new_case_with_id, db_case_df, plaintiff_df
        
    except Exception as e:
        logger.error(f"Error processing docket {docket_number}: {e}")
        raise
    finally:
        driver.quit()

if __name__ == '__main__':
    # Example usage
    asyncio.run(process_single_docket("22-cv-01234", article_content={}))