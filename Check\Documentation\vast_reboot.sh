#!/usr/bin/env bash
# mirror stdout+stderr to a boot log
exec >>/var/log/onstart.log 2>&1
# echo every command for easy debugging
set -x
# fail fast & loud
set -euo pipefail

echo "Restarting redis..."
# Using systemctl is the modern way, but vast.ai does not have systemctl => we use service....
service redis-server restart

echo "Switching to /workspace..."
cd /workspace
VENV_DIR="/venv/main/bin"
HYPERCORN="${VENV_DIR}/hypercorn"

# convert CRLF→LF if needed (no-op when already Unix)
if grep -q $'\r' .env; then
  echo "[onstart] Converting .env from CRLF to LF"
  sed -i 's/\r$//' .env
fi

echo "Loading .env into current shell..."
set -a
. .env
set +a

echo "Syncing variables to /etc/environment (idempotent) so that it is available to every shell..."
while IFS='=' read -r k v; do
  if grep -q "^${k}=" /etc/environment; then
      sed -i "s|^${k}=.*|${k}=${v}|" /etc/environment
  else
      echo "${k}=${v}" >> /etc/environment
  fi
done < <(grep -Ev '^\s*($|#)' .env)


echo "Restarting cloudflared..."

service cloudflared restart
# echo "Setting up cf-ddns cron job..."
# chmod +x /workspace/cf-ddns.sh
# echo "*/5 * * * * root /workspace/cf-ddns.sh >> /var/log/cf-ddns.log 2>&1" > /etc/cron.d/cf-ddns
# service cron restart

echo "Ensuring Node Exporter is installed and running..."
# --- Node Exporter Setup ---
NODE_EXPORTER_BINARY="/usr/local/bin/node_exporter"
NODE_EXPORTER_VERSION="1.9.1" # Specify a version for consistency
NODE_EXPORTER_URL="https://github.com/prometheus/node_exporter/releases/download/v${NODE_EXPORTER_VERSION}/node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz"
TMUX_SESSION_NAME="node_exporter"

# 1. Install Node Exporter if not present (idempotent)
if [ ! -f "${NODE_EXPORTER_BINARY}" ]; then
    echo "Node Exporter not found. Installing v${NODE_EXPORTER_VERSION}..."
    # Use a temporary directory for clean download and extraction
    TEMP_DIR=$(mktemp -d)
    curl -sL "${NODE_EXPORTER_URL}" | tar -xz -C "${TEMP_DIR}" --strip-components=1
    mv "${TEMP_DIR}/node_exporter" "${NODE_EXPORTER_BINARY}"
    rm -rf "${TEMP_DIR}"
    echo "Node Exporter installed successfully to ${NODE_EXPORTER_BINARY}"
else
    echo "Node Exporter is already installed."
fi

# This ensures that everything (including HF's chache) are in the same location as if I run manually
export HOME=/workspace
# ----- One HF cache for everything -----
export HF_HOME=/workspace/hf_cache
export HF_HUB_CACHE=/workspace/hf_cache/hub
export HF_XET_CACHE=/workspace/hf_cache/xet
export TRANSFORMERS_CACHE=/workspace/hf_cache/transformers
export HF_DATASETS_CACHE=/workspace/hf_cache/datasets
# make sure it exists
mkdir -p /workspace/hf_cache/{hub,xet,transformers,datasets}

# PyTorch: reduce fragmentation & cap caching allocator
export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:128,expandable_segments:True,garbage_collection_threshold:0.9"

# Paddle: grow-on-demand and smaller pool
export FLAGS_allocator_strategy=auto_growth
export FLAGS_fraction_of_gpu_memory_to_use=0.30
export FLAGS_initial_gpu_memory_in_mb=512
export FLAGS_reallocate_gpu_memory_in_mb=256


start_tmux_once () {
  local session=$1; shift
  if ! tmux has-session -t "$session" 2>/dev/null; then
      echo "Launching $session …"
      tmux new-session -d -s "$session" "$@"
  else
      echo "$session already running."
  fi
}

# Start the embedding service in TMUX, you can see the logs by attaching to the session, but NOT in graphana
start_tmux_once app_embeddings /venv/main/bin/python app_embeddings.py
# Start the ARQ worker in TMUX
start_tmux_once arq_worker /venv/main/bin/arq Check.ARQ.worker.WorkerSettings --watch Check
start_tmux_once arq_pdf_worker /venv/main/bin/arq Check.ARQ.pdf_worker.PdfWorkerSettings

# Prometheus metrics directory setup
export PROMETHEUS_MULTIPROC_DIR=/workspace/prometheus_metrics
mkdir -p $PROMETHEUS_MULTIPROC_DIR
rm -f $PROMETHEUS_MULTIPROC_DIR/*.db

start_tmux_once fastapi_app /venv/main/bin/hypercorn app_apistudio:app --workers 4 --bind 0.0.0.0:5000
start_tmux_once node_exporter "${NODE_EXPORTER_BINARY}"