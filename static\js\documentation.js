// Theme toggle functionality
function toggleTheme() {
    const body = document.body;
    const themeToggle = document.querySelector('.theme-toggle');
    const darkTheme = document.getElementById('prism-dark-theme');
    
    if (body.getAttribute('data-theme') === 'dark') {
        body.removeAttribute('data-theme');
        themeToggle.textContent = '🌙';
        darkTheme.disabled = true;
        localStorage.setItem('theme', 'light');
    } else {
        body.setAttribute('data-theme', 'dark');
        themeToggle.textContent = '☀️';
        darkTheme.disabled = false;
        localStorage.setItem('theme', 'dark');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeToggle = document.querySelector('.theme-toggle');
    const darkTheme = document.getElementById('prism-dark-theme');
    
    if (savedTheme === 'dark') {
        document.body.setAttribute('data-theme', 'dark');
        themeToggle.textContent = '☀️';
        darkTheme.disabled = false;
    }
}

// Navigation functionality
function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const sections = document.querySelectorAll('.section');

    // Handle navigation clicks
    navItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = item.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({ behavior: 'smooth' });
                updateActiveNavItem(item);
            }
        });
    });

    // Handle scroll spy
    window.addEventListener('scroll', () => {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.pageYOffset >= sectionTop - 100) {
                current = section.getAttribute('id');
            }
        });

        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('href') === `#${current}`) {
                item.classList.add('active');
            }
        });
    });
}

function updateActiveNavItem(activeItem) {
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    activeItem.classList.add('active');
}

// Code tab functionality
function initializeCodeTabs() {
    const codeContainers = document.querySelectorAll('.code-container');
    
    codeContainers.forEach(container => {
        const tabs = container.querySelectorAll('.code-tab');
        const blocks = container.querySelectorAll('.code-block');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const lang = tab.getAttribute('data-lang');
                
                // Update active tab
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Update active code block
                blocks.forEach(block => {
                    block.classList.remove('active');
                    if (block.getAttribute('data-lang') === lang) {
                        block.classList.add('active');
                    }
                });
            });
        });
    });
}

// Copy code functionality
function copyCode(button) {
    const container = button.closest('.code-container');
    const activeBlock = container.querySelector('.code-block.active');
    const code = activeBlock.querySelector('code');
    
    if (code) {
        const text = code.textContent;
        navigator.clipboard.writeText(text).then(() => {
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            button.classList.add('copied');
            
            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('copied');
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy code:', err);
        });
    }
}

// Disclosure functionality
function toggleDisclosure(header) {
    const disclosure = header.parentElement;
    const arrow = header.querySelector('.disclosure-arrow');
    
    disclosure.classList.toggle('open');
    
    if (disclosure.classList.contains('open')) {
        arrow.textContent = '▼';
    } else {
        arrow.textContent = '▶';
    }
}

// Global language toggle functionality
function initializeGlobalLanguageToggle() {
    // This would be implemented if you want a global Python/JS toggle
    // For now, each code block has its own toggle
}

// Responsive sidebar toggle (for mobile)
function initializeMobileMenu() {
    // Add mobile menu button if needed
    if (window.innerWidth <= 768) {
        // Mobile-specific functionality can be added here
    }
}

// Initialize all functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeTheme();
    initializeNavigation();
    initializeCodeTabs();
    initializeGlobalLanguageToggle();
    initializeMobileMenu();
});

// Handle window resize
window.addEventListener('resize', () => {
    initializeMobileMenu();
});
