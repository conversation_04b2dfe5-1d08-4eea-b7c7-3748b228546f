"""
Trademark Database Operations Module

This module provides functions to interact with the PostgreSQL database for trademark data.
"""

import os
import psycopg2
import psycopg2.extras
from Common.uuid_utils import generate_uuid
from dotenv import load_dotenv
from logdata import log_message
import re
import datetime
import time
import pandas as pd

# Load environment variables
load_dotenv()

# Removed logging configuration block
# logger = logging.getLogger(__name__) # Removed logger initialization

def get_db_connection():
    """
    Create a connection to the PostgreSQL database.

    Returns:
        connection: PostgreSQL database connection
    """
    try:
        conn = psycopg2.connect(
            host=os.getenv("POSTGRES_HOST"),
            port=os.getenv("POSTGRES_PORT"),
            user=os.getenv("POSTGRES_USER"),
            password=os.getenv("POSTGRES_PASSWORD"),
            dbname=os.getenv("POSTGRES_DB")
        )
        return conn
    except Exception as e:
        log_message(f"Error connecting to database: {str(e)}", level='ERROR')
        raise


def upsert_trademarks(trademarks):
    """
    Insert or update trademark records in the database using UPSERT logic.

    Args:
        trademarks (list): List of trademark dictionaries to insert/update

    Returns:
        int: Number of records processed
    """
    # Filter trademarks to keep only the last entry for each ser_no
    original_count = len(trademarks)

    log_message(f"Received {original_count} trademarks for upsert.", level='INFO')

    filtered_trademarks = {}
    skipped_count = 0
    for trademark in trademarks:
        ser_no = trademark.get('ser_no')
        if ser_no:
            filtered_trademarks[ser_no] = trademark
        else:
            log_message(f"Skipping trademark due to missing 'ser_no': {trademark}", level='WARNING')
            skipped_count += 1

    trademarks = list(filtered_trademarks.values())
    log_message(f"Filtered trademarks: {len(trademarks)} remaining after removing duplicates/missing ser_no (skipped {skipped_count}).", level='INFO')

    # Check if the list is empty after filtering
    if not trademarks:
        log_message("No trademarks left to upsert after filtering.", level='INFO')
        return 0

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Define the columns to update
        columns = [
            'id', 'reg_no', 'ser_no', 'TRO', 'applicant_name', 'mark_text', 'int_cls',
            'filing_date', 'nb_suits', 'country_codes', 'mark_current_status_code',
            'mark_feature_code', 'mark_standard_character_indicator',
            'mark_disclaimer_text_daily', 'mark_image_colour_statement_daily',
            'mark_translation_statement_daily', 'name_portrait_statement_daily',
            'mark_description_statement_daily', 'certification_mark_statement_daily',
            'lining_stippling_statement_daily', 'section_2f_statement_daily',
            'goods_services', 'goods_services_text_daily', 'case_file_statements_other', 'info_source', 'image_source'
        ]

        # Prepare the SQL statement for UPSERT
        placeholders = ', '.join(['%s'] * len(columns))
        column_names = ', '.join(columns)

        # Build the SET clause for the UPDATE part
        # Exclude 'id' and 'ser_no' from update as they are primary/unique keys
        update_set = ', '.join([f"{col} = EXCLUDED.{col}" for col in columns if col not in ['id', 'ser_no']])

        sql = f"""
        INSERT INTO trademarks ({column_names})
        VALUES ({placeholders})
        ON CONFLICT (id)
        DO UPDATE SET {update_set}
        """

        # Process trademarks in batches
        batch_size = 5000
        processed = 0

        for i in range(0, len(trademarks), batch_size):
            batch = trademarks[i:i+batch_size]
            batch_values = []

            for trademark in batch:
                # Generate UUID for the trademark
                ser_no = trademark.get('ser_no')
                if ser_no:
                    trademark['id'] = generate_uuid(ser_no)
                else:
                    # This case should ideally be caught by the initial filtering, but as a safeguard
                    log_message(f"Cannot generate UUID for trademark due to missing 'ser_no': {trademark}", level='WARNING')
                    continue # Skip this trademark if ser_no is missing

                # Set info_source
                trademark['info_source'] = trademark.pop('last_updated_source', "not specified")

                # Prepare values in the correct order
                row_values = [trademark.get(col) for col in columns]
                batch_values.append(row_values)

            # Execute batch insert
            psycopg2.extras.execute_batch(cursor, sql, batch_values)
            conn.commit()
            processed += len(batch)
            log_message(f"Databsase upsert: Processed {processed}/{len(trademarks)} trademark records")

        return processed

    except Exception as e:
        if conn:
            conn.rollback()
        log_message(f"Error upserting trademarks: {str(e)}", level='ERROR')
        raise

    finally:
        if conn:
            conn.close()

def update_image_source(ser_no, image_source):
    """
    Update the image_source field for a trademark record.

    Args:
        ser_no (str): Serial number of the trademark
        image_source (str): Source of the image (e.g., 'USPTO_URL', 'NotFound')

    Returns:
        bool: True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        sql = """
        UPDATE trademarks
        SET image_source = %s
        WHERE ser_no = %s
        """

        cursor.execute(sql, (image_source, ser_no))
        conn.commit()

        return cursor.rowcount > 0

    except Exception as e:
        if conn:
            conn.rollback()
        log_message(f"Error updating image source for {ser_no}: {str(e)}", level='ERROR')
        return False

    finally:
        if conn:
            conn.close()

def delete_null_ser_no_entries():
    """
    Delete all trademark entries where ser_no is NULL.

    Returns:
        int: Number of records deleted
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        sql = """
        DELETE FROM trademarks
        WHERE ser_no IS NULL
        """

        cursor.execute(sql)
        deleted_count = cursor.rowcount
        conn.commit()

        log_message(f"Deleted {deleted_count} trademark records with NULL ser_no")
        return deleted_count

    except Exception as e:
        if conn:
            conn.rollback()
        log_message(f"Error deleting trademarks with NULL ser_no: {str(e)}")
        raise

    finally:
        if conn:
            conn.close()

def get_last_trademark_update_time():
    """
    Get the most recent update_time from the trademarks table.
    Returns:
        datetime or None: The latest update_time, or None if table is empty.
    """
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT MAX(update_time) FROM trademarks")
        result = cursor.fetchone()
        return result[0] if result and result[0] is not None else None
    finally:
        conn.close()

def get_latest_daily_info_source_date():
    """
    Get the most recent date from info_source where it starts with 'daily_' and contains a date in yymmdd format.
    Returns:
        datetime.date or None: The latest date found, or None if not found.
    """
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT info_source FROM trademarks WHERE info_source LIKE 'daily_%' ORDER BY info_source DESC LIMIT 1000")
        rows = cursor.fetchall()
        max_date = None
        for row in rows:
            info_source = row[0]
            # Match pattern like daily_apc250616.zip
            m = re.search(r'daily_.*?(\d{6})', info_source)
            if m:
                date_str = m.group(1)
                try:
                    date_obj = datetime.datetime.strptime(date_str, "%y%m%d").date()
                    if max_date is None or date_obj > max_date:
                        max_date = date_obj
                except Exception:
                    continue
        return max_date
    finally:
        conn.close()

def delete_trademarks_by_reg_no(reg_nos):
    """
    Delete trademark entries where reg_no matches any value in reg_nos.
    Args:
        reg_nos (list): List of reg_no strings to delete.
    Returns:
        int: Number of records deleted
    """
    if not reg_nos:
        return 0
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        sql = """
        DELETE FROM trademarks WHERE reg_no = ANY(%s)
        """
        cursor.execute(sql, (reg_nos,))
        deleted = cursor.rowcount
        conn.commit()
        return deleted
    except Exception as e:
        if conn:
            conn.rollback()
        log_message(f"Error deleting trademarks by reg_no {reg_nos}: {str(e)}", level='ERROR')
        return 0
    finally:
        if conn:
            conn.close()

def get_all_existing_ser_no():
    """
    Get all existing serial numbers (ser_no) from the trademarks table.
    Returns:
        set: Set of all ser_no strings in the database.
    """
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT ser_no FROM trademarks")
        rows = cursor.fetchall()
        return set(row[0] for row in rows if row[0])
    finally:
        conn.close()


def get_tro_protected_ser_nos():
    """
    Get set of ser_no values that have TRO flag set to TRUE.
    These trademarks should NOT be deleted even if they are expired.

    Returns:
        set: Set of ser_no values with TRO flag = TRUE
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT ser_no FROM trademarks WHERE tro IS TRUE")
        results = cursor.fetchall()

        tro_protected = set(row[0] for row in results if row[0])
        log_message(f"Found {len(tro_protected)} TRO-protected trademarks", level='INFO')

        return tro_protected

    except Exception as e:
        log_message(f"Error fetching TRO-protected ser_nos: {str(e)}", level='ERROR')
        return set()
    finally:
        if conn:
            conn.close()


def delete_trademarks_by_ser_no(ser_nos):
    """
    Delete trademark entries where ser_no matches any value in ser_nos.
    Args:
        ser_nos (list): List of ser_no strings to delete.
    Returns:
        int: Number of records deleted
    """
    if not ser_nos:
        return 0
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        sql = """
        DELETE FROM trademarks WHERE ser_no = ANY(%s)
        """
        cursor.execute(sql, (ser_nos,))
        deleted = cursor.rowcount
        conn.commit()
        return deleted
    except Exception as e:
        if conn:
            conn.rollback()
        log_message(f"Error deleting trademarks by ser_no {ser_nos}: {str(e)}", level='ERROR')
        return 0
    finally:
        if conn:
            conn.close()

def get_table_from_db(table_name, chunk_size=1000, where_clause: str = None):
    start_time = time.time()
    all_rows = []
    columns = []
    df = pd.DataFrame()  # Ensure df is always defined

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                print(f"Fetching '{table_name}' from PostgreSQL: ", end="", flush=True)

                query = f"SELECT * FROM {table_name}"
                if where_clause and where_clause.strip():
                    query += f" WHERE {where_clause}"

                cursor.execute(query)
                columns = [desc[0] for desc in cursor.description]

                fetched_count = 0
                while True:
                    fetch_start_time = time.time()
                    # Fetch data in chunks
                    chunk = cursor.fetchmany(chunk_size)
                    if not chunk:
                        break

                    all_rows.extend(chunk)
                    fetched_count += len(chunk)
                    fetch_duration = time.time() - fetch_start_time
                    total_duration = time.time() - start_time
                    print(f" | {fetched_count/1000:.0f}k ({fetch_duration:.1f}s)", end="", flush=True)

        # Convert to DataFrame
        if not all_rows:
            print(f"Warning: No data fetched for table {table_name}.")
            df = pd.DataFrame([], columns=columns)
        else:
            df = pd.DataFrame(all_rows, columns=columns)
            del all_rows # Free memory

        print(f" | DONE ({time.time() - start_time:.2f}s)")

    except Exception as e:
        print(f"An error occurred while fetching or processing table {table_name}: {e}")

    return df



if __name__ == "__main__":
    # Delete all entries where ser_no is NULL
    # deleted_count = delete_null_ser_no_entries()
    get_table_from_db("trademarks")
