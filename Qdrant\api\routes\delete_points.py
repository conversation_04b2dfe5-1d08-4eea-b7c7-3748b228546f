"""
Delete points endpoint for removing points from collections.
"""

from fastapi import APIRouter, Depends, HTTPException, status

from models.schemas import DeletePointsRequest, DeletePointsResponse
from utils.auth import verify_token
from services.qdrant_service import delete_points

router = APIRouter()

@router.post("/delete_points", response_model=DeletePointsResponse, dependencies=[Depends(verify_token)])
async def delete_points_endpoint(request: DeletePointsRequest):
    """
    Delete Points endpoint.
    Delete points from a collection.
    
    Args:
        request: The delete points request.
        
    Returns:
        The delete points response.
    """
    # Validate collection name
    if request.collection_name not in ["IP_Assets", "Product_Images"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid collection name. Must be 'IP_Assets' or 'Product_Images'."
        )
    
    # Delete points
    result = delete_points(request.collection_name, request.point_ids)
    
    return DeletePointsResponse(
        status="submitted",
        operation_id=result.operation_id,
        result=True
    )
