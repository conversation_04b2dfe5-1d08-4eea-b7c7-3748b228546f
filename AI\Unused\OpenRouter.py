import os
from openai import OpenAI

# Free models are limited to 20 requests/minute and 200 requests/day

# !!! Can be used with links from the internet !!! This is the only model/provider that can do this

def openrouter_qwen(prompt, model_name="qwen/qwen2.5-vl-72b-instruct:free"):
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=os.environ["OPENROUTER_API_KEY"],
    )

    completion = client.chat.completions.create(
        model=model_name,
        messages=[{
            "role": "user",
            "content": [
                {"type": "text","text": prompt},
                {"type": "image_url", "image_url": {"url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"}}
            ]
        }]
    )
    # print(completion.choices[0].message.content)
    return completion.choices[0].message.content



if __name__ == "__main__":
    # print(openrouter_text("Tell me a joke", "meta-llama/llama-3.2-90b-vision-instruct:free"))
    # print(openrouter_text("Tell me a joke", "meta-llama/llama-3.2-11b-vision-instruct:free"))
    # print(openrouter_text("Tell me a joke", "google/gemini-exp-1206:free"))
    # print(openrouter_text("Tell me a joke", "google/gemini-exp-1121:free"))
    print(openrouter_qwen("Describe the image in detail"))



