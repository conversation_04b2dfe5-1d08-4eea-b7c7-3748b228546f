{% include 'common/api_studio_header.html' %}
<script src="{{ url_path_for('static', path='api_studio/reverse-check.js') }}"></script>

    <div class="container main-content">
        <div class="left">
            <h2 data-i18n="reverse_check_results">Reverse Check Results</h2>
            
            <!-- API Key Input -->
            <div class="form-group">
                <label for="reverse_check_api_key" data-i18n="api_key">API Key:</label><span class="required-field">*</span>
                <input type="text" id="reverse_check_api_key" name="reverse_check_api_key" placeholder="Enter your API key">
            </div>

            <!-- Date Range Selection -->
            <div class="form-group">
                <label for="start_date" data-i18n="start_date">Start Date:</label><span class="required-field">*</span>
                <input type="date" id="start_date" name="start_date">
            </div>
            
            <div class="form-group">
                <label for="end_date" data-i18n="end_date">End Date:</label><span class="required-field">*</span>
                <input type="date" id="end_date" name="end_date">
            </div>

            <button type="button" id="fetchReverseCheckBtn" class="submit-btn" data-i18n="fetch_reverse_check">Fetch Reverse Check</button>

            <!-- Reverse Check List -->
            <div id="reverseCheckList" class="reverse-check-list" style="margin-top: 20px;">
                <h3 data-i18n="available_reverse_checks">Available Reverse Checks</h3>
                <div id="reverseCheckTree" class="reverse-check-tree">
                    <!-- Reverse checks will be populated here -->
                </div>
            </div>
        </div>

        <div class="right">
            <div class="output-header">
                <h2 data-i18n="reverse_check_details">Reverse Check Details</h2>
                <div class="controls">
                    <button id="toggleReportBtn" class="toggle-btn" data-i18n="hide_report">Hide Report</button>
                </div>
            </div>
            
            <div id="output" class="output">
                <p data-i18n="select_reverse_check">Select a reverse check from the left panel to view details</p>
            </div>
        </div>
    </div>

    <!-- Hidden input for language state -->
    <input type="hidden" id="language" value="en">

    <script>
        // Initialize the reverse check page
        document.addEventListener('DOMContentLoaded', function() {
            initializeReverseCheckPage();
        });
    </script>
</body>
</html>
