body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #bbbbbb;
}

.button-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.button-group {
    display: flex;
    gap: 10px;
}

button, .button {
    padding: 12px 24px;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    display: inline-block;
}

button:hover, .button:hover {
    background-color: #2980b9;
}

input[type="date"] {
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
}


.checkbox-wrapper {
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

.checkbox-wrapper input[type="checkbox"] {
    margin-right: 5px;
    vertical-align: middle;
}

.checkbox-wrapper label {
    vertical-align: middle;
}


#progressBar {
    display: flex;
    margin: 20px 0;
}

.step {
    flex: 1;
    padding: 10px;
    text-align: center;
    border: 1px solid #ccc;
    cursor: pointer;
}

.step + .step {
    border-left: none;
}

.step.pending {
    background-color: #f0f0f0;
}

.step.running {
    background-color: #ffe680;
}

.step.completed {
    background-color: #b3ffb3;
}

.step.failed {
    background-color: #ff9999;
}

#logsContainer {
    height: 65vh;
    overflow-y: auto;
    border: 1px solid #282828;
    padding: 10px;
    margin-top: 20px;
    background-color: white;
}

#logs {
    font-family: monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
}

button {
    padding: 10px 20px;
    font-size: 16px;
}

.progress {
    height: 40px;
    /* margin-bottom: 20px; */
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 4px;
    box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
}

.progress-bar {
    float: left;
    width: 0%;
    height: 100%;
    font-size: 20px;
    line-height: 40px;
    color: #000000;
    text-align: center;
    background-color: #337ab7;
    box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
    transition: width .6s ease;
}
