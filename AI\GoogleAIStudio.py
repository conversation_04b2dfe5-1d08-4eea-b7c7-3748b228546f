import requests
import json

BASE_URL = "http://localhost:2048/v1"

def get_available_models():
    """
    Fetches the list of available models from the API.
    Assumes an OpenAI-compatible /v1/models endpoint.
    """
    models_url = f"{BASE_URL}/models"
    print(f"Attempting to fetch models from: {models_url}")
    try:
        response = requests.get(models_url, timeout=10) # Added timeout
        response.raise_for_status()  # Raises an HTTPError for bad responses (4XX or 5XX)
        models_data = response.json()
        
        # Standard OpenAI API format: {'data': [{'id': 'model1'}, ...]}
        if 'data' in models_data and isinstance(models_data['data'], list):
            model_ids = [model.get('id') for model in models_data['data'] if isinstance(model, dict) and 'id' in model]
            return model_ids
        else:
            print(f"Unexpected format for models data. Expected a 'data' key with a list of model objects.")
            print(f"Received: {json.dumps(models_data, indent=2)}")
            return [] # Return empty list if format is not as expected
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred while fetching models: {http_err}")
        if hasattr(response, 'content'):
            print(f"Response content: {response.content.decode(errors='ignore')}")
    except requests.exceptions.Timeout:
        print(f"Timeout occurred while trying to connect to {models_url}")
    except requests.exceptions.RequestException as req_err:
        print(f"Error during request to fetch models: {req_err}")
    except json.JSONDecodeError:
        print("Failed to decode JSON response when fetching models.")
        if hasattr(response, 'content'):
            print(f"Response content: {response.content.decode(errors='ignore')}")
    return None # Indicates failure to fetch or parse models

def ask_for_joke(model_name="aistudio-proxy"):
    """
    Asks the AI for a joke using the specified model.
    """
    chat_url = f"{BASE_URL}/chat/completions"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": "Tell a joke"}]
    }
    print(f"\nAttempting to get a joke using model: {model_name} from: {chat_url}")
    try:
        response = requests.post(chat_url, headers=headers, json=payload, timeout=30) # Added timeout
        response.raise_for_status()
        chat_response = response.json()
        
        # Standard OpenAI API format for chat completion response
        if (chat_response.get("choices") and 
            isinstance(chat_response["choices"], list) and 
            len(chat_response["choices"]) > 0 and
            isinstance(chat_response["choices"][0], dict) and
            chat_response["choices"][0].get("message") and 
            isinstance(chat_response["choices"][0]["message"], dict) and
            chat_response["choices"][0]["message"].get("content")):
            return chat_response["choices"][0]["message"]["content"]
        else:
            print("Unexpected format for chat completion response.")
            print(f"Received: {json.dumps(chat_response, indent=2)}")
            return "Could not retrieve a joke due to response format."
            
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred while asking for a joke: {http_err}")
        if hasattr(response, 'content'):
            print(f"Response content: {response.content.decode(errors='ignore')}")
    except requests.exceptions.Timeout:
        print(f"Timeout occurred while trying to connect to {chat_url}")
    except requests.exceptions.RequestException as req_err:
        print(f"Error during request to ask for a joke: {req_err}")
    except json.JSONDecodeError:
        print("Failed to decode JSON response when asking for a joke.")
        if hasattr(response, 'content'):
            print(f"Response content: {response.content.decode(errors='ignore')}")
    return "Failed to get a joke due to an error."

if __name__ == "__main__":
    print("--- 1. Getting the list of models ---")
    models = get_available_models()
 
    print(f"\n--- 2. Asking the AI to 'tell a joke' ---")
    joke = ask_for_joke()
    
    print("\nAI's Joke:")
    print(joke)