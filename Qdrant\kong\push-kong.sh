#!/bin/sh
set -eu
set -f

# Script to update SSL certificate in Kong when cert is updated by certbot. Put in /ssl/renewal-hooks/deploy/
# To test: docker exec -it certbot /bin/sh then: certbot renew --force-renewal

# ---- CONFIG YOU EDIT ----
KONG_ADMIN="http://kong:8001"
SNI_NAME="*.maidalv.com"
# Path to renewed files for THIS certificate (certbot sets $RENEWED_LINEAGE)
CERT_FILE="$RENEWED_LINEAGE/fullchain.pem"
KEY_FILE="$RENEWED_LINEAGE/privkey.pem"
# Optional: Kong Admin auth (if you protect Admin API)
AUTH_HEADER="--header Authorization: bearer 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ"
# AUTH_HEADER=""

# ---- sanity checks ----
if [[ -z "${RENEWED_LINEAGE:-}" ]]; then
  echo "ERROR: RENEWED_LINEAGE is not set. This script is meant to run as a certbot deploy-hook." >&2
  exit 1
fi
[[ -r "$CERT_FILE" && -r "$KEY_FILE" ]] || { echo "ERROR: cannot read $CERT_FILE or $KEY_FILE"; exit 1; }

# ---- Find which Certificate ID serves this SNI ----
# URL-encode '*' -> '%2A' (safe for SNIs like *.maidalv.com)
SNI_ENC=$(echo "$SNI_NAME" | sed 's/\*/%2A/g')
CERT_ID=$(curl -fsS -G $AUTH_HEADER "$KONG_ADMIN/snis" --data-urlencode "name=$SNI_NAME"  | jq -r '.data[0].certificate.id // empty')

# ---- Load the certificate ----
if [ -n "$CERT_ID" ]; then
  # ---- PATCH the existing Certificate (hot swap) ----
  curl -fsS -X PATCH $AUTH_HEADER -F "cert=@${CERT_FILE}" -F "key=@${KEY_FILE}" "$KONG_ADMIN/certificates/$CERT_ID" >/dev/null
  echo "Updated Kong certificate $CERT_ID for SNI $SNI_NAME."
else
  # ---- CREATE a new Certificate and attach the SNI ----
  curl -sS -X POST $AUTH_HEADER -F "cert=@${CERT_FILE}" -F "key=@${KEY_FILE}" -F "snis[]=$SNI_NAME" "$KONG_ADMIN/certificates"
  echo "Created new Kong certificate and attached SNI $SNI_NAME."
fi

echo "Updated Kong certificate $CERT_ID for SNI $SNI_NAME."
