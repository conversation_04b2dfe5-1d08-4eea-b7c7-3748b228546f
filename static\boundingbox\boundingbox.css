/* Keep specific styles for boundingbox */
/* Input Grid - Keep specific grid */
.input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* Adjusted min width */
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.results-container h2 { /* Style heading within results */
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    font-size: 1.1rem;
    font-weight: 600;
}

/* Medium screens - Keep specific adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    /* Removed .ip-items rule */
    .input-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
}

/* --- BoundingBox Specific Styles --- */

.input-container h2 {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.upload-group {
    grid-column: span 2; /* Make upload area wider */
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    background-color: var(--image-box-background);
    transition: border-color 0.2s, background-color 0.2s;
    margin-top: var(--spacing-xs); /* Add space below label */
}

.upload-area:hover {
    border-color: var(--accent-color);
    background-color: #404040;
}

.upload-area p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

#image-preview {
    max-width: 100%;
    max-height: 200px;
    margin-top: var(--spacing-md);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.resize-group label:first-child { /* Target the main label */
    margin-bottom: var(--spacing-sm); /* Add space below main label */
}

.resize-inputs {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.resize-inputs div {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.resize-inputs label {
    margin-bottom: 0; /* Remove bottom margin for inline labels */
    font-size: var(--text-size);
    font-weight: normal;
    color: var(--text-secondary);
}

.resize-inputs input[type="number"] {
    width: 70px; /* Fixed width for number inputs */
    padding: var(--spacing-xs);
}

.result-content {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns */
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.result-item h3 {
    font-size: var(--heading-size);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

#result-image-container,
#json-output-container {
    background-color: var(--image-box-background);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

#result-image {
    display: block;
    max-width: 100%;
    max-height: 400px;
    margin-top: var(--spacing-sm);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

#no-result-image {
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.json-header {
    display: flex;
    justify-content: flex-end; /* Position button to the right */
    margin-bottom: var(--spacing-sm);
}

.json-output-box {
    background-color: #1a1a1a;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: var(--spacing-sm);
    font-family: monospace;
    font-size: var(--text-size);
    color: var(--text-color);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
}

/* --- BoundingBox Specific Styles --- */

.input-container h2 {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.upload-group {
    grid-column: span 2; /* Make upload area wider */
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    background-color: var(--image-box-background);
    transition: border-color 0.2s, background-color 0.2s;
    margin-top: var(--spacing-xs); /* Add space below label */
}

.upload-area:hover {
    border-color: var(--accent-color);
    background-color: #404040;
}

.upload-area p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

#image-preview {
    max-width: 100%;
    max-height: 200px;
    margin-top: var(--spacing-md);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.resize-group label:first-child { /* Target the main label */
    margin-bottom: var(--spacing-sm); /* Add space below main label */
}

.resize-inputs {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.resize-inputs div {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.resize-inputs label {
    margin-bottom: 0; /* Remove bottom margin for inline labels */
    font-size: var(--text-size);
    font-weight: normal;
    color: var(--text-secondary);
}

.resize-inputs input[type="number"] {
    width: 70px; /* Fixed width for number inputs */
    padding: var(--spacing-xs);
}

.result-content {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns */
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.result-item h3 {
    font-size: var(--heading-size);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

#result-image-container,
#json-output-container {
    background-color: var(--image-box-background);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

#result-image {
    display: block;
    max-width: 100%;
    max-height: 400px;
    margin-top: var(--spacing-sm);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

#no-result-image {
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.json-header {
    display: flex;
    justify-content: flex-end; /* Position button to the right */
    margin-bottom: var(--spacing-sm);
}

.json-output-box {
    background-color: #1a1a1a;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: var(--spacing-sm);
    font-family: monospace;
    font-size: var(--text-size);
    color: var(--text-color);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
}

/* Removed .error rule (use common styles) */
/* Responsive adjustments for results */
@media (max-width: 900px) {
    .result-content {
        grid-template-columns: 1fr; /* Stack results vertically */
    }
    #result-image, .json-output-box {
        max-height: 300px; /* Adjust max height */
    }
}
/* --- Styles for Output Type Radio Buttons --- */
.radio-group {
    display: flex;
    gap: var(--spacing-md); /* Space between radio buttons */
    margin-top: var(--spacing-xs); /* Space below the label */
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs); /* Space between radio and text */
    font-size: var(--text-size);
    font-weight: normal; /* Override default label weight */
    margin-bottom: 0; /* Override default label margin */
    cursor: pointer;
}

.radio-group input[type="radio"] {
    width: auto; /* Override default input width */
    margin: 0; /* Reset margin */
    cursor: pointer;
}

/* --- Styles for Segmentation Masks --- */

/* Container style - similar to other result items */
#segmentation-masks-container {
    background-color: var(--image-box-background);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    grid-column: span 2; /* Make it span both columns in the grid layout */
}

/* Grid layout for masks */
#segmentation-masks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); /* Responsive grid */
    gap: var(--spacing-md); /* Space between mask items */
    margin-top: var(--spacing-sm);
}

/* Individual mask item container */
.mask-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    background-color: var(--card-background); /* Slightly different background */
    padding: var(--spacing-sm);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

/* Mask image style */
.mask-item img {
    max-width: 100%;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: var(--spacing-xs);
}

/* Mask label style */
.mask-item p {
    font-size: var(--detail-size);
    color: var(--text-secondary);
    margin: 0; /* Reset paragraph margin */
    word-break: break-word; /* Allow long labels to wrap */
}

/* Adjust result content grid if masks are present */
.result-content {
    /* Keep the two-column layout by default */
    grid-template-columns: 1fr 1fr;
}

/* Responsive adjustments for masks container */
@media (max-width: 900px) {
    #segmentation-masks-container {
        grid-column: span 1; /* Stack below other results on smaller screens */
    }
    #segmentation-masks-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); /* Smaller items on mobile */
    }
}