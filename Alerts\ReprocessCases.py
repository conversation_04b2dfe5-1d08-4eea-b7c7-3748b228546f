# Alerts/ReprocessCases.py
"""
Centralized case reprocessing functionality.
This module provides a unified interface for reprocessing cases across different parts of the application.
"""

import time
import os
import pandas as pd
import asyncio
from typing import Dict, Any, Optional, List, Union, Tuple
from logdata import log_message
from DatabaseManagement.ImportExport import get_table_from_GZ
import json
import langfuse
import traceback
from langfuse import observe
import getpass
from Common.Constants import local_case_folder, sanitize_name


def are_case_files_missing(images_data: Any, images_base_path: str, langfuse_trace_id: Optional[str] = None) -> bool:
    """
    Checks if any image files listed in the 'images' data structure are missing from the filesystem.
    This is used in 'resume' mode to decide whether to force a 'full_reprocess'.
    It now accumulates all missing files and logs them to a span in Langfuse if a trace ID is provided.
    """
    if not images_data or not isinstance(images_data, (dict, str)):
        return False

    if isinstance(images_data, str):
        try:
            images_dict = json.loads(images_data)
        except (json.JSONDecodeError, ValueError):
            return False
    else:
        images_dict = images_data

    if not isinstance(images_dict, dict):
        return False

    files_to_check = set()
    for ip_type in ['trademarks']:
        ip_data = images_dict.get(ip_type)
        if not isinstance(ip_data, dict):
            continue

        for key, details in ip_data.items():
            if key: files_to_check.add(key)
            if isinstance(details, dict):
                if 'full_filename' in details and isinstance(details['full_filename'], list):
                    for fname in details['full_filename']:
                        if fname: files_to_check.add(fname)
                if 'ser_no' in details and isinstance(details['ser_no'], list):
                    for sno in details['ser_no']:
                        if sno: files_to_check.add(f"{sno}.webp")

    if not files_to_check:
        return False

    missing_files = []
    for filename in files_to_check:
        file_path = os.path.join(images_base_path, filename)
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        log_message(f"{len(missing_files)} file(s) missing in resume mode, triggering full reprocess.", level="INFO")
        
        if langfuse_trace_id:
            try:
                with langfuse.get_client().start_as_current_span(name='missing-case-files-error', trace_context={"trace_id": langfuse_trace_id}, level='ERROR') as span:
                    span.update(input={'images_base_path': images_base_path})
                    span.update(output={'missing_files': missing_files, 'file_count': len(missing_files)})
            except Exception as e:
                log_message(f"Failed to create Langfuse span: {e}", level="ERROR")

        return True

    return False


async def reprocess_cases(
    cases_to_reprocess: Union[pd.DataFrame, List[int], int],
    processing_options: Optional[Dict[str, Any]] = None,
    trace_name: str = "Case Reprocess",
    full_cases_df: Optional[pd.DataFrame] = None,
    plaintiff_df: Optional[pd.DataFrame] = None
) -> Tuple[bool, Dict[str, Any]]:
    """
    Unified function for reprocessing cases using the new approach.

    Args:
        cases_to_reprocess: Can be:
            - DataFrame containing cases to reprocess
            - List of case IDs
            - Single case ID (int)
        processing_options: Dictionary of processing options. If None, uses default options.
        trace_name: Name for the trace (for logging/monitoring)
        full_cases_df: Full cases DataFrame. If None, will be loaded from database.
        plaintiff_df: Plaintiff DataFrame. If None, will be loaded from database.

    Returns:
        bool: True if all cases processed successfully, False otherwise
    """

    # Import required modules
    from Alerts.LexisNexis import Login
    from Alerts.LexisNexis.Arrive_On_Case_Page import arrive_on_case_page
    from Alerts.CaseProcessor import process_case
    from selenium.common.exceptions import WebDriverException

    # Default processing options
    if processing_options is None:
        processing_options = {
            'update_steps': True,
            'process_pictures': True,
            'upload_files_nas': True,
            'upload_files_cos': True,
            'run_plaintiff_overview': True,
            'run_summary_translation': True,
            'run_step_translation': True,
            'save_to_db': True,
            'processing_mode': 'full_reprocess',
            'refresh_days_threshold': 15
        }


    # Convert input to DataFrame of cases to reprocess
    if isinstance(cases_to_reprocess, int):
        # Single case ID
        case_ids = [cases_to_reprocess]
    elif isinstance(cases_to_reprocess, list):
        # List of case IDs
        case_ids = cases_to_reprocess
    elif isinstance(cases_to_reprocess, pd.DataFrame):
        # DataFrame - extract case IDs
        if 'id' in cases_to_reprocess.columns:
            case_ids = cases_to_reprocess['id'].tolist()
        else:
            log_message("DataFrame must contain 'id' column", level="ERROR")
            return False, {}
    else:
        log_message(f"Invalid cases_to_reprocess type: {type(cases_to_reprocess)}", level="ERROR")
        return False, {}

    log_message(f"Total cases_to_reprocess: {len(case_ids)}", level="INFO")

    if not case_ids:
        log_message("No cases to reprocess", level="INFO")
        return True, {}

    # Load DataFrames if not provided
    if full_cases_df is None:
        log_message("Loading cases DataFrame from database...", level="INFO")
        full_cases_df = get_table_from_GZ("tb_case", force_refresh=True)
        if full_cases_df is None or full_cases_df.empty:
            log_message("Failed to load cases DataFrame", level="ERROR")
            return False, {}

    if plaintiff_df is None:
        log_message("Loading plaintiff DataFrame from database...", level="INFO")
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)
        if plaintiff_df is None or plaintiff_df.empty:
            log_message("Failed to load plaintiff DataFrame", level="ERROR")
            return False, {}

    # Filter cases_df to only include cases we want to reprocess
    cases_to_reprocess_df = full_cases_df[full_cases_df['id'].isin(case_ids)].sort_values('date_filed', ascending=False)

    if cases_to_reprocess_df.empty:
        log_message(f"No matching cases found in database for IDs: {case_ids}", level="WARNING")
        return False, {}

    log_message(f"Starting reprocessing of {len(cases_to_reprocess_df)} cases with {trace_name}", level="INFO")
    total_count = len(cases_to_reprocess_df)
    
    driver = None
    
    # Process each case
    cases_no_ip_then_some_ip = []
    cases_no_ip_then_all_ip = []
    cases_some_ip_then_more_ip = []
    cases_some_ip_then_all_ip = []
    cases_ip_regressed = []
    cases_already_all_ip = []
    title_changes = []
    cases_with_more_steps = []
    cases_closed = []
    success_count = 0

    try:
        for i, (idx, case_row) in enumerate(cases_to_reprocess_df.iterrows()):
            case_id = case_row['id']

            # Inner loop to handle retries for a single case
            case_successfully_processed = False
            max_retries = 3
            attempt = 0
            while not case_successfully_processed and attempt < max_retries:
                attempt += 1
                try:
                    # On-demand browser creation for the first attempt or any retry
                    if driver is None:
                        log_message(f"🔄 Starting new browser session for case {case_id} (Attempt {attempt}/{max_retries})", level="INFO")
                        driver = Login.get_logged_in_browser()
                        if not driver:
                            log_message("Failed to get a logged-in browser. Will retry after a delay.", level="ERROR")
                            time.sleep(10) # Wait before next retry if login itself fails
                            continue # Go to next attempt in the 'while' loop

                    log_message(f"{i+1}/{total_count} : Reprocessing case {case_id} ({case_row.get('docket', 'Unknown docket')})", level="INFO")

                    trace_id = langfuse.get_client().create_trace_id()
                    # We create a span just for the purpose of updating the trace. Apparently there is no other way.
                    with langfuse.get_client().start_as_current_span(name="Case Information", trace_context={"trace_id": trace_id}) as span:
                        langfuse.get_client().update_current_trace(name=trace_name, user_id=getpass.getuser(), input={"CaseIdentifier": {'case_id': case_id}, "OptionsSummary": processing_options})
                        langfuse.get_client().update_current_span(input={"DocketNumber": case_row['docket'], "CaseID": case_id, "CaseTitle": case_row['title'], "DateFiled": case_row['date_filed'], "PlaintiffID": case_row['plaintiff_id']})

                    # Arrive on case page
                    arrival_success, arrival_df_slice, case_idx = arrive_on_case_page(driver, full_cases_df, case_identifier={'case_id': case_id}, langfuse_trace_id=trace_id)

                    if not arrival_success:
                        log_message(f"Failed to arrive on case page for case {case_id} (logical failure).", level="WARNING")
                        break # Exit the 'while' loop for this case and move to the next in the 'for' loop
                    
                    # Create a mutable copy of processing options for this case
                    current_processing_options = processing_options.copy()

                    # If in resume mode, check for missing files and switch to full_reprocess if needed
                    if current_processing_options.get('processing_mode') == 'resume':
                        images_raw = full_cases_df.at[case_idx, 'images']
                        
                        # Construct the expected local image directory path
                        docket_number = full_cases_df.loc[case_idx, 'docket']
                        date_filed = full_cases_df.loc[case_idx, 'date_filed']
                        local_case_dir = os.path.join(local_case_folder, sanitize_name(f"{date_filed.strftime('%Y-%m-%d')} - {docket_number}"))
                        local_case_images_dir = os.path.join(local_case_dir, "images")

                        if are_case_files_missing(images_raw, local_case_images_dir, langfuse_trace_id=trace_id):
                            log_message(f"Missing image files for case {case_id}. Switching to 'full_reprocess' mode.", level="WARNING")
                            current_processing_options['processing_mode'] = 'full_reprocess'
                    
                    # Pre-process state - handle NaN, string JSON, or dict
                    images_status_raw = full_cases_df.at[case_idx, 'images_status']
                    if pd.isna(images_status_raw):
                        images_status = {}
                    elif isinstance(images_status_raw, dict):
                        images_status = images_status_raw
                    elif isinstance(images_status_raw, str):
                        try:
                            images_status = json.loads(images_status_raw)
                        except (json.JSONDecodeError, ValueError):
                            images_status = {}
                    else:
                        images_status = {}
                        
                    ip_state_raw = images_status.get('ip_manager_state', {}) if isinstance(images_status, dict) else {}
                    pre_ip_state = ip_state_raw if isinstance(ip_state_raw, dict) else {}
                    pre_ip_json = json.loads(json.dumps(pre_ip_state))
                    pre_title = full_cases_df.at[case_idx, 'title'] if 'title' in full_cases_df.columns else ''
                    pre_class_code = full_cases_df.at[case_idx, 'class_code'] if 'class_code' in full_cases_df.columns else ''
                    pre_steps_processed = images_status.get('steps_processed', []) if isinstance(images_status, dict) else []

                    # Process the case
                    success, updated_case_df_row = await process_case(driver, current_processing_options, full_cases_df, case_idx, plaintiff_df, langfuse_trace_id=trace_id)

                    # Post-process state - handle NaN, string JSON, or dict
                    post_images_status_raw = updated_case_df_row.at[case_idx, 'images_status']
                    if pd.isna(post_images_status_raw):
                        post_images_status = {}
                    elif isinstance(post_images_status_raw, dict):
                        post_images_status = post_images_status_raw
                    elif isinstance(post_images_status_raw, str):
                        try:
                            post_images_status = json.loads(post_images_status_raw)
                        except (json.JSONDecodeError, ValueError):
                            post_images_status = {}
                    else:
                        post_images_status = {}
                    
                    post_ip_state_raw = post_images_status.get('ip_manager_state', {}) if isinstance(post_images_status, dict) else {}
                    post_ip_state = post_ip_state_raw if isinstance(post_ip_state_raw, dict) else {}
                    post_ip_json = json.loads(json.dumps(post_ip_state))
                    
                    if post_ip_json:
                        for ip_type in post_ip_json:
                            pre_raw = pre_ip_json.get(ip_type, {})
                            post_raw = post_ip_json.get(ip_type, {})

                            # Use found_reg_nos count for more accurate tracking
                            pre_found_reg_nos = set(pre_raw.get("found_reg_nos", []))
                            post_found_reg_nos = set(post_raw.get("found_reg_nos", []))

                            pre_state = {
                                "goal_met": pre_raw.get("goal_met", False),
                                "count": len(pre_found_reg_nos),
                                "found_reg_nos": pre_found_reg_nos
                            }
                            post_state = {
                                "goal_met": post_raw.get("goal_met", False),
                                "count": len(post_found_reg_nos),
                                "found_reg_nos": post_found_reg_nos
                            }

                            # Track IP progression based on actual found registration numbers
                            if pre_state['goal_met'] and not post_state['goal_met']:
                                cases_ip_regressed.append((case_id, ip_type))

                            elif pre_state['goal_met'] and post_state['goal_met']:
                                cases_already_all_ip.append((case_id, ip_type))

                            elif not pre_state['goal_met'] and not post_state['goal_met']:
                                if pre_state['count'] == 0 and post_state['count'] > 0:
                                    cases_no_ip_then_some_ip.append((case_id, ip_type))
                                elif pre_state['count'] > 0 and post_state['count'] > pre_state['count']:
                                    cases_some_ip_then_more_ip.append((case_id, ip_type))
                                elif pre_state['count'] > 0 and post_state['count'] < pre_state['count']:
                                    cases_ip_regressed.append((case_id, ip_type))

                            elif not pre_state['goal_met'] and post_state['goal_met']:
                                if pre_state['count'] == 0:
                                    cases_no_ip_then_all_ip.append((case_id, ip_type))
                                elif pre_state['count'] > 0:
                                    cases_some_ip_then_all_ip.append((case_id, ip_type))
                    
                    # Track additional changes
                    post_title = updated_case_df_row.at[case_idx, 'title'] if 'title' in updated_case_df_row.columns else ''
                    post_class_code = updated_case_df_row.at[case_idx, 'class_code'] if 'class_code' in updated_case_df_row.columns else ''
                    post_steps_processed = post_images_status.get('steps_processed', []) if isinstance(post_images_status, dict) else []

                    # Check for title changes
                    if pre_title != post_title and pre_title and post_title:
                        title_changes.append((case_id, pre_title, post_title))

                    # Check for more steps
                    if len(post_steps_processed) > len(pre_steps_processed):
                        cases_with_more_steps.append((case_id, len(pre_steps_processed), len(post_steps_processed)))

                    # Check for case closure
                    pre_closed = pre_class_code in ['Closed', 'Terminated']
                    post_closed = post_class_code in ['Closed', 'Terminated']
                    if not pre_closed and post_closed:
                        cases_closed.append((case_id, post_class_code))
                    
                    if success:
                        success_count += 1
                    
                    # If we reach this point, the case has been processed successfully in this attempt.
                    case_successfully_processed = True

                except WebDriverException as case_e:
                    log_message(f"‼️ WebDriver Error on case {case_id}, Attempt {attempt}/{max_retries}. Browser crashed. Restarting...", level="ERROR")
                    log_message(traceback.format_exc(), level="ERROR")
                    if driver:
                        try:
                            driver.quit()
                        except Exception:
                            pass
                    driver = None # Signal that a new driver is needed for the next attempt.
                    time.sleep(5) # Wait a few seconds before retrying.

                except Exception as case_e:
                    log_message(f"Error reprocessing case {case_id}: {str(case_e)}, {traceback.format_exc()}", level="ERROR")
                    # This is a non-browser error, break the retry loop and move to the next case.
                    break
            
            # After the while loop, if the case was never processed successfully, log a final failure message.
            if not case_successfully_processed:
                log_message(f"🚨 FAILED to process case {case_id} after {max_retries} attempts. Skipping.", level="CRITICAL")

        # Create tracking dictionary with all variables
        tracking_dict = {
            'cases_no_ip_then_some_ip': cases_no_ip_then_some_ip,
            'cases_no_ip_then_all_ip': cases_no_ip_then_all_ip,
            'cases_some_ip_then_more_ip': cases_some_ip_then_more_ip,
            'cases_some_ip_then_all_ip': cases_some_ip_then_all_ip,
            'cases_ip_regressed': cases_ip_regressed,
            'cases_already_all_ip': cases_already_all_ip,
            'total_count': total_count,
            'title_changes': title_changes,
            'cases_with_more_steps': cases_with_more_steps,
            'cases_closed': cases_closed,
            'success_count': success_count
        }

        log_message(f"🔥🔥🔥 Completed reprocessing: {success_count}/{total_count} cases successful 🔥🔥🔥", level="INFO")

        return success_count == total_count, tracking_dict

    except Exception as e:
        log_message(f"Critical error during case reprocessing loop: {str(e)}", level="ERROR")
        log_message(traceback.format_exc(), level="ERROR")
        # Return an empty dict for tracking as the loop was interrupted
        return False, {}

    finally:
        # Final cleanup to ensure the last browser instance is closed
        if driver:
            log_message("Final cleanup: closing browser.", level="INFO")
            try:
                driver.quit()
            except Exception:
                pass # Ignore errors on final quit


def _get_nested_value(data: Any, keys: List[str], default: Any = None) -> Any:
    """
    Safely retrieves a value from a nested dictionary or object using a list of keys.
    Returns the default value if any key in the path is not found or if data is not a dictionary.
    """
    if not isinstance(data, dict):
        return default
    
    current_value = data
    for key in keys:
        if isinstance(current_value, dict) and key in current_value:
            current_value = current_value[key]
        else:
            return default
    return current_value

def _has_valid_copyright_image(copyrights_dict: dict) -> bool:
    """
    Checks if a given 'copyrights' dictionary contains at least one valid copyright image.
    A valid image is one where the key (image filename) corresponds to its 'full_filename' value
    by appending '_full.webp' to the key's base name.
    """
    if not copyrights_dict:
        return False
    
    for key, value_dict in copyrights_dict.items():
        if isinstance(value_dict, dict) and "full_filename" in value_dict and \
           isinstance(value_dict["full_filename"], list) and \
           len(value_dict["full_filename"]) > 0:
            
            if value_dict["full_filename"][0].replace("_full.webp", ".webp") != key or "TinEye" in key or "Google" in key or "GenAI" in key:
                return True # Found at least one valid copyright image
    return False # No valid copyright images found in the dictionary

def filter_plaintiffs_with_no_copyright_images(cases_df: pd.DataFrame) -> list:
    """
    Filters and returns a list of plaintiff_ids for whom ALL associated cases
    have no valid copyright images.
    
    Args:
        cases_df (pd.DataFrame): DataFrame containing case data, expected to have
                                 'plaintiff_id' and 'images' columns.
                                 The 'images' column should contain dictionaries
                                 with a 'copyrights' key, which is itself a dictionary.
                                 
    Returns:
        list: A list of plaintiff_ids that meet the criteria.
    """
    
    # Apply the check for each case's 'copyrights' dictionary
    cases_df['has_valid_copyright_image'] = cases_df['images'].apply(
        lambda img_col: _has_valid_copyright_image(img_col.get('copyrights', {}))
        if isinstance(img_col, dict) else False
    )
    
    # Group by plaintiff_id and check if ALL cases for that plaintiff have NO valid copyright images
    plaintiffs_no_copyright_images = cases_df.groupby('plaintiff_id')['has_valid_copyright_image'].apply(lambda group: not group.any())
    
    # Get the plaintiff_ids where the condition is True
    filtered_plaintiff_ids = plaintiffs_no_copyright_images[plaintiffs_no_copyright_images].index.tolist()
    
    # Drop the temporary column
    cases_df.drop(columns=['has_valid_copyright_image'], inplace=True)
    
    return filtered_plaintiff_ids





# Example Usage (Illustrative - requires actual instances)
async def main():
    start_time = time.time()
    # This block is for testing/illustration purposes only
    # Replace with actual setup in your application context
    print("CaseProcessor module loaded. Define actual instances and call process_case.")
    # Example:
    cases_df = get_table_from_GZ("tb_case", force_refresh=True) # Added force_refresh=False
    plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=True) # Added force_refresh=False

    # my_df = cases_df[pd.isna(cases_df['plaintiff_id'])] # Cases without plaintiff_id

    # Normalize the 'images' column
    # default_empty_images = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    # cases_df['images'] = cases_df['images'].apply(lambda x: default_empty_images if pd.isna(x) or x == {} else x)

    # Identify plaintiffs where NONE of their cases have IP images
    # plaintiffs_with_no_images = cases_df.groupby('plaintiff_id')['images'].apply(lambda group: all(img == default_empty_images for img in group))
    # plaintiff_ids_with_no_images = plaintiffs_with_no_images[plaintiffs_with_no_images].index.tolist()

    # Filter cases_df to include only cases from these plaintiffs
    # cases_from_plaintiffs_with_no_images_df = cases_df[cases_df['plaintiff_id'].isin(plaintiff_ids_with_no_images)]

    # Now, from the cases_from_plaintiffs_with_no_images_df, pick the most recent one for each plaintiff
    # This ensures we are only considering plaintiffs who have NO images on ANY of their cases.
    # representative_cases_no_ip_df = cases_from_plaintiffs_with_no_images_df.loc[cases_from_plaintiffs_with_no_images_df.groupby('plaintiff_id')['date_filed'].idxmax()]
    # representative_cases_no_ip_df = representative_cases_no_ip_df[representative_cases_no_ip_df["nos_description"].notna()]

    # my_df_with_ai_summary = representative_cases_no_ip_df[representative_cases_no_ip_df["aisummary"].fillna("") != ""]
    # my_df_without_ai_summary = representative_cases_no_ip_df[representative_cases_no_ip_df["aisummary"].fillna("") == ""]
    #=> we have 3 priority groups:
    # (1) new cases without plaintiff ID,
    # (2) existing cases (1 per plaintiff) without AI summary,
    # (3) existing cases (1 per plaintiff) with AI summary
    
    
    ### Copyright fixing:
    # 1. Copyright cases with missing IP, 1 per plaintiff, not plaintiff 9
    # not_plaintiff_9_df = cases_df[cases_df["plaintiff_id"] != 9]
    # not_plaintiff_9_df['nos_description'] = not_plaintiff_9_df['nos_description'].apply(lambda x: "No_NOS" if pd.isna(x) else x)
    
    # for index, row in not_plaintiff_9_df.iterrows():
    #     if row["images_status"] and isinstance(row["images_status"], str):
    #         try:
    #             json.loads(row["images_status"])
    #         except:
    #             print(f"images_status is not valid json: {row['docket']}, {row['images_status']}")
    # not_plaintiff_9_df["images_status"] = not_plaintiff_9_df["images_status"].apply(lambda x: json.loads(x) if not pd.isna(x) and isinstance(x, str) else x)
    # copyright_nos_df = not_plaintiff_9_df[not_plaintiff_9_df["nos_description"].str.contains("Copyrights")]
    
    # Safely access 'is_relevant' using the new helper function
    # not_plaintiff_9_df['is_relevant_copyright'] = not_plaintiff_9_df.apply(
        # lambda row: _get_nested_value(row.get('images_status'), ['ip_manager_state', 'copyright', 'is_relevant'], False),
        # axis=1
    # )
    # copyright_relevant = not_plaintiff_9_df[not_plaintiff_9_df['is_relevant_copyright'] == True]
    
    # all_copyright_df = pd.concat([copyright_nos_df, copyright_relevant]).drop_duplicates(subset=['docket', 'date_filed'])

    # filtered_plaintiff_ids = filter_plaintiffs_with_no_copyright_images(all_copyright_df)
    # cases_from_plaintiffs_with_no_copyright_images_df = all_copyright_df[all_copyright_df['plaintiff_id'].isin(filtered_plaintiff_ids)]
    # representative_cases_no_copyright_df = cases_from_plaintiffs_with_no_copyright_images_df.loc[cases_from_plaintiffs_with_no_copyright_images_df.groupby('plaintiff_id')['date_filed'].idxmax()]
    # import datetime
    # representative_cases_no_copyright_df = representative_cases_no_copyright_df[representative_cases_no_copyright_df["update_time"] < datetime.datetime(2025, 6, 8, 1, 43, 0)]
    
    
    processing_options = {
        'update_steps': True, # Forces update of case steps from source
        'process_pictures': True, # Process images from PDFs, always true for now!
        'upload_files_nas': True, # Upload files to NAS
        'upload_files_cos': True, # Upload files to COS
        'run_plaintiff_overview': True, # Run AI plaintiff overview
        'run_summary_translation': True, # Run AI summary translation
        'run_step_translation': True, # Run AI step translation
        'save_to_db': True, # Save results to DB
        'processing_mode': 'full_reprocess', # Processing mode: 'full_reprocess' (default) or 'resume'
        'refresh_days_threshold': 2000 # Refresh threshold in days
    }
    
    # urgent = cases_df[(cases_df["date_filed"]>=pd.to_datetime("2025-06-05").date())]
    #sort
    # urgent = urgent.sort_values(by='date_filed', ascending=False)
    
    # os.environ["LNusername"] = "jslawpclegal"
    # os.environ["LNpassword"] = "Khlvshi2021="
    # os.environ["LN_chrome_folder"] = "chrome_user_data_pclegal"
    
    # await reprocess_cases(cases_to_reprocess=my_df, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)
    
    # Cases where plaintiff_id is not 9 and nos_description is na
    # remaining_cases = cases_df[(cases_df["plaintiff_id"] != 9) & (cases_df["nos_description"].isna())]
    # Now lets select only 1 per plaintiff_id, the most recent one
    # remaining_cases = remaining_cases.loc[remaining_cases.groupby('plaintiff_id')['date_filed'].idxmax()]
    remaining_cases = cases_df[cases_df['docket'] == '1:25-cv-10760']
    await reprocess_cases(cases_to_reprocess=remaining_cases, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)
    
    # case_id_to_process = 14196
    # await reprocess_cases(cases_to_reprocess=case_id_to_process, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)

if __name__ == '__main__':
    asyncio.run(main())
