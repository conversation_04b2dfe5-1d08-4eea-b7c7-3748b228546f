#!/usr/bin/env python3
"""
Utility functions for PostgreSQL copyright operations.
Provides shared functionality for copyright and copyright_files table operations,
file naming conventions, and method hierarchy logic.
"""

import os
import sys
import re
from typing import Dict, Optional, Tuple, List
from datetime import datetime, date
import pandas as pd

sys.path.append(os.getcwd())
from IP.Patents_Bulk.patent_db_grant import get_db_connection


# Method hierarchy: Exhibit >> TinEye >> GoogleVision >> GenAI
METHOD_HIERARCHY = {
    'Exhibit': 1,
    'TinEye': 2, 
    'GoogleVision': 3,
    'GenAI': 4
}

def get_method_priority(method: str) -> int:
    """Get the priority of a method (lower number = higher priority)."""
    return METHOD_HIERARCHY.get(method, 999)  # Unknown methods get lowest priority

def is_better_method(new_method: str, existing_method: str) -> bool:
    """Check if new_method has higher priority than existing_method."""
    return get_method_priority(new_method) < get_method_priority(existing_method)

def generate_copyright_filename(reg_no: str, method: str) -> str:
    """Generate standardized copyright filename: {reg_no}_{method}.webp"""
    return f"{reg_no}_{method}.webp"

def generate_copyright_full_filename(reg_no: str, method: str) -> str:
    """Generate standardized copyright full filename: {reg_no}_{method}_full.webp"""
    return f"{reg_no}_{method}_full.webp"

def parse_copyright_filename(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Parse copyright filename to extract registration number and method.
    
    Args:
        filename: The filename to parse (e.g., "VA0001234567_Exhibit.webp")
        
    Returns:
        Tuple of (reg_no, method) or (None, None) if parsing fails
    """
    # Remove _full suffix if present
    base_filename = filename.replace('_full.webp', '.webp')
    
    # Pattern: {reg_no}_{method}.webp
    match = re.match(r'^([A-Za-z0-9]+)_([A-Za-z]+)\.webp$', base_filename)
    if match:
        return match.group(1), match.group(2)
    
    return None, None

def split_reg(reg_no: str) -> list:
    """Split the registration number into its components."""
    formated_reg_no = re.sub(r'[^a-zA-Z0-9]', '', reg_no)
    return re.findall(r'([a-zA-Z]+|[0-9]+)', formated_reg_no)

def zero_pad_reg_no(chart: str, number: str) -> str:
    """Zero pad the registration number based on its chart type."""
    if chart == 'MD':  # Internally generated number: MD + 4 digit plaintiff id + 4 digit copyright count
        formated_reg_no = chart + number.zfill(8)
    elif len(chart) == 3:  # e.g. VAu123456789
        if len(number) > 9:
            number = number[-9:]  # Ensure we only take the last 9 digits
        if chart[2] == 'U':
            chart = chart[:2] + 'u'
    
        formated_reg_no = chart + number.zfill(9)
    else:  # e.g. VA0123456789
        if len(number) > 10:
            number = number[-10:]  # Ensure we only take the last 9 digits        
        formated_reg_no = chart + number.zfill(10)
    
    return formated_reg_no

def standardize_reg_no(reg_no: str) -> str:
    """Standardize registration number format."""
    split_str = split_reg(reg_no)
    if len(split_str) == 2:
        return zero_pad_reg_no(split_str[0], split_str[1])
    return reg_no

class CopyrightDatabaseManager:
    """Manages PostgreSQL operations for copyright and copyright_files tables."""
    
    def __init__(self):
        self.db_conn = None
        
    def __enter__(self):
        self.db_conn = get_db_connection()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()
    
    def query_copyright_files(self, registration_number: str) -> List[Dict]:
        """Query copyright_files table for a specific registration number."""
        if not self.db_conn:
            raise RuntimeError("Database connection not established")
            
        cursor = self.db_conn.cursor()
        try:
            query = """
                SELECT filename, method, production, type, create_time, update_time
                FROM copyrights_files 
                WHERE registration_number = %s
                ORDER BY create_time DESC
            """
            cursor.execute(query, (registration_number,))
            columns = [desc[0] for desc in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            return results
        finally:
            cursor.close()
    
    def get_best_existing_file(self, registration_number: str) -> Optional[Dict]:
        """Get the best existing file for a registration number based on method hierarchy."""
        files = self.query_copyright_files(registration_number)
        if not files:
            return None
            
        # Sort by method priority (lower priority number = better method)
        files.sort(key=lambda f: get_method_priority(f['method']))
        return files[0] if files else None
    
    def should_update_file(self, registration_number: str, new_method: str) -> Tuple[bool, Optional[str]]:
        """
        Determine if we should update the file based on method hierarchy and production flag.
        
        Returns:
            Tuple of (should_update, action) where action is 'add' or 'update' or None
        """
        best_existing = self.get_best_existing_file(registration_number)
        
        if not best_existing:
            return True, 'add'
            
        existing_method = best_existing['method']
        is_production = best_existing.get('production', False)
        
        if is_better_method(new_method, existing_method):
            if is_production:
                return True, 'add'  # Add new file, keep existing
            else:
                return True, 'update'  # Update existing record
        else:
            return False, None  # Use existing file (inferior method)
    
    def upsert_copyright_file(self, registration_number: str, filename: str, method: str, 
                            production: bool = False, file_type: str = 'image') -> bool:
        """Upsert a file record into the copyrights_files table."""
        if not self.db_conn:
            raise RuntimeError("Database connection not established")
            
        cursor = self.db_conn.cursor()
        try:
            query = """
                INSERT INTO copyrights_files (registration_number, filename, method, production, type)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (filename) DO UPDATE SET
                    registration_number = EXCLUDED.registration_number,
                    method = EXCLUDED.method,
                    production = EXCLUDED.production,
                    type = EXCLUDED.type,
                    update_time = NOW()
            """
            cursor.execute(query, (registration_number, filename, method, production, file_type))
            self.db_conn.commit()
            return True
        except Exception as e:
            print(f"Error upserting copyright file: {e}")
            self.db_conn.rollback()
            return False
        finally:
            cursor.close()
    
    def update_copyright_file_record(self, old_filename: str, new_filename: str, new_method: str) -> bool:
        """Update an existing copyright file record with new filename and method."""
        if not self.db_conn:
            raise RuntimeError("Database connection not established")
            
        cursor = self.db_conn.cursor()
        try:
            query = """
                UPDATE copyrights_files 
                SET filename = %s, method = %s, update_time = NOW()
                WHERE filename = %s
            """
            cursor.execute(query, (new_filename, new_method, old_filename))
            self.db_conn.commit()
            return cursor.rowcount > 0
        except Exception as e:
            print(f"Error updating copyright file record: {e}")
            self.db_conn.rollback()
            return False
        finally:
            cursor.close()

    def query_copyrights_table(self, registration_numbers: List[str]) -> List[Dict]:
        """Query the copyrights table for specific registration numbers."""
        if not self.db_conn:
            raise RuntimeError("Database connection not established")
            
        if not registration_numbers:
            return []
            
        cursor = self.db_conn.cursor()
        try:
            placeholders = ','.join(['%s'] * len(registration_numbers))
            query = f"""
                SELECT * FROM copyrights 
                WHERE registration_number IN ({placeholders})
            """
            cursor.execute(query, registration_numbers)
            columns = [desc[0] for desc in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            return results
        finally:
            cursor.close()

def get_existing_filename_for_reg_no(registration_number: str) -> Optional[str]:
    """Get the best existing filename for a registration number."""
    with CopyrightDatabaseManager() as db_manager:
        best_file = db_manager.get_best_existing_file(registration_number)
        return best_file['filename'] if best_file else None

def handle_copyright_file_update(registration_number: str, new_filename: str, new_method: str, 
                                production: bool = False) -> Tuple[bool, str]:
    """
    Handle copyright file update based on method hierarchy.
    
    Returns:
        Tuple of (success, final_filename_to_use)
    """
    with CopyrightDatabaseManager() as db_manager:
        should_update, action = db_manager.should_update_file(registration_number, new_method)
        
        if not should_update:
            # Use existing file
            existing_file = db_manager.get_best_existing_file(registration_number)
            return True, existing_file['filename'] if existing_file else new_filename
        
        if action == 'add':
            # Add new file
            success = db_manager.upsert_copyright_file(
                registration_number, new_filename, new_method, production
            )
            return success, new_filename
        
        elif action == 'update':
            # Update existing record
            existing_file = db_manager.get_best_existing_file(registration_number)
            if existing_file:
                success = db_manager.update_copyright_file_record(
                    existing_file['filename'], new_filename, new_method
                )
                return success, new_filename
            else:
                # Fallback to add if no existing file found
                success = db_manager.upsert_copyright_file(
                    registration_number, new_filename, new_method, production
                )
                return success, new_filename
        
        return False, new_filename
