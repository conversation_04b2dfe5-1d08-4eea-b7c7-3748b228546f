global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # === MAIN SERVER MONITORING ===
  # 1. Scrape Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      # Use the service name defined in docker-compose
      - targets: ['prometheus:9090']

  # 2. Scrape Qdrant (Main)
  - job_name: 'qdrant'
    metrics_path: /metrics # Qdrant exposes metrics here by default
    scheme: https
    static_configs:
      # Use the service name and default port defined in docker-compose
      - targets: ['qdrant:6333']
    tls_config:
          # This allows Prometheus to connect even if the hostname 'qdrant' doesn't match the certificate's name 'maidalv.com'.
          insecure_skip_verify: true
    authorization:
      type: Bearer
      credentials: 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ

  # 3. Scrape Node Exporter (Host OS Metrics) - Main
  - job_name: 'node-exporter'
    static_configs:
      # Use the service name and default port (9100)
      - targets: ['node-exporter:9100']

  # 4. Scrape PostgreSQL Exporter - Main
  - job_name: 'postgres-exporter'
    static_configs:
      # Use the service name and default port (9187)
      - targets: ['postgres-exporter:9187']

  # 5. Scrape cAdvisor (Container Metrics) - Main
  - job_name: 'cadvisor'
    static_configs:
      # Use the service name and default port (8080)
      - targets: ['cadvisor:8080']

  # === BACKUP SERVER MONITORING ===
  # 6. Scrape Node Exporter on Backup Server
  - job_name: 'node-exporter-backup'
    static_configs:
      - targets: ['api2.maidalv.com:9100']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'backup-server'

  # 7. Scrape PostgreSQL Exporter on Backup Server
  - job_name: 'postgres-exporter-backup'
    static_configs:
      - targets: ['api2.maidalv.com:9187']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'backup-server'

  # 8. Scrape cAdvisor on Backup Server
  - job_name: 'cadvisor-backup'
    static_configs:
      - targets: ['api2.maidalv.com:8080']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'backup-server'

  # 9. Scrape Qdrant on Backup Server
  - job_name: 'qdrant-backup'
    metrics_path: /metrics
    scheme: https
    static_configs:
      - targets: ['vectorstore2.maidalv.com:6333']
    tls_config:
      insecure_skip_verify: true
    authorization:
      type: Bearer
      credentials: 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'backup-server'

  # === BLACKBOX MONITORING (URLs/Endpoints) ===
  # 6. Scrape Blackbox Exporter (Uptime/Downtime) - Main Server
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]  # Look for a HTTP 2xx response.
    static_configs:
      - targets:
        - https://api.maidalv.com    # Target to probe
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox_exporter:9115  # The blackbox exporter's real hostname:port.

  # 10. Blackbox monitoring for Backup Server endpoints
  - job_name: 'blackbox-backup'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://api2.maidalv.com/docs
        - https://vectorstore2.maidalv.com
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox_exporter:9115

  # 7. Scrape Node Exporter on Vast.ai (Remote Host)
  - job_name: 'vast-node-exporter'
    scheme: https # Assuming Cloudflare Tunnel provides TLS
    static_configs:
      - targets: ['vast-metrics.maidalv.com'] # The public endpoint for the node exporter
    tls_config:
      # This is often needed when behind a proxy like Cloudflare,
      # which might present a cert for maidalv.com, not vast-metrics.maidalv.com specifically in some configs.
      # If your Cloudflare cert is a wildcard (*.maidalv.com), you might not need this.
      insecure_skip_verify: true

  # 11. Scrape FastAPI App Metrics
  - job_name: 'fastapi-app'
    metrics_path: /metrics
    scheme: https
    static_configs:
      - targets:
        - 'apiv.maidalv.com'
        - 'apig.maidalv.com'
        - 'apih.maidalv.com'
    tls_config:
      insecure_skip_verify: true