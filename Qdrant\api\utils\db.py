"""
Database utilities for the API.
"""

import psycopg2
import psycopg2.extras
import os
import redis
from datetime import timedelta

# Redis client for caching API keys
redis_client = redis.Redis(host=os.getenv("REDIS_HOST", "localhost"), port=int(os.getenv("REDIS_PORT", 6379)), db=0, decode_responses=True)
API_KEYS_CACHE_KEY = "api_keys_cache"
API_KEYS_CACHE_EXPIRY_SECONDS = timedelta(hours=24).total_seconds()

def get_db_connection():
    """
    Get a connection to the PostgreSQL database.
    
    Returns:
        A connection to the PostgreSQL database.
    """
    conn = psycopg2.connect(
        host=os.getenv("POSTGRES_HOST"),
        port=os.getenv("POSTGRES_PORT"),
        user=os.getenv("POSTGRES_USER"),
        password=os.getenv("POSTGRES_PASSWORD"),
        dbname=os.getenv("POSTGRES_DB")
    )
    conn.autocommit = True
    return conn


def get_ip_asset_metadata(asset_ids, ip_types):
    """
    Get metadata for IP assets from PostgreSQL.
    
    Args:
        asset_ids: A list of IP asset IDs.
        
    Returns:
        A dictionary mapping IP asset IDs to their metadata.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
    
    # Convert asset_ids to a list of UUIDs
    uuids = list(asset_ids)
    metadata = {}
    
    # Query trademarks
    if "Trademark" in ip_types:
        cursor.execute("SELECT * FROM trademarks WHERE id = ANY(%s::uuid[])",(uuids,))
        trademarks = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "trademark", "data": data} for id, data in trademarks.items()})
    
    # Query patents
    if "Patent" in ip_types:
        cursor.execute("SELECT * FROM patents WHERE id = ANY(%s::uuid[])",(uuids,))
        patents = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "patent", "data": data} for id, data in patents.items()})
    
    # Query copyrights
    if "Copyright" in ip_types:
        cursor.execute("SELECT * FROM copyrights WHERE id = ANY(%s::uuid[])",(uuids,))
        copyrights = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "copyright", "data": data} for id, data in copyrights.items()})
    
    # Check for missing assets
    found_ids = set(metadata.keys())
    requested_ids = set(asset_ids)
    missing_ids = requested_ids - found_ids
    for missing_id in missing_ids:
        print(f"⚠️  Error: Asset ID {missing_id} not found in the database.")

    # Close connection
    cursor.close()
    conn.close()
    
    return metadata


def get_reverse_check_results_by_client_id_and_date(client_id, start_date, end_date=None):
    """
    Get all records from the reverse_check_result table for a given client_id and date/date range.
    Args:
        client_id: The client identifier to filter by.
        start_date: The start date to filter by (string, e.g., '2024-06-15').
        end_date: The end date to filter by (string, e.g., '2024-06-20'). If None, only start_date is used.
    Returns:
        A list of row dicts from reverse_check_result.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    if end_date is None:
        # Single date query
        cursor.execute("SELECT * FROM reverse_check_result WHERE client_id = %s::text AND DATE(create_time) = %s ORDER BY create_time DESC", (client_id, start_date))
    else:
        # Date range query
        cursor.execute(
            "SELECT * FROM reverse_check_result WHERE client_id = %s::text AND DATE(create_time) BETWEEN %s AND %s ORDER BY create_time DESC",
            (client_id, start_date, end_date)
        )

    results = [dict(row) for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return results


