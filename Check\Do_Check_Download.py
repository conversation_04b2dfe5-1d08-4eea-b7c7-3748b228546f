import os, asyncio, aiohttp, base64, time, traceback, re, uuid
from FileManagement.Tencent_COS import get_cos_client, download_from_cos
import urllib.parse
from urllib.parse import quote
from langfuse import observe
import langfuse


class ImageDownloadError(Exception):
   """Custom exception for image download failures."""
   pass

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# Global aiohttp session for downloads
download_session = None

async def init_download_session():
   """Initializes the shared aiohttp client session for downloads."""
   global download_session
   if download_session is None:
       timeout = aiohttp.ClientTimeout(total=60, connect=15, sock_read=30)
       connector = aiohttp.TCPConnector(ssl=False, force_close=False, limit=10, ttl_dns_cache=300)
       download_session = aiohttp.ClientSession(timeout=timeout, connector=connector)

async def close_download_session():
   """Closes the shared aiohttp client session for downloads."""
   global download_session
   if download_session:
       await download_session.close()
       download_session = None

# Global lock management for downloads
_download_locks = {}
_lock_for_locks_dict = asyncio.Lock() # Lock to protect access to the _download_locks dictionary

async def download_from_url(url, local_path, error_file=None, error_message=None):
    # Normalize path for consistent lock keying and existence checks
    abs_local_path = os.path.abspath(local_path)

    # Get or create a lock for this specific path, protecting _download_locks dict access
    async with _lock_for_locks_dict:
        if abs_local_path not in _download_locks:
            _download_locks[abs_local_path] = asyncio.Lock()
        path_lock = _download_locks[abs_local_path]

    async with path_lock: # Acquire the specific lock for this path
        # After acquiring the lock, check if the file already exists.
        # If so, another coroutine likely downloaded it while we were waiting.
        if os.path.exists(local_path): # Use non-absolute local_path for user-facing messages/operations if preferred
            # print(f"\033[94mFile {local_path} already exists. Skipping download for {url}.\033[0m")
            return True

        retries = 3
        delay = 1
        timeout = aiohttp.ClientTimeout(total=60, connect=15, sock_read=30) # With connect = 10 I often get timeout when downloading form china to germany
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Connection": "keep-alive"
        }

        # Use a temporary file for atomic download. Original naming is fine with the lock.
        temp_path = f"{local_path}.tmp"
        
        try: # This try/finally ensures temp_path cleanup within the lock
            os.makedirs(os.path.dirname(local_path), exist_ok=True) # Ensure directory for final local_path exists

            for attempt in range(retries):
                try:
                    global download_session
                    if not download_session:
                       await init_download_session()

                    async with download_session.get(url, headers=headers, allow_redirects=True) as response:
                       response.raise_for_status()
                       
                       # Write to temp_path
                       with open(temp_path, 'wb') as fd:
                           while True:
                               chunk = await response.content.read(8192)
                               if not chunk:
                                   break
                               fd.write(chunk)
                    
                    # Successfully downloaded to temp_path
                    # Now, atomically move it to local_path
                    # Remove existing file before renaming to allow overwrite (os.rename fails on Windows if dest exists)
                    if os.path.exists(local_path): # This check might seem redundant due to the one at the start of path_lock
                                                   # but could be a safeguard if an external process created the file
                                                   # or if a very old version existed.
                        try:
                            os.remove(local_path)
                        except OSError as e_remove:
                            # If we can't remove it, rename will likely fail. Log and let it try.
                            print(f"\033[93mWarning: Could not remove existing {local_path} before rename: {e_remove}. Proceeding with rename attempt.\033[0m")
                    
                    os.rename(temp_path, local_path) # temp_path should be released by with open()
                    
                    if attempt > 0: # Log if it wasn't the first attempt
                        print(f"\033[92mAttempt {attempt + 1}/{retries}: Successfully downloaded {url} to {local_path}\033[0m")
                    return True # Download successful
                        
                except Exception as e:
                    print(f"\033[91mAttempt {attempt + 1}/{retries} error downloading {url} (to {local_path} via {temp_path}): {e}\033[0m")
                    if os.path.exists(temp_path): # If download failed, clean up partial temp file before retry
                        try:
                            os.remove(temp_path)
                        except OSError as e_remove_tmp:
                            print(f"\033[93mWarning: Could not remove temporary file {temp_path} after failed attempt: {e_remove_tmp}\033[0m")
                    
                    if attempt < retries - 1:
                        await asyncio.sleep(delay)
                        delay *= 2
                    else: # Last attempt failed
                        # Error will be recorded outside the loop by the caller's logic
                        pass 

        finally:
            # Ensure the specific temp_path for this locked operation is cleaned up if it still exists
            # (e.g., if all retries failed, or an unexpected error before rename)
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except OSError as e:
                    print(f"\033[93mWarning: Could not remove final temporary file {temp_path}: {e}\033[0m")
        
        # If all retries failed and we've exited the loop
        if error_file:
            with open(error_file, 'a') as f:
                if error_message:
                    f.write(error_message)
                else:
                    f.write(f"{url} - Failed after {retries} attempts to download to {local_path}\n")
        return False # Download failed after all retries

# This might not be optimal. Need to compare with using pool = SimpleThreadPool()  pool.add_task(client.download_file, test_bucket, file_cos_key, localName)
# Documentation: https://www.tencentcloud.com/document/product/436/46469
async def async_download_from_cos(client, bucket, cos_key, local_path):
    # Wrap the synchronous download_from_cos in run_in_executor to make it non-blocking
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, download_from_cos, client, bucket, cos_key, local_path)

def sanitize_filename_from_url(url):
    parsed_url = urllib.parse.urlparse(url)
    filename = os.path.basename(parsed_url.path)
    if not filename:
        filename = "downloaded_file"
    # Replace problematic characters for Windows filenames.
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return filename


@observe(capture_input=False, capture_output=False)
async def download(client, bucket, temp_dir, check_id, main_product_image, other_product_images=[], client_ip_images=[], reference_images=[]):
    image_counter = 0

    async def download_image(image, image_type):
        nonlocal image_counter
        print(f"Downloading {image[0:150] if image else 'empty image'}... of type {image_type}")

        image_source_type = "unknown"
        
        try:
            if not image:
                return {'status': 'failure', 'reason': 'empty_image_data', 'source': 'unknown', 'image': ''}

            # If the image is a base64 string
            if image.startswith("data:") or (not "http" in image.lower() and re.match(r'^[A-Za-z0-9+/]+={0,2}$', image.strip())):
                image_source_type = "base64"
                try:
                    ext = "png"
                    encoded = image
                    if image.startswith("data:"):
                        header, encoded = image.split(",", 1)
                        if "image/jpeg" in header: ext = "jpg"
                        elif "image/webp" in header: ext = "webp"
                    
                    image_counter += 1
                    unique_id = uuid.uuid4()
                    local_file_name = f"{image_type}_{image_counter}_{unique_id}.{ext}"
                    local_path = os.path.join(temp_dir, image_type, local_file_name)

                    temp_path = f"{local_path}.tmp"
                    os.makedirs(os.path.dirname(temp_path), exist_ok=True)
                    with open(temp_path, "wb") as f:
                        f.write(base64.b64decode(encoded))
                    os.rename(temp_path, local_path)

                    print(f"Successfully decoded and saved base64 data to {local_path}")
                    return {'status': 'success', 'path': local_path, 'source': image_source_type, 'image': image[:100]}
                except Exception as e:
                    failure_reason = f"base64_decoding_failed: {str(e)}"
                    print(f"Error decoding base64 data: {e}")
                    traceback.print_exc()
                    return {'status': 'failure', 'reason': failure_reason, 'source': image_source_type, 'image': image[:100]}

            # If the image is an HTTP URL
            elif "http" in image.lower():
                image_source_type = "url"
                
                # Validate URL contains a valid image extension, ignoring query parameters
                path_without_query = urllib.parse.urlparse(image).path
                if not any(ext in path_without_query.lower() for ext in ['.jpg', '.jpeg', '.png', '.webp']):
                    failure_reason = "invalid_url"
                    print(f"Invalid image URL (extension missing): {image}")
                    return {'status': 'failure', 'reason': failure_reason, 'source': image_source_type, 'image': image}

                filename = sanitize_filename_from_url(image)
                local_path = os.path.join(temp_dir, image_type, filename)
                if await download_from_url(image, local_path):
                    return {'status': 'success', 'path': local_path, 'source': image_source_type, 'image': image}
                else:
                    failure_reason = "download_failed"
                    return {'status': 'failure', 'reason': failure_reason, 'source': image_source_type, 'image': image}

            # Otherwise, assume it's stored on COS
            else:
                image_source_type = "cos"
                cos_key = f"checks/{check_id}/query/{image}"
                local_path = os.path.join(temp_dir, image_type, image)
                if await async_download_from_cos(client, bucket, cos_key, local_path):
                    return {'status': 'success', 'path': local_path, 'source': image_source_type, 'image': image}
                else:
                    failure_reason = "download_failed"
                    return {'status': 'failure', 'reason': failure_reason, 'source': image_source_type, 'image': image}
        except Exception as e:
            return {'status': 'failure', 'reason': f'unknown_error: {str(e)}', 'source': image_source_type, 'image': image}


    tasks = [download_image(img, "ip") for img in client_ip_images]
    tasks.append(download_image(main_product_image, "product"))
    tasks.extend([download_image(img, "product") for img in other_product_images])
    tasks.extend([download_image(img, "reference") for img in reference_images])

    results = await asyncio.gather(*tasks)

    # --- Aggregation and Langfuse span update ---
    success_count = 0
    failure_count = 0
    source_counts = {'url': 0, 'base64': 0, 'cos': 0, 'unknown': 0}
    failures = []
    
    for res in results:
        source_counts[res['source']] = source_counts.get(res['source'], 0) + 1
        if res['status'] == 'success':
            success_count += 1
        else:
            failure_count += 1
            failures.append({'reason': res['reason'], 'image': res.get('image', '')})

    langfuse.get_client().update_current_span(
        input={'total_images': len(tasks), 'source_counts': source_counts},
        output={'success_count': success_count,'failure_count': failure_count,'failures': failures}
    )

    if failure_count > 0:
        first_error_reason = failures[0]['reason']
        # Instead of returning an error tuple, raise a specific exception that can be caught by the ARQ task.
        raise ImageDownloadError(first_error_reason)
 
    # --- Path sorting ---
    all_paths = [res.get('path') for res in results] # Use .get to avoid KeyError if 'path' is missing on failure
    
    ip_len = len(client_ip_images)
    # The number of product images is 1 (main) + number of others
    prod_len = 1 + len(other_product_images)

    local_client_ip_images = [p for p in all_paths[:ip_len] if p]
    local_product_images = [p for p in all_paths[ip_len : ip_len + prod_len] if p]
    local_reference_images = [p for p in all_paths[ip_len + prod_len:] if p]

    return local_product_images, local_client_ip_images, local_reference_images



# async def async_download(temp_dir, check_id, main_product_image, other_product_images, ip_images, reference_images):
#     client, bucket = get_cos_client()

#     # Prepare download tasks
#     async def download_ip_image(ip_image):
#         if "http" in ip_image.lower():
#             local_ip_path = os.path.join(temp_dir, "ip", ip_image.split('/')[-1])
#             if await async_download_from_url(ip_image, local_ip_path):
#                 return local_ip_path
#         else:
#             local_ip_path = os.path.join(temp_dir, "ip", ip_image)
#             cos_key = f"checks/{check_id}/ip/{ip_image}"
#             if await async_download_from_cos(client, bucket, cos_key, local_ip_path):
#                 return local_ip_path
#         return None

#     async def download_product_image(product_image, image_type="product"): # image_type to differentiate main and other, though currently not used
#         if "http" in product_image.lower():
#             local_product_path = os.path.join(temp_dir, image_type, product_image.split('/')[-1])
#             if await async_download_from_url(product_image, local_product_path):
#                 return local_product_path
#         else:
#             cos_key = f"checks/{check_id}/{image_type}/{product_image}"
#             local_product_path = os.path.join(temp_dir, image_type, product_image) # Corrected path to use image_type
#             if await async_download_from_cos(client, bucket, cos_key, local_product_path):
#                 return local_product_path
#         return None

#     async def download_reference_image(reference_image):
#         if "http" in reference_image.lower():
#             local_reference_path = os.path.join(temp_dir, "reference", reference_image.split('/')[-1])
#             if await async_download_from_url(reference_image, local_reference_path):
#                 return local_reference_path
#         else:
#             cos_key = f"checks/{check_id}/reference/{reference_image}"
#             local_reference_path = os.path.join(temp_dir, "reference", reference_image)
#             if await async_download_from_cos(client, bucket, cos_key, local_reference_path):
#                 return local_reference_path
#         return None


#     # Concurrently download IP images
#     ip_tasks = [download_ip_image(img) for img in ip_images]
#     local_ip_images = await asyncio.gather(*ip_tasks)
#     local_ip_images = [path for path in local_ip_images if path] # filter out None values if download failed

#     # Concurrently download product images (main and others)
#     product_tasks = [download_product_image(main_product_image, "product")]
#     if other_product_images:
#         product_tasks.extend([download_product_image(img) for img in other_product_images])
#     local_product_images = await asyncio.gather(*product_tasks)
#     local_product_images = [path for path in local_product_images if path] # filter out None values

#     # Concurrently download reference images
#     reference_tasks = [download_reference_image(img) for img in reference_images]
#     local_reference_images = await asyncio.gather(*reference_tasks)
#     local_reference_images = [path for path in local_reference_images if path] # filter out None values

#     return local_product_images, local_ip_images, local_reference_images



# def download(temp_dir, check_id, main_product_image, other_product_images, ip_images, reference_images):
#     client, bucket = get_cos_client()
#     # Download and save IP images
#     local_ip_images = []
#     for ip_image in ip_images:
#         if "http" in ip_image.lower():
#             local_ip_path = os.path.join(temp_dir, "ip", ip_image.split('/')[-1])
#             if download_from_url(ip_image, local_ip_path):
#                 local_ip_images.append(local_ip_path)
#         else:
#             local_ip_path = os.path.join(temp_dir, "ip", ip_image)
#             cos_key = f"checks/{check_id}/ip/{ip_image}"
#             if download_from_cos(client, bucket, cos_key, local_ip_path):
#                 local_ip_images.append(local_ip_path)

#     # Download and save product images
#     local_product_images = []
#     if "http" in main_product_image.lower():
#         local_main_path = os.path.join(temp_dir, "product", main_product_image.split('/')[-1])
#         if download_from_url(main_product_image, local_main_path):
#             local_product_images.append(local_main_path)
#     else:
#         cos_key = f"checks/{check_id}/product/{main_product_image}"
#         local_main_path = os.path.join(temp_dir, "product", main_product_image)
#         if download_from_cos(client, bucket, cos_key, local_main_path):
#             local_product_images.append(local_main_path)

#     for other_image in other_product_images:
#         if "http" in other_image.lower():
#             local_other_path = os.path.join(temp_dir, "product", other_image.split('/')[-1])
#             if download_from_url(other_image, local_other_path):
#                 local_product_images.append(local_other_path)
#         else:
#             cos_key = f"checks/{check_id}/product/{other_image}"
#             local_other_path = os.path.join(temp_dir, "product", other_image)
#             if download_from_cos(client, bucket, cos_key, local_other_path):
#                 local_product_images.append(local_other_path)

#     local_reference_images = []
#     for reference_image in reference_images:
#         if "http" in reference_image.lower():
#             local_reference_path = os.path.join(temp_dir, "reference", reference_image.split('/')[-1])
#             if download_from_url(reference_image, local_reference_path):
#                 local_reference_images.append(local_reference_path)
#         else:
#             cos_key = f"checks/{check_id}/reference/{reference_image}"
#             local_reference_path = os.path.join(temp_dir, "reference", reference_image)
#             if download_from_cos(client, bucket, cos_key, local_reference_path):
#                 local_reference_images.append(local_reference_path)

#     return local_product_images, local_ip_images, local_reference_images


if __name__ == "__main__":
    # Example test (add more concurrent calls to test the lock)
    async def test_concurrent_downloads():
        url = 'https://cdn.10100.com/p/8483b40dce0c468d956e1d8fe402947d.png'
        path1 = 'D:/test_image_concurrent.png'
        path2 = 'D:/test_image_concurrent.png' # Same path
        path3 = 'D:/another_test_image.png'
        
        # Ensure target directory exists for testing
        os.makedirs(os.path.dirname(path1), exist_ok=True)
        os.makedirs(os.path.dirname(path3), exist_ok=True)

        # Clean up previous test files if they exist
        if os.path.exists(path1): os.remove(path1)
        if os.path.exists(path3): os.remove(path3)

        print("Starting concurrent downloads test...")
        results = await asyncio.gather(
            download_from_url(url, path1, error_file="error.log"),
            download_from_url(url, path2, error_file="error.log"), # Same URL, same path
            download_from_url('https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png', path3, error_file="error.log")
        )
        print(f"Test results: {results}")
        if os.path.exists(path1): print(f"File {path1} downloaded.")
        if os.path.exists(path3): print(f"File {path3} downloaded.")

    asyncio.run(test_concurrent_downloads())
    # Original test:
    # asyncio.run(download_from_url('https://cdn.10100.com/p/8483b40dce0c468d956e1d8fe402947d.png', 'D:/test.png'))
