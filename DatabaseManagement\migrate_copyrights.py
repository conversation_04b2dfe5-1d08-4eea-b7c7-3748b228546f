
import re
import pandas as pd
from dotenv import load_dotenv
import os, sys
from psycopg2.extras import execute_values
sys.path.append(os.getcwd())
from IP.Trademarks_Bulk.trademark_db import get_db_connection, get_table_from_db

# Load environment variables
load_dotenv()

def create_copyrights_files_table():
    """Create the copyrights_files table in the database."""
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                CREATE TABLE IF NOT EXISTS "public"."copyrights_files" (
                    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                    "filename" text,
                    "registration_number" text,
                    "method" text,
                    "production" bool,
                    "type" varchar(50),
                    "create_time" timestamptz NOT NULL DEFAULT now(),
                    "update_time" timestamptz NOT NULL DEFAULT now(),
                    PRIMARY KEY ("id")
                );
            """)
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_copyrights_files_registration_number ON public.copyrights_files USING btree (registration_number);
            """)
    print("Table 'copyrights_files' created successfully.")

def migrate_data():
    """Migrate data from copyrights to copyrights_files."""
    print("Fetching 'copyrights' table...")
    copyrights_df = get_table_from_db("copyrights")

    if copyrights_df.empty:
        print("No data found in 'copyrights' table. Migration not needed.")
        return

    # Get production files
    production_folder = r'D:\Documents\Programing\TRO\Documents\IP-TRO\copyrights\Production'
    production_files = os.listdir(production_folder)
    production_filenames = {f.split('_', 1)[1] for f in production_files if '_' in f}

    print(f"Processing {len(copyrights_df)} records from 'copyrights' table.")
    new_rows = []
    for _, row in copyrights_df.iterrows():
        filenames = row.get('filenames')
        if not filenames:
            continue

        for filename in filenames:
            if not filename:
                continue

            # Extract method from filename
            match = re.match(r'([^_]+)_([^_]+)(_.*)?\..*', filename)
            method = match.group(2) if match else None

            if not method:
                print(f"Warning: no method found for filename {filename}.")

            # Check if the file is in production
            is_production = filename in production_filenames

            new_rows.append({
                'filename': filename,
                'registration_number': row['registration_number'],
                'method': method,
                'production': is_production,
                'type': None
            })

    if not new_rows:
        print("No filenames found to migrate.")
        return

    print(f"Inserting {len(new_rows)} new records into 'copyrights_files'.")
    files_df = pd.DataFrame(new_rows)
    
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # Use execute_values for efficient batch insertion
            data_to_insert = [
                (row['filename'], row['registration_number'], row['method'], row['production'], row['type'])
                for _, row in files_df.iterrows()
            ]
            
            execute_values(
                cur,
                "INSERT INTO copyrights_files (filename, registration_number, method, production, type) VALUES %s",
                data_to_insert,
                page_size=1000
            )
        conn.commit()
    print("Data insertion into 'copyrights_files' complete.")
    
    # Check for production files not found in copyrights
    migrated_filenames = {row['filename'] for row in new_rows}
    unmatched_production_files = production_filenames - migrated_filenames

    if unmatched_production_files:
        print(f"Warning: {len(unmatched_production_files)} production files were not found in the copyrights data and were not migrated.")
        print("Unmatched production files:")
        for f in sorted(list(unmatched_production_files)):
            print(f"  - {f}")

def drop_old_columns():
    """Drop the old columns from the copyrights table."""
    print("Dropping old columns from 'copyrights' table...")
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                ALTER TABLE "public"."copyrights"
                DROP COLUMN IF EXISTS "filenames",
                DROP COLUMN IF EXISTS "method",
                DROP COLUMN IF EXISTS "production",
                DROP COLUMN IF EXISTS "type";
            """)
    print("Old columns dropped successfully.")

if __name__ == "__main__":
    create_copyrights_files_table()
    migrate_data()
    drop_old_columns()
    print("Migration complete.")