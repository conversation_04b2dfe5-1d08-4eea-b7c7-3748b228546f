import os
import sys
sys.path.append(os.getcwd())
import cv2
import os
import shutil
import asyncio
from logdata import log_message
from langfuse import observe
import langfuse
from IP.Copyrights.Copyright_Get_Image_Google import get_copyright_images_from_google_api
from Common.Constants import sanitize_name
from Alerts.IPTrackingManager import IPTrackingManager # Added import

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)



@observe(capture_input=False, capture_output=False)
async def get_copyright_images_from_google(df, index, case_images_directory, ip_manager: IPTrackingManager):
    """
    Processes copyright registration numbers by searching for images on Google,
    saves the best match, updates the dataframe, and records findings using IPTrackingManager.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        ip_manager: Instance of IPTrackingManager to track IP processing state.

    Returns:
        int: The number of copyright images successfully processed from Google.
    """
    reg_data_df = ip_manager.get_copyrights_without_images() # Retrieve the DataFrame
    if reg_data_df.empty:
        log_message(f"  Google CR Search: No missing pictures initially provided by IP manager for case index {index}.", level='INFO')
        return 0 # No missing pictures to process

    # Filter for VA numbers BEFORE searching Google
    va_reg_data_df = reg_data_df[reg_data_df['reg_no'].astype(str).str.upper().str.startswith('VA')].copy()

    if va_reg_data_df.empty:
        log_message(f"  Google CR Search: No 'VA' prefixed registration numbers to search on Google for case index {index}.", level='INFO')
        return 0 # No "VA" pictures to process

    log_message(f"  🔨 Searching Google for missing copyright images: {len(va_reg_data_df)} 'VA' prefixed registration numbers: {",".join(va_reg_data_df['reg_no'])}", level='INFO')
    folder_for_all_google_results = os.path.join(case_images_directory, "google_copyright_results")

    # Run Google image search only for VA numbers
    va_reg_data_df = await get_copyright_images_from_google_api(va_reg_data_df, folder_for_all_google_results, case_images_directory)

    # Process the search results (which are now only for VA numbers)
    processed_count = 0
    for _, reg_row in va_reg_data_df.iterrows(): # Iterate over the VA-filtered DataFrame
        reg_no_str = str(reg_row['reg_no'])

        if reg_row['best_google_image'] is not None:
            try:
                img = cv2.imread(reg_row['best_google_image'])
                new_name_full = sanitize_name(df.at[index, "docket"]) + "_regno_" + reg_no_str + "_full.webp"
                new_name = new_name_full.replace("_full", "")
                cv2.imwrite(os.path.join(case_images_directory, new_name_full), img, [cv2.IMWRITE_WEBP_QUALITY, 80])
                os.remove(reg_row['best_google_image'])
                shutil.copy(os.path.join(case_images_directory, new_name_full), os.path.join(case_images_directory, new_name))
                # Ensure reg_no is always a list of strings
                df.at[index, 'images']['copyrights'][new_name] = {'full_filename': [new_name_full], 'reg_no': [reg_no_str]}
                processed_count += 1

                # Update ip_manager state
                location_id = f"google-{reg_no_str}"
                ip_manager.record_finding('copyright', location_id, [reg_no_str])

                # Update database google_status to 'image_found'
                ip_manager.add_copyright_to_dataframe({"registration_number": reg_no_str, "image_found": 1, "image_source": "google"}, location_id)


            except Exception as e:
                print(f"\033[91m 🔥 Error processing {reg_row['best_google_image']}: {e}. Image not added to the case.\033[0m")
                print(f"\033[91m 🔥 Error processing {reg_row['best_google_image']}. Image not added to the case.\033[0m")
                # Update database google_status to 'no_image_found' on error
                ip_manager.add_copyright_to_dataframe({"registration_number": reg_no_str, "image_found": 0}, location_id)
        else:
            # No image found for this registration number
            ip_manager.add_copyright_to_dataframe({"registration_number": reg_no_str, "image_found": 0}, location_id)

    # If we successfully processed any images, mark for validation
    if processed_count > 0:
        df.at[index, 'validation_status'] = 'review_required'
        df.at[index, 'images_status']['copyright_status']['bygoogle']['count'] += processed_count

    langfuse.get_client().update_current_span(
        input={
            "NumRegNosToLookFor": len(reg_data_df),
            "RegNosToLookFor": reg_data_df['reg_no'].to_string(index=False)
        },
        output={
            "CopyrightsFoundCount": len(df.at[index, 'images']['copyrights'])
        }
    )

    # langfuse.get_client().update_current_span is already called at the end of this function with input/output.
    return processed_count