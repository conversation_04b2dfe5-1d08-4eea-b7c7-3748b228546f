/* ======== Cover ======== */
/* Make the cover span the full printable height: rows [top auto][bottom flex] */
.cover{
  border: 1mm solid var(--navy);
  text-align:center;  /* Center the table, and logo and title and company name */
  display: flex; /* flex container to have the seal stick to the bottom */
  flex-direction: column;
  height: 275mm; /* 297mm - 2*10mm page margin - 2*1mm border */
  width: 188mm; /* 210mm - 2*10mm page margin - 2*1mm border */
  background:#e7e7e7;
  margin: 0mm auto; /* center on page */
  box-sizing: border-box; /* include border in height */
}

/* .brand{ padding: 5mm 3mm 5mm 5mm; } */
.brand-logo{ height:24mm;} /* height of the logo image */
.brand-meta{ padding-top: 5mm; } /* height of the logo image */

.title{
  background: var(--navy);
  color: #fff;
  padding: 4mm;
  margin: 4mm 0;
  font-size:16pt;
  font-weight:700;
  letter-spacing:.3px;
  text-align: center;  /* Makes it centered when it wraps */
}

.meta-grid{
  display:grid; 
  grid-template-columns: repeat(3, 1fr); /* 3 columns */
  padding-bottom: 3mm;
  width: 100%;
  margin: 0 auto;
}

.meta-grid .label{
    color:var(--muted);/* light gray */
    padding: 2mm;
} 
.meta-grid .value{
    font-size:11pt; 
    font-weight:700;
}

.risk-badge{ padding:3pt 8pt; border-radius:4px; font-weight:800; font-size:10pt; white-space:nowrap; }
.risk-high{ background:#FDE2E2; color:#B91C1C; border:0.3mm solid #F19999; }
.risk-med { background:#FEF3C7; color:#92400E; border:0.3mm solid #FCD34D; }
.risk-low { background:#D1FAE5; color:#065F46; border:0.3mm solid #6EE7B7; }


/* ===== Product cards ===== */
/* .cover-products{
  background-color: #065F46;
} */

.section-title{
  margin-bottom: 6mm;
  font-size: 13pt;
  font-weight: 700;
}
.product-grid{
  display:grid; 
  grid-template-columns: repeat(2, 1fr); 
  gap:2mm;
  width: 100%;
  margin-right: 2mm;
  margin-left: 2mm;
}
.product-card{
  height: 42mm;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.summary{
  width:90%; border-collapse: collapse; border:0.3mm solid var(--line);
  font-size: 9.5pt;
  margin: 0 auto;  /* center the table */
}
.summary th, .summary td{
  padding: 3pt 5pt; border-bottom:0.3mm solid var(--line); border-right:0.3mm solid var(--line);
}
.summary thead th{ background:var(--accent-bg); font-weight:700; }
.risk-pill{ padding:1pt 5pt; border-radius:9999px; font-weight:700; font-size:8.5pt; }
.risk-pill.risk-high{ background:#FDE2E2; color:#B91C1C; border:0.3mm solid #F19999; }
.risk-pill.risk-med { background:#FEF3C7; color:#92400E; border:0.3mm solid #FCD34D; }
.risk-pill.risk-low { background:#D1FAE5; color:#065F46; border:0.3mm solid #6EE7B7; }
.seal{ text-align:center; margin-top:auto; padding-top:8mm; position:relative; }
.seal img{ width:28mm; opacity:.95; position: relative; z-index: 2; }
.seal::before{
  content: '';
  position: absolute;
  left: 10%;
  right: 10%;
  top: 50%;
  height: 0.5mm;
  background: var(--navy);
}
hr{ 
  border:none; 
  border-top: 0.3mm solid var(--navy); 
  margin: 6mm auto;
  width: 80%;
}