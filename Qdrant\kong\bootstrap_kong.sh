#!/usr/bin/env bash
set -euo pipefail
ADMIN=http://localhost:8001
KONG_ADMIN_TOKEN='2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ'

### !!!!! INSTRUCTIONS: Edit the vast IP/PORT. Execute manually. Do not add the JWT plugin to the Service (last step) until ready

# 1) Enable Prometheus metrics (global)
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/plugins -d name=prometheus >/dev/null

# 2) Upstream with active+passive health checks and least-connections LB
for svc in check-api check-api-inverted; do
  curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT $ADMIN/upstreams/$svc \
    -H 'content-type: application/json' \
    -d '{
      "name": "$svc",
      "algorithm":"least-connections",
      "healthchecks":{
        "active":{"type":"http","http_path":"/health","timeout":2,"healthy":{"interval":10,"successes":2},"unhealthy":{"interval":5,"http_failures":1,"tcp_failures":1,"timeouts":1}},
        "passive":{"type":"http","healthy":{"successes":2},"unhealthy":{"http_failures":2,"tcp_failures":1,"timeouts":1}}
      }
    }' >/dev/null
done

# 3) Targets: Vast (primary) + local Docker API (fallback)
#    Replace VAST_HOST and PORT at first run; you can script this later.
VAST_HOST="***********"    # <- set the current Vast IP here
VAST_PORT="40324"       # <- set the current Vast port here
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/upstreams/check-api/targets -d "target=${VAST_HOST}:${VAST_PORT}" -d "weight=100" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/upstreams/check-api-inverted/targets -d "target=${VAST_HOST}:${VAST_PORT}" -d "weight=1" >/dev/null

# Local fallback: your existing API container on the shared network
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/upstreams/check-api/targets -d "target=tro_app_api:5000" -d "weight=1" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/upstreams/check-api-inverted/targets -d "target=tro_app_api:5000" -d "weight=100" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/upstreams/check-api-inverted/targets -d "target=tro_app_api:5089" -d "weight=0"  >/dev/null

# 4.1) Services & Routes (per-path so you can attach per-service rate limits)
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT $ADMIN/services/check -d "host=check-api" -d "port=80" -d "protocol=http" -d "connect_timeout=3000" -d "write_timeout=10000" -d "read_timeout=10000" -d "retries=1" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT $ADMIN/services/check_lite -d "host=check-api" -d "port=80" -d "protocol=http" -d "connect_timeout=3000" -d "write_timeout=10000" -d "read_timeout=10000" -d "retries=1" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT $ADMIN/services/check_admin -d "host=check-api" -d "port=80" -d "protocol=http" -d "connect_timeout=3000" -d "write_timeout=10000" -d "read_timeout=10000" -d "retries=1" >/dev/null

curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check/routes -d "name=check-route" -d "hosts[]=api.maidalv.com" -d "paths[]=/check_api" -d "strip_path=false" -d "preserve_host=true" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check_lite/routes -d "name=check-lite-route" -d "hosts[]=api.maidalv.com" -d "paths[]=/check_lite" -d "strip_path=false" -d "preserve_host=true" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check_admin/routes -d "name=get-check-dates" -d "hosts[]=api.maidalv.com" -d "paths[]=/history/get_check_dates" -d "strip_path=false" -d "preserve_host=true" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check_admin/routes -d "name=get-checks-for-date" -d "hosts[]=api.maidalv.com" -d "paths[]=/history/get_checks_for_date" -d "strip_path=false" -d "preserve_host=true" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check_admin/routes -d "name=get-check-details" -d "hosts[]=api.maidalv.com" -d "paths[]=/history/get_check_details" -d "strip_path=false" -d "preserve_host=true" >/dev/null

# 4.2) Reverse Check
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT $ADMIN/services/reverse_check -d "host=api" -d "port=8000" -d "protocol=http" -d "connect_timeout=3000" -d "write_timeout=10000" -d "read_timeout=10000" -d "retries=1" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/reverse_check/routes -d "name=reverse-check-status" -d "hosts[]=api.maidalv.com" -d "paths[]=/reverse_check_status" -d "strip_path=false" -d "preserve_host=true" >/dev/null

# 4.3) Catch-all service for all other paths
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT $ADMIN/services/non_api -d "host=check-api" -d "port=80" -d "protocol=http" -d "connect_timeout=3000" -d "write_timeout=10000" -d "read_timeout=10000" -d "retries=1" >/dev/null
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/non_api/routes -d "name=Catch-All" -d "hosts[]=api.maidalv.com" -d "strip_path=false" -d "preserve_host=true" >/dev/null


# 5) Auth: API-key (key-auth) at the Route or Service level (here per Service)
for svc in check check_lite check_admin reverse_check; do
  curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/$svc/plugins -d name=key-auth >/dev/null
done

# 6.1) Rate limiting per Service (per-minute/day/month) by Consumer, using Redis
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check/plugins \
  -d name=rate-limiting \
  -d config.limit_by=consumer \
  -d config.policy=redis \
  -d config.redis.host=redis \
  -d config.redis.port=6379 \
  -d config.minute=1 \
  -d config.day=100 \
  -d config.month=300000 >/dev/null
  
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check_lite/plugins \
  -d name=rate-limiting \
  -d config.limit_by=consumer \
  -d config.policy=redis \
  -d config.redis.host=redis \
  -d config.redis.port=6379 \
  -d config.minute=10 \
  -d config.day=200 >/dev/null

# 6.2) ACL (User group allowed / denied) per Service
# Members of group "no-check" cannot access check service (and similar for check_lite)
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check/plugins -d name=acl -d config.deny=no-check
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check_lite/plugins -d name=acl -d config.deny=no-check-lite
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check_admin/plugins -d name=acl -d config.deny=no-admin

# Then to block a consumer you add hime to the list (no UI way to do this)
# curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/consumers/alice/acls -d group=no-check

# 6.3) Access for Serge to reach container directly on vectorstore1.maidalv.com
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT $ADMIN/services/check-container -d "host=check-api-inverted" -d "port=80" -d "protocol=http" -d "connect_timeout=3000" -d "write_timeout=10000" -d "read_timeout=10000" -d "retries=1"
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check-container/routes -d "name=check-container-web" -d "hosts[]=vectorstore1.maidalv.com" -d "strip_path=false" -d "preserve_host=true"
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/check-container/routes -d "name=check-container-api" -d "paths[]=/check_api" -d "paths[]=/check_lite" -d "paths[]=/reverse_check_status" -d "paths[]=/history/get_check_dates" -d "paths[]=/history/get_checks_for_date" -d "paths[]=/history/get_check_details" -d "hosts[]=vectorstore1.maidalv.com" -d "strip_path=false" -d "preserve_host=true"
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/routes/check-container-api/plugins -d name=key-auth
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/routes/check-container-api/plugins -d name=acl -d config.allow=only-serge
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/consumers/TestSerge/acls -d group=only-serge


# 6.4) GRPC cponnection to vast
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X PUT "$ADMIN/services/embeddings" -d "host=${VAST_HOST}" -d "port=${VAST_PORT}" -d "protocol=grpc" -d "connect_timeout=30000" -d "read_timeout=600000" -d "write_timeout=600000"
curl -fsS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST "$ADMIN/services/embeddings/routes" -d "name=embeddings-grpc" -d "protocols[]=grpc" -d "protocols[]=grpcs" -d "hosts[]=embeddings.maidalv.com" -d "paths[]=/"


# 7) Put Admin API behing Kong (for Auth): Add cloudflare access as a consumer with JWT. Set the Service, then the route, then the key-auth plugin, then add the CORS plugin to allow access from the Manager UI on a different subdomain
# What happends: user goes to https://kong1.maidalv.com/services -> Cloudflare Access -> IDP from microsoft -> Cloudflare Tunnel to Kong Manager UI (8002) -> 
# Kong Admin API request to https://kong1.maidalv.com/km-admin/services -> Cloudflare Tunnel to 127.0.0.1:443 (i.e. the kong api gateway) with the JWT coockie -> 
# Kong's JWT plugin checks the JWT coockie -> Kong's Request Transformer plugin then adds the Kong-Admin-Token header -> Kong serves the Admin API request
# Cloudflare requirements: 2 tunnels (kong1.maidalv.com -> http 127.0.0.1:8002, kongapi.maidalv.com -> https 127.0.0.1:443 with Access activated), 1 app

curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services \
  -H "content-type: application/json" \
  -d '{
        "name": "kong-admin-api",
        "protocol": "http",
        "host": "127.0.0.1",
        "port": 8001,
        "retries": 0,
        "connect_timeout": 2000,
        "read_timeout": 60000,
        "write_timeout": 60000
      }'

curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/admin-api-svc/routes \
  -H "content-type: application/json" \
  -d '{
        "name": "admin-api-route",
        "hosts": ["kong1.maidalv.com"],
        "paths": ["/km-admin"],
        "strip_path": true,
        "protocols": ["https"]
      }'

# Add the consumer
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/consumers -d "username=cloudflare-access"

# Add the JWT keys for Cloudflare Access (for each key)
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/consumers/cloudflare-access/jwt \
  -d algorithm=RS256 \
  -d key=af6b97395ecadcb8ba71fcce85507bf6424ada0998e3b2b01797e38e776af5c7 \
  --data-urlencode "rsa_public_key=$(cat cf_keys/cf-access-af6b97395ecadcb8ba71fcce85507bf6424ada0998e3b2b01797e38e776af5c7.pem)"

curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/consumers/cloudflare-access/jwt \
  -d algorithm=RS256 \
  -d key=f4c21fdd389f7d3678692ec6489298c2b1854572e105ca61a0791e2a05b86d01 \
  --data-urlencode "rsa_public_key=$(cat cf_keys/cf-access-f4c21fdd389f7d3678692ec6489298c2b1854572e105ca61a0791e2a05b86d01.pem)"

# Add the Kong-Admin-Token header to the request then add the Kong Admin Token to the docker compose then 
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/admin-api-svc/plugins \
  --data 'name=request-transformer' \
  --data-urlencode 'config.add.headers=Kong-Admin-Token: 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ'

# Add the JWT plugin to the Service
curl -sS -H "Kong-Admin-Token: $KONG_ADMIN_TOKEN" -X POST $ADMIN/services/admin-api-svc/plugins \
  -H 'content-type: application/json' \
  -d '{
    "name": "jwt",
    "config": {
      "header_names": ["Cf-Access-Jwt-Assertion"],
      "cookie_names": ["CF_Authorization"],
      "key_claim_name": "kid",
      "claims_to_verify": ["exp", "nbf"],
      "run_on_preflight": false
    }
  }'

echo "Kong bootstrap complete."
