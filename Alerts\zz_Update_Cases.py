import pandas as pd
# import os
import pandas as pd
import time
import traceback
from datetime import datetime
# Removed: from DatabaseManagement.Connections import get_gz_connection
from DatabaseManagement.ImportExport import get_table_from_GZ # Added
from logdata import log_message, update_step_status, finish_run, get_context, task_context
# Import the new WorkflowManager
from . import zz_WorkflowManager
from Common.Constants import FILE_TYPE_STRATEGY_IP_ONLY # Import default strategy


def update_cases_day(refresh_date):
    """Placeholder for potential future daily update logic."""
    pass


def update_all_subscribed_cases(run_id, threshold_days):
    """
    Fetches subscribed open cases and delegates processing to WorkflowManager.
    """
    try:
        start_time = time.time()
        with task_context(run_id, 'Get Subscribed Cases'):
            update_step_status(run_id, 'Get Subscribed Cases', 'Running')
            log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Starting Subscribed Case Update (Run ID: {run_id})...')
            log_message(f'Fetching list of subscribed open cases...')

            subscribed_case_ids = []
            try:
                # Fetch the list of subscribed case IDs using DataFrames
                log_message("Loading cases and user_case data to find subscribed cases...") # Use log_message
                cases_df = get_table_from_GZ("tb_case")
                user_case_df = get_table_from_GZ("tb_user_case")

                if cases_df is None or user_case_df is None:
                     raise Exception("Failed to load case or user_case data.")

                # Filter for open cases
                open_cases_df = cases_df[cases_df['class_code'] == 'Open']

                # Merge to find subscribed open cases
                subscribed_open_cases = pd.merge(open_cases_df, user_case_df, left_on='id', right_on='case_id')

                # Get unique IDs
                subscribed_case_ids = subscribed_open_cases['id'].unique().tolist()

            except Exception as db_err:
                log_message(f"Error loading or filtering data to find subscribed cases: {db_err}", level="ERROR")
                raise # Re-raise to be caught by the main handler

            if not subscribed_case_ids:
                log_message("No subscribed open cases found to update.")
                update_step_status(run_id, 'Get Subscribed Cases', 'Completed')
                finish_run(run_id, 'Completed - No cases') # Use specific status
                return

            log_message(f'Found {len(subscribed_case_ids)} subscribed open cases to process.')
            update_step_status(run_id, 'Get Subscribed Cases', 'Completed')

        # Define options for the update workflow
        # Consider making threshold_days influence force_refresh or other options
        update_options = {
            'file_type_strategy': FILE_TYPE_STRATEGY_IP_ONLY, # Or another appropriate default for updates
            'force_refresh': False, # Usually False for routine updates
            'update_steps': True, # Definitely True for updates
            'process_pictures': True, # Process pictures during update?
            'upload_files': True, # Upload any new files?
            # 'run_ai_tasks': False, # Deprecated
            'run_plaintiff_overview': False, # Keep AI disabled for routine updates
            'run_summary_translation': False,
            'run_step_translation': False,
            'refresh_cache_after_ai': False, # No AI ran, so no refresh needed
            'max_retries': 2,
            'delay_between_retries': 5
            # Add threshold_days if CaseProcessor needs it?
            # 'threshold_days': threshold_days
        }

        # Delegate processing to WorkflowManager
        with task_context(run_id, 'Process Cases'):
             update_step_status(run_id, 'Process Cases', 'Running')
             log_message(f"Calling WorkflowManager to process {len(subscribed_case_ids)} cases...")

             zz_WorkflowManager.process_specific_cases(
                 run_id=run_id,
                 case_list=subscribed_case_ids,
                 options=update_options
             )
             # WorkflowManager now handles logging completion status and counts
             update_step_status(run_id, 'Process Cases', 'Completed') # Mark step complete

        # WorkflowManager and CaseProcessor handle individual case status updates and final run logging.
        # No need for separate DB upload or frontend refresh steps here.

        duration = time.time() - start_time
        log_message(f"Subscribed Case Update (Run ID: {run_id}) orchestrated in {duration:.2f} seconds. Individual case processing managed by WorkflowManager.")
        # finish_run is handled by WorkflowManager

    except Exception as e:
        error_msg = traceback.format_exc()
        current_run_id, current_step_name = get_context()
        log_message(f'Error during update_all_subscribed_cases orchestration (Run ID: {run_id}): {error_msg}', level='CRITICAL')
        if current_run_id is not None and current_step_name is not None:
            update_step_status(current_run_id, current_step_name, 'Failed')
        # finish_run is handled by WorkflowManager's error path
        raise # Re-raise so the process wrapper knows it failed


# --- Removed Helper Functions ---
# update_a_case, update_a_case_docketbird
# Their logic is now handled by Alerts.CaseProcessor.process_case


if __name__ == "__main__":
    # Example of how to run the refactored function locally
    from logdata import create_new_run
    log_message("Starting local test run for update_all_subscribed_cases...", level='INFO')
    test_run_id = create_new_run()
    log_message(f"Generated Run ID: {test_run_id}", level='INFO')
    try:
        # Set a threshold (e.g., update cases not checked in 10 days)
        update_all_subscribed_cases(test_run_id, threshold_days=10)
        log_message(f"Local test run (ID: {test_run_id}) completed.", level='INFO')
    except Exception as main_e:
        log_message(f"Local test run (ID: {test_run_id}) failed: {main_e}", level='ERROR', exc_info=True)
