import requests
import zipfile
import os
import re
import xml.etree.ElementTree as ET
from pathlib import Path
import logging
import asyncio
from concurrent.futures import ProcessPoolExecutor

from IP.Patents_Bulk.patent_cpc_ipc_definition import upsert_patent_cpc_definitions

# --- Constants and Configuration ---
DOWNLOAD_URL = "https://www.cooperativepatentclassification.org/sites/default/files/cpc/bulk/CPCSchemeXML202505.zip"
VERSION = "2025-05-01"
SAVE_DIRECTORY_BASE_NAME = "Documents/IP/Patents/USPTO_CPC_definition"
ZIP_FILE_NAME = "CPCSchemeXML202505.zip"
EXTRACT_TO_FOLDER_NAME = "cpc_xml_files_extracted"
FILE_PATTERN_REGEX_STR = r"cpc-scheme-[A-Z].+\.xml"
SYMBOL_REGEX_STR = r"^([A-HY])(\d{2})([A-Z])(\d{1,4})/(\d+)$"
MAX_CONCURRENT_XML_PARSERS = os.cpu_count() or 1 # Use number of CPUs for parallel parsing

# --- Basic Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- Function Definitions ---
def download_file(url: str, save_dir_path: str, file_name: str) -> str | None:
    """
    Downloads a file from a given URL and saves it to a specified directory.

    Args:
        url: The URL to download the file from.
        save_dir_path: The directory path to save the downloaded file.
        file_name: The name to save the file as.

    Returns:
        The full path to the downloaded file if successful, None otherwise.
    """
    save_file_path = os.path.join(save_dir_path, file_name)
    try:
        logging.info(f"Ensuring directory exists: {save_dir_path}")
        os.makedirs(save_dir_path, exist_ok=True)

        logging.info(f"Starting download from {url} to {save_file_path}")
        response = requests.get(url, stream=True, timeout=60)  # Added timeout
        response.raise_for_status()  # Will raise an HTTPError for bad responses (4XX or 5XX)

        with open(save_file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        logging.info(f"Successfully downloaded {file_name} to {save_file_path}")
        return save_file_path
    except requests.exceptions.HTTPError as e:
        logging.error(f"HTTP error occurred while downloading {url}: {e}")
    except requests.exceptions.ConnectionError as e:
        logging.error(f"Connection error occurred while downloading {url}: {e}")
    except requests.exceptions.Timeout as e:
        logging.error(f"Timeout occurred while downloading {url}: {e}")
    except requests.exceptions.RequestException as e:
        logging.error(f"An error occurred during download from {url}: {e}")
    except IOError as e:
        logging.error(f"IOError occurred while saving file {save_file_path}: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in download_file: {e}")
    return None


def unzip_files(zip_path: str, extract_path: str) -> bool:
    """
    Unzips a zip file to a specified extraction path.

    Args:
        zip_path: The path to the zip file.
        extract_path: The directory where the contents will be extracted.

    Returns:
        True if unzipping was successful, False otherwise.
    """
    try:
        logging.info(f"Ensuring extraction directory exists: {extract_path}")
        os.makedirs(extract_path, exist_ok=True)

        logging.info(f"Starting to unzip {zip_path} to {extract_path}")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
        logging.info(f"Successfully unzipped {zip_path} to {extract_path}")
        return True
    except FileNotFoundError:
        logging.error(f"Error: Zip file not found at {zip_path}")
    except zipfile.BadZipFile:
        logging.error(f"Error: Failed to unzip. The file at {zip_path} may be corrupted or not a zip file.")
    except PermissionError:
        logging.error(f"Error: Permission denied when trying to extract to {extract_path} or read {zip_path}.")
    except IOError as e:
        logging.error(f"IOError occurred during unzipping: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in unzip_files: {e}")
    return False


def parse_symbol(symbol_text: str, compiled_regex: re.Pattern) -> dict | None:
    """
    Parses a classification symbol string using a compiled regular expression.

    Args:
        symbol_text: The classification symbol string (e.g., "C03C27/048").
        compiled_regex: The compiled regular expression pattern for parsing.

    Returns:
        A dictionary with parsed components ("Section", "Class", "Subclass",
        "Main_Group", "Subgroup") if successful, None otherwise.
    """
    if not symbol_text:
        return None
    match = compiled_regex.match(symbol_text.strip())
    if match:
        return {
            "section": match.group(1),
            "class": match.group(2),
            "subclass": match.group(3),
            "main_group": match.group(4),
            "sub_group": match.group(5)
        }
    else:
        logging.debug(f"Symbol '{symbol_text}' did not match regex.")
        return None


def process_xml_file(file_path: str, compiled_symbol_regex: re.Pattern) -> list[dict]:
    """
    Parses an XML file to extract classification symbols and their definitions.

    Args:
        file_path: The path to the XML file.
        compiled_symbol_regex: The compiled regex for parsing classification symbols.

    Returns:
        A list of dictionaries, where each dictionary contains parsed symbol
        components and the definition. Returns an empty list if errors occur
        or no valid items are found.
    """
    file_data: list[dict] = []
    try:
        logging.debug(f"Processing XML file: {file_path}")
        tree = ET.parse(file_path)
        root = tree.getroot()

        # Using .// to find elements anywhere under the current element
        for item_element in root.findall(".//classification-item"):
            if item_element.get("not-allocatable") == "false":
                item_info: dict[str, any] = {
                    "classification_type": "CPC",  # Hardcoded as CPC
                    "publish_date": VERSION,       # Use the existing VERSION constant
                }

                symbol_element = item_element.find("classification-symbol")
                parsed_symbol_data = None
                if symbol_element is not None and symbol_element.text:
                    symbol_text = symbol_element.text.strip()
                    parsed_symbol_data = parse_symbol(symbol_text, compiled_symbol_regex)
                    if parsed_symbol_data:
                        item_info.update(parsed_symbol_data)
                    else:
                        logging.warning(f"Could not parse symbol: '{symbol_text}' in file {file_path}")

                definition_text = None
                title_element = item_element.find("class-title")
                if title_element is not None:
                    # Find all <text> elements, including those nested within other elements like <title-part>
                    text_parts = [
                        text_node.text.strip()
                        for text_node in title_element.findall(".//text")
                        if text_node.text and text_node.text.strip()
                    ]
                    if text_parts:
                        definition_text = "; ".join(text_parts)
                        item_info["definition"] = definition_text
                    else:
                        logging.debug(f"No <text> content found in <class-title> for an item in {file_path}")

                # Only add if we have both essential parts: a parsed symbol and a definition
                if parsed_symbol_data and definition_text:
                    file_data.append(item_info)
                elif parsed_symbol_data and not definition_text:
                    logging.debug(f"Symbol found but no definition for item in {file_path}. Symbol: {parsed_symbol_data.get('Section', '')}{parsed_symbol_data.get('Class', '')}{parsed_symbol_data.get('Subclass', '')}{parsed_symbol_data.get('Main_Group', '')}/{parsed_symbol_data.get('Subgroup', '')}")


        logging.debug(f"Finished processing {file_path}. Found {len(file_data)} items.")

    except ET.ParseError as e:
        logging.error(f"XML ParseError in file {file_path}: {e}")
    except FileNotFoundError:
        logging.error(f"Error: XML file not found at {file_path}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in process_xml_file for {file_path}: {e}")
    return file_data


async def main():
    """
    Main asynchronous function to orchestrate the download, unzipping,
    and parallel processing of CPC XML data.
    """
    logging.info("Starting main orchestration logic.")
    all_extracted_data: list[dict] = []
    loop = asyncio.get_running_loop()

    home_dir = Path.home()
    logging.debug(f"Home directory determined as: {home_dir}")

    full_save_directory = home_dir / SAVE_DIRECTORY_BASE_NAME
    full_zip_download_path = full_save_directory / ZIP_FILE_NAME
    extraction_target_path = full_save_directory / EXTRACT_TO_FOLDER_NAME

    logging.info(f"Target save directory: {full_save_directory}")
    logging.info(f"Target zip download path: {full_zip_download_path}")
    logging.info(f"Target extraction path: {extraction_target_path}")

    # 1. Download the file (synchronous)
    downloaded_zip_file_path = download_file(DOWNLOAD_URL, str(full_save_directory), ZIP_FILE_NAME)
    if not downloaded_zip_file_path:
        logging.error("Download failed. Exiting script.")
        return
    logging.info(f"File successfully downloaded to: {downloaded_zip_file_path}")

    # 2. Unzip the file (synchronous)
    unzip_successful = unzip_files(str(downloaded_zip_file_path), str(extraction_target_path))
    if not unzip_successful:
        logging.error("Unzipping failed. Exiting script.")
        return
    logging.info(f"Files successfully unzipped to: {extraction_target_path}")

    # 3. Compile Regex Patterns
    try:
        compiled_file_pattern = re.compile(FILE_PATTERN_REGEX_STR)
        compiled_symbol_regex = re.compile(SYMBOL_REGEX_STR) # This will be passed to processes
        logging.info("Regex patterns compiled successfully.")
    except re.error as e:
        logging.error(f"Failed to compile regex patterns: {e}. Exiting script.")
        return

    # 4. Process XML files in parallel using ProcessPoolExecutor
    logging.info(f"Starting to process XML files from: {extraction_target_path} using up to {MAX_CONCURRENT_XML_PARSERS} processes.")
    
    xml_files_to_process = []
    try:
        if not os.path.exists(extraction_target_path) or not os.path.isdir(extraction_target_path):
            logging.error(f"Extraction directory does not exist or is not a directory: {extraction_target_path}. Exiting script.")
            return
        
        for filename in os.listdir(str(extraction_target_path)):
            if compiled_file_pattern.match(filename):
                full_file_path = os.path.join(str(extraction_target_path), filename)
                xml_files_to_process.append(full_file_path)
            else:
                logging.debug(f"Skipping file (does not match pattern): {filename}")

        if not xml_files_to_process:
            logging.warning(f"No files matching the pattern '{FILE_PATTERN_REGEX_STR}' were found in {extraction_target_path}.")
            # No need to exit here, will just result in no data.
        else:
            logging.info(f"Found {len(xml_files_to_process)} XML files to process.")

    except FileNotFoundError:
        logging.error(f"Error: Extraction directory not found at {extraction_target_path} during file listing. Exiting script.")
        return
    except Exception as e:
        logging.error(f"An unexpected error occurred during file listing: {e}")
        return

    if xml_files_to_process:
        with ProcessPoolExecutor(max_workers=MAX_CONCURRENT_XML_PARSERS) as process_executor:
            tasks = []
            for full_file_path in xml_files_to_process:
                logging.info(f"Queueing XML processing for: {os.path.basename(full_file_path)}")
                # process_xml_file is a synchronous function, run in executor
                task = loop.run_in_executor(process_executor, process_xml_file, full_file_path, compiled_symbol_regex)
                tasks.append(task)
            
            results_from_processes = await asyncio.gather(*tasks, return_exceptions=True)

            processed_file_count = 0
            for i, result in enumerate(results_from_processes):
                file_path_processed = xml_files_to_process[i]
                if isinstance(result, Exception):
                    logging.error(f"Error processing file {os.path.basename(file_path_processed)}: {result}")
                elif result is not None: # process_xml_file returns list[dict] or empty list
                    all_extracted_data.extend(result)
                    logging.info(f"Successfully processed {os.path.basename(file_path_processed)}, extracted {len(result)} items.")
                    processed_file_count +=1
                else:
                    logging.warning(f"Received unexpected None result for {os.path.basename(file_path_processed)}") # Should not happen
            
            logging.info(f"Finished processing all queued XML files. {processed_file_count} files processed successfully out of {len(xml_files_to_process)}.")

    # 5. Upsert data to database
    if all_extracted_data:
        logging.info(f"Attempting to upsert {len(all_extracted_data)} CPC definitions to the database.")
        upserted_count = upsert_patent_cpc_definitions(all_extracted_data)
        logging.info(f"Upsert operation completed. {upserted_count} records were processed for upsertion.")
    else:
        logging.info("No data extracted, skipping database upsert.")


if __name__ == "__main__":
    logging.info("Script execution started.")
    asyncio.run(main())
    logging.info("Script execution finished.")