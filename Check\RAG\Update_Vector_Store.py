# Step 1: Download all images not already in IP/Copyright/All/, and put in IP/Copyright/New/2025-02-19/All/
# Step 2: Copy IP/Copyright/New/2025-02-19/All/ to IP/Copyright/New/2025-02-19/Nodupe/
# Step 3: remove any picture in IP/Copyright/New/2025-02-19/Nodupe/ that is a duplicated or near duplicated with either another picture in IP/Copyright/New/2025-02-19/Nodupe/ or IP/Copyright/Production/
# Step 4: Let the user delete some more manually
# Step 5: Copy all the remaining pictures from IP/Copyright/New/2025-02-19/Nodupe/ to IP/Copyright/Production/

# !!!! WSL environement is not 
import os
import sys
sys.path.append(os.getcwd())
import shutil
from datetime import datetime
from tqdm import tqdm
import cv2
import numpy as np
import os
from multiprocessing import Pool, cpu_count
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
import pytesseract
from AI.LLM_shared import get_json
from DatabaseManagement.ImportExport import get_table_from_GZ
import asyncio
from Common.Constants import local_ip_tro_folder
from AI.llm_caching import vertex_genai_multi_with_cache


# Import helper functions from your existing modules
from Check.RAG.Collect_Images import remove_duplicates, remove_near_duplicates, collect_all_images, classify_near_duplicates, classify_trademark_images, remove_patent_duplicates


os.environ['OMP_THREAD_LIMIT'] = '1'  
# OMP_THREAD_LIMIT=1 setting only affects OpenMP parallelization, which is used internally by Tesseract OCR. It is critical for Tesseract to run at ok speed in a container. It won't affect:
# 1. Python's threading (threading module)
# 2. Python's multiprocessing (multiprocessing module)
# 3. Flask's thread handling
# 4. Your start_task endpoint or any other threading in your application

if os.name == "nt":
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
else:
    pytesseract.pytesseract.tesseract_cmd = 'tesseract'


def update_vector_store(case_type, npy_filename, threshold, llm_threshold, refresh_db=True):
    """
    Updates the vector store following these steps:

    Step 1: Download new images that are not in the ./All folder.
    Step 2: Copy the new images from ./New/{DATE}/All to ./New/{DATE}/Nodupe.
    Step 3: Remove duplicates and near-duplicates from the ./New/{DATE}/Nodupe folder,
            both internally and compared to images in ./Production.
    Step 4: Wait for manual review of the ./New/{DATE}/Nodupe folder.
    Step 5: Copy the remaining images from ./New/{DATE}/Nodupe to ./Production.
    """
    # Define your folder paths. Adjust these as needed for your environment.
    DATE = datetime.now().strftime("%Y-%m-%d")
    ALL_FOLDER = os.path.join(local_ip_tro_folder, case_type, "All")
    os.makedirs(ALL_FOLDER, exist_ok=True)
    NEW_ALL_FOLDER = os.path.join(local_ip_tro_folder, case_type, "New", DATE, "All")
    NEW_NODUPE_FOLDER = os.path.join(local_ip_tro_folder, case_type, "New", DATE, "Nodupe")
    PRODUCTION_FOLDER = os.path.join(local_ip_tro_folder, case_type, "Production")
    os.makedirs(PRODUCTION_FOLDER, exist_ok=True)
    


    df = get_table_from_GZ("tb_case", force_refresh=refresh_db)

    # print("\n\n========== Step 1: Downloading New Images to ./New/{DATE}/All folder ==========\n")
    # os.makedirs(NEW_ALL_FOLDER, exist_ok=True)
    # asyncio.run(collect_all_images(df, NEW_ALL_FOLDER, case_type=case_type, existing_folder_path=ALL_FOLDER))
    # print(f"Downloaded new images to {NEW_ALL_FOLDER}")


    # print("\n\n========== Step 2: Copying new images to ./New/{DATE}/Nodupe folder and to ./All folder ==========\n")
    # if os.path.exists(NEW_NODUPE_FOLDER):
    #     shutil.rmtree(NEW_NODUPE_FOLDER)
    # os.makedirs(NEW_NODUPE_FOLDER, exist_ok=True)

    # items = os.listdir(NEW_ALL_FOLDER)
    # with tqdm(total=len(items), desc="Copying files") as pbar:
    #     for item in items:
    #         s = os.path.join(NEW_ALL_FOLDER, item)
    #         d1 = os.path.join(NEW_NODUPE_FOLDER, item)
    #         d2 = os.path.join(ALL_FOLDER, item)
    #         shutil.copy2(s, d1) # much faster than shutil.copytree
    #         shutil.copy2(s, d2) # much faster than shutil.copytree
    #         pbar.update(1)
    # print(f"Copied files from {NEW_ALL_FOLDER} to {NEW_NODUPE_FOLDER}")


    # print("\n\n========== Step 3: Removing duplicates in Nodupe folder ==========\n")
    # # Remove exact duplicates (by MD5 hash, as implemented in remove_duplicates).
    # remove_duplicates(NEW_NODUPE_FOLDER, reference_folder=PRODUCTION_FOLDER)
    
    # if case_type == "copyrights":
    #     remove_bad_copyright_images(NEW_NODUPE_FOLDER, case_type)
    
    # # Remove near duplicates within the Nodupe folder: from testing using classify_near_duplicates, the best threshold is 
    # classify_near_duplicates(NEW_NODUPE_FOLDER, reference_folder=PRODUCTION_FOLDER)
    # remove_near_duplicates(NEW_NODUPE_FOLDER, reference_folder=PRODUCTION_FOLDER, threshold=threshold, llm_threshold=llm_threshold)
    
    # if case_type == "patents":
    #     # Remove duplicates by patent name and page number
    #     # remove_patent_duplicates(PRODUCTION_FOLDER)
    #     remove_patent_duplicates(NEW_NODUPE_FOLDER)
    #     remove_pages_header(NEW_NODUPE_FOLDER)
        

    # if case_type == "trademarks":
    #     classify_trademark_images(df, NEW_NODUPE_FOLDER) # between logo and text
    #     NEW_NODUPE_FOLDER = os.path.join(NEW_NODUPE_FOLDER, "Logo")
    #     # classify_near_duplicates(NEW_NODUPE_FOLDER, reference_folder=PRODUCTION_FOLDER)
    #     remove_near_duplicates(NEW_NODUPE_FOLDER, reference_folder=PRODUCTION_FOLDER, threshold=threshold, llm_threshold=llm_threshold)


    # print(f"Removed duplicates in {NEW_NODUPE_FOLDER}")


    # print("\n\n========== Step '4: Manual Review ==========\n")
    # print(f"Please review the files in the folder: {NEW_NODUPE_FOLDER}")
    # input("Press Enter to continue after manual deletion of unwanted images, removing page header for patents ...")


    # print("\n\n========== Step 5: Copying reviewed images into Production ==========\n")
    # for filename in os.listdir(NEW_NODUPE_FOLDER):
    #     src_path = os.path.join(NEW_NODUPE_FOLDER, filename)
    #     dst_path = os.path.join(PRODUCTION_FOLDER, filename)
    #     shutil.copy(src_path, dst_path)
    # print(f"Copied files from {NEW_NODUPE_FOLDER} to {PRODUCTION_FOLDER}")


    print("\n\n========== Step 6: Update the vector store ==========\n")
    # Make a backup of the existing vector store
    if os.path.exists(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_filename)):
        shutil.copy(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_filename), os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', f"{npy_filename.replace('.npy', '')}_{DATE}.npy"))

    # Build the new vector store
    plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=refresh_db)
    if case_type == "copyrights":
        from Check.RAG.Build_Vector_Store import build_copyright_images_embeddings_dataset
        build_copyright_images_embeddings_dataset(PRODUCTION_FOLDER, df, plaintiffs_df, npy_filename)
    elif case_type == "trademarks":
        from Check.RAG.Build_Vector_Store import build_trademark_logo_embeddings_dataset
        build_trademark_logo_embeddings_dataset(PRODUCTION_FOLDER, df, plaintiffs_df, npy_filename)
    elif case_type == "patents":
        from Check.RAG.Build_Vector_Store import build_patent_images_embeddings_dataset, build_patent_text_embeddings_dataset
        build_patent_images_embeddings_dataset(PRODUCTION_FOLDER, df, plaintiffs_df, npy_filename)
        if os.path.exists(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_filename.replace("Images.npy", "Texts.npy"))):
            shutil.copy(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_filename.replace("Images.npy", "Texts.npy")), os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', f"{npy_filename.replace('Images.npy', 'Texts')}_{DATE}.npy"))
        build_patent_text_embeddings_dataset(df, plaintiffs_df, npy_filename.replace("Images.npy", "Texts.npy"))
    else:
        raise ValueError(f"Case type {case_type} not supported")

    print("Vector store update complete!")





def remove_bad_copyright_images(folder_path, case_type):
    print(f"Removing certificates and other bad images from {folder_path}")

    parent_dir = os.path.dirname(folder_path.rstrip('/'))
    removed_folder = os.path.join(parent_dir, 'RemovedBadImages')
    os.makedirs(removed_folder, exist_ok=True)

    filenames = os.listdir(folder_path)

    num_processes = cpu_count()  # Use the number of CPU cores
    print(f"Using {num_processes} processes")
    with Pool(processes=num_processes) as pool:
        # Create a list of arguments for each process
        args = [(os.path.join(folder_path, filename), removed_folder, case_type) for filename in filenames]

        # Changed to use imap_unordered with wrapper lambda
        with tqdm(total=len(filenames), desc="Removing bad images") as pbar:
            for _ in pool.imap_unordered(_process_file_wrapper, args):
                pbar.update(1)


def _process_file_wrapper(args):
    return process_file(*args)

def process_file(image_path, removed_folder, case_type):
    result = must_exclude(image_path, case_type)
    if result:
        filename = os.path.basename(image_path)
        print(f"Removing bad image: {filename}")
        shutil.move(image_path, os.path.join(removed_folder, filename))



def get_thresholds(template_dirs):
    filepaths = []
    for template_dir in template_dirs:
        for filename in os.listdir(template_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp')):
                filepaths.append(os.path.join(template_dir, filename))

    filepaths = filepaths[:30]
    for i, filepath in enumerate(filepaths):
        img = cv2.imread(filepath)
        if img is None:
            print(f"Error: Could not read template at {filepath}")
            continue
                
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        h, s, v = cv2.split(hsv)
        mean_saturation = np.mean(s)  # Certificated are below 10 if with picture, and below 3 if no picture on it
        std_saturation = np.std(s)   # Certificated are below 35 if with picture, and below 12 if no picture on it
        print(f"File {i}: Mean saturation: {mean_saturation}, Std saturation: {std_saturation}")


    for i, filepath in enumerate(filepaths):
        img = cv2.imread(filepath)
        if img is None:
            print(f"Error: Could not read template at {filepath}")
                
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        gray_bgr = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)  # Convert back to BGR for comparison (3 dimentions but still grayscale)
        mse = np.mean((img.astype("float") - gray_bgr.astype("float")) ** 2)  # Certificated are below 3 if no picture on it  (otherwise all over the place)
        print(f"File {i}: MSE: {mse}")

    
    for i, filepath in enumerate(filepaths):
        img = cv2.imread(filepath, cv2.IMREAD_GRAYSCALE)
        if img.shape[0] / img.shape[1] > 10 or img.shape[1] / img.shape[0] > 10:
            print(f"File {i}: {os.path.basename(filepath)} is not a copyright picture based on size")


    for i, filepath in enumerate(filepaths):
        img = cv2.imread(filepath, cv2.IMREAD_GRAYSCALE)

        denoised_image = cv2.fastNlMeansDenoising(img, None, h=25, templateWindowSize=7, searchWindowSize=21)
        ocr_text, ocr_data = OCRProcessor.text_in_image(denoised_image)
        print(f"File {i}: ocr_text for {os.path.basename(filepath)}: {len(ocr_text)}")
        if ("name" not in ocr_text.lower() or "character name" in ocr_text.lower()) and \
            "date" not in ocr_text.lower() and \
            "correspondence" not in ocr_text.lower() and \
            not is_page_at_bottom(ocr_data, img):

            print(f"File {i}: {os.path.basename(filepath)} is not a certificate based on content")
    


def must_exclude(image_path, case_type):
    # This function is used to exclude images that are definitely certificates, or with wrong shape or empty or blacked out
    
    saturation_mean_threshold=50
    saturation_std_threshold=45
    mse_threshold=200
    ocr_threshold=200

    img = cv2.imread(image_path)
    
    if img is None:
        print(f"Error: Could not read image at {image_path}")
        return False
    
    # 1. Saturation Analysis (HSV)
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    mean_saturation = np.mean(s)
    std_saturation = np.std(s)

    if mean_saturation > saturation_mean_threshold or std_saturation > saturation_std_threshold:
        return False  # Not grayscale based on saturation 

    img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 2. Grayscale Comparison (Optional)
    gray_bgr = cv2.cvtColor(img_gray, cv2.COLOR_GRAY2BGR)  # Convert back to BGR for comparison (3 dimentions but still grayscale)
    mse = np.mean((img.astype("float") - gray_bgr.astype("float")) ** 2)
    if mse > mse_threshold:
        return False  # Not grayscale based on MSE
    
    # 3. Extreme cases: abnormal size or mostly empty pages
    if img.shape[0] / img.shape[1] > 10 or img.shape[1] / img.shape[0] > 10:
        print(f"File {os.path.basename(image_path)} must be excluded based on size")
        return True
    
    # 4. OCR Analysis
    denoised_image = cv2.fastNlMeansDenoising(img_gray, None, h=25, templateWindowSize=7, searchWindowSize=21)
    ocr_text, ocr_data = OCRProcessor.text_in_image(denoised_image)

    if len(ocr_text) > ocr_threshold or \
        ("name" in ocr_text.lower() and "character name" not in ocr_text.lower()) or \
        ("date" in ocr_text.lower()) or \
        ("correspondence" in ocr_text.lower()) or \
        is_page_at_bottom(ocr_data, img):

        return True
    
    else:
        if mean_saturation < 0.1 and std_saturation < 0.5 and mse < 0.1:
            # 5. OCR is negative, but the page might be empty or almost empty. Ask LLM
            prompt_list = [
                ("text", 'This image has been collected from a document. It is either an image that has been submitted to the US Copyright office, or something else like a page of text, an empty page, a blacked out page, etc. Is it an image that could have been submitted to the US Copyright office? You answer in this format: {"answer"": "image"} or {"answer": "something else"}'),
                ("image_path", image_path)
            ]
            
            ai_answer = vertex_genai_multi_with_cache(
                case_type=case_type,
                cache_filename="must_exclude.json",
                prompt_list=prompt_list,
                image_path1=image_path
            )
            json_answer = get_json(ai_answer)
            print(f"Asked LLM if {os.path.basename(image_path)} is an image. LLM answer: {json_answer}")
            if "answer" in json_answer and json_answer["answer"] == "image":
                return False
            else:
                return True
        else:
            return False
        

def is_page_at_bottom(ocr_data, img):
    image_height = img.shape[0]
    if 'text' not in ocr_data:
        return False
    
    for i, word in enumerate(ocr_data['text']):
        if "page" in word.lower():
            y_location = ocr_data['top'][i] + ocr_data['height'][i] / 2
            # Check if the word is in the bottom half (if so, it is a certificate). !! At the top there is always the word page in blue text.
            if y_location > image_height / 2:
                return True
    return False



def remove_pages_header(folder_path):
    print(f"Removing page header from {folder_path}")

    before_cropping_folder = f"{folder_path}_before_cropping"
    os.makedirs(before_cropping_folder, exist_ok=True)

    num_processes = cpu_count()  # Use the number of CPU cores
    print(f"Using {num_processes} processes")

    filenames = os.listdir(folder_path)

    with Pool(processes=num_processes) as pool:
        # Create a list of arguments for each process
        args = [(os.path.join(folder_path, filename), before_cropping_folder) for filename in filenames if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]

        # Changed to use imap_unordered with wrapper lambda
        with tqdm(total=len(filenames), desc="Removing page header") as pbar:
            for _ in pool.imap_unordered(_remove_page_header, args):
                pbar.update(1)


def _remove_page_header(args):
    return remove_page_header(*args)

def remove_page_header(image_path, before_cropping_folder):
    # copy file to {folder_path}_before_cropping
    shutil.copy(image_path, os.path.join(before_cropping_folder, os.path.basename(image_path)))

    img = cv2.imread(image_path)
    ocr_text, ocr_data = OCRProcessor.text_in_image(img)
    if "sheet" in ocr_text.lower():
        for i, text in enumerate(ocr_data['text']):
            if "sheet" in text.lower():
                sheet_index = i
                break
        y_location = ocr_data['top'][sheet_index] + 1.5 * ocr_data['height'][sheet_index]
    elif "patent" in ocr_text.lower():
        for i, text in enumerate(ocr_data['text']):
            if "patent" in text.lower():
                patent_index = i
                break
        y_location = ocr_data['top'][patent_index] + 1.5 * ocr_data['height'][patent_index]
    else:
        print(f"File {os.path.basename(image_path)} does not contain the word sheet or patent. Ocr text: {ocr_text}")
        return 0
    
    # remove anything above y_location from img
    img = img[int(y_location):, :, :]
    cv2.imwrite(image_path, img)

if __name__ == "__main__":
    LANGFUSE_TRACING_ENABLED = False
    # os.environ['LANGFUSE_TRACING_ENABLED'] = 'false'
    os.environ['LANGFUSE_TRACING_ENABLED'] = 'False'
    # template_directories = [
    #     os.path.join(os.getcwd(), 'data', 'ImageTemplates', "Copyright1"),
    #     os.path.join(os.getcwd(), 'data', 'ImageTemplates', "Copyright2"),
    #     os.path.join(os.getcwd(), 'data', 'ImageTemplates', "Copyright3"),
    # ]

    # file_dir = os.path.join(local_ip_tro_folder, "Copyright", "Production")
    
    # NEW_TEST_FOLDER = os.path.join(local_ip_tro_folder, "Copyright", "New", "2025-02-19", "Test")
    # get_thresholds([NEW_TEST_FOLDER])

    update_vector_store("copyrights", "EmbeddingsCopyright.npy", threshold=60, llm_threshold=30, refresh_db=True)
    # update_vector_store("patents", "EmbeddingsPatentImages.npy", threshold=20, llm_threshold=30, refresh_db=False)
    # update_vector_store("trademarks", "EmbeddingsTrademarkLogo.npy", threshold=60, llm_threshold=30, refresh_db=False)