import asyncio
from collections import defaultdict
from concurrent.futures import <PERSON>hr<PERSON>PoolExecutor
from time import sleep
from typing import Optional

import pytest
from langchain.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from langfuse import get_client, observe
from langfuse.langchain import CallbackHandler
from langfuse.media import LangfuseMedia
# from langfuse import get_client
import langfuse

@observe
def sdc_test(source = "test_source", destination = "test_destination"):
    print("This is a test function for SDC.")
    
    langfuse.get_client().update_current_span(
        metadata={"source": source, "destination": destination}
    )

    return("hihi output")