# 1000 free per month
# https://cloud.google.com/vision/docs/detecting-logos#vision_logo_detection-python

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Add the project root directory to the Python path
import cv2
from google.cloud import vision

# Free 1000 per month the 1.5 usd / 1000
# https://cloud.google.com/vision/pricing
def Google_detect_logos(path):
    """Detects logos in the file."""
    client = vision.ImageAnnotatorClient()

    with open(path, "rb") as image_file:
        content = image_file.read()

    image = vision.Image(content=content)  # image file as a base64 encoded string

    response = client.logo_detection(image=image)
    logos = response.logo_annotations
    print("Logos:")

    original_image = cv2.imread(path)

    extracted_logos = []
    logos_descriptions = []

    for logo in logos:
        print(f"{logo.description} - {logo.score}")

        bounds = [(vertex.x, vertex.y) for vertex in logo.bounding_poly.vertices]
        left = int(min(point[0] for point in bounds))
        top = int(min(point[1] for point in bounds))
        right = int(max(point[0] for point in bounds))
        bottom = int(max(point[1] for point in bounds))

        extracted_logo = original_image[top:bottom, left:right]
        extracted_logos.append(extracted_logo)
        logos_descriptions.append((logo.description, logo.score))


    if response.error.message:
        raise Exception(
            "{}\nFor more info on error messages, check: "
            "https://cloud.google.com/apis/design/errors".format(response.error.message)
        )
    
    return extracted_logos, logos_descriptions
    

def Google_detect_logos_uri(uri):
    """Detects logos in the file located in Google Cloud Storage or on the Web."""
    from google.cloud import vision

    client = vision.ImageAnnotatorClient()
    image = vision.Image()
    image.source.image_uri = uri

    response = client.logo_detection(image=image)
    logos = response.logo_annotations
    print("Logos:")

    for logo in logos:
        print(logo.description)

    if response.error.message:
        raise Exception(
            "{}\nFor more info on error messages, check: "
            "https://cloud.google.com/apis/design/errors".format(response.error.message)
        )


if __name__ == "__main__":
    image_path = "D:/Win10User/Downloads/Screenshot 2024-11-04 232034.jpg"
    Google_detect_logos(image_path)
    # detect_logos_uri("https://www.google.com/20170327_01_T1/LC08_L1GT_044034_20170318_20170327_01_T1_B5.TIF")
    # prompt = "Is there a logo or trademark in this image? From which brand? What is your confidence level? Return the answer in a json: {brand:'coca cola', confidence: 0.87}"
    # answer = llm_call_image(prompt, image_path=image_path)
    # print(answer)
