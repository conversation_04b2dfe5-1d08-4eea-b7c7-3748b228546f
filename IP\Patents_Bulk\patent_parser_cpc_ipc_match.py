import json
from IP.Patents_Bulk.patent_db_grant import get_db_connection
import logging
import os
import pandas as pd

log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_db.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# Global cache for CPC/IPC definitions
_cpc_ipc_definitions_df = None


def match_cpc_ipc(document_id, cpc_entries, ipc_entries):

    abnormal_log_entries = []
    cpc_ipc_entries = []
    if isinstance(cpc_entries, list):
        for cpc_entry in cpc_entries:
            found_definition_id = None
            if not isinstance(cpc_entry, dict):
                abnormal_log_entries.append(f"Document ID: {document_id} - Reason: Invalid CPC entry format. Entry: {json.dumps(cpc_entry)}.")
                continue

            # 1. Attempt CPC Full Match
            definition_id, status = find_definition_id(cpc_entry)
            
            if status != 'not_found':
                found_definition_id = definition_id
                cpc_ipc_entries.append({'id': found_definition_id, 'section': cpc_entry.get('section'), 'class': cpc_entry.get('class'), 'subclass': cpc_entry.get('subclass'), 'main_group': cpc_entry.get('main_group'), 'subgroup': cpc_entry.get('sub_group')})
                continue # Move to the next CPC entry

            # 2. If CPC full match not found, check IPC that look alike to this CPC
            if isinstance(ipc_entries, list):
                cpc_symbol = cpc_entry.get('symbol') # Get CPC symbol
                if cpc_symbol:
                    for ipc_entry in ipc_entries:
                        if not isinstance(ipc_entry, dict):
                            abnormal_log_entries.append(f"Document ID: {document_id} - Reason: Invalid IPC entry format during CPC fallback. Entry: {json.dumps(ipc_entry)}.")
                            continue

                        ipc_symbol = ipc_entry.get('symbol') # Get IPC symbol
                        if ipc_symbol and cpc_symbol.startswith(ipc_symbol): # Check if CPC symbol starts with IPC symbol
                            # Attempt IPC Lookup (full and truncated)
                            current_ipc_symbol = ipc_symbol
                            while len(current_ipc_symbol) > 0:
                                # Construct classification_data dict for IPC lookup
                                ipc_lookup_data = {
                                    'section': ipc_entry.get('section'),
                                    'class': ipc_entry.get('class'),
                                    'subclass': ipc_entry.get('subclass'),
                                    'main_group': ipc_entry.get('main_group'),
                                    'subgroup': current_ipc_symbol # Use truncated symbol for subgroup
                                }
                                definition_id, status = find_definition_id(ipc_lookup_data)
                                if definition_id:
                                    found_definition_id = definition_id
                                    cpc_ipc_entries.append({'id': found_definition_id, 'section': ipc_entry.get('section'), 'class': ipc_entry.get('class'), 'subclass': ipc_entry.get('subclass'), 'main_group': ipc_entry.get('main_group'), 'subgroup': ipc_entry.get('sub_group')})
                                    break # Found an IPC match, move to the next CPC entry

                                current_ipc_symbol = current_ipc_symbol[:-1] # Truncate IPC symbol

                            if found_definition_id:
                                break # Found an IPC match for this CPC, move to the next CPC entry

            # 3. If no full CPC or any IPC match found
            if found_definition_id is None:
                # Print abnormal match - can't find matches for this specific CPC entry
                abnormal_log_entries.append(f"Document ID: {document_id} - CPC Entry: '{json.dumps(cpc_entry)}'")
    elif "B" in document_id:
        abnormal_log_entries.append(f"Document ID: {document_id} - Reason: No CPC or IPC entries and patent type is 'B'.")
        
    # Remove duplicates
    seen_ids = set()
    unique_cpc_ipc_entries = []
    for entry in cpc_ipc_entries:
        if entry['id'] not in seen_ids:
            seen_ids.add(entry['id'])
            unique_cpc_ipc_entries.append(entry)
    
    return unique_cpc_ipc_entries


def find_definition_id(classification_data: dict) -> tuple[int | None, str]:
    """
    Attempts to find a classification definition ID based on the provided data,
    classification type, and fallback rules.
    Uses the global DataFrame cache for much faster lookups.

    Args:
        classification_data: Dictionary containing classification components

    Returns:
        tuple[int | None, str]: (definition_id, status_string)
        Status strings: 'found', 'abnormal_truncated', 'abnormal_zero',
                        'abnormal_null', 'not_found', 'error'
    """
    global _cpc_ipc_definitions_df

    # Ensure caches are loaded
    if _cpc_ipc_definitions_df is None:
        if not load_cpc_ipc_definitions_cache():
            logger.error("Failed to load CPC/IPC definitions cache")
            return None, 'error'

    section = classification_data.get('section')
    class_val = classification_data.get('class')
    subclass = classification_data.get('subclass')
    main_group = classification_data.get('main_group')
    sub_group = classification_data.get('subgroup')

    if not all([section, class_val, subclass]):
         return None, 'missing_components' # Basic validation


    def execute_lookup_df(mg, sg, mg_match_type, sg_match_type):
        """
        Execute lookup using DataFrame filtering with optimized memory usage.
        Uses a single variable for all filtering stages to reduce memory overhead.

        Optimization:
        - Reuses a single 'subset' variable instead of creating multiple intermediate DataFrames
        - Avoids explicit copy operation
        - Applies filters sequentially
        """
        try:
            # OPTIMIZATION: Use single variable for all filtering stages
            lookup_key = f"{section}_{class_val}_{subclass}"
            subset = _cpc_ipc_definitions_df[_cpc_ipc_definitions_df['lookup_key'] == lookup_key]

            if len(subset) == 0:
                return None

            # Apply main_group filter
            if mg_match_type == 'exact':
                if mg is not None:
                    subset = subset[subset['main_group'] == mg]
                else:
                    subset = subset[subset['main_group'].isna()]
            elif mg_match_type == 'ends_with':
                if mg is not None:
                    subset_extact = subset[subset['main_group'] == mg] # Much faster! 
                    if len(subset_extact) > 0:
                        subset = subset_extact
                    else:
                        subset = subset[subset['main_group'].str.endswith(mg, na=False)]
            elif mg_match_type == 'is_null':
                subset = subset[subset['main_group'].isna()]

            # Apply sub_group filter
            if sg_match_type == 'exact':
                if sg is not None:
                    subset = subset[subset['sub_group'] == sg]
                else:
                    subset = subset[subset['sub_group'].isna()]
            elif sg_match_type == 'starts_with':
                if sg is not None:
                    subset_exact = subset[subset['sub_group'] == sg] # Much faster! 
                    if len(subset_exact) > 0:
                        subset = subset_exact
                    else:
                        subset = subset[subset['sub_group'].str.startswith(sg, na=False)]
            elif sg_match_type == 'is_null':
                subset = subset[subset['sub_group'].isna()]

            if len(subset) > 0:
                return subset.iloc[0]['id']
            return None

        except Exception as e:
            logger.error(f"Error during optimized DataFrame lookup (data={classification_data}, mg={mg}, sg={sg}, mg_match={mg_match_type}, sg_match={sg_match_type}): {e}", exc_info=True)
            return None

    # 1. Attempt Primary Match (main_group ends with, sub_group starts with)
    if main_group is not None and sub_group is not None:
        definition_id = execute_lookup_df(main_group, sub_group, 'ends_with', 'starts_with')
        if definition_id:
            return definition_id, 'found'

    # 3. Fallbacks (Truncation, '00', NULL)
    original_sub_group = sub_group
    if original_sub_group is not None:
        # Truncation fallback
        current_sub_group = original_sub_group
        while len(current_sub_group) > 1 and current_sub_group[0] == '0':
            current_sub_group = current_sub_group[1:]
            definition_id = execute_lookup_df(main_group, current_sub_group, 'exact', 'exact')
            if definition_id:
                return definition_id, 'abnormal_truncated_leading_zero'        
        
        current_sub_group = original_sub_group
        while len(current_sub_group) > 0 and (len(current_sub_group) > 1 or current_sub_group != '0'):
            current_sub_group = current_sub_group[:-1]
            definition_id = execute_lookup_df(main_group, current_sub_group, 'exact', 'exact')
            if definition_id:
                return definition_id, 'abnormal_truncated_ending_digit'

        # '00' fallback
        definition_id = execute_lookup_df(main_group, '00', 'exact', 'exact')
        if definition_id:
            return definition_id, 'abnormal_zero'

    # NULL fallback for main_group and sub_group
    definition_id = execute_lookup_df(None, None, 'is_null', 'is_null')
    if definition_id:
        return definition_id, 'abnormal_null'

    # If none of the above found a match
    return None, 'not_found'


def get_cpc_ipc_definitions_df():
    """
    Returns the globally cached CPC/IPC definitions DataFrame.
    Ensures the cache is loaded before returning.
    """
    global _cpc_ipc_definitions_df
    if _cpc_ipc_definitions_df is None:
        load_cpc_ipc_definitions_cache()
    return _cpc_ipc_definitions_df

def set_cpc_ipc_definitions_df(df):
    """Sets the global CPC/IPC definitions DataFrame cache.
    This is primarily used by multiprocessing worker initializers."""
    global _cpc_ipc_definitions_df
    _cpc_ipc_definitions_df = df

def load_cpc_ipc_definitions_cache():
    """
    Load all CPC/IPC definitions from the database into a global pandas DataFrame cache.
    This significantly speeds up find_definition_id by avoiding repeated database queries.

    Memory optimizations:
    - Creates concatenation column for fast initial filtering
    - Converts string fields to categorical for memory efficiency
    - Uses fixed-length zero-padded strings for main_group/sub_group
    - Optimizes data types and uses uint16 for IDs
    - Converts publish_date to more efficient date format

    Returns:
        bool: True if cache loaded successfully, False otherwise
    """
    global _cpc_ipc_definitions_df

    if _cpc_ipc_definitions_df is not None:
        # logger.info("CPC/IPC definitions cache already loaded.")
        return True

    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection for loading CPC/IPC definitions cache.")
            return False

        # Only fetch the columns we need for lookups
        sql = """
            SELECT id, classification_type, section, class, subclass, main_group, sub_group
            FROM patents_cpc_ipc_definitions
            ORDER BY classification_type, section, class, subclass, main_group, sub_group
        """

        logger.info("Loading CPC/IPC definitions cache from database...")
        df = pd.read_sql_query(sql, conn)

        # Memory optimization: Calculate original memory usage
        original_memory_bytes = df.memory_usage(deep=True).sum()
        original_memory_mb = original_memory_bytes / (1024 * 1024)
        logger.info(f"Original cache memory usage: {original_memory_mb:.2f} MB ({original_memory_bytes:,} bytes)")

        # Memory optimization 1: Create concatenation column for fast lookup
        # This combines classification_type + section + class + subclass for initial filtering
        df['lookup_key'] = (df['section'].astype(str) + '_' + df['class'].astype(str) + '_' + df['subclass'].astype(str))

        # Convert lookup_key to categorical for memory efficiency
        df['lookup_key'] = pd.Categorical(df['lookup_key'])

        # Memory optimization 2: Convert string fields to categorical
        df['classification_type'] = pd.Categorical(df['classification_type'])
        df['section'] = pd.Categorical(df['section'])
        df['class'] = pd.Categorical(df['class'])
        df['subclass'] = pd.Categorical(df['subclass'])

        # Memory optimization 3: Convert main_group/sub_group to category
        df['main_group'] = df['main_group'].apply(lambda x: x if pd.isna(x) else str(x)).astype('category')
        df['sub_group'] = df['sub_group'].apply(lambda x: x if pd.isna(x) else str(x)).astype('category')



        # Calculate optimized memory usage
        optimized_memory_bytes = df.memory_usage(deep=True).sum()
        optimized_memory_mb = optimized_memory_bytes / (1024 * 1024)
        memory_reduction = ((original_memory_bytes - optimized_memory_bytes) / original_memory_bytes) * 100

        logger.info(f"Successfully loaded {len(df)} CPC/IPC definitions into cache.")
        logger.info(f"Optimized cache memory usage: {optimized_memory_mb:.2f} MB ({optimized_memory_bytes:,} bytes)")
        logger.info(f"Memory reduction: {memory_reduction:.1f}% ({(original_memory_bytes - optimized_memory_bytes):,} bytes saved)")

        # Set the global variable after successful loading
        _cpc_ipc_definitions_df = df

        return True

    except Exception as e:
        logger.error(f"Error loading CPC/IPC definitions cache: {e}", exc_info=True)
        _cpc_ipc_definitions_df = None
        return False
    finally:
        if conn:
            conn.close()