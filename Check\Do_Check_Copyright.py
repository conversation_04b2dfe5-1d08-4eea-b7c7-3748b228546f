import os
from FileManagement.Tencent_COS import async_upload_file_with_retry
from AI.GCV_GetImageParts import get_image_parts_async
from langfuse import observe
import asyncio
import time
import shutil
from Common import Constants
from Check.RAG.qdrant_search import find_similar_assets_qdrant
from Check.Create_Report import create_check_report, create_product_url
import langfuse

@observe(capture_input=False, capture_output=False)
async def check_copyrights(client, bucket, temp_dir, client_id, check_id, local_product_images, local_client_ip_images, local_reference_images, cases_df, plaintiff_df, embedding_task=None, language='zh'):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "check_id": check_id,
        "local_product_images": local_product_images,
        "local_client_ip_images": local_client_ip_images,
        "local_reference_images": local_reference_images,
        "language": language
    })
    # 1. Bounding Boxes: Get parts of product pictures
    start_time = time.time()
    parts_tasks = []
    for product_image in local_product_images:
        product_url = create_product_url(check_id, product_image)
        image_url = ["", product_url.replace(" ", "%20").replace("http:", "https:")]
        parts_tasks.append(get_image_parts_async("Individual images that might be copyrighted", product_image, image_url=image_url, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX, part_name_infix="_cw"))

    parts_results = await asyncio.gather(*parts_tasks)
    images_parts = [part for sublist in parts_results for part in sublist]
    local_images_parts_paths = [part["path"] for part in images_parts]

    # Exclude images that are less than 600 pixels or where the max(height/width, width/height) > 2.2
    # get_image_parts_async already excluded images
    filtered_image_parts = []
    for part in images_parts:
        try:
            width = int(part["width"])
            height = int(part["height"])
            if width * height >= 1000 and max(width / height, height / width) <= 2.7:
                filtered_image_parts.append(part)
        except (ValueError, KeyError) as e:
            print(f"Error processing image part: {part}. Error: {e}")
            # Decide how to handle errors.  Maybe skip this part, or log it.
            continue  # Skip to the next part

    local_images_parts_paths = [part["path"] for part in filtered_image_parts]
    print(f"\033[33mCopyright: Got parts of product images in {time.time() - start_time:.1f} seconds\033[0m")

    # 2. Check copyright infringement on TRO copyrights
    start_time = time.time()

    # Use Qdrant for vector search
    query_image_paths=local_images_parts_paths + local_client_ip_images + local_reference_images + local_product_images
    top_n = 3 if len(query_image_paths) > 10 else 4 if len(query_image_paths) > 5 else 6  # This is the number of resuls per input picture
    sim_results = await find_similar_assets_qdrant(
        query_image_paths=query_image_paths,
        check_id=check_id,
        client_id=client_id,
        ip_type="Copyright",
        temp_dir=temp_dir,
        cases_df=cases_df,
        plaintiff_df=plaintiff_df,
        plaintiff_id=None,
        top_n=top_n, # 3 per pictures => could be 50!
        similarity_threshold=0.7,
        embedding_task=embedding_task
    )

    print(f"\033[33mCopyright: Copyright RAG done for {len(local_client_ip_images+local_reference_images+local_product_images)} pictures and {len(local_images_parts_paths)} parts in {time.time() - start_time:.1f} seconds\033[0m")

    if sim_results:
        start_time = time.time()

        # Concurrently process each copyright check
        copyright_check_tasks = [create_check_report(
            ip_type="Copyright",
            check_id=check_id,
            result=result,
            client=client,
            bucket=bucket,
            model_name="gemini-2.0-flash-exp",
            language=language
        ) for result in sim_results]
        
        # Upload to COS the images parts (the full image is already uploaded in "check" function) used for these results. We start the upload now, we wait for the result after the "check_one_copyright" are done
        product_part_paths = set([result["product_local_path"] for result in sim_results if "part" in result["product_local_path"]])
        copyright_product_image_upload_task = [asyncio.create_task(
            async_upload_file_with_retry(client=client, bucket=bucket, key=f"checks/{check_id}/query/{os.path.basename(product_part_path)}", file_path=product_part_path)
        ) for product_part_path in product_part_paths]

        copyright_results = await asyncio.gather(*copyright_check_tasks)
        await asyncio.gather(*copyright_product_image_upload_task)

        # Filter out None results (where is_copyright["final_answer"] was not "yes")
        filtered_copyright_results = [r for r in copyright_results if r]

    else:
        filtered_copyright_results = []

    print(f"\033[33m ✅ Copyright: Copyright Report Analysis DONE, for {len(sim_results)} RAG results in {time.time() - start_time:.1f} seconds\033[0m")
    langfuse.get_client().update_current_span(output={"results": filtered_copyright_results})
    return filtered_copyright_results


if __name__ == '__main__':
    # This block allows the script to be run directly for testing purposes.
    import tempfile
    import sys

    # Ensure the project root is in the Python path to resolve imports correctly.
    sys.path.append(os.getcwd())

    from DatabaseManagement.ImportExport import get_table_from_GZ

    async def main():
        """Main function to run a standalone copyright check on a single local image."""
        print("--- Running Standalone Copyright Check ---")
        
        # HARDCODED: Provide a path to a local image for testing.
        test_image_path = r"D:\Win10User\Downloads\ChuanshenGuida\product_1_15949c7b-3705-4eec-90d7-9a8f54818c3e_part_0.jpg"
        test_image_path = r"D:\Win10User\Downloads\Screenshot 2025-07-20 101411.png"
        test_image_path = r"D:\Win10User\Downloads\Image_20250810203836_65.jpg"
        
        local_product_images = [test_image_path]
        
        # Create a persistent directory for output, named after the test file.
        output_dir = os.path.join(os.path.dirname(test_image_path), os.path.splitext(os.path.basename(test_image_path))[0] + "_output")
        os.makedirs(output_dir, exist_ok=True)
        print(f"Using output directory: {output_dir}")

        if not os.path.exists(test_image_path):
            print(f"Test image not found at {test_image_path}. Creating a dummy file.")
            os.makedirs(os.path.dirname(test_image_path), exist_ok=True)
            with open(test_image_path, 'w') as f:
                f.write("dummy") # Create a dummy file

        cases_df = get_table_from_GZ("tb_case", force_refresh=False)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)

        print(f"Checking file: {local_product_images[0]}")
        results = await check_copyrights(
            client=None,
            bucket=None,
            temp_dir=output_dir, # Pass the persistent output directory
            client_id="local_test",
            check_id="local_test",
            local_product_images=local_product_images,
            local_client_ip_images=[],
            local_reference_images=[],
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            embedding_task=None
        )
        print("\n--- Copyright Check Results ---")
        print(results)
        print("--- End of Report ---")

    asyncio.run(main())