# from langfuse import Lang<PERSON>
import pandas as pd
import os
import json
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.Langfuse.Langfuse import get_langfuse_client
import asyncio
import tempfile
from Check.Do_Check_Download import download_from_url
from langfuse import observe
import langfuse
from AI.LLM_shared import get_json



########################### 
##### Compare prompts #####
###########################

@observe()
async def run_prompt_on_item_observe(experiment_name, prompt_name, prompt_version, dataset_name, prompt_list, image_url):

    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url, model_name="gemini-2.0-flash-exp", useVertexAI=True)
    
    langfuse.get_client().update_current_span(
        name=f"Experiment={experiment_name}",
        session_id=f"Prompt={prompt_name}, Version={prompt_version}, Dataset={dataset_name}",
        input=prompt_list
    )

    return get_json(ai_answer)

async def run_prompt_on_item(experiment_name, prompt_name, prompt_version, dataset_name, prompt, item, temp_dir, langfuse):
        
    # Rebuild the prompt list
    # The documentation recommends using prompt.compile(images=item.input["images"]) which replaces {{images}} in the prompt with item.input["images"]
    # But for me, I need to extract the images, and \n is most easy way. So I dont want to mix prompt with images, because prompt has many \n in it.
    prompt_list = [("text", prompt.prompt.replace("{{images}}", ""))] # get the compiled prompt
    image_url = [""]
    for line in item.input["images"].split("\n"):
        if line.startswith("![Alt text](http"):
            image_path_online = line.replace("![Alt text](", "").replace(")", "")
            image_path_local = f"{temp_dir}/{os.path.basename(image_path_online)}"
            await download_from_url(image_path_online, image_path_local)
            prompt_list.append(("image_path", image_path_local))
            image_url.append(image_path_online)
        else:
            if len(prompt_list) > 0 and prompt_list[-1][0] == "text":
                prompt_list[-1] = ("text", prompt_list[-1][1] + f"{line}\n")
            else:
                prompt_list.append(("text", f"{line}\n"))
                image_url.append("")

    # Use item.observe() to link the dataset item and get the observation
    with item.observe(run_name=experiment_name) as trace_id:

        ai_answer = await run_prompt_on_item_observe(experiment_name, prompt_name, prompt_version, dataset_name, prompt_list, image_url)

        langfuse.score(  # Use the observation object directly
            trace_id=trace_id,
            name="exact_match",
            value=simple_evaluation(ai_answer, item.expected_output)
        )
    
    return ai_answer



async def run_prompt_experiment(experiment_name, prompt_name, prompt_version, dataset_name, model_name="gemini-2.0-flash-exp"):
    """
    Runs an experiment on the a dataset.

    Args:
        experiment_name (str): The new (or existing) name of the experiment run. This will show up in the Langfuse UI.
        prompt_name (str): As setup in Langfuse.
        prompt_version (int): As setup in Langfuse.
        dataset_name (str): As setup in Langfuse, need to create a dataset and put items in it.
    """

    langfuse = get_langfuse_client() # get the client
    dataset = langfuse.get_dataset(dataset_name)  # Get the dataset
    prompt = langfuse.get_prompt(prompt_name, version=prompt_version)

    tasks = []

    with tempfile.TemporaryDirectory() as temp_dir:
        for item in dataset.items:
            tasks.append(run_prompt_on_item(experiment_name, prompt_name, prompt_version, dataset_name, prompt, item, temp_dir, langfuse))
        
        ai_answers = await asyncio.gather(*tasks)

    langfuse.get_client().flush()
    langfuse.flush()





########################### 
##### Compare models #####
###########################

@observe()
async def run_model_on_item_observe(experiment_name, model_name, dataset_name, prompt_list, image_url):

    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url, model_name=model_name, useVertexAI=True)
    
    langfuse.get_client().update_current_span(
        name=f"Experiment={experiment_name}",
        session_id=f"Model={model_name}, Dataset={dataset_name}",
        input=prompt_list
    )

    return ai_answer



async def run_model_on_item(experiment_name, dataset_name, model_name, item, temp_dir, langfuse):
        
    # Rebuild the prompt list
    # The documentation recommends using prompt.compile(images=item.input["images"]) which replaces {{images}} in the prompt with item.input["images"]
    # But for me, I need to extract the images, and \n is most easy way. So I dont want to mix prompt with images, because prompt has many \n in it.
    prompt_list = [("text", item.input)] # get the compiled prompt
    image_url = [""]

    # Use item.observe() to link the dataset item and get the observation
    with item.observe(run_name=experiment_name) as trace_id:
        ai_answer = await run_model_on_item_observe(experiment_name, model_name, dataset_name, prompt_list, image_url)

        langfuse.score(  # Use the observation object directly
            trace_id=trace_id,
            name="length",
            value=len(ai_answer)
        )
    return ai_answer


async def run_model_experiment(experiment_name, dataset_name, model_name):
    langfuse = get_langfuse_client() # get the client
    dataset = langfuse.get_dataset(dataset_name)  # Get the dataset
    # prompt = langfuse.get_prompt(prompt_name, version=prompt_version)

    tasks = []

    with tempfile.TemporaryDirectory() as temp_dir:
        for item in dataset.items:
            tasks.append(run_model_on_item(experiment_name, dataset_name, model_name, item, temp_dir, langfuse))
        
        ai_answers = await asyncio.gather(*tasks)

    langfuse.get_client().flush()
    langfuse.flush()



#########################



def simple_evaluation(output, expected_output):
    if isinstance(output, str):
        output = json.loads(output)
    
    if isinstance(expected_output, str):
        expected_output = json.loads(expected_output)

    return output == expected_output



if __name__ == "__main__":
    print("hi")
    # Prompt experiment
    # asyncio.run(run_experiment("Report Prompt v3", "Copyright Report", 3, "Copyright Sample"))
    # asyncio.run(run_experiment("Report Prompt v4", "Copyright Report", 4, "Copyright Sample"))

    # Model experiment
    import Common.Constants as Constants
    asyncio.run(run_model_experiment("Translation Pro", "Translation", model_name=Constants.SMART_MODEL_FREE))
    asyncio.run(run_model_experiment("Translation Flash", "Translation", model_name="gemini-2.0-flash-exp"))