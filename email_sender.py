import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import sqlite3
from logdata import db_path, log_message

def send_email_report(run_id):
    # Fetch logs from the database
    print(f"Fetching logs from database for run ID: {run_id}")
    logs = fetch_logs_from_db(run_id)
    print(f"Logs fetched: {logs}")

    # Compose email
    msg = MIMEMultipart()
    msg['From'] = '<EMAIL>'
    msg['To'] = '<EMAIL>'
    msg['Subject'] = 'Daily Log Report'

    # Create plain text version
    # text_body = format_logs_as_text(logs)
    # msg.attach(MIMEText(text_body, 'plain'))

    # Create HTML version
    html_body = format_logs_as_html(logs)  # New function needed
    msg.attach(MIMEText(html_body, 'html'))

    # Send email via SMTP server
    with smtplib.SMTP('smtp.email.us-ashburn-1.oci.oraclecloud.com', 587) as server:
        server.starttls()
        server.login(os.getenv('OCI_EMAIL_USERNAME'), os.getenv('OCI_EMAIL_PASSWORD'))
        server.send_message(msg)

    print("Email sent successfully!")
    
def send_email(target_email, title, content):

    # Compose email
    msg = MIMEMultipart()
    msg['From'] = '<EMAIL>'
    msg['To'] = target_email
    msg['Subject'] = title

    msg.attach(MIMEText(content, 'plain'))

    # Send email via SMTP server
    with smtplib.SMTP('smtp.email.us-ashburn-1.oci.oraclecloud.com', 587) as server:
        server.starttls()
        server.login(os.getenv('OCI_EMAIL_USERNAME'), os.getenv('OCI_EMAIL_PASSWORD'))
        server.send_message(msg)

    print("Email sent successfully!")


def fetch_logs_from_db(run_id):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    
    # Get run information
    c.execute('''SELECT start_time, end_time, status 
                 FROM runs WHERE id=?''', (run_id,))
    run_info = c.fetchone()
    
    # Get all steps and their logs
    c.execute('''SELECT s.name, s.status, s.start_time, s.end_time, 
                        GROUP_CONCAT(l.timestamp || ' [' || l.level || '] ' || l.message, '\n')
                 FROM steps s
                 LEFT JOIN logs l ON s.id = l.step_id
                 WHERE s.run_id = ?
                 GROUP BY s.id
                 ORDER BY s.id''', (run_id,))
    steps_logs = c.fetchall()
    
    conn.close()
    return run_info, steps_logs

def format_logs_as_text(logs):
    run_info, steps_logs = logs
    start_time, end_time, status = run_info
    
    # Format timestamps for run info
    start_time = start_time.replace('T', ' ').split('.')[0]
    end_time = end_time.replace('T', ' ').split('.')[0] if end_time else 'Still running'
    
    # Format the email body
    body = [
        f"Run Status: {status}",
        f"Started: {start_time}",
        f"Ended: {end_time}\n",
        "Step Details:",
        "-------------"
    ]
    
    for step_name, step_status, step_start, step_end, step_logs in steps_logs:
        body.append(f"\n**{step_name}**")
        body.append(f"Status: {step_status}")
        if step_start:
            step_start = step_start.replace('T', ' ').split('.')[0]
            body.append(f"Started: {step_start}")
        if step_end:
            step_end = step_end.replace('T', ' ').split('.')[0]
            body.append(f"Ended: {step_end}")
        
        if step_logs:
            body.append("\nLogs:")
            cleaned_logs = []
            for log_line in step_logs.split('\n'):
                if log_line.strip():
                    # Extract timestamp and message
                    parts = log_line.split('] ')
                    if len(parts) >= 1:
                        # Find the last timestamp in the message
                        message = parts[-1]
                        # Look for time format HH:MM:SS
                        import re
                        time_match = re.search(r'\d{2}:\d{2}:\d{2}', message)
                        if time_match:
                            time_str = time_match.group()
                            # Clean up the message by removing any timestamp prefixes
                            message = re.sub(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}([.]\d+)?: ?', '', message)
                            cleaned_logs.append(f"{time_str}     {message}")
                        else:
                            # If no timestamp found, use the step start time
                            time_str = step_start.split(' ')[1]
                            cleaned_logs.append(f"{time_str}   {message}")
            
            body.append('\n'.join(cleaned_logs))
        
        body.append("-" * 50)
    
    return "\n".join(body)


def format_logs_as_html(logs):
    run_info, steps_logs = logs
    start_time, end_time, status = run_info
    
    # Format timestamps for run info
    start_time = start_time.replace('T', ' ').split('.')[0]
    end_time = end_time.replace('T', ' ').split('.')[0] if end_time else 'Still running'
    
    # Format the email body with HTML
    body = [
        '<html><body>',
        f'<p><strong>Run Status:</strong> {status}</p>',
        f'<p><strong>Started:</strong> {start_time}</p>',
        f'<p><strong>Ended:</strong> {end_time}</p>',
        '<h2>Step Details:</h2>',
        '<hr/>'
    ]
    
    i = 1
    for step_name, step_status, step_start, step_end, step_logs in steps_logs:
        body.append(f'<h3>{i} - {step_name}</h3>')
        body.append(f'<p><strong>Status:</strong> {step_status}')
        if step_start:
            step_start = step_start.replace('T', ' ').split('.')[0]
            body.append(f'<br><strong>Started:</strong> {step_start}')
        if step_end:
            step_end = step_end.replace('T', ' ').split('.')[0]
            body.append(f'<br><strong>Ended:</strong> {step_end}')
        body.append('</p>')
        
        if step_logs:
            body.append('<p style="margin-bottom: 0;"><strong>Logs:</strong></p>')
            body.append('<pre style="font-family: monospace; margin-top: 0;">')
            cleaned_logs = []
            for log_line in step_logs.split('\n'):
                if log_line.strip():
                    parts = log_line.split('] ')
                    if len(parts) >= 1:
                        message = parts[-1]
                        import re
                        time_match = re.search(r'\d{2}:\d{2}:\d{2}', message)
                        if time_match:
                            time_str = time_match.group()
                            message = re.sub(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}([.]\d+)?: ?', '', message)
                            cleaned_logs.append(f'{time_str}   {message}')
                        else:
                            time_str = step_start.split(' ')[1]
                            cleaned_logs.append(f'{time_str}   {message}')
            body.append('\n'.join(cleaned_logs))
            body.append('</pre>')
        
        body.append('<hr/>')
        body.append('<hr/>')
        i += 1
    
    body.append('</body></html>')
    return '\n'.join(body)

if __name__ == "__main__":    
    send_email_report(88)
    
    
    # Connect to database and get a random run ID
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    
    # Get the most recent completed run
    c.execute('''
        SELECT id 
        FROM runs 
        WHERE status = 'Completed'
        ORDER BY RANDOM()
        LIMIT 1
    ''')
    
    result = c.fetchone()
    conn.close()
    
    if result:
        run_id = result[0]
        print(f"Sending email report for run ID: {run_id}")
        send_email_report(run_id)
        print("Email sent successfully!")
    else:
        print("No completed runs found in the database")

