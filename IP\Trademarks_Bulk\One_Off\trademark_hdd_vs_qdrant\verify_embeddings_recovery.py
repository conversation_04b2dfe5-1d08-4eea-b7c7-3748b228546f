#!/usr/bin/env python3
"""
Verify Embeddings Recovery Tool

This script reads a CSV file with serial numbers and verifies the status of each record across:
1. Database (trademarks table)
2. HDD (image files)
3. Qdrant (embeddings)

Output format is a clean terminal summary and table.
Includes separate tabular output for records with found Qdrant embeddings.
"""

import os
import sys
import csv
import time
from tqdm import tqdm
from dotenv import load_dotenv
from qdrant_client import QdrantClient

# Import database functions (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from trademark_db import get_db_connection

# Import image utility functions (reusing existing code)
from trademark_image import get_image_subdirectory

# Import Common constants and UUID utilities (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from Common.Constants import local_ip_folder

# Import UUID generation function (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..','..', '..', '..', 'Qdrant', 'api', 'Common'))
from Common.uuid_utils import generate_uuid

# Load environment variables
load_dotenv()

# Configuration
BASE_DIR = os.path.join(local_ip_folder, "Trademarks")
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")

# Qdrant configuration
QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = "IP_Assets"

# Default CSV file path
DEFAULT_CSV_PATH = os.path.join(os.path.dirname(__file__), 'missing_embeddings_results', 'missing_embeddings_20250724_200507.csv')


def read_csv_serial_numbers(csv_path):
    """
    Read serial numbers from CSV file.
    
    Args:
        csv_path (str): Path to the CSV file
        
    Returns:
        list: List of serial numbers
    """
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        return []
    
    serial_numbers = []
    try:
        with open(csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                serial_numbers.append(row['ser_no'])
        
        print(f"✅ Successfully read {len(serial_numbers)} serial numbers from CSV")
        return serial_numbers
        
    except Exception as e:
        print(f"❌ Error reading CSV file: {str(e)}")
        return []


def get_database_details(ser_no):
    """
    Get database record for serial number.
    Reuses logic from lookup_serial_number.py
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT ser_no, reg_no, mark_feature_code, image_source, mark_text
            FROM trademarks 
            WHERE ser_no = %s
        """, (ser_no,))
        
        result = cursor.fetchone()
        if result:
            return {
                'ser_no': result[0],
                'reg_no': result[1],
                'mark_feature_code': result[2],
                'image_source': result[3],
                'mark_text': result[4]
            }
        else:
            return None
            
    except Exception:
        return None
    finally:
        if conn:
            conn.close()


def get_hdd_details(ser_no):
    """
    Check if image file exists on HDD.
    Reuses logic from lookup_serial_number.py
    """
    try:
        image_sub_dir = get_image_subdirectory(ser_no)
        if not image_sub_dir:
            return {'exists': False}
            
        image_path = os.path.join(IMAGES_DIR, image_sub_dir, f"{ser_no}.webp")
        exists = os.path.exists(image_path)
        
        return {'exists': exists, 'path': image_path}
        
    except Exception:
        return {'exists': False}


def get_qdrant_details(ser_no):
    """
    Check if embedding exists in Qdrant.
    Reuses logic from lookup_serial_number.py
    """
    try:
        client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=30)
        point_id = generate_uuid(ser_no)
        
        # Try to retrieve the point
        points = client.retrieve(
            collection_name=COLLECTION_NAME,
            ids=[point_id],
            with_payload=False,
            with_vectors=False
        )
        
        return {'exists': points and len(points) > 0}
        
    except Exception:
        return {'exists': False}


def verify_serial_numbers(serial_numbers):
    """
    Verify status of serial numbers across database, HDD, and Qdrant.
    Reuses logic from lookup_serial_number.py with progress bar.
    """
    results = []
    
    print(f"\n🔍 Verifying {len(serial_numbers)} serial numbers...")
    
    # Use tqdm for progress bar
    for ser_no in tqdm(serial_numbers, desc="Processing records", unit="record"):
        # Get data for this serial number
        db_info = get_database_details(ser_no)
        hdd_info = get_hdd_details(ser_no)
        qdrant_info = get_qdrant_details(ser_no)
        
        # Determine statuses
        db_status = "✅ Found" if db_info else "❌ Missing"
        hdd_status = "✅ Found" if hdd_info['exists'] else "❌ Missing"
        qdrant_status = "✅ Found" if qdrant_info['exists'] else "❌ Missing"
        
        # Determine pipeline status
        if db_info and hdd_info['exists'] and qdrant_info['exists']:
            pipeline_status = "✅ COMPLETE"
        elif db_info and hdd_info['exists'] and not qdrant_info['exists']:
            pipeline_status = "⚠️ NEEDS EMBEDDING"
        elif db_info and not hdd_info['exists'] and not qdrant_info['exists']:
            pipeline_status = "⚠️ NEEDS RECOVERY"
        elif not db_info:
            pipeline_status = "❌ NOT IN DB"
        else:
            pipeline_status = "❌ INCOMPLETE"
        
        results.append({
            'ser_no': ser_no,
            'db_status': db_status,
            'hdd_status': hdd_status,
            'qdrant_status': qdrant_status,
            'pipeline_status': pipeline_status,
            'db_info': db_info,
            'hdd_info': hdd_info,
            'qdrant_info': qdrant_info
        })
    
    return results


def print_table_summary(results):
    """
    Print results in a table format similar to lookup_serial_number.py
    """
    if not results:
        print("\n❌ No results to display")
        return

    # Table headers
    headers = ["Serial Number", "Database", "HDD", "Qdrant", "Pipeline Status"]

    # Calculate column widths
    col_widths = [max(len(str(header)), 12) for header in headers]
    for result in results:
        col_widths[0] = max(col_widths[0], len(result['ser_no']))
        col_widths[1] = max(col_widths[1], 8)  # "✅ Found" or "❌ Missing"
        col_widths[2] = max(col_widths[2], 8)
        col_widths[3] = max(col_widths[3], 8)
        col_widths[4] = max(col_widths[4], len(result['pipeline_status']))

    # Print table
    print("\n" + "=" * (sum(col_widths) + len(headers) * 3 + 1))
    print(f"📊 CSV RECORDS VERIFICATION SUMMARY TABLE")
    print("=" * (sum(col_widths) + len(headers) * 3 + 1))

    # Print headers
    header_row = "| "
    for i, header in enumerate(headers):
        header_row += f"{header:<{col_widths[i]}} | "
    print(header_row)

    # Print separator
    separator = "|-"
    for width in col_widths:
        separator += f"{'-'*width}-|-"
    print(separator[:-1])

    # Print data rows (limit to first 20 for readability)
    display_count = min(len(results), 20)
    for i, result in enumerate(results[:display_count]):
        row = f"| {result['ser_no']:<{col_widths[0]}} | "
        row += f"{result['db_status']:<{col_widths[1]}} | "
        row += f"{result['hdd_status']:<{col_widths[2]}} | "
        row += f"{result['qdrant_status']:<{col_widths[3]}} | "
        row += f"{result['pipeline_status']:<{col_widths[4]}} |"
        print(row)

    if len(results) > 20:
        print(f"| ... and {len(results) - 20} more records")

    print(f"{'=' * (sum(col_widths) + len(headers)*3 + 1)}")


def print_found_qdrant_embeddings_table(results):
    """
    Print separate table for records with found Qdrant embeddings.
    """
    # Filter records with found Qdrant embeddings
    found_embeddings = [r for r in results if r['qdrant_status'] == "✅ Found"]
    
    if not found_embeddings:
        # print(f"\n📊 FOUND QDRANT EMBEDDINGS: None")
        return
    
    print(f"\n📊 FOUND QDRANT EMBEDDINGS TABLE ({len(found_embeddings)} records)")
    print("=" * 80)
    
    # Table headers
    headers = ["Serial Number", "Database", "HDD", "Qdrant", "Pipeline Status"]
    
    # Calculate column widths
    col_widths = [max(len(str(header)), 12) for header in headers]
    for result in found_embeddings:
        col_widths[0] = max(col_widths[0], len(result['ser_no']))
        col_widths[1] = max(col_widths[1], 8)
        col_widths[2] = max(col_widths[2], 8)
        col_widths[3] = max(col_widths[3], 8)
        col_widths[4] = max(col_widths[4], len(result['pipeline_status']))
    
    # Print headers
    header_row = "| "
    for i, header in enumerate(headers):
        header_row += f"{header:<{col_widths[i]}} | "
    print(header_row)
    
    # Print separator
    separator = "|-"
    for width in col_widths:
        separator += f"{'-'*width}-|-"
    print(separator[:-1])
    
    # Print all found embedding records
    for result in found_embeddings:
        row = f"| {result['ser_no']:<{col_widths[0]}} | "
        row += f"{result['db_status']:<{col_widths[1]}} | "
        row += f"{result['hdd_status']:<{col_widths[2]}} | "
        row += f"{result['qdrant_status']:<{col_widths[3]}} | "
        row += f"{result['pipeline_status']:<{col_widths[4]}} |"
        print(row)
    
    print("=" * 80)


def print_summary_statistics(results):
    """
    Print summary statistics similar to lookup_serial_number.py
    """
    print(f"\n{'='*80}")
    print(f"📊 CSV RECORDS VERIFICATION SUMMARY")
    print(f"{'='*80}")

    total_records = len(results)

    # Count statuses
    db_found = sum(1 for r in results if r['db_status'] == "✅ Found")
    hdd_found = sum(1 for r in results if r['hdd_status'] == "✅ Found")
    qdrant_found = sum(1 for r in results if r['qdrant_status'] == "✅ Found")

    print(f"\n📋 COMPONENT STATUS:")
    print(f"   Database records found: {db_found:,} / {total_records:,} ({(db_found/total_records*100):.1f}%)")
    print(f"   HDD image files found: {hdd_found:,} / {total_records:,} ({(hdd_found/total_records*100):.1f}%)")
    print(f"   Qdrant embeddings found: {qdrant_found:,} / {total_records:,}")
    if qdrant_found > 0:
        print(f"   Qdrant embeddings percentage: {(qdrant_found/total_records*100):.1f}%")

    # Count pipeline statuses
    pipeline_counts = {}
    for result in results:
        status = result['pipeline_status']
        pipeline_counts[status] = pipeline_counts.get(status, 0) + 1

    print(f"\n⚠️ PIPELINE STATUS BREAKDOWN:")
    for status in ["✅ COMPLETE", "⚠️ NEEDS EMBEDDING", "⚠️ NEEDS RECOVERY", "❌ NOT IN DB", "❌ INCOMPLETE"]:
        count = pipeline_counts.get(status, 0)
        if count > 0:
            percentage = (count / total_records) * 100
            print(f"   {status}: {count:,} ({percentage:.1f}%)")


def main(csv_path=None):
    """
    Main function to verify CSV records.

    Args:
        csv_path (str): Path to CSV file (optional, uses default if not provided)
    """
    # Use provided path or default
    if not csv_path:
        csv_path = DEFAULT_CSV_PATH

    print("🔍 Starting CSV Records Verification")
    print(f"📄 CSV file: {csv_path}")
    print("=" * 50)

    # Step 1: Read CSV file
    serial_numbers = read_csv_serial_numbers(csv_path)
    if not serial_numbers:
        print("❌ No serial numbers to verify")
        return

    # Step 2: Verify records
    start_time = time.time()
    results = verify_serial_numbers(serial_numbers)
    verification_time = time.time() - start_time

    # Step 3: Print summary statistics
    print_summary_statistics(results)

    # Step 4: Print found Qdrant embeddings table (if any)
    # print_found_qdrant_embeddings_table(results)

    # Step 5: Print main table summary
    print_table_summary(results)

    # Final summary
    print(f"\n✅ Verification completed in {verification_time:.1f} seconds")


if __name__ == "__main__":
    # Configuration parameters
    # CSV_PATH = None  # Set to specific path if needed, otherwise uses default
    CSV_PATH = "IP/Trademarks_Bulk/One_Off/trademark_hdd_vs_qdrant/missing_embeddings_results/missing_embeddings_20250729_163852.csv"

    # Run the main function
    main(csv_path=CSV_PATH)
