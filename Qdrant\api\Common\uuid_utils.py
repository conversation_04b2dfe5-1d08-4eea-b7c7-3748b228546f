import uuid
import hashlib
import os


def generate_uuid(ser_no):
    """Generate consistent UUID from ser_no (TM) or rego_no (CP) or filename (PT) using UUID version 5 (SHA-1 hash)"""
    if not isinstance(ser_no, str):
        ser_no = str(ser_no)
    # Use uuid.uuid5 to generate a UUID from a namespace UUID and a name (ser_no)
    # uuid.NAMESPACE_OID is a predefined namespace UUID.
    # This ensures consistency: the same ser_no will always produce the same UUID.
    return str(uuid.uuid5(uuid.NAMESPACE_OID, ser_no))


def generate_obfuscated_key(reg_no: str) -> str:
    """
    Generates an obfuscated key for an image filename using SHA256.
    
    Args:
        reg_no (str): The registration number of the IP asset.
        secret (bytes): A secret byte string used to salt the hash.
                        Defaults to _IMAGE_SECRET.
                        
    Returns:
        str: The SHA256 hexdigest of the combined reg_no and secret.
    """
    key_raw = reg_no.encode('utf-8') + os.getenv("IMAGE_SECRET").encode("utf-8")
    return hashlib.sha256(key_raw).hexdigest()


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    print(generate_uuid("87323957"))
    print(os.getenv("IMAGE_SECRET"))
    print(generate_obfuscated_key("D1002360"))
