# Can upload file to OneDrive of any user
# Can upload file to SharePoint of any site
# Can create direct download link for any file on OneDrive or SharePoint

import msal
import requests
import os
import time, sys, os, asyncio
import tempfile
sys.path.append(os.getcwd())
from Common.Constants import nas_case_folder
from FileManagement.NAS import NASConnection
from Check.Do_Check_Download import download_from_url
from FileManagement.GC_COS import upload_to_gcs, download_from_gcs, delete_from_gcs
from FileManagement.Tencent_COS import get_cos_client, download_from_cos
from qcloud_cos import CosServiceError

# Configuration (Replace with your actual values)
CLIENT_ID = "7e6b75a8-20d4-4817-ab79-79bbe2d11325"
CLIENT_SECRET = "****************************************"  # Or use a certificate
TENANT_ID = "7fa645ad-bbf4-43da-b9fe-69eec9ee44a7"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPES = ["https://graph.microsoft.com/.default"]  # Important: Use /.default for client credentials
GRAPH_API_ENDPOINT = "https://graph.microsoft.com/v1.0"

def get_access_token():
    """Acquires an access token using the client credentials flow."""
    app = msal.ConfidentialClientApplication(
        CLIENT_ID, authority=AUTHORITY, client_credential=CLIENT_SECRET
    )  # Or use client_credential=certificate_thumbprint if using a certificate

    token_result = app.acquire_token_for_client(scopes=SCOPES)

    if "access_token" in token_result:
        return token_result["access_token"]
    else:
        print(token_result.get("error"))
        print(token_result.get("error_description"))
        return None
    

def upload_file_to_onedrive_user(access_token, img_path, user_principal_name="<EMAIL>", onedrive_folder="/"):
    """Uploads a file to a specific user's OneDrive."""
    filename = os.path.basename(img_path)
    upload_url = f"{GRAPH_API_ENDPOINT}/users/{user_principal_name}/drive/root:{onedrive_folder}/{filename}:/content"

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/octet-stream",
    }

    with open(img_path, "rb") as f:
        response = requests.put(upload_url, headers=headers, data=f)    

    if response.status_code == 201 or response.status_code == 200:
        file_id = response.json()["id"]
        print(f"File uploaded successfully. File ID: {file_id}")
        return file_id
    else:
        print(f"Error uploading file: {response.status_code} - {response.text}")
        return None


def get_onedrive_direct_file_link(access_token, user_principal_name, file_path):

    # 1. Get the file's metadata
    file_metadata_url = (
        f"{GRAPH_API_ENDPOINT}/users/{user_principal_name}/drive/root:{file_path}"
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
    }
    response = requests.get(file_metadata_url, headers=headers)

    if response.status_code != 200:
        print(f"Error getting file metadata: {response.status_code} - {response.text}")
        return None

    file_metadata = response.json()

    # 2. Extract the server-relative URL of the file
    server_relative_url = file_metadata["webUrl"]

    # 3. Construct the direct image URL
    direct_image_url = f"{server_relative_url}?web=1"

    return direct_image_url


def upload_file_to_sharepoint(access_token, site_id, file_path, sharepoint_folder="/"):
    """
    Uploads a file to a SharePoint site.

    Args:
        access_token: Your Microsoft Graph API access token.
        site_id: The ID of your SharePoint site.
        file_path: The local path to the file you want to upload.
        sharepoint_folder: The folder in SharePoint where you want to upload the file.
                           Defaults to "/Shared Documents".
    """
    filename = os.path.basename(file_path)
    upload_url = f"{GRAPH_API_ENDPOINT}/sites/{site_id}/drive/root:{sharepoint_folder}/{filename}:/content"

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/octet-stream",
    }

    with open(file_path, "rb") as f:
        response = requests.put(upload_url, headers=headers, data=f)

    if response.status_code == 201 or response.status_code == 200:
        file_data = response.json()
        file_id = file_data["id"]
        print(f"File uploaded successfully to SharePoint. File ID: {file_id}")
        return file_id
    else:
        print(f"Error uploading file to SharePoint: {response.status_code} - {response.text}")
        return None


def get_sharepoint_direct_file_link(access_token, site_id, file_path):
    """
    Gets a direct image URL from SharePoint.

    Args:
        access_token: Your Microsoft Graph API access token.
        site_id: The ID of your SharePoint site.
        file_path: The server-relative path to the file (e.g., "/Shared Documents/image.jpg").
    """

    # 1. Get the file's metadata
    file_metadata_url = (
        f"{GRAPH_API_ENDPOINT}/sites/{site_id}/drive/root:{file_path}"
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
    }
    response = requests.get(file_metadata_url, headers=headers)

    if response.status_code != 200:
        print(f"Error getting file metadata: {response.status_code} - {response.text}")
        return None

    file_metadata = response.json()

    # 2. Extract the server-relative URL of the file
    server_relative_url = file_metadata["webUrl"]

    # 3. Construct the direct image URL
    direct_image_url = f"{server_relative_url}?web=1"

    return direct_image_url


def compare_speed_nas_vs_onedrive_vs_S3():
    # Conclusion: 2mb file: 8 sec for Ondrive, 8 sec for GCS, 19 sec for NAS, 12 sec for Tencent (from China with US VPN)
    test_file = "D:/Documents/Programing/TRO/USside/Documents/Case Files/2025-03-14 - 1_25-cv-02710/1/IN_DC_1_25-cv-02710_2025-03-14_1_ 3_Ex_3.pdf"
    test_file_basename = os.path.basename(test_file)
    temp_dir = tempfile.gettempdir()
    onedrive_download_path = os.path.join(temp_dir, "onedrive_download.pdf")
    nas_download_path = os.path.join(temp_dir, "nas_download.pdf")
    gcs_download_path = os.path.join(temp_dir, "gcs_download.pdf")
    cos_download_path = os.path.join(temp_dir, "cos_download.pdf")

    onedrive_folder = "/MyImages"
    onedrive_user = "<EMAIL>"
    onedrive_remote_path = f"{onedrive_folder}/{test_file_basename}"

    nas_remote_path = f"{nas_case_folder}/{test_file_basename}"

    gcs_bucket_name = "trodata_bucket"
    gcs_blob_name = f"speed_tests/{test_file_basename}"

    cos_key = f"speed_tests/{test_file_basename}"

    print(f"--- Starting Speed Comparison using file: {test_file} ---")
    print(f"--- File Size: {os.path.getsize(test_file) / (1024*1024):.2f} MB ---")

    print("\n--- Testing OneDrive ---")
    onedrive_total_start_time = time.time()
    access_token = get_access_token()
    print(f"OneDrive Access token time: {time.time() - onedrive_total_start_time:.2f} seconds")

    start_time_upload = time.time()
    test_file_id = upload_file_to_onedrive_user(access_token, test_file, onedrive_user, onedrive_folder)
    if test_file_id:
        print(f"OneDrive Upload time: {time.time() - start_time_upload:.2f} seconds")

        start_time_download = time.time()
        direct_image_url = get_onedrive_direct_file_link(access_token, onedrive_user, onedrive_remote_path)
        if direct_image_url:
            print(f"OneDrive Get link time: {time.time() - start_time_download:.2f} seconds")
            start_time_actual_download = time.time()
            try:
                asyncio.run(download_from_url(direct_image_url, onedrive_download_path))
                print(f"OneDrive Download time: {time.time() - start_time_actual_download:.2f} seconds")
            except Exception as e:
                 print(f"OneDrive Download failed: {e}")
        else:
            print("OneDrive Failed to get download link.")
    else:
        print("OneDrive Upload failed.")
    print(f"Total Time taken OneDrive: {time.time() - onedrive_total_start_time:.2f} seconds")

    print("\n--- Testing NAS ---")
    nas_total_start_time = time.time()
    try:
        with NASConnection() as nas:
            print(f"NAS Connection time: {time.time() - nas_total_start_time:.2f} seconds")

            start_time_upload = time.time()
            nas.transfer_file_with_scp(test_file, nas_remote_path, to_nas=True)
            print(f"NAS Upload time: {time.time() - start_time_upload:.2f} seconds")

            start_time_download = time.time()
            nas.transfer_file_with_scp(nas_download_path, nas_remote_path, to_nas=False)
            print(f"NAS Download time: {time.time() - start_time_download:.2f} seconds")

    except Exception as e:
        print(f"NAS Test failed: {e}")
    print(f"Total Time taken NAS: {time.time() - nas_total_start_time:.2f} seconds")

    print("\n--- Testing Google Cloud Storage (GCS) ---")
    gcs_total_start_time = time.time()
    gcs_upload_url = None
    try:
        start_time_upload = time.time()
        gcs_upload_url = upload_to_gcs(test_file, gcs_bucket_name, gcs_blob_name)
        if gcs_upload_url:
            print(f"GCS Upload time: {time.time() - start_time_upload:.2f} seconds")

            start_time_download = time.time()
            download_success = download_from_gcs(gcs_bucket_name, gcs_blob_name, gcs_download_path)
            if download_success:
                print(f"GCS Download time: {time.time() - start_time_download:.2f} seconds")
            else:
                print("GCS Download failed.")
        else:
            print("GCS Upload failed.")

    except Exception as e:
        print(f"GCS Test failed: {e}")
    finally:
        if gcs_upload_url:
             try:
                 start_time_delete = time.time()
                 delete_from_gcs(gcs_bucket_name, gcs_blob_name)
                 print(f"GCS Delete time: {time.time() - start_time_delete:.2f} seconds")
             except Exception as e:
                 print(f"GCS Delete failed for {gcs_blob_name}: {e}")
        print(f"Total Time taken GCS: {time.time() - gcs_total_start_time:.2f} seconds")


    print("\n--- Testing Tencent Cloud Object Storage (COS) ---")
    cos_total_start_time = time.time()
    cos_client, cos_bucket = None, None
    uploaded_to_cos = False
    try:
        start_time_client = time.time()
        cos_client, cos_bucket = get_cos_client()
        print(f"COS Get client time: {time.time() - start_time_client:.2f} seconds")

        start_time_upload = time.time()
        try:
            response = cos_client.upload_file(
                Bucket=cos_bucket,
                LocalFilePath=test_file,
                Key=cos_key,
                EnableMD5=False,
                MAXThread=10
            )
            uploaded_to_cos = True
            print(f"COS Upload time: {time.time() - start_time_upload:.2f} seconds")
        except Exception as e:
            print(f"COS Upload failed: {e}")

        if uploaded_to_cos:
            start_time_download = time.time()
            download_success = download_from_cos(cos_client, cos_bucket, cos_key, cos_download_path)
            if download_success:
                print(f"COS Download time: {time.time() - start_time_download:.2f} seconds")
            else:
                print("COS Download failed.")

    except Exception as e:
        print(f"COS Test failed: {e}")

    finally:
        if uploaded_to_cos and cos_client and cos_bucket:
             try:
                 start_time_delete = time.time()
                 cos_client.delete_object(Bucket=cos_bucket, Key=cos_key)
                 print(f"COS Delete time: {time.time() - start_time_delete:.2f} seconds")
             except CosServiceError as e:
                 print(f"COS Delete failed for {cos_key}: {e.get_error_code()} - {e.get_error_msg()}")
             except Exception as e:
                 print(f"COS Delete failed for {cos_key}: {e}")
        print(f"Total Time taken COS: {time.time() - cos_total_start_time:.2f} seconds")


    print("\n--- Cleaning up temporary downloaded files ---")
    for f_path in [onedrive_download_path, nas_download_path, gcs_download_path, cos_download_path]:
        try:
            if os.path.exists(f_path):
                os.remove(f_path)
        except Exception as e:
            print(f"Failed to remove temp file {f_path}: {e}")

    print("\n--- Speed Comparison Complete ---")



if __name__ == "__main__":
    compare_speed_nas_vs_onedrive_vs_S3()
    
    # img_path = "D:/Win10User/Downloads/313dff300cbe725038eaaa7788413ed66ea9139385690b40bcf0961743c8bc9a_part_14.jpg"  # Replace with the actual path to your image
    # folder = "/MyImages"  # Optional: Specify a subfolder in OneDrive
    # user_principal_name = "<EMAIL>"

    # site_id = "0njf7.sharepoint.com,0f4f17cb-99e7-4464-928a-209cde225f2f"  # This is the Langfuse Site
    # file_path = f"{folder}/313dff300cbe725038eaaa7788413ed66ea9139385690b40bcf0961743c8bc9a_part_14.jpg"  # Replace with the server-relative path to your image in SharePoint

    # access_token = get_access_token()

    # if access_token:
    #     file_id = upload_file_to_onedrive_user(access_token, img_path, user_principal_name, folder)
    #     # file_id = upload_file_to_sharepoint(access_token, site_id, img_path, folder)
    #     if file_id:
    #         direct_image_url = get_onedrive_direct_file_link(access_token, user_principal_name, file_path)
    #         # direct_image_url = get_sharepoint_direct_file_link(access_token, site_id, file_path)
    #         print(direct_image_url)