import mysql.connector
import time
from datetime import datetime
import sys
import socket
# Removed: import logging
import subprocess
import os
from logdata import log_message # Added logdata import

# Removed logging configuration block

def check_tcp_connection(host, port):
    try:
        # Create socket with shorter timeout
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)

        # Record start time for SYN
        syn_start = time.time()
        result = sock.connect_ex((host, port))
        connect_time = time.time() - syn_start

        if result == 0:
            # Get socket details
            local_addr = sock.getsockname()
            remote_addr = sock.getpeername()
            log_message(f"TCP Connection Details:", level='INFO')
            log_message(f"  Local Address: {local_addr}", level='INFO')
            log_message(f"  Remote Address: {remote_addr}", level='INFO')
            log_message(f"  Connect Time: {connect_time:.3f}s", level='INFO')

            # Get TCP info if possible
            try:
                tcp_info = sock.getsockopt(socket.IPPROTO_TCP, socket.TCP_INFO)
                log_message(f"  TCP Info: {tcp_info}", level='INFO')
            except:
                pass

        sock.close()
        return result, connect_time
    except Exception as e:
        log_message(f"TCP connection check error: {e}", level='ERROR')
        return -1, 0

def test_connection(host, user, password, database, port=3306):
    start_time = time.time()

    # First test TCP
    tcp_result, tcp_time = check_tcp_connection(host, port)
    if tcp_result != 0:
        log_message(f"TCP connection failed with error code {tcp_result}", level='ERROR')

        # Try ping when TCP fails
        try:
            ping_output = subprocess.check_output(
                ['ping', '-c', '1', '-W', '5', host],
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            log_message(f"Ping result during TCP failure:\n{ping_output.strip()}", level='INFO')
        except subprocess.CalledProcessError as e:
            log_message(f"Ping failed during TCP failure: {e.output.strip()}", level='ERROR')

        return False, time.time() - start_time

    try:
        # Then try MySQL connection
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            connect_timeout=10
        )

        cursor = conn.cursor()
        cursor.execute("SELECT @@version")
        version = cursor.fetchone()
        cursor.close()
        conn.close()

        elapsed = time.time() - start_time
        log_message(f"Connection successful - MySQL Version: {version[0]}", level='INFO')
        log_message(f"Times - TCP: {tcp_time:.3f}s, Total: {elapsed:.3f}s", level='INFO')
        return True, elapsed

    except mysql.connector.Error as err:
        elapsed = time.time() - start_time
        log_message(f"MySQL Error: {err}", level='ERROR')
        log_message(f"Times - TCP: {tcp_time:.3f}s, Total: {elapsed:.3f}s", level='ERROR')
        return False, elapsed
    except Exception as err:
        elapsed = time.time() - start_time
        log_message(f"Unexpected error: {err}", level='ERROR')
        log_message(f"Times - TCP: {tcp_time:.3f}s, Total: {elapsed:.3f}s", level='ERROR')
        return False, elapsed

def main():
    HOST = os.getenv("MYSQL_HOST")
    DATABASE = os.getenv("MYSQL_DATABASE")
    USER = os.getenv("MYSQL_USER")
    PASSWORD = os.getenv("MYSQL_PASSWORD")

    total_attempts = 0
    failed_attempts = 0
    total_time = 0
    consecutive_failures = 0

    try:
        while True:
            total_attempts += 1
            success, elapsed = test_connection(HOST, USER, PASSWORD, DATABASE)
            total_time += elapsed

            if not success:
                failed_attempts += 1
                consecutive_failures += 1
                if consecutive_failures >= 3:
                    log_message(f"Three consecutive failures detected!", level='WARNING')
                    # Try traceroute on consecutive failures
                    try:
                        traceroute = subprocess.check_output(
                            ['traceroute', '-n', HOST],
                            stderr=subprocess.STDOUT,
                            universal_newlines=True
                        )
                        log_message(f"Traceroute during failures:\n{traceroute.strip()}", level='INFO')
                    except subprocess.CalledProcessError as e:
                        log_message(f"Traceroute failed: {e.output.strip()}", level='ERROR')
            else:
                consecutive_failures = 0

            if total_attempts % 10 == 0:
                success_rate = ((total_attempts - failed_attempts) / total_attempts) * 100
                avg_time = total_time / total_attempts
                log_message(f"Statistics after {total_attempts} attempts:", level='INFO')
                log_message(f"Success rate: {success_rate:.1f}%", level='INFO')
                log_message(f"Average connection time: {avg_time:.3f}s", level='INFO')

            time.sleep(5)

    except KeyboardInterrupt:
        log_message("\nMonitoring stopped by user", level='INFO')
        if total_attempts > 0:
            success_rate = ((total_attempts - failed_attempts) / total_attempts) * 100
            avg_time = total_time / total_attempts
            log_message(f"\nFinal Statistics:", level='INFO')
            log_message(f"Total attempts: {total_attempts}", level='INFO')
            log_message(f"Failed attempts: {failed_attempts}", level='INFO')
            log_message(f"Success rate: {success_rate:.1f}%", level='INFO')
            log_message(f"Average connection time: {avg_time:.3f}s", level='INFO')

if __name__ == "__main__":
    main()