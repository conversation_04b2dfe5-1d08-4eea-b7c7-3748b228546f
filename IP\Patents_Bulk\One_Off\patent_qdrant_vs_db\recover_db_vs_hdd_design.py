import os
import sys
import csv
from tqdm import tqdm
from dotenv import load_dotenv
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from IP.Patents_Bulk.patent_parser import parse_grant_xml_chunk
from IP.Patents_Bulk.patent_db_grant import upsert_patent_grants
from Common.Constants import local_ip_folder

# Load environment variables
load_dotenv()

DISCREPANCY_CSV = os.path.join(os.path.dirname(__file__), 'discrepancy_results', 'init', 'hdd_not_in_db_20250815_121935.csv')
MODE = "subset"  # Use same mode as main pipeline

def find_xml_for_image(image_path):
    # Try to find XML in the same folder or parent folder
    folder = Path(image_path).parent
    xml_files = list(folder.glob("*.xml"))
    if xml_files:
        return str(xml_files[0])
    # Try parent folder
    parent_folder = folder.parent
    xml_files = list(parent_folder.glob("*.xml"))
    if xml_files:
        return str(xml_files[0])
    return None

def main():
    print("Starting HDD vs DB recovery pipeline...")
    missing_records = []
    with open(DISCREPANCY_CSV, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            missing_records.append(row)
    print(f"Found {len(missing_records):,} files to recover.")
    # Parse XML files and upsert
    parsed_grants = []
    for rec in tqdm(missing_records, desc="Parsing patent XML for images"):
        image_path = rec['file_path']
        xml_path = find_xml_for_image(image_path)
        if xml_path and os.path.exists(xml_path):
            records, _ = parse_grant_xml_chunk(xml_path, mode=MODE, dest_dir=None)
            if records:
                parsed_grants.extend(records)
        else:
            print(f"XML not found for image: {image_path}")
    print(f"Parsed {len(parsed_grants):,} patent records. Upserting to DB...")
    upserted = upsert_patent_grants(parsed_grants, mode=MODE)
    print(f"Upserted {upserted:,} records to DB.")
    print("HDD vs DB recovery completed.")

if __name__ == "__main__":
    main()