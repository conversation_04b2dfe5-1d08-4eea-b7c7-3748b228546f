#!/usr/bin/env python3
"""
Migration script to transfer API keys from JSON file to database.
Run this script once to migrate the data.
"""

import sys
import os

# Add the Qdrant API path to sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Qdrant', 'api'))

from utils.db import create_api_keys_table, migrate_api_keys_from_json, get_api_keys_as_dict

def main():
    """Main migration function"""
    try:
        print("🔄 Starting API keys migration...")
        
        # Step 1: Create the table
        print("📋 Creating check_client_api_keys table...")
        create_api_keys_table()
        print("✅ Table created successfully")
        
        # Step 2: Migrate data from JSON
        json_file_path = "api_keys.json"
        if not os.path.exists(json_file_path):
            print(f"❌ Error: {json_file_path} not found")
            return False
            
        print(f"📥 Migrating data from {json_file_path}...")
        migrate_api_keys_from_json(json_file_path)
        print("✅ Data migration completed")
        
        # Step 3: Verify migration
        print("🔍 Verifying migration...")
        api_keys_dict = get_api_keys_as_dict()
        print(f"✅ Successfully loaded {len(api_keys_dict)} API keys from database")
        
        # Show a few examples
        print("\n📊 Sample API keys:")
        for i, (api_key, config) in enumerate(api_keys_dict.items()):
            if i >= 3:  # Show only first 3
                break
            print(f"  {api_key}: {config['client_name']} (ID: {config['id']})")
        
        if len(api_keys_dict) > 3:
            print(f"  ... and {len(api_keys_dict) - 3} more")
            
        print("\n🎉 Migration completed successfully!")
        print("💡 You can now update your applications to use database-backed API keys")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
