# USPTO Trademark Data Processing Tool

This tool handles the downloading, processing, and database loading of USPTO trademark data. It supports three modes of operation:

1. **Historical Batch Import**: One-time bulk import of historical trademark data (pre-2025)
2. **Daily Catch-up**: Download and process historical daily trademark update files from Jan 1st, 2025
3. **Ongoing Daily Processing**: Automatically download and process the latest daily trademark update file

## Prerequisites

- Python 3.8+
- PostgreSQL database
- NAS storage for backups
- USPTO API key (for daily updates)

## Installation

1. Install required Python packages:

```bash
pip install -r requirements.txt
```

2. Set up environment variables in a `.env` file:

```
# PostgreSQL connection
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=username
POSTGRES_PASSWORD=password
POSTGRES_DB=database_name

# USPTO API
USPTO_API_KEY=your_api_key

# NAS connection
NAS_HOST=nas_host
NAS_USERNAME=nas_username
NAS_PASSWORD=nas_password
```

## Directory Structure

The tool creates and uses the following directory structure:

```
IP/Trademarks/
├── pre2025/         # Historical ZIP files
├── daily/           # Daily update ZIP files
├── Images/          # Downloaded trademark images
│   └── XX/          # Last 2 digits of serial number
│       └── YY/      # 2 digits before last 2 digits
└── logs/            # Log files
```

## Usage

### Historical Batch Import

To perform a one-time bulk import of historical trademark data (pre-2025):

```bash
python daily_trademark_report.py --mode historical
```

This will download and process all 82 historical ZIP files from USPTO.

### Daily Catch-up

To download and process historical daily trademark update files from a specified start date:

```bash
python daily_trademark_report.py --mode catchup --start-date 2025-01-01 --end-date 2025-03-15
```

If `--start-date` is not provided, it defaults to Jan 1st, 2025.
If `--end-date` is not provided, it defaults to today's date.

### Ongoing Daily Processing

To automatically download and process the latest daily trademark update file:

```bash
python daily_trademark_report.py --mode daily
```

This will process the previous day's file by default. To process a specific date:

```bash
python daily_trademark_report.py --mode daily --date 2025-03-15
```

### Test Processing

To test the tool with a specific date (2025-03-15):

```bash
python daily_trademark_report.py --mode test
```

## Scheduling

For ongoing daily processing, you can set up a scheduled task to run the script daily:

### Using cron (Linux/macOS)

```bash
# Run daily at 5:00 AM
0 5 * * * cd /path/to/project && python IP/Trademarks/daily_trademark_report.py --mode daily
```

### Using Task Scheduler (Windows)

Create a batch file (e.g., `run_trademark_update.bat`):

```batch
cd /d D:\path\to\project
python IP\Trademarks\daily_trademark_report.py --mode daily
```

Then schedule this batch file to run daily using Windows Task Scheduler.

## Database Schema

The tool uses the `trademarks` table in PostgreSQL with the following schema:

```sql
CREATE TABLE trademarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reg_no TEXT,
    ser_no TEXT,
    TRO TEXT,
    applicant_name TEXT,
    mark_text TEXT,
    int_cls BIGINT[],
    filing_date DATE,
    plaintiff_ids BIGINT[],
    nb_suits BIGINT,
    country_codes TEXT[],
    associated_marks TEXT[],
    info_source TEXT,
    image_source TEXT,
    certificate_source TEXT,
    mark_current_status_code INTEGER,
    mark_feature_code INTEGER,
    mark_standard_character_indicator BOOLEAN,
    mark_disclaimer_text TEXT[],
    mark_disclaimer_text_daily TEXT[],
    mark_image_colour_claimed_text TEXT,
    mark_image_colour_part_claimed_text TEXT,
    mark_image_colour_statement_daily TEXT[],
    mark_translation_statement_daily TEXT,
    name_portrait_statement_daily TEXT,
    mark_description_statement_daily TEXT,
    certification_mark_statement_daily TEXT,
    lining_stippling_statement_daily TEXT,
    section_2f_statement_daily TEXT,
    national_design_code TEXT[],
    goods_services JSONB,
    goods_services_text_daily TEXT,
    case_file_statements_other JSONB,
    mark_current_status_external_description_text TEXT,
    create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

## Logging

The tool logs all activities to both console and log files:

- `daily_trademark_report.log`: Main script log
- `trademark_parser.log`: XML parsing log
- `trademark_db.log`: Database operations log
- `trademark_image.log`: Image download log
- `trademark_file.log`: File management log

## Error Handling

The tool implements robust error handling:

- Network errors during downloads are retried with exponential backoff
- Database errors are logged and transactions are rolled back
- XML parsing errors are caught and logged, allowing processing to continue
- Missing files or 404 errors are handled gracefully

## Image Processing

For trademarks with `mark_feature_code` equal to 2, 3, or 5, the tool downloads the image from USPTO and stores it in the `Images` directory with a structured path based on the serial number.

## NAS Backup

After processing, ZIP files are sent to NAS for backup using the `NASConnection` class. XML files are deleted after processing to save space.
