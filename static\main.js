document.addEventListener('DOMContentLoaded', function () {
    const startTaskButton = document.getElementById('startTask');
    const startUpdateTaskButton = document.getElementById('startUpdateTask');
    const progressBar = document.getElementById('progressBar');
    const logsDiv = document.getElementById('logs');
    const refreshApiKeysButton = document.getElementById('refreshApiKeys');
    let eventSource = null;
    let currentStepName = '';
    let lastRunningStepName = '';
    let lastDisplayedLogMessage = null;

    if (startTaskButton) {
        startTaskButton.addEventListener('click', function () {
            // Clear existing logs when starting a new run
            logsDiv.innerHTML = '';
            
            // Reset progress bar steps
            const steps = progressBar.querySelectorAll('.step');
            steps.forEach(step => {
                step.classList.remove('pending', 'running', 'completed', 'failed');
                step.classList.add('pending');
            });

            // Get the selected date or use today's date
            let selectedDate = document.getElementById('taskDate').value;
            if (!selectedDate) {
                const today = new Date();
                selectedDate = today.toISOString().split('T')[0];
            }

            // Get the selected loop_back_days
            const loopBackDays = document.getElementById('loopBackDays').value;
    
            fetch('/start_task', { 
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    selectedDate: selectedDate,
                    loopBackDays: loopBackDays,
                    forceRedo: document.getElementById('forceRedo').checked
                })
            })
                .then(response => response.json())
                .then(data => {
                    runId = data.run_id;
                    monitorRun(runId);
                });
        });
    }

    if (startUpdateTaskButton) {
        startUpdateTaskButton.addEventListener('click', function () {
            // Clear existing logs when starting a new run
            logsDiv.innerHTML = '';
            
            // Reset progress bar steps
            const steps = progressBar.querySelectorAll('.step');
            steps.forEach(step => {
                step.classList.remove('pending', 'running', 'completed', 'failed');
                step.classList.add('pending');
            });

            // Get the selected loop_back_days
            const loopBackDays = document.getElementById('loopBackDays').value;
    
            fetch('/start_update_task', { 
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    loopBackDays: loopBackDays
                })
            })
                .then(response => response.json())
                .then(data => {
                    runId = data.run_id;
                    monitorRun(runId);
                });
        });
    }

    if (refreshApiKeysButton) {
        refreshApiKeysButton.addEventListener('click', function() {
            fetch('/refresh_api_keys', {
                method: 'POST',
                credentials: 'same-origin'  // This ensures cookies are sent
            })
            .then(response => {
                if (!response.ok) {
                    if(response.status === 401) {
                        window.location.href = '/login';  // Redirect to login if unauthorized
                        return;
                    }
                    throw new Error('Refresh failed');
                }
                return response.json();
            })
            .then(data => {
                alert(`API keys refreshed! Loaded ${data.keys_loaded} keys`);
            })
            .catch(error => {
                alert('Error refreshing keys: ' + error.message);
            });
        });
    }

    // Only auto-monitor when viewing a historical run
    if (!startTaskButton && !startUpdateTaskButton && typeof runId !== 'undefined' && runId !== null) {
        monitorRun(runId);
    }

    function monitorRun(runId) {
        currentStepName = '';
        lastRunningStepName = '';
        stepNames = [];
        eventSource = new EventSource('/stream/' + runId);
        eventSource.onmessage = function (e) {
            try {
                const data = JSON.parse(e.data);
                if (data.type === 'log') {
                    handleLogMessage(data);
                } else if (data.type === 'status') {
                    console.log("eventSource Status Message: ", data);
                    handleStatusUpdate(data);
                }
            } catch (error) {
                console.error('Error parsing JSON:', error);
                console.log('Received data:', e.data);
            }
        };
    }

    
    // function handleLogMessage(logData) {
    //     console.log("handleLogMessage: ", logData, "currentStepName: ", currentStepName, "lastDisplayedLogId: ", lastDisplayedLogMessage);
    //     if (logData.step === currentStepName && logData.message !== lastDisplayedLogMessage) {
    //         console.log("handleLogMessage: ", logData.message);
    //         const logsDiv = document.getElementById('logs');
    //         logsDiv.innerHTML += '<p>' + logData.message + '</p>';
            
    //         // Scroll to the bottom of the logs div
    //         const logsContainer = document.getElementById('logsContainer');
    //         logsContainer.scrollTop = logsContainer.scrollHeight;

    //         lastDisplayedLogMessage = logData.message;
    //     }
    // }

    function handleLogMessage(logData) {
        console.log("handleLogMessage: ", logData, "\ncurrentStepName: ", currentStepName, "\nlastDisplayedLogMessage: ", lastDisplayedLogMessage);
        if (logData.step === currentStepName && logData.message !== lastDisplayedLogMessage) {
            const logsDiv = document.getElementById('logs');
    
            const ocrMatch = logData.message.match(/\(OCR\).*page count: (\d+)/);
            const pageMatch = logData.message.match(/text_in_page:.*page \d+ done/);
            if (ocrMatch) { // Check if the message indicates OCR processing
                const totalPages = parseInt(ocrMatch[1]);
                console.log('Starting OCR process with ', totalPages, ' pages');
                if (totalPages > 0) {
                    const progressBarHtml = `
                        <div id="ocrProgressBarContainer">
                            <div id="ocrProgressBar" class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                        </div>
                    `;
                    logsDiv.insertAdjacentHTML('afterend', progressBarHtml);
                    // Store total pages as a data attribute
                    const container = document.getElementById('ocrProgressBarContainer');
                    container.dataset.totalPages = totalPages;
                }
            }
            else if (pageMatch) { // Check for completed pages
                console.log('pageMatch');
                const container = document.getElementById('ocrProgressBarContainer');
                if (container) {
                    const totalPages = parseInt(container.dataset.totalPages);
                    // Extract the current page number directly from the message
                    const currentPage = parseInt(logData.message.match(/page (\d+) done/)[1]);
                    console.log(`Page completed: ${currentPage}/${totalPages}`);
                    
                    const progress = (currentPage / totalPages) * 100;
                    const progressBar = document.querySelector('#ocrProgressBar .progress-bar');
                    if (progressBar) {
                        progressBar.style.width = `${progress}%`;
                        progressBar.setAttribute('aria-valuenow', progress);
                        progressBar.textContent = `${Math.round(progress)}%`;
                        
                        // Only remove progress bar when we've actually completed all pages
                        if (currentPage >= totalPages) {
                            console.log('OCR completed, removing progress bar');
                            setTimeout(() => removeProgressBar('OCR Complete'), 1000);
                        }
                    }
                }
            }
            else {
                // Remove any existing progress bar
                removeProgressBar(logData.message);
    
                // Add the log message to the logs div
                logsDiv.innerHTML += '<p>' + logData.message + '</p>';
            }
    
            // Scroll to the bottom of the logs div
            const logsContainer = document.getElementById('logsContainer');
            logsContainer.scrollTop = logsContainer.scrollHeight;
    
            lastDisplayedLogMessage = logData.message;
        }
    }
    
    function removeProgressBar(message='') {
        const existingProgressBarContainer = document.getElementById('ocrProgressBarContainer');
        if (existingProgressBarContainer) {
            console.log('Progress bar removed by removeProgressBar function. Message: ', message);
            existingProgressBarContainer.remove();
        }
    }
    

    function handleStatusUpdate(statusData) {
        const steps = statusData.steps;
        let currentRunningStepName = '';
    
        // Update stepNames if it's empty (first status update)
        if (stepNames.length === 0) {
            console.log("handleStatusUpdate1: ", steps);
            stepNames = steps.map(step => step[0]);
            highlightStep(); // Now we can set up the click listeners
        }
    
        steps.forEach((step, index) => {
            console.log("handleStatusUpdate2: ", step);
            const [stepName, stepStatus] = step;
            const stepDiv = progressBar.querySelector(`.step[data-step="${index}"]`);
            stepDiv.classList.remove('pending', 'running', 'completed', 'failed');
            stepDiv.classList.add(stepStatus.toLowerCase());
            if (stepStatus.toLowerCase() === 'running') {
                currentRunningStepName = stepName;
            }
        });
    
        // Only fetch logs if the running step has changed
        if (currentRunningStepName && currentRunningStepName !== lastRunningStepName) {
            currentStepName = currentRunningStepName;
            console.log("handleStatusUpdate3: ", currentStepName);
            fetchLogs(runId, stepNames.indexOf(currentStepName));
            lastRunningStepName = currentRunningStepName;
        }
    
        if (statusData.run_status !== 'Running') {
            eventSource.close();
        }
    }

    function highlightStep() {
        const steps = progressBar.querySelectorAll('.step');
        steps.forEach((step, index) => {
            step.addEventListener('click', function () {
                currentStepName = stepNames[index];
                fetchLogs(runId, index);
            });
        });
    }
    
    
    function fetchLogs(runId, stepIndex) {
        fetch(`/get_logs/${runId}/${stepIndex}`)
            .then(response => response.json())
            .then(data => {
                logsDiv.innerHTML = '';
                lastDisplayedLogId = null;
                data.logs.forEach(log => {
                    if (log.id !== lastDisplayedLogId) {
                        logsDiv.innerHTML += '<p>' + log.message + '</p>';
                        lastDisplayedLogMessage = log.message;
                    }
                });
            });
    }
});