import pandas as pd
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch, get_table_from_GZ, get_table_from_GZ_with_connection, insert_df_to_GZ_batch_no_drop
from DatabaseManagement.Connections import empty_table, get_gz_connection
from AI.Translation import translate_steps
import async<PERSON>
from xlwings import view
# Usage: view(df)

def delete_duplicate_steps():
    """All the logic is handled within the SQL query:
    - ROW_NUMBER(): The core of the improvement is the use of the ROW_NUMBER() window function. Here's how it works:
    - PARTITION BY case_id, step_nb: This divides the rows into groups based on unique combinations of case_id and step_nb.
    - ORDER BY update_time DESC: Within each group, it orders the rows by update_time in descending order (most recent first).
    - ROW_NUMBER() ... as rn: This assigns a unique rank (1, 2, 3, ...) to each row within each group, based on the ordering. The most recent update_time gets rank 1.
    - Subquery: The inner SELECT statement creates a temporary table with the id, update_time, and the calculated row number (rn).
    - WHERE rn > 1: The outer SELECT filters this temporary table, keeping only rows where rn > 1. This effectively selects all the duplicate rows except the one with the highest update_time (which has rn = 1).
    - Outer DELETE: Finally, the DELETE statement removes all rows from tb_case_steps whose id is in the set of ids selected by the subquery."""

    # Identify and remove duplicate steps in the dataframe.
    connection = get_gz_connection()

    # Get the initial number of rows
    cursor = connection.cursor()
    cursor.execute("SELECT COUNT(*) FROM tb_case_steps")
    initial_count = cursor.fetchone()[0]
    print(f"Initial number of rows: {initial_count}")

    # delete duplicate steps keeping the most recent entry (using update_time)
    query = """
        DELETE FROM tb_case_steps
        WHERE id IN (
            SELECT id FROM (
                SELECT id, update_time,
                ROW_NUMBER() OVER (PARTITION BY case_id, step_nb ORDER BY update_time DESC) as rn
                FROM tb_case_steps
            ) s
            WHERE rn > 1
        )
    """
    cursor.execute(query)
    connection.commit()

    # Get the final number of rows
    cursor.execute("SELECT COUNT(*) FROM tb_case_steps")
    final_count = cursor.fetchone()[0]
    print(f"Final number of rows: {final_count}")

    cursor.close()
    connection.close()


def steps_missing_translation():
    cases_steps_df = get_table_from_GZ("tb_case_steps", force_refresh=True)
    cases_steps_df = cases_steps_df[cases_steps_df["case_id"] > 5000]
    # cases_steps_df = cases_steps_df[cases_steps_df["case_id"] <= 12000]
    # cases_steps_df_old = cases_steps_df.copy()
    # view(cases_steps_df_old)
    cases_steps_df = asyncio.run(translate_steps(cases_steps_df))
    # view(cases_steps_df)
    insert_and_update_df_to_GZ_batch(cases_steps_df, "tb_case_steps", "id")


def get_table_from_backup_db():
    with get_gz_connection(host="maidalv.com",port=3307,) as gz_connection:
        df_case_steps = get_table_from_GZ_with_connection(gz_connection, "tb_case_steps")
    df_case_steps.to_excel("D:/Win10User/Downloads/Steps_USA.xlsx", index=False)


def restore_backup():
    # df_case_steps = pd.read_excel("D:/Win10User/Downloads/Steps.xlsx")
    # empty_table("tb_case_steps")
    with get_gz_connection(host="maidalv.com",port=3307,) as gz_connection:
        df_case_steps = get_table_from_GZ_with_connection(gz_connection, "tb_case_steps") 
    df_case_steps.to_pickle("D:/Win10User/Downloads/Steps_GZ_USA.pkl")
    df_case_steps = pd.read_pickle("D:/Win10User/Downloads/Steps_USA.pkl")
    insert_df_to_GZ_batch_no_drop(df_case_steps, "tb_case_steps")


if __name__ == "__main__":
    delete_duplicate_steps()
    steps_missing_translation()
    # df_case_steps = get_table_from_GZ("tb_case_steps")
    # df_case_steps.to_pickle("D:/Win10User/Downloads/Steps_GZ_no_dupe.pkl")

