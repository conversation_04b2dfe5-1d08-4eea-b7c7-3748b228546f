import os
import sys
import time
import grpc
import asyncio
from pathlib import Path

# Base: batch of 10: 102.65, batch of 50: 101.75

# torch.backends.cuda.matmul.allow_tf32 = True
# torch.backends.cudnn.allow_tf32 = True
# torch.set_float32_matmul_precision("high")
# with torch.inference_mode():
#     if self.device.type == "cuda":
#         with torch.autocast(device_type="cuda", dtype=torch.float16):
#             feats = self.model.get_image_features(**inputs)
# 79.7

# Batching embeddings: 79.7

# 




# Add the parent directory to the Python path to allow importing from Protos
sys.path.append(str(Path(__file__).resolve().parent.parent))

from Protos import embedding_pb2
from Protos import embedding_pb2_grpc

PATENT_DATA_PATH = Path("/Documents/IP/Patents/USPTO_Grants/Zip/patent_extract_147wkf3q")
BATCH_SIZE = 50  # As defined in embedding_queue.py

def get_tiff_files(base_path, num_folders=20):
    """Gets all tiff files from the first `num_folders` directories."""
    tiff_files = []
    folder_count = 0
    if not base_path.exists():
        print(f"Error: Base path does not exist: {base_path}")
        return []

    for folder in sorted(base_path.iterdir()):
        if folder.is_dir():
            if folder_count >= num_folders:
                break
            
            for file_path in folder.glob('*.TIF'):
                tiff_files.append(file_path)
            
            folder_count += 1
            
    return tiff_files

async def main():
    """Main function to run the speed test."""
    print("Starting gRPC speed test...")

    # Set up gRPC channel and stub
    token = os.getenv("API_BEARER_TOKEN")
    if not token:
        print("API_BEARER_TOKEN environment variable not set.")
        return

    # Create a metadata preprocessor to add the bearer token
    class AuthInterceptor(grpc.UnaryUnaryClientInterceptor):
        def intercept_unary_unary(self, continuation, client_call_details, request):
            metadata = []
            if client_call_details.metadata is not None:
                metadata = list(client_call_details.metadata)
            metadata.append(('authorization', f'Bearer {token}'))
            client_call_details = client_call_details._replace(metadata=metadata)
            return continuation(client_call_details, request)

    # grpc_channel = grpc.insecure_channel('embeddings.maidalv.com', options=[  # '93.82.137.6:40381'
    #     ('grpc.max_send_message_length', 100 * 1024 * 1024),
    #     ('grpc.max_receive_message_length', 100 * 1024 * 1024)
    # ])
    
    creds = grpc.ssl_channel_credentials()  # uses system CAs
    grpc_channel = grpc.secure_channel('embeddings.maidalv.com:443', creds, options=[
        ('grpc.max_send_message_length', 100 * 1024 * 1024),
        ('grpc.max_receive_message_length', 100 * 1024 * 1024)
    ])
    
    intercepted_channel = grpc.intercept_channel(grpc_channel, AuthInterceptor())
    grpc_stub = embedding_pb2_grpc.EmbeddingServiceStub(intercepted_channel)

    # Get list of TIFF files
    tiff_files = get_tiff_files(PATENT_DATA_PATH)
    if not tiff_files:
        print("No TIFF files found. Exiting.")
        return

    print(f"Found {len(tiff_files)} TIFF files to process.")
    
    
    # Warm-up request
    print("Sending a warm-up request...")
    try:
        warmup_path = tiff_files[0]
        with open(warmup_path, "rb") as img_file:
            image_bytes = img_file.read()
        
        warmup_image_info = embedding_pb2.ImageInfo(
            image_data=image_bytes,
            original_filename=os.path.basename(warmup_path)
        )
        
        warmup_request = embedding_pb2.GetImageSplitWithEmbeddingsRequest(images=[warmup_image_info])
        grpc_stub.GetImageSplitWithEmbeddings(warmup_request)
        print("Warm-up request successful.")
    except Exception as e:
        print(f"Warm-up request failed: {e}")

    total_time = 0
    total_requests = 0

    for i in range(0, len(tiff_files), BATCH_SIZE):
        batch_paths = tiff_files[i:i + BATCH_SIZE]
        image_infos = []

        for image_path in batch_paths:
            try:
                with open(image_path, "rb") as img_file:
                    image_bytes = img_file.read()
                
                image_infos.append(embedding_pb2.ImageInfo(
                    image_data=image_bytes,
                    original_filename=os.path.basename(image_path)
                ))
            except Exception as e:
                print(f"Failed to read image {image_path}: {e}")
                continue
        
        if not image_infos:
            continue

        request = embedding_pb2.GetImageSplitWithEmbeddingsRequest(images=image_infos)

        try:
            start_time = time.time()
            response = grpc_stub.GetImageSplitWithEmbeddings(request)
            end_time = time.time()

            duration = end_time - start_time
            total_time += duration
            total_requests += 1
            
            print(f"Batch {total_requests}: Processed {len(image_infos)} images in {duration:.4f} seconds.")
            # You can add more detailed processing of the response if needed
            # For example, checking `len(response.results)`

        except grpc.RpcError as e:
            print(f"gRPC error on batch {total_requests + 1}: {e.code()} - {e.details()}")
        except Exception as e:
            print(f"An error occurred on batch {total_requests + 1}: {e}")

    if total_requests > 0:
        average_time = total_time / total_requests
        print("\n--- Test Summary ---")
        print(f"Total requests: {total_requests}")
        print(f"Total time: {total_time:.4f} seconds")
        print(f"Average time per batch: {average_time:.4f} seconds")
        print("--------------------")

    grpc_channel.close()

if __name__ == "__main__":
    asyncio.run(main())