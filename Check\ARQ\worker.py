import os
from arq.connections import RedisSettings
from Check.ARQ.tasks import process_check_task, process_check_lite_task
from Check.Data_Cache import update_dataframe_cache
from Check.gRPC_Client import init_grpc_channel, close_grpc_channel

async def on_startup(ctx):
    """
    Load models and cache in the worker process before it starts processing jobs.
    """
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-a81ed7b3-cbf2-494a-a6ff-c00b21778891"
    print("Set Langfuse environment variables for worker.")
    print("Initializing models for worker...")
    update_dataframe_cache()
    init_grpc_channel()
    print("Models and gRPC channel initialized for worker.")

async def on_shutdown(ctx):
    """
    Close the gRPC channel when the worker shuts down.
    """
    print("Closing gRPC channel...")
    await close_grpc_channel()
    print("gRPC channel closed.")

class WorkerSettings:
    functions = [process_check_task, process_check_lite_task]
    on_startup = on_startup
    on_shutdown = on_shutdown
    redis_settings = RedisSettings(host='localhost', port=6379)
    job_timeout = 300  # 5 minutes