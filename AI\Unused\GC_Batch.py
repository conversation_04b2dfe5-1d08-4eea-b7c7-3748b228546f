from google import genai
from google.genai.types import CreateBatchJobConfig
import pandas as pd
import os
import time
from datetime import datetime
from FileManagement.GC_COS import upload_to_gcs
from AI.GC_Credentials import get_gcs_credentials
import json
from google.cloud import storage
from AI.LLM_shared import get_json

# !!!!!! The minimum size of a context cache is 32,769 tokens => that is not for me!

# Configuration
PROJECT_ID = os.environ.get("GOOGLE_CLOUD_PROJECT")
REGION = os.environ.get("GOOGLE_CLOUD_LOCATION", "us-central1")
GCS_BUCKET_NAME = "trodata_bucket"
CREDENTIALS = get_gcs_credentials()
# MODEL_ID = "gemini-2.0-flash-lite-preview-02-05"
MODEL_ID = "gemini-2.0-flash-001"


def prepare_batch_input(product_path):
    """Prepares batch input files and uploads to GCS"""
    gcs_client = storage.Client(credentials=CREDENTIALS)
    gcs_bucket = gcs_client.bucket(GCS_BUCKET_NAME)
    
    # First upload all product images to GCS
    image_uris = []
    files = os.listdir(product_path)
    files = files[:10]
    for file in files:
        local_path = os.path.join(product_path, file)
        gcs_path = f"genai_batch_input/images/{file}"
        upload_to_gcs(local_path, gcs_bucket, gcs_path)
        image_uris.append(f"gs://{GCS_BUCKET_NAME}/{gcs_path}")
    
    # Create JSONL input file
    jsonl_path = "batch_requests.jsonl"
    with open(jsonl_path, "w") as f:
        for uri in image_uris:
            request = {
                "request": {
                    "contents": [{
                        "role": "user",
                        "parts": [
                            {"text": "This is a product being sold. What is the brand? How confident are you? Return JSON: {\"brand\":\"coca cola\", \"confidence\": 0.87}. If you do not know the brand, return {\"brand\":\"unknown\", \"confidence\": 0}"},
                            {"file_data": {
                                "file_uri": uri,
                                "mime_type": "image/jpeg"
                            }}
                        ]
                    }],
                    "generationConfig": {"temperature": 0}
                }
            }
            f.write(json.dumps(request) + "\n")
    
    # Upload JSONL to GCS
    jsonl_gcs_path = "genai_batch_input/requests.jsonl"
    upload_to_gcs(jsonl_path, gcs_bucket, jsonl_gcs_path)
    return f"gs://{GCS_BUCKET_NAME}/{jsonl_gcs_path}"

def process_batch_results(output_uri, files):
    """Processes batch results from GCS"""
    # Download and parse results
    results_df = pd.DataFrame(columns=["picture", "Plaintiff", "AI_brand", "AI_confidence"])
    
    # Load results (assuming single output file)
    # Construct the correct path to the predictions file.
    gcs_client = storage.Client(credentials=CREDENTIALS)
    bucket = gcs_client.bucket(GCS_BUCKET_NAME)
    
    # List blobs in the output directory
    blobs = bucket.list_blobs(prefix=output_uri.split(GCS_BUCKET_NAME + "/")[1] + "/")  # e.g. 'genai_batch_output/20250205173814/'
    predictions_file = None
    for blob in blobs:
        if blob.name.endswith("predictions.jsonl"):

            predictions_file = blob.name
            break
    
    if predictions_file is None:
        raise FileNotFoundError("Could not find predictions.jsonl in the output directory.")

    df = pd.read_json(f"gs://{GCS_BUCKET_NAME}/{predictions_file}", lines=True)
    df = df.join(pd.json_normalize(df["response"], "candidates"))
    
    input_tokens = 0
    output_tokens = 0
    for i, row in df.iterrows():
        try:
            answer = get_json(row['response']['candidates'][0]['content']['parts'][0]['text'])

            plaintiff = files[i].replace("_cropped", "").split("_")[-1].split(".")[0]

            input_tokens += row['response']['usageMetadata']['promptTokenCount']
            output_tokens += row['response']['usageMetadata']['candidatesTokenCount']

            if isinstance(answer, dict):
                results_df.loc[len(results_df)] = [
                    files[i], plaintiff,
                    answer.get("brand"),
                    answer.get("confidence")
                ]
            else:
                results_df.loc[len(results_df)] = [files[i], plaintiff, "parse_error", 0]
        except Exception as e:
            print(f"Error processing row {i}: {e}")
            results_df.loc[len(results_df)] = [files[i], "error", "error", 0]
    
    return results_df, input_tokens, output_tokens

def get_brand_gemini_batch():
    product_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/3. Products - SmartCrop"
    files = os.listdir(product_path)
    
    # 1. Prepare input data
    input_uri = prepare_batch_input(product_path)
    
    # 2. Create batch client
    client = genai.Client(credentials=CREDENTIALS, project=PROJECT_ID, location=REGION)
    
    # 3. Create batch job
    output_uri = f"gs://{GCS_BUCKET_NAME}/genai_batch_output/{datetime.now().strftime('%Y%m%d%H%M%S')}"
    batch_job = client.batches.create(
        model=MODEL_ID,
        src=input_uri,
        config=CreateBatchJobConfig(dest=output_uri)
    )
    
    # 4. Monitor job status
    start_time = time.time()
    while True:
        batch_job = client.batches.get(name=batch_job.name)
        if batch_job.state in ["JOB_STATE_SUCCEEDED", "JOB_STATE_FAILED"]:
            break
        elapsed = time.time() - start_time
        print(f"Job status: {batch_job.state} - waiting 30 seconds... (Elapsed time: {elapsed:.1f}s)")
        time.sleep(30)
    
    total_time = time.time() - start_time
    print(f"Job completed in {total_time:.1f} seconds with status {batch_job.state}")
    
    if batch_job.state == "JOB_STATE_FAILED":
        raise Exception(f"Batch job failed: {batch_job.error}")
    
    # 5. Process results
    results_df, input_tokens, output_tokens = process_batch_results(output_uri, files)
    results_df.to_excel("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/results_batch.xlsx", index=False)

if __name__ == "__main__":
    get_brand_gemini_batch()